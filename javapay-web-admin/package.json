{"name": "ele-admin-pro-js", "version": "1.8.1", "private": true, "scripts": {"dev": "vite", "serve": "vite build && vite preview", "test": "cross-env NODE_OPTIONS=--max_old_space_size=16384 vite build --mode test", "build": "cross-env NODE_OPTIONS=--max_old_space_size=16384 vite build", "lint:eslint": "eslint --cache --max-warnings 0  \"src/**/*.{vue,js}\" --fix", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite/", "clean:lib": "rimraf node_modules"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@ant-design/colors": "^6.0.0", "@ant-design/icons-vue": "^6.1.0", "@bytemd/plugin-gfm": "^1.13.1", "@tinymce/tinymce-vue": "^4.0.7", "ant-design-vue": "^3.1.1", "axios": "^0.26.1", "bytemd": "^1.13.1", "countup.js": "^2.1.0", "cropperjs": "^1.5.12", "dayjs": "^1.11.0", "echarts": "^5.3.2", "echarts-wordcloud": "^2.0.0", "ele-admin-pro": "^1.8.1", "font-awesome": "^4.7.0", "github-markdown-css": "^5.1.0", "imagemin-web": "^1.1.1", "jquery": "^3.6.0", "js-image-compressor": "^2.0.0", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "number-precision": "^1.6.0", "pinia": "^2.0.13", "qs": "^6.10.3", "raphael": "^2.3.0", "tinymce": "^5.10.3", "vue": "3.2.30", "vue-echarts": "^6.0.2", "vue-i18n": "^9.1.9", "vue-router": "^4.0.14", "vuedraggable": "^4.1.0", "xgplayer": "^2.31.6", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-legacy": "^1.8.0", "@vitejs/plugin-vue": "^2.3.1", "@vue/compiler-sfc": "3.2.30", "cross-env": "^7.0.3", "eslint": "^8.12.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.6.0", "less": "^4.1.2", "postcss": "^8.4.12", "prettier": "^2.6.2", "unplugin-vue-components": "^0.19.3", "vite": "^2.9.5", "vite-plugin-compression": "^0.5.1", "vue-eslint-parser": "^8.3.0"}}