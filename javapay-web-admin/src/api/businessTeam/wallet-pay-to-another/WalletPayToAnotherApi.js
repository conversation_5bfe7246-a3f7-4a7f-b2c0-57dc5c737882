import Request from '@/utils/request-util';

/**
 * 代付管理Api
 */
export class WalletPayToAnotherApi {
  /** 分页 */
  static findPage(params) {
    return Request.postAndLoadData('/walletPayToAnother/page', params);
  }

  /** 详情 */
  static detail(params) {
    return Request.postAndLoadData('/walletPayToAnother/detail', params);
  }

  /** 汇总 */
  static sum(params) {
    return Request.postAndLoadData('/walletPayToAnother/sum', params);
  }
}
