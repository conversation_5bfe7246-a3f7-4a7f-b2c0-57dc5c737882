import Request from '@/utils/request-util';

/**
 * 提现批次管理Api
 */
export class WithdrawBatchManageApi {
  /** 分页列表 */
  static findPage(params) {
    return Request.postAndLoadData('/profitRemitBatch/page', params);
  }

  /** 详情 */
  static detail(params) {
    return Request.postAndLoadData('/profitRemitBatch/detail', params);
  }

  /** 出款文件记录下载 */
  static download(data) {
    return Request.requestByConfigNoData({
      url: '/profitRemitBatch/download',
      method: 'post',
      data,
      responseType: 'blob'
    });
  }
}
