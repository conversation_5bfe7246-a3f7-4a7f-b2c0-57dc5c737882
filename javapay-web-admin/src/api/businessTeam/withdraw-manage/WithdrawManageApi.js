import Request from '@/utils/request-util';

/**
 * 提现管理Api
 */
export class WithdrawManageApi {
  /** 分页列表 */
  static findPage(params) {
    return Request.postAndLoadData('/orgWithdrawInfo/page', params);
  }

  /** 详情 */
  static detail(params) {
    return Request.postAndLoadData('/orgWithdrawInfo/detail', params);
  }

  /** 手续费详情 */
  static feeDetail(params) {
    return Request.postAndLoadData('/orgWithdrawFee/detail', params);
  }

  /** 生成出款批次 */
  static generateBatchNo(params) {
    return Request.post('/orgWithdrawInfo/generateBatchNo', params);
  }

  /** 人工处理 */
  static resultManualHandle(params) {
    return Request.post('/orgWithdrawInfo/resultManualHandle', params);
  }

  /** 汇总 */
  static sum(params) {
    return Request.postAndLoadData('/orgWithdrawInfo/sum', params);
  }

  /** 出款文件记录下载 */
  static download(data) {
    return Request.requestByConfig({
      url: '/orgWithdrawInfo/download',
      method: 'post',
      data,
      responseType: 'blob'
    });
  }

  /** 获取提现出款通道信息 */
  static getWithdrawRemitChannel(params) {
    return Request.postAndLoadData('/orgWithdrawInfo/getWithdrawRemitChannel', params);
  }

  /** 获取提现税费信息 */
  static getWithdrawTaxInfo(params) {
    return Request.postAndLoadData('/orgWithdrawInfo/getWithdrawTaxInfo', params);
  }

  /** 钱包提现 */
  static walletWithdraw(params) {
    return Request.post('/orgWithdrawInfo/walletWithdraw', params);
  }

  /** 查钱包余额 */
  static getProfitBalance(params) {
    return Request.postAndLoadData('/orgWithdrawInfo/getOrgAccountBalance', params);
  }

  /** 获取大区账户余额 */
  static getRegionAccountBalance(params) {
    return Request.postAndLoadData('/orgWithdrawInfo/getRegionAccountBalance', params);
  }

    /** 钱包提现 */
    static transferOpt(params) {
      return Request.post('/orgWithdrawInfo/transferOpt', params);
    }

    /** 审核 */
    static invoiceCheck(params) {
      return Request.post('/orgWithdrawInfo/invoiceCheck', params);
    }
    /** 机构状态查询 */
    static getOrgStatus(params) {
      return Request.postAndLoadData('/orgStatus/detail', params);
    }
    /** 查询钉灵工签约三要素（反显） */
    static queryOrgAuthInfo(params) {
      return Request.postAndLoadData('/dlg/queryOrgAuthInfo', params);
    }

    /** 钉灵工签约 */
    static signDlg(params) {
     return Request.post('/dlg/sign', params);
   }
}
