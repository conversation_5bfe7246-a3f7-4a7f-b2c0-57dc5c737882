import Request from '@/utils/request-util';
import { useUserStore } from '@/store/modules/user';

// userType值  平台运营 0、 大区 1、 运营中心 2、 代理商 3
export let userType = useUserStore().info.userType;
if (!userType) throw new Error('用户等级获取有误!');
const pathNames = ['', 'region', 'branch', 'agent', '', 'agent'];
let dynamicPathByUser = pathNames[Number(userType)];

/**
 * 结算卡Api
 */
export class PaymentCardApi {
  // 分页
  static findPage(params) {
    if (userType === '0') {
      return Request.postAndLoadData(`/bankCard/findPage`, params);
    }
    return Request.postAndLoadData(`/${dynamicPathByUser}/bankCardFindPage`, params);
  }

  // 添加
  static add(params) {
    return Request.post(`/orgBankCard/add`, params);
  }

  // 编辑
  static edit(params) {
    return Request.post(`/orgBankCard/edit`, params);
  }
}
