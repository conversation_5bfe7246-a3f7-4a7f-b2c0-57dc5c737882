import Request from '@/utils/request-util';

/**
 * 未/伪激活配置api
 */
export class ActiveRuleConfigApi {
  /**
   * 获取分页
   */
  static findPage(params) {
    return Request.postAndLoadData('/activeRuleConf/page', params);
  }

  /**
   * 获取列表
   */
  static list(params) {
    return Request.postAndLoadData('/activeRuleConf/list', params);
  }


  /**
   * 添加
   */
  static add(params) {
    return Request.post('/activeRuleConf/add', params);
  }

  /**
   * 编辑
   */
  static edit(params) {
    return Request.post('/activeRuleConf/edit', params);
  }

}
