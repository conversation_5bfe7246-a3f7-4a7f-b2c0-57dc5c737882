import Request from '@/utils/request-util';

/**
 * 代理商api
 */
export class AgentCenterApi {
  //分页数据
  static findPage(params) {
    return Request.postAndLoadData('/agent/page', params);
  }

  static findSubAgentPage(params) {
    return Request.postAndLoadData('/agent/subAgentPage', params);
  }

  //新增
  static add(params) {
    return Request.post('/agent/add', params);
  }

  //编辑
  static edit(params) {
    return Request.post('/agent/edit', params);
  }

  //活动政策/详情
  static detailCashbackPolicy(params) {
    return Request.postAndLoadData('/cashbackPolicy/detail', params);
  }

  //编辑直属下级活动返现政策
  static editActivityCashBackPolicy(params) {
    return Request.post('/cashbackPolicy/edit', params);
  }

  //驳回编辑
  static rejectEdit(params) {
    return Request.post('/agent/rejectEdit', params);
  }

  //查看详情
  static detail(params) {
    return Request.postAndLoadData('/agent/detail', params);
  }

  //查看详情
  static detailReject(params) {
    return Request.postAndLoadData('/agent/rejectEditDetail', params);
  }

  //查看个人详情
  static personDetail(params) {
    return Request.postAndLoadData('/agent/personDetail', params);
  }

  // 修改结算周期、结算方式
  static editSettleRemit(params) {
    return Request.post('/agent/editSettleRemit', params);
  }

  // 修改结算周期、结算方式(批量)
  static batchEditSettleRemit(params) {
    return Request.post('/agent/batchEditSettleRemit', params);
  }

  //参数配置-添加
  static addParamConf(params) {
    return Request.post('/agentParamConf/add', params);
  }

  //参数配置-详情
  static detailParamConf(params) {
    return Request.postAndLoadData('/agentParamConf/detail', params);
  }

  //参数配置-编辑
  static editParamConf(params) {
    return Request.post('/agentParamConf/edit', params);
  }

  // 一代编辑税点（仅支持一代操作）
  static editTaxPoint(params) {
    return Request.post('/agent/editTaxPoint', params);
  }

  // 编辑APP品牌类型
  static editAppBrandType(params) {
    return Request.post('/agent/editAppBrand', params);
  }

  // 状态详情
  static statusDetail(params) {
    return Request.postAndLoadData('/agentStatus/detail', params);
  }

  // 状态编辑
  static statusEdit(params) {
    return Request.post('/agentStatus/edit', params);
  }

    //查看详情
    static getOrgReportRecord(params) {
      return Request.postAndLoadData('/orgReportRecord/detail', params);
    }

    //查看详情
    static editOrgReportRecord(params) {
      return Request.post('/orgReportRecord/edit', params);
    }
}
