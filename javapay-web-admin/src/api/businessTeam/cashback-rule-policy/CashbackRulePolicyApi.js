import Request from '@/utils/request-util';

/**
 * 激活/返现政策管理api
 */
export class CashbackRulePolicyApi {
  /**
   * 获取分页
   */
  static findPage(params) {
    return Request.postAndLoadData('/cashbackRulePolicy/page', params);
  }

  /**
   * 获取历史分页
   */
  static findHisPage(params) {
    return Request.postAndLoadData('/cashbackRulePolicyHis/page', params);
  }

  /**
   * 获取列表
   */
  static list(params) {
    return Request.postAndLoadData('/cashbackRulePolicy/list', params);
  }

  /**
   * 获取政策名称列表
   */
  static policyNameList(params) {
    return Request.postAndLoadData('/cashbackRulePolicy/policyNameList', params);
  }

  /**
   * 添加
   */
  static add(params) {
    return Request.post('/cashbackRulePolicy/addRulePolicy', params);
  }

  /**
   * 机构自身政策编辑
   */
  static selfEdit(params) {
    return Request.post('/cashbackRulePolicy/editSelfRulePolicy', params);
  }

  /**
   * 直属子代激活/返现政策编辑（一代或子代）
   */
  static subAgentEdit(params) {
    return Request.post('/cashbackRulePolicy/subAgentEdit', params);
  }

  /**
   * 直属子代政策开通/补开通
   */
  static subAgentOpen(params) {
    return Request.post('/cashbackRulePolicy/subAgentOpen', params);
  }

  /**
   * 直属子代政策关闭(仅一级代理有权限)
   */
  static subAgentClose(params) {
    return Request.post('/cashbackRulePolicy/subAgentClose', params);
  }

  /**
   * 获取直属下级的开通情况
   */
  static getDirectAgentOpenSt(params) {
    return Request.postAndLoadData('/cashbackRulePolicy/getDirectAgentOpenSt', params);
  }
}
