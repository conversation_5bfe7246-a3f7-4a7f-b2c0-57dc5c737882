import Request from '@/utils/request-util';
import { useUserStore } from '@/store/modules/user';

// userType值  平台运营 0、 大区 1、 运营中心 2、 代理商 3 、下级代理商 5
export let userType = useUserStore().info.userType;
if (!userType) throw new Error('用户等级获取有误!');
const pathNames = ['', 'region', 'branch', 'agent', '', 'agent'];
let dynamicPathByUser = pathNames[Number(userType)];

/**
 * 费率政策Api
 */
export class RatePolicyApi {
  //分页
  static findPage(params) {
    return Request.postAndLoadData('/ratePolicy/page', params);
  }

  //列表
  static list(params) {
    return Request.postAndLoadData('/ratePolicy/list', params);
  }

  //查看
  static detail(params) {
    return Request.postAndLoadData('/ratePolicy/detail', params);
  }

  //查看
  static delete(params) {
    return Request.post('/ratePolicy/delete', params);
  }

  //获取个人基本信息
  static personDetail(params) {
    return Request.postAndLoadData(`/${dynamicPathByUser}/personDetail`, params);
  }

  //获取银行卡信息
  static accountDetail(params) {
    return Request.postAndLoadData(`/${dynamicPathByUser}/accountDetail`, params);
  }

  //添加
  static add(params) {
    return Request.post(`/ratePolicy/addRatePolicy`, params);
  }

  //修改
  static edit(params) {
    return Request.post(`/ratePolicy/editRatePolicy`, params);
  }

  //费率政策同步到下级
  static syncRatePolicy(params) {
    return Request.post(`/ratePolicy/syncRatePolicy`, params);
  }

  //费率政策同步到商户
  static syncMerchantRatePolicy(params) {
    return Request.post(`/ratePolicy/syncRatePolicyToMerch`, params);
  }

  //获取运营中心列表，并标记相同的费率政策
  static listBranchAndMarkNoSame(params) {
    return Request.postAndLoadData(`/ratePolicy/listAndMarkSame`, params);
  }

  //获取指定机构的费率政策列表
  static listOfOrg(params) {
    return Request.postAndLoadData(`/ratePolicy/listOfOrg`, params);
  }

  /**
   * 费率政策历史记录
   */

  //分页
  static findHisPage(params) {
    return Request.postAndLoadData('/ratePolicyHis/page', params);
  }

  //列表
  static listHis(params) {
    return Request.postAndLoadData('/ratePolicyHis/list', params);
  }

  //查看
  static detailHis(params) {
    return Request.postAndLoadData('/ratePolicyHis/detail', params);
  }

  /**
   * 费率政策同步记录
   */

  //分页
  static findSyncPage(params) {
    return Request.postAndLoadData('/ratePolicySync/page', params);
  }

  //列表
  static listSync(params) {
    return Request.postAndLoadData('/ratePolicySync/list', params);
  }

  //查看
  static detailSync(params) {
    return Request.postAndLoadData('/ratePolicySync/detail', params);
  }
}
