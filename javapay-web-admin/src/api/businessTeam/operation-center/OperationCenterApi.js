import Request from '@/utils/request-util';

/**
 * 运营中心api
 */
export class OperationCenterApi {
  //分页数据
  static findBranchPage(params) {
    return Request.postAndLoadData('/branch/page', params);
  }

  //新增
  static addBranch(params) {
    return Request.post('/branch/add', params);
  }

  //编辑基本信息
  static editBranch(params) {
    return Request.post('/branch/edit', params);
  }

  //删除
  static deleteBranch(params) {
    return Request.post('/branch/delete', params);
  }

  //查看详情
  static detailBranch(params) {
    return Request.postAndLoadData('/branch/detail', params);
  }

  //查看个人详情
  static personDetail(params) {
    return Request.postAndLoadData('/branch/personDetail', params);
  }

  // 修改结算周期、结算方式
  static editSettleRemit(params) {
    return Request.post('/branch/editSettleRemit', params);
  }

  // 修改结算周期、结算方式(批量)
  static batchEditSettleRemit(params) {
    return Request.post('/branch/batchEditSettleRemit', params);
  }

  // 编辑税点
  static editTaxPoint(params) {
    return Request.post('/branch/editTaxPoint', params);
  }

  // 编辑APP品牌类型
  static editAppBrandType(params) {
    return Request.post('/branch/editAppBrand', params);
  }

  // 状态详情
  static statusDetail(params) {
    return Request.postAndLoadData('/branchStatus/detail', params);
  }

  // 状态编辑
  static statusEdit(params) {
    return Request.post('/branchStatus/edit', params);
  }
}
