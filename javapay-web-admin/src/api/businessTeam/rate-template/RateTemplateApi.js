import Request from '@/utils/request-util';

/**
 * 费率模板Api
 */
export class RateTemplateApi {
  //分页
  static findPage(params) {
    return Request.postAndLoadData('/rateSysTemp/page', params);
  }

  //添加
  static add(params) {
    return Request.post('/rateSysTemp/add', params);
  }

  //查看
  static detail(params) {
    return Request.postAndLoadData('/rateSysTemp/detail', params);
  }

  //修改
  static edit(params) {
    return Request.post('/rateSysTemp/edit', params);
  }

  //获取列表
  static list(params) {
    return Request.postAndLoadData('/rateSysTemp/list', params);
  }

  //银行编码列表同步道费率政策
  static bankCodesSyncToPolicy(params) {
    return Request.post('/rateSysTemp/bankCodesSyncToPolicy', params);
  }

  //银行编码列表同步道费率信息
  static bankCodesSyncToRate(params) {
    return Request.post('/rateSysTemp/bankCodesSyncToRate', params);
  }


}
