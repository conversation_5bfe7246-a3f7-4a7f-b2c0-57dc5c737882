import Request from '@/utils/request-util';

/**
 * 活动返现配置管理api
 */
export class ActivityCashbackTemplateManageApi {
/**
   * 获取页数列表
   */
static findPages(params) {
    return Request.postAndLoadData('/activityCashbackTemplate/page', params);
  }

  /**
   * 获取列表
   */
  static findList(params) {
    return Request.postAndLoadData('/activityCashbackTemplate/list', params);
  }

  /**
   * 新增
   */
  static add(params) {
    return Request.post('/activityCashbackTemplate/add', params);
  }

  /**
   * 修改
   */
  static edit(params) {
    return Request.post('/activityCashbackTemplate/edit', params);
  }

  /**
   * 详情
   */
  static detail(params) {
    return Request.postAndLoadData('/activityCashbackTemplate/detail', params);
  }

  /**
   * 删除
   */
  static delete(params) {
    return Request.post('/activityCashbackTemplate/delete', params);
  }

   /**
   * 活动模板列表
   */
   static querySelfList(params) {
    return Request.postAndLoadData('/activityCashbackTemplate/querySelfList', params);
  }

  /**
   * 活动政策模板添加反显查询

   */
  static displayDetail(params) {
    return Request.postAndLoadData('/activityCashbackTemplate/queryAddDisplay', params);
  }


  /**
   * 活动政策/详情
   */
  static detailCashbackPolicy(params) {
    return Request.postAndLoadData('/cashbackPolicy/detail', params);
  }

  /**
   * 活动政策/编辑
   */
  static editCashbackPolicy(params) {
    return Request.post('/cashbackPolicy/edit', params);
  }


}
