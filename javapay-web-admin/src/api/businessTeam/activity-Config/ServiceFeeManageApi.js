import Request from '@/utils/request-util';

/**
 * 服务费管理api
 */
export class ServiceFeeManageApi {
/**
   * 获取页数列表
   */
static findPages(params) {
    return Request.postAndLoadData('/serviceFeeConf/page', params);
  }

  /**
   * 获取列表
   */
  static list(params) {
    return Request.postAndLoadData('/serviceFeeConf/list', params);
  }

  /**
   * 新增
   */
  static add(params) {
    return Request.post('/serviceFeeConf/add', params);
  }

  /**
   * 修改
   */
  static edit(params) {
    return Request.post('/serviceFeeConf/edit', params);
  }

  /**
   * 详情
   */
  static detail(params) {
    return Request.postAndLoadData('/serviceFeeConf/detail', params);
  }

  /**
   * 删除
   */
  static delete(params) {
    return Request.post('/serviceFeeConf/delete', params);
  }

  /**
   * 修改有效状态
   */
  static changeStatus(params) {
    return Request.post('/serviceFeeConf/changeStatus', params);
  }

   /**
   * (已开通)服务费政策列表（无参）
   */
   static selfOpenList(params) {
    return Request.postAndLoadData('/serviceFeePolicy/selfOpenList', params);
  }
}
