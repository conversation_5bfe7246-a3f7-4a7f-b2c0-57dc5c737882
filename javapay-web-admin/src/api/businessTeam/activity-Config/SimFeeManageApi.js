import Request from '@/utils/request-util';

/**
 * 流量费管理api
 */
export class SimFeeManageApi {
/**
   * 获取页数列表
   */
static findPages(params) {
    return Request.postAndLoadData('/simFeeConfig/page', params);
  }

  /**
   * 获取列表
   */
  static list(params) {
    return Request.postAndLoadData('/simFeeConfig/list', params);
  }

  /**
   * 新增
   */
  static add(params) {
    return Request.post('/simFeeConfig/add', params);
  }

  /**
   * 修改
   */
  static edit(params) {
    return Request.post('/simFeeConfig/edit', params);
  }

  /**
   * 详情
   */
  static detail(params) {
    return Request.postAndLoadData('/simFeeConfig/detail', params);
  }

  /**
   * 删除
   */
  static delete(params) {
    return Request.post('/simFeeConfig/delete', params);
  }

  /**
   * 修改有效状态
   */
  static changeStatus(params) {
    return Request.post('/simFeeConfig/changeStatus', params);
  }

    /**
   * (已开通)流量费政策列表（无参）
   */
    static selfOpenList(params) {
      return Request.postAndLoadData('/simFeePolicy/selfOpenList', params);
    }
}
