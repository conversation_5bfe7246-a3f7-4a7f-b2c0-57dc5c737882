import Request from '@/utils/request-util';

/**
 * 机构通道报备Api
 */
export class OrgReportRecordApi {
  /** 分页 */
  static findPage(params) {
    return Request.postAndLoadData('/orgReportRecord/page', params);
  }

  /** 详情 */
  static detail(params) {
    return Request.postAndLoadData('/orgReportRecord/detail', params);
  }

  /** 编辑 */
  static edit(params) {
    return Request.post('/orgReportRecord/edit', params);
  }

  /** 重新报备 */
  static changeReportStatus(params) {
    return Request.post('/orgReportRecord/changeReportStatus', params);
  }
}
