import Request from '@/utils/request-util';

/**
 * 大区管理Api
 */
export class RegionManageApi {
  //分页列表
  static findPage(params) {
    return Request.postAndLoadData('/region/page', params);
  }

  //添加
  static add(params) {
    return Request.post('/region/add', params);
  }

  //删除
  static delete(params) {
    return Request.post('/region/delete', params);
  }

  //批量删除
  static batchDelete(params) {
    return Request.post('/region/batchDelete', params);
  }

  //获取基本信息
  static detail(params) {
    return Request.postAndLoadData('/region/detail', params);
  }

  //获取个人基本信息
  static personDetail(params) {
    return Request.postAndLoadData('/region/personDetail', params);
  }

  //修改基本信息
  static edit(params) {
    return Request.post('/region/edit', params);
  }

  // 修改费率信息
  static editRate(params) {
    return Request.post('/region/editRate', params);
  }

  // 查看费率信息详情
  static rateDetail(params) {
    return Request.postAndLoadData('/region/rateDetail', params);
  }

  // 修改结算周期、结算方式
  static editSettleRemit(params) {
    return Request.post('/region/editSettleRemit', params);
  }

  // 修改结算周期、结算方式(批量)
  static batchEditSettleRemit(params) {
    return Request.post('/region/batchEditSettleRemit', params);
  }

  // 编辑税点
  static editTaxPoint(params) {
    return Request.post('/region/editTaxPoint', params);
  }

  // 编辑APP品牌类型
  static editAppBrandType(params) {
    return Request.post('/region/editAppBrand', params);
  }

  // 状态详情
  static statusDetail(params) {
    return Request.postAndLoadData('/regionStatus/detail', params);
  }

  // 状态编辑
  static statusEdit(params) {
    return Request.post('/regionStatus/edit', params);
  }

  // 获取大区状态
  static getOneRegionStatus(params) {
    return Request.postAndLoadData('/regionStatus/getOneByParams', params);
  }

  //参数配置-详情
  static detailParamConf(params) {
    return Request.postAndLoadData('/regionParamConf/detail', params);
  }

  //参数配置-编辑
  static editParamConf(params) {
    return Request.post('/regionParamConf/edit', params);
  }
}
