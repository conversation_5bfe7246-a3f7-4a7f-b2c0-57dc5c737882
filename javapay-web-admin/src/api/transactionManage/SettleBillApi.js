import Request from '@/utils/request-util';

/**
 * 结算账单Api
 */
export class SettleBillApi {
  //分页
  static findPage(params) {
    return Request.postAndLoadData('/profitSettleBill/page', params);
  }

  //详情
  static detail(params) {
    return Request.postAndLoadData('/profitSettleBill/detail', params);
  }

  //统计
  static sum(params) {
    return Request.postAndLoadData('/profitSettleBill/sum', params);
  }

  //列表
  static list(params) {
    return Request.postAndLoadData('/profitSettleBill/list', params);
  }

  //手动统计
  static manualStatisticsHandle(params) {
    return Request.post('/profitSettleBill/manualOpt', params);
  }

  //添加差错账单记录
  static addErrorProfitBills(params) {
    return Request.post('/profitSettleBill/addErrorProfitBills', params);
  }


  //下载
  static downloadProfitSettleBill(data) {
    return Request.requestByConfigNoData({
      url: '/profitSettleBill/downloadProfitSettleBill',
      method: 'post',
      data,
      responseType: 'blob'
    });
  }
}
