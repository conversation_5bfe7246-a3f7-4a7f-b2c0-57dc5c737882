import Request from '@/utils/request-util';

/**
 * 分润核销Api
 */
export class ProfitVerifyApi {
  /** 分页 */
  static findPage(params) {
    return Request.postAndLoadData('/profitVerify/page', params);
  }

  /** 详情 */
  static detail(params) {
    return Request.postAndLoadData('/profitVerify/detail', params);
  }

  /** 汇总 */
  static sum(params) {
    return Request.postAndLoadData('/profitVerify/profitSummary', params);
  }

  /** 提交开票审核 */
  static submitInvoiceReview(params) {
    return Request.post('/profitVerify/submitInvoiceReview', params);
  }

  /** 审核 */
  static checkReview(params) {
    return Request.post('/profitVerify/checkReview', params);
  }

  /** 手动批量插入分润核销记录 */
  static manualBatchAdd(params) {
    return Request.post('/profitVerify/manualBatchAdd', params);
  }

  /** 机构图片上传 */
  static uploadOrgImages(params) {
    return Request.postAndLoadData('/web/org/uploadImages', params);
  }

  //下载
  static downloadProfitVerify(data) {
    return Request.requestByConfigNoData({
      url: '/profitVerify/downloadProfitVerify',
      method: 'post',
      data,
      responseType: 'blob'
    });
  }

  //下载分润发票
  static downloadProfitInvoice(data) {
    return Request.requestByConfigNoData({
      url: '/profitVerify/downloadProfitInvoice',
      method: 'post',
      data,
      responseType: 'blob'
    });
  }

}
