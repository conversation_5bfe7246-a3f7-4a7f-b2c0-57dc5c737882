import Request from '@/utils/request-util';

/**
 * 商户提现记录操作Api
 */
export class WithdrawalRecordApi {
  /** 分页 */
  static findPage(params) {
    return Request.postAndLoadData('/withdrawTransInfo/page', params);
  }

  /** 详情 */
  static detail(params) {
    return Request.postAndLoadData('/withdrawTransInfo/detail', params);
  }

  /** 审核操作 */
  static check(params) {
    return Request.post('/withdrawTransInfo/manualHandle', params);
  }

  /** 退汇操作 */
  static reexchange(params) {
    return Request.post('/withdrawTransInfo/reexchange', params);
  }

    /** 统计 */
    static sum(params) {
      return Request.postAndLoadData('/withdrawTransInfo/sum', params);
    }
}
