import Request from '@/utils/request-util';

/**
 * 分润发票信息Api
 */
export class ProfitInvoiceInfoApi {
  /** 分页 */
  static findPage(params) {
    return Request.postAndLoadData('/profitInvoiceInfo/page', params);
  }

  /** 详情 */
  static detail(params) {
    return Request.postAndLoadData('/profitInvoiceInfo/detail', params);
  }

  /** 提交开票审核 */
  static submitInvoiceReview(params) {
    return Request.post('/profitInvoiceInfo/submitInvoiceReview', params);
  }

  /** 审核 */
  static checkReview(params) {
    return Request.post('/profitInvoiceInfo/checkReview', params);
  }


}
