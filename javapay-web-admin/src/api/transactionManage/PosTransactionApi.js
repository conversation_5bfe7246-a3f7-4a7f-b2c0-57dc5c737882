import Request from '@/utils/request-util';

/**
 * POS交易Api
 */
export class PosTransactionApi {
  /** 分页 */
  static findPage(params) {
    return Request.postAndLoadData('/posTransInfo/page', params);
  }

  /** 详情 */
  static detail(params) {
    return Request.postAndLoadData('/posTransInfo/detail', params);
  }

  /** 详情 */
  static posSum(params) {
    return Request.postAndLoadData('/posTransInfo/sum', params);
  }
  /** 导出 */
  static exportExcel(data) {
    return Request.requestByConfigNoData({
      url: '/posTransInfo/exportExcel',
      method: 'post',
      data,
      responseType: 'blob'
    });
  }
}
