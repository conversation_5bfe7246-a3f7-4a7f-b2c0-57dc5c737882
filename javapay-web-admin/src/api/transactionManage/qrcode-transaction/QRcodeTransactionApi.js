import Request from '@/utils/request-util';

/**
 * 二维码交易api
 */
export class QRcodeTransactionApi {
  /**
   * 分页查询
   */
  static getQRcodeTransInfoPages(params) {
    return Request.postAndLoadData('/qrcodeTransInfo/page', params);
  }

  //下载二维码交易
  static downloadQRCodeTransactionExcel(data) {
    return Request.requestByConfig({
      url: '/qrcodeTransInfo/downloadTransInfo',
      method: 'get',
      params: data,
      responseType: 'blob'
    });
  }

  //下载分润明细
  static downloadProfitStatisticsExcel(data) {
    return Request.requestByConfig({
      url: '/qrcodeTransInfo/downloadTransProfit',
      method: 'get',
      params: data,
      responseType: 'blob'
    });
  }

  //手续费详情
  static transFeeDetail(params) {
    return Request.postAndLoadData('/qrcodeTransInfo/feeDetail', params);
  }

  //手续费详情
  static transFeeSummary(params) {
    return Request.postAndLoadData('/transFee/summary', params);
  }
}
