import Request from '@/utils/request-util';

/**
 * 风险案例同步Api
 */
export class RiskyCaseSyncApi {
  /**
   * 银联交易风险案例同步
   */
  static riskUnionTransQuery(params) {
    return Request.post('/risk/riskUnionTransQuery', params);
  }

  /**
   * 银联调单同步
   */
  static riskDispatchOrderQuery(params) {
    return Request.post('/risk/riskDispatchOrderQuery', params);
  }

  /**
   * 银联退单同步
   */
  static riskRetreatOrderQuery(params) {
    return Request.post('/risk/riskRetreatOrderQuery', params);
  }

  /**
   * A/T交易风险案例同步
   */
  static riskCodeTransQuery(params) {
    return Request.post('/risk/riskCodeTransQuery', params);
  }
}
