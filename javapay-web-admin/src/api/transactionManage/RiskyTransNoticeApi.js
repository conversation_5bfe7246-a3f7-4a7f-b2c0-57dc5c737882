import Request from '@/utils/request-util';

/**
 * 风险交易通知Api
 */
export class RiskyTransNoticeApi {
  /** 分页 */
  static findPage(params) {
    return Request.postAndLoadData('/riskNotify/page', params);
  }

  /** 详情 */
  static detail(params) {
    return Request.postAndLoadData('/riskNotify/detail', params);
  }

  /** 图片统一上传 */
  static uploadImages(params) {
    return Request.postAndLoadData('/web/merchant/uploadImages', params);
  }

  /** 银联交易风险案例处理 */
  static ylTransRiskCaseOperate(params) {
    return Request.post('/risk/ylTransRiskCaseOperate', params);
  }

  /** 银联交易调单处理 */
  static ylTransRiskDispatchOrderOperate(params) {
    return Request.post('/risk/ylTransRiskDispatchOrderOperate', params);
  }

  /** 银联交易退单处理 */
  static ylTransRiskRetreatOrderOperate(params) {
    return Request.post('/risk/ylTransRiskRetreatOrderOperate', params);
  }

  /** A/T扫码风险案例(资料上传) */
  static atCodeRiskCaseInfoUpload(params) {
    return Request.post('/risk/atCodeRiskCaseInfoUpload', params);
  }
}
