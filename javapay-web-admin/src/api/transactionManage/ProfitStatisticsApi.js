import Request from '@/utils/request-util';

/**
 * 分润统计Api
 */
export class ProfitStatisticsApi {
  //分页
  static findPage(params) {
    return Request.postAndLoadData('/profitStatistic/page', params);
  }

  //详情
  static detail(params) {
    return Request.postAndLoadData('/profitStatistic/detail', params);
  }

  //列表
  static list(params) {
    return Request.postAndLoadData('/profitStatistic/list', params);
  }

  //手动统计
  static manualStatisticsHandle(params) {
    return Request.post('/profitStatistic/manualOpt', params);
  }

    //下载
    static downloadTransProfit(data) {
      return Request.requestByConfigNoData({
        url: '/profitStatistic/downloadTransProfit',
        method: 'post',
        data,
        responseType: 'blob'
      });
    }
}
