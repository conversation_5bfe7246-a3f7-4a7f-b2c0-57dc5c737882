import Request from '@/utils/request-util';

/**
 * 终端厂商api
 *
 */
export class TerminalFactoryApi {
  /**
   * 分页
   */
  static findPage(params) {
    return Request.postAndLoadData('/terminalFactory/page', params);
  }

  /**
   * 新增
   */
  static add(params) {
    return Request.post('/terminalFactory/add', params);
  }

  /**
   * 修改
   */
  static edit(params) {
    return Request.post('/terminalFactory/edit', params);
  }

  /**
   * 删除
   */
  static delete(params) {
    return Request.post('/terminalFactory/delete', params);
  }

  /**
   * 详情
   */
  static detail(params) {
    return Request.postAndLoadData('/terminalFactory/detail', params);
  }

  /**
   * 列表
   */
  static list(params) {
    return Request.postAndLoadData('/terminalFactory/list', params);
  }

}
