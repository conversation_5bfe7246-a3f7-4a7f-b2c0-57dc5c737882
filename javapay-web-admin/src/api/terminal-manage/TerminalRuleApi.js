import Request from '@/utils/request-util';

/**
 * 终端规则api
 *
 */
export class TerminalRuleApi {
  /**
   * 分页
   */
  static findPage(params) {
    return Request.postAndLoadData('/terminalRule/page', params);
  }

  /**
   * 新增
   */
  static add(params) {
    return Request.post('/terminalRule/add', params);
  }

  /**
   * 修改
   */
  static edit(params) {
    return Request.post('/terminalRule/edit', params);
  }

  /**
   * 删除
   */
  static delete(params) {
    return Request.post('/terminalRule/delete', params);
  }

  /**
   * 详情
   */
  static detail(params) {
    return Request.postAndLoadData('/terminalRule/detail', params);
  }

  /**
   * 列表
   */
  static list(params) {
    return Request.postAndLoadData('/terminalRule/list', params);
  }

}
