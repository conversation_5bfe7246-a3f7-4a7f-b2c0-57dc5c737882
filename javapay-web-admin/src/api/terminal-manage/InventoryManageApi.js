import Request from '@/utils/request-util';

/**
 * 终端管理Api
 */
export class InventoryManageApi {
  /**
   * 终端列表
   */

  //分页列表
  static findPages(params) {
    return Request.postAndLoadData('/terminalInfo/page', params);
  }

  //分页列表
  static findPagesActive(params) {
    return Request.postAndLoadData('/terminalActiveInfo/page', params);
  }

  //激活奖励规则
  static activeRuleList(params) {
    return Request.postAndLoadData('/terminalActiveInfo/activeRuleList', params);
  }
  //达标奖励规则
  static standardRuleList(params) {
    return Request.postAndLoadData('/terminalActiveInfo/standardRuleList', params);
  }

  //入库(号码类型)
  static addToLibraryNumberType(params) {
    return Request.post('/terminalInfo/add', params);
  }

  //入库(号码类型)
  static addTerminalActiveInfo(params) {
    return Request.post('/terminalActiveInfo/addTermToBranch', params);
  }
  //入库(号码类型)
  static addTermToAgent(params) {
    return Request.post('/terminalActiveInfo/addTermToAgent', params);
  }

  //下载文件
  static download(data) {
    return Request.requestByConfigNoData({
      url: '/terminalInfo/download',
      method: 'post',
      data: data,
      responseType: 'blob'
    });
  }

  //上传文件批量入库
  static upload(data) {
    return Request.requestByConfig({
      url: '/terminalInfo/upload',
      method: 'post',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  }

  //上传文件批量入库
  static uploadActuvity(data) {
    return Request.requestByConfig({
      url: '/terminalActiveInfo/batchSaveTermToBranchByFile',
      method: 'post',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  }
  //上传文件批量入库
  static batchSaveTermToAgentByFile(data) {
    return Request.requestByConfig({
      url: '/terminalActiveInfo/batchSaveTermToAgentByFile',
      method: 'post',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  }

  /**
   * 修改基本信息
   */
  static editBaseInfo(params) {
    return Request.post('/terminalInfo/modifyTerminalBaseInfo', params);
  }

  /**
   * 修改营销活动
   */
  static editActivityInfo(params) {
    return Request.post('/terminalInfo/modifyServiceFee', params);
  }

  /**
   * 修改费率信息
   */
  static editRateInfo(params) {
    return Request.post('/terminalInfo/modifyRateInfo', params);
  }

  /**
   * 单个回收
   */
  static singleRecycle(params) {
    return Request.post('/terminalInfo/singleRecycleTerminal', params);
  }

  /**
   * 单个删除
   */
  static singleDelete(params) {
    return Request.post('/terminalInfo/singleDelete', params);
  }

  /**
   * 终端划拨
   */
  static transferActiveTerminal(params) {
    return Request.post('/terminalInfo/transferActiveTerminal', params);
  }

  /**
   * 终端划拨
   */
  static transferTerminal(params) {
    return Request.post('/terminalInfo/transferTerminal', params);
  }

  /**
   * 终端回拨
   */
  static callbackTerminal(params) {
    return Request.post('/terminalInfo/callbackTerminal', params);
  }

  /**
   * 批量变更终端的商户费率政策
   */
  static batchSnModifyMerchRatePolicy(params) {
    return Request.post('/terminalInfo/batchSnModifyMerchRatePolicy', params);
  }

  /**
   * 获取活动终端所属返现规则列表
   */
  static fetchScreenActiveTerminalRules(params) {
    return Request.postAndLoadData('/terminalActiveInfo/fetchScreenActiveTerminalRules', params);
  }

  /**
   * 获取自身返现政策信息(终端入库-下拉框)
   */
  static fetchOwnerCashPolicyInfo(params) {
    return Request.postAndLoadData('/terminalActiveInfo/fetchOwnerCashPolicyInfo', params);
  }

  /**
   * 获取目标机构返现政策信息(终端入库)
   */
  static fetchTargetOrgCashPolicyInfo(params) {
    return Request.postAndLoadData('/terminalActiveInfo/fetchTargetOrgCashPolicyInfo', params);
  }

  /**
   * 获取活动终端所属返现规则列表
   */
  static fetchScreenActiveTerminalRulesByFile(data) {
    return Request.requestByConfig({
      url: '/terminalActiveInfo/fetchScreenActiveTerminalRulesByFile',
      method: 'post',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  }

  /**
   * 终端调拨(平台运营操作)
   */
  static terminalTransferToTarget(params) {
    return Request.post('/terminalInfo/terminalTransferToTarget', params);
  }

  /**
   * 终端文件调拨(平台运营操作)
   */
  static terminalTransferToTargetByFile(params) {
    return Request.post('/terminalInfo/terminalTransferToTargetByFile', params);
  }

  /**
   * 终端换绑
   */
  static changeBindTermSn(params) {
    return Request.post('/terminalInfo/changeBindTermSn', params);
  }

  /**
   * 终端撤机
   */
  static abortTermSn(params) {
    return Request.post('/terminalInfo/snUnbindV2', params);
  }

  /**
   * 终端换绑
   */
  static unbindTermSn(params) {
    return Request.post('/terminalInfo/unbind', params);
  }

  //终端文件下发(代理商机构操作)
  static terminalAgentTransferToTargetByFile(data) {
    return Request.requestByConfig({
      url: '/terminalInfo/terminalEnhanceTransferToTargetByFile',
      method: 'post',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  }

  //终端文件下发(活动终端)
  static transferActiveTerminalByFile(data) {
    return Request.requestByConfig({
      url: '/terminalInfo/transferActiveTerminalByFile',
      method: 'post',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  }
  //终端文件回拨(代理商机构操作)
  static terminalAgentCallBackToTargetByFile(data) {
    return Request.requestByConfig({
      url: '/terminalInfo/terminalAgentCallBackToTargetByFile',
      method: 'post',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  }
  //代理商终端批量下发模版文件下载
  static agentTransferTerminalTemplate(data) {
    return Request.requestByConfigNoData({
      url: '/agentTransferTerminalTemplate/download',
      method: 'post',
      data,
      responseType: 'blob'
    });
  }
  //代理商终端批量回拨模版文件下载
  static agentCallBackTerminalTemplate(data) {
    return Request.requestByConfigNoData({
      url: '/agentCallBackTerminalTemplate/download',
      method: 'post',
      data,
      responseType: 'blob'
    });
  }
  //批量修改费率信息(代理商操作)
  static terminalModifyRateByFile(data) {
    return Request.requestByConfig({
      url: '/terminalInfo/terminalModifyRateByFile',
      method: 'post',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  }

  /**
   * 批量修改费率信息(代理商操作)
   */
  static terminalModifyRate(params) {
    return Request.post('/terminalInfo/terminalModifyRate', params);
  }

  //代理商批量修改终端费率模版文件下载
  static agentModifyRateTerminalTemplate(data) {
    return Request.requestByConfigNoData({
      url: '/agentModifyRateTerminalTemplate/download',
      method: 'post',
      data,
      responseType: 'blob'
    });
  }
  //终端下载
  static terminalDownload(data) {
    return Request.requestByConfigNoData({
      url: '/terminalInfo/terminalDownload',
      method: 'post',
      data,
      responseType: 'blob'
    });
  }
  //终端文件删除
  static terminalDeleteByFile(data) {
    return Request.requestByConfig({
      url: '/terminalInfo/terminalDeleteByFile',
      method: 'post',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  }

  //删除终端模版文件下载
  static deleteTerminalTemplateFile(data) {
    return Request.requestByConfigNoData({
      url: '/deleteTerminalTemplateFile/download',
      method: 'post',
      data,
      responseType: 'blob'
    });
  }

  /**
   * 终端SN起始号-营销活动配置批量更新(流量费/服务费/费率)
   */
  static batchUpdateTerminalActiveConf(params) {
    return Request.post('/terminalInfo/batchUpdateTerminalActiveConf', params);
  }

  /**
   * 终端文件-营销活动配置批量更新(流量费/服务费/费率)
   */
  static batchUpdateTerminalActiveConfByFile(data) {
    return Request.requestByConfig({
      url: '/terminalInfo/batchUpdateTerminalActiveConfByFile',
      method: 'post',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  }
}
