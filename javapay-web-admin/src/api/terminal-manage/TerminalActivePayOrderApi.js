import Request from '@/utils/request-util';

/**
 * 终端权益订单记录api
 *
 */
export class TerminalActivePayOrderApi {
  /**
   * 分页
   */
  static findPage(params) {
    return Request.postAndLoadData('/terminalActivePayOrder/page', params);
  }

  /**
   * 汇总
   */
  static sum(params) {
    return Request.postAndLoadData('/terminalActivePayOrder/sum', params);
  }


  //下载
  static downloadPayorder(data) {
    return Request.requestByConfigNoData({
      url: '/terminalActivePayOrder/downloadPayorder',
      method: 'post',
      data,
      responseType: 'blob'
    });
  }

}
