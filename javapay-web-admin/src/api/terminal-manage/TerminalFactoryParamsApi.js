import Request from '@/utils/request-util';

/**
 *终端厂商品牌参数Api
 */
export class TerminalFactoryParamsApi {
  /*
   分页
  */
  static findPage(params) {
    return Request.postAndLoadData('/terminalFactoryParams/page', params);
  }

  /*
   添加
  */
  static add(params) {
    return Request.post('/terminalFactoryParams/add', params);
  }

  /*
   编辑
  */
  static edit(params) {
    return Request.post('/terminalFactoryParams/edit', params);
  }

  /*
   详情
  */
  static detail(params) {
    return Request.postAndLoadData('/terminalFactoryParams/detail', params);
  }


  /*
   删除
  */
  static delete(params) {
    return Request.post('/terminalFactoryParams/delete', params);
  }
}
