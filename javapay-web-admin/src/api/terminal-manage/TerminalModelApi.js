import Request from '@/utils/request-util';

/**
 * 终端型号api
 *
 */
export class TerminalModelApi {
  /**
   * 分页
   */
  static findPage(params) {
    return Request.postAndLoadData('/terminalModel/page', params);
  }

  /**
   * 新增
   */
  static add(params) {
    return Request.post('/terminalModel/add', params);
  }

  /**
   * 修改
   */
  static edit(params) {
    return Request.post('/terminalModel/edit', params);
  }

  /**
   * 删除
   */
  static delete(params) {
    return Request.post('/terminalModel/delete', params);
  }

  /**
   * 详情
   */
  static detail(params) {
    return Request.postAndLoadData('/terminalModel/detail', params);
  }

  /**
   * 列表
   */
  static list(params) {
    return Request.postAndLoadData('/terminalModel/list', params);
  }

}
