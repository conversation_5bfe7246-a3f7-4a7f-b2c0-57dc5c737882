import Request from '@/utils/request-util';

/**
 * 店铺行业Api
 */
export class StoreMccApi {
  // 分页列表
  static findPage(params) {
    return Request.postAndLoadData('/storeMcc/page', params);
  }

  // 列表
  static list(params) {
    return Request.postAndLoadData('/storeMcc/list', params);
  }

  // 添加
  static add(params) {
    return Request.post('/storeMcc/add', params);
  }

  // 详情
  static detail(params) {
    return Request.postAndLoadData('/storeMcc/detail', params);
  }

  // 修改
  static edit(params) {
    return Request.post('/storeMcc/edit', params);
  }

  // 删除
  static delete(params) {
    return Request.post('/storeMcc/delete', params);
  }
}
