import Request from '@/utils/request-util';

/**
 * 快速商户资料管理
 *
 */
export class FastMerchPoolApi {
  /**
   * 列表
   */
  static findPage(params) {
    return Request.postAndLoadData('/fastMerchDataPool/page', params);
  }

  /**
   * 新增
   */
  static add(params) {
    return Request.post('/fastMerchDataPool/add', params);
  }

  /**
   * 修改
   */
  static edit(params) {
    return Request.post('/fastMerchDataPool/edit', params);
  }

  /**
   * 修改
   */
  static detail(params) {
    return Request.postAndLoadData('/fastMerchDataPool/detail', params);
  }

  /**
   * 上传营业执照
   */
  static uploadLicenseImage(params) {
    return Request.post('/fastMerchDataPool/uploadLicenseImage', params);
  }

  /**
   * 批量导入资料
   */
  static excelBatchImport(data) {
    return Request.requestByConfig({
      url: '/fastMerchDataPool/excelBatchImport',
      method: 'post',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  }

    /**
     * 批量导入数据模板文件下载
     */
    static downloadTemplateFile(data) {
      return Request.requestByConfigNoData({
        url: '/fastMerchDataPool/templateFile',
        method: 'post',
        data,
        responseType: 'blob'
      });
    }
}
