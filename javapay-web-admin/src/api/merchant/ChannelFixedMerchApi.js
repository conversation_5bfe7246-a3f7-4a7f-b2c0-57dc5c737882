import Request from '@/utils/request-util';

/**
 * 固定商户管理
 *
 */
export class ChannelFixedMerchApi {
    /**
   * 列表
   */
  static findPage(params) {
    return Request.postAndLoadData('/channelFixedMerch/page', params);
  }


  /**
   * 新增
   */
  static add(params) {
    return Request.post('/channelFixedMerch/add', params);
  }

  /**
   * 修改
   */
  static edit(params) {
    return Request.post('/channelFixedMerch/edit', params);
  }

}
