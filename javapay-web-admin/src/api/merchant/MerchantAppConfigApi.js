import Request from '@/utils/request-util';

/**
 * 商户应用配置
 *
 */
export class MerchantAppConfigApi {
    /**
   * 列表
   */
  static findPage(params) {
    return Request.postAndLoadData('/web/merchantAppConfig/page', params);
  }


  /**
   * 新增
   */
  static add(params) {
    return Request.post('/web/merchantAppConfig/add', params);
  }

  /**
   * 修改
   */
  static edit(params) {
    return Request.post('/web/merchantAppConfig/edit', params);
  }

  /**
   * 详情
   */
  static detail(params) {
    return Request.postAndLoadData('/web/merchantAppConfig/detail', params);
  }


  /**
   * 删除
   */
  static delete(params) {
    return Request.post('/web/merchantAppConfig/delete', params);
  }

}