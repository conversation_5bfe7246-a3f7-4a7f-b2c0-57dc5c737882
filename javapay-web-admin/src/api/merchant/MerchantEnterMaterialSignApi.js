import Request from '@/utils/request-util';

/**
 * 进件资料签约Api
 */
export class MerchantEnterMaterialSignApi {
  // 分页
  static findPage(params) {
    return Request.postAndLoadData('/merchantSignEnterMaterial/page', params);
  }

  // 添加
  static add(params) {
    return Request.post('/merchantSignEnterMaterial/add', params);
  }

  // 编辑
  static edit(params) {
    return Request.post('/merchantSignEnterMaterial/edit', params);
  }

  // 详情
  static detail(params) {
    return Request.postAndLoadData('/merchantSignEnterMaterial/detail', params);
  }

   // 删除
   static delete(params) {
    return Request.post('/merchantSignEnterMaterial/delete', params);
  }

  /**
   * MCC与通道关联
   */

    // 银联MCC查询（扫码）
    static unionMccCode(params) {
      return Request.postAndLoadData('/unionMcc/qrcodeType', params);
    }

    // 银联MCC查询（快捷）
    static unionMccEpos(params) {
      return Request.postAndLoadData('/unionMcc/eposType', params);
    }

    // 经营类目查询（快捷/快付通）
    static chnKftCategory(params) {
      return Request.postAndLoadData('/chnKft/category', params);
    }

    // 微信MCC查询
    static wechatMcc(params) {
      return Request.postAndLoadData('/wechatMcc/queryList', params);
    }

    // 支付宝MCC查询
    static alipayMcc(params) {
      return Request.postAndLoadData('/alipayMcc/queryList', params);
    }

}
