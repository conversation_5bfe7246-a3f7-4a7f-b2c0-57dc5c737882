import Request from '@/utils/request-util';

/**
 * 店铺名称Api
 */
export class StoreNameApi {
  // 分页
  static findPage(params) {
    return Request.postAndLoadData('/storeName/page', params);
  }

  // 列表
  static list(params) {
    return Request.postAndLoadData('/storeName/list', params);
  }

  // 添加
  static add(params) {
    return Request.post('/storeName/add', params);
  }

  // 详情
  static detail(params) {
    return Request.postAndLoadData('/storeName/detail', params);
  }

  // 修改
  static edit(params) {
    return Request.post('/storeName/edit', params);
  }

  // 删除
  static delete(params) {
    return Request.post('/storeName/delete', params);
  }
}
