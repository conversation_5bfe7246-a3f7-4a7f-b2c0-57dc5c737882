import Request from '@/utils/request-util';

/**
 * 快速商户门店照
 *
 */
export class FastMerchImageApi {
  /**
   * 分页
   */
  static findPage(params) {
    return Request.postAndLoadData('/fastMerchImage/page', params);
  }

  /**
   * 新增
   */
  static add(params) {
    return Request.post('/fastMerchImage/add', params);
  }

  /**
   * 修改
   */
  static edit(params) {
    return Request.post('/fastMerchImage/edit', params);
  }

  /**
   * 详情
   */
  static detail(params) {
    return Request.postAndLoadData('/fastMerchImage/detail', params);
  }
}
