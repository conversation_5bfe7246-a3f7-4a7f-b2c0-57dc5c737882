import Request from '@/utils/request-util';

/**
 * 商户管理Api
 */
export class MerchantApi {
  //商户列表
  static merchantPage(params) {
    return Request.postAndLoadData('/merchant/page', params);
  }

  //商户详情
  static merchantDetail(params) {
    return Request.postAndLoadData('/merchant/detail', params);
  }

    // 获取经纬度
  static queryLocationList(params) {
    return Request.postAndLoadData('/locationMap/queryLocationList', params);
  }

  //商户详细信息查询
  static merchantDetailDetail(params) {
    return Request.postAndLoadData('/merchantDetail/detail', params);
  }

  //商户详细编辑
  static merchantDetailEdit(params) {
    return Request.post('/merchantDetail/edit', params);
  }

  //查看累计/限制详情
  static cumulateLimitDetail(params) {
    return Request.postAndLoadData('/merchant/cumulateLimitDetail', params);
  }

  //费率详情
  static rateDetail(params) {
    return Request.postAndLoadData('/merchantRate/list', params);
  }

  //商户修改
  static merchantEdit(params) {
    return Request.post('/merchant/edit', params);
  }

  //进件列表
  static signList(params) {
    return Request.postAndLoadData('/merchantSignOrder/page', params);
  }

  //进件列表下载
  static exportSignListExcel(data) {
    return Request.requestByConfigNoData({
      url: '/merchantSignOrder/exportExcel',
      method: 'post',
      data,
      responseType: 'blob'
    });
  }

  //进件详情
  static signDetail(params) {
    return Request.postAndLoadData('/merchantSignOrder/detail', params);
  }

  //查询商户交易配置
  static tradeConfigDetail(params) {
    return Request.postAndLoadData('/merchantTradeChlRoute/payMethod/queryList', params);
  }

  //交易配置编辑
  static tradeConfigEdit(params) {
    return Request.post('/merchantTradeChlRoute/payMethod/edit', params);
  }

  //查询通道商户列表
  static merchantList(params) {
    return Request.postAndLoadData('/channelMerchant/merchantList', params);
  }

  //查询通道列表
  static payOrgList(params) {
    return Request.postAndLoadData('/channelMerchant/payOrgList', params);
  }

  //结算卡列表
  static paycardList(params) {
    return Request.postAndLoadData('/merchantBankCard/page', params);
  }

  //结算卡详情
  static paycardDetail(params) {
    return Request.postAndLoadData('/merchantBankCard/detail', params);
  }

  //企业商户审核
  static enterpriseMerchAudit(params) {
    return Request.post('/merchant/enterpriseMerchAudit', params);
  }

  //查看通道图片
  static detailImage(params) {
    return Request.postAndLoadData('/merchantSignOrder/detailImage', params);
  }

  //下载分润明细
  static downloadMerchantRateExcel(data) {
    return Request.requestByConfigNoData({
      url: '/merchantRate/download',
      method: 'post',
       data,
      responseType: 'blob'
    });
  }

  //编辑费率
  static editMerchantRate(params) {
    return Request.post('/merchantRate/edit', params);
  }

  //编辑费率
  static changeRatePolicy(params) {
    return Request.post('/merchantRate/changeRatePolicy', params);
  }

  //下载商户费率信息编辑模版
  static downloadRateEditTemplate(params) {
    return Request.requestByConfig({
      url: '/merchantRate/downloadRateEditTemplate',
      method: 'get',
      params,
      responseType: 'blob'
    });
  }

  //批量修改商户费率
  static batchEditMerchRate(data) {
    return Request.requestByConfig({
      url: '/merchantRate/batchEdit',
      method: 'post',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  }

  //分页查询（变更）
  static merchantRateEditPage(params) {
    return Request.postAndLoadData('/merchantRateEditOpt/page', params);
  }

  //查看详情（变更）
  static merchantRateEditDetail(params) {
    return Request.postAndLoadData('/merchantRateEditOpt/detail', params);
  }

  //审核
  static audit(params) {
    return Request.post('/merchantSignOrder/executePreAudit', params);
  }
}
