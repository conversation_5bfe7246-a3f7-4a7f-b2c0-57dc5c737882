import Request from '@/utils/request-util';

/**
 * 进件资料分组Api
 */
export class MerchantEnterMaterialGroupApi {
  // 分页
  static findPage(params) {
    return Request.postAndLoadData('/merchantEnterMaterialGroup/page', params);
  }

  // 添加
  static add(params) {
    return Request.post('/merchantEnterMaterialGroup/add', params);
  }

  // 编辑
  static edit(params) {
    return Request.post('/merchantEnterMaterialGroup/edit', params);
  }

  // 同步
  static sync(params) {
    return Request.post('/merchantEnterMaterialGroup/sync', params);
  }

  // 详情
  static detail(params) {
    return Request.post('/merchantEnterMaterialGroup/detail', params);
  }

  // 列表
  static list(params) {
    return Request.postAndLoadData('/merchantEnterMaterialGroup/list', params);
  }
}
