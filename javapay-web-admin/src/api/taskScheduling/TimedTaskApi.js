import Request from '@/utils/request-util';

/**
 * 定时任务Api
 */
export class TimedTaskApi {
  //分页列表
  static findPage(params) {
    return Request.postAndLoadData('/page/task/page', params);
  }

  //执行记录分页列表
  static findRecodPage(params) {
    return Request.postAndLoadData('/taskExecutionRecord/page', params);
  }

  //添加
  static add(params) {
    return Request.post('/page/task/add', params);
  }

  //编辑
  static edit(params) {
    return Request.post('/page/task/edit', params);
  }

  //详情
  static detail(params) {
    return Request.postAndLoadData('/page/task/detail', params);
  }

  //删除
  static delete(params) {
    return Request.post('/page/task/delete', params);
  }

  //强制执行任务
  static runTaskForcibly(params) {
    return Request.post('/page/task/runTaskForcibly', params);
  }

  //查询任务开关
  static queryTaskSwitch(params) {
    return Request.postAndLoadData('/task/queryTaskSwitch', params);
  }

  //暂停任务开关
  static pauseTaskSystem(params) {
    return Request.post('/task/pauseTaskSystem', params);
  }

  //开启任务开关
  static continueTaskSystem(params) {
    return Request.post('/task/continueTaskSystem', params);
  }
}
