import Request from '@/utils/request-util';

/**
 * 多人多次付款超出商圈Api
 */
export class MultipleGeofenceLimitApi {
  //分页列表
  static findPage(params) {
    return Request.postAndLoadData('/multipleGeofenceLimit/page', params);
  }

  //添加
  static add(params) {
    return Request.post('/multipleGeofenceLimit/add', params);
  }

  //详情
  static detail(params) {
    return Request.postAndLoadData('/multipleGeofenceLimit/detail', params);
  }

  //编辑
  static edit(params) {
    return Request.post('/multipleGeofenceLimit/edit', params);
  }

  //删除
  static delete(params) {
    return Request.post('/multipleGeofenceLimit/delete', params);
  }

}
