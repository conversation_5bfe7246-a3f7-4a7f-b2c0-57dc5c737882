import Request from '@/utils/request-util';

/**
 * 风控白名单管理
 */
export class RiskWhitelistManageApi {

    /**
     * 查询列表
     */
    static findPage(params) {
        return Request.postAndLoadData('/riskWhite/page', params);
    }

    /**
     * 新增
     */
    static add(params) {
        return Request.post('/riskWhite/add', params);
    }

    /**
     * 修改
     */
    static edit(params) {
        return Request.post('/riskWhite/edit', params);
    }

    /**
     * 删除
     */
    static delete(params) {
        return Request.post('/riskWhite/delete', params);
    }

}
