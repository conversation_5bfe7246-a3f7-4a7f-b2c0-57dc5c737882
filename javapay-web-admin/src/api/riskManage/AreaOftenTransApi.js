import Request from '@/utils/request-util';

/**
 * 不同地区频繁交易风控Api
 */
export class AreaOftenTransApi {
  //分页列表
  static findPage(params) {
    return Request.postAndLoadData('/areaOftenTrans/page', params);
  }

  //添加
  static add(params) {
    return Request.post('/areaOftenTrans/add', params);
  }

  //详情
  static detail(params) {
    return Request.postAndLoadData('/areaOftenTrans/detail', params);
  }

  //编辑
  static edit(params) {
    return Request.post('/areaOftenTrans/edit', params);
  }

  //删除
  static delete(params) {
    return Request.post('/areaOftenTrans/delete', params);
  }

}
