import Request from '@/utils/request-util';


/**
 * 支付频繁超出商圈范围风控
 */
export class PayFrequentBeyondScopeRiskManageApi {

    /**
     * 获取数据列表
     */
    static getGeofenceLimitPages(params) {
        return Request.postAndLoadData('/geofenceLimit/page', params);
    }

    /**
     * 新增
     */
    static add(params) {
        return Request.post('/geofenceLimit/add', params);
    }

    /**
     * 编辑
     */
    static edit(params) {
        return Request.post('/geofenceLimit/edit', params);
    }

    /**
     * 删除
     */
    static delete(params) {
        return Request.post('/geofenceLimit/delete', params);
    }

}