import Request from '@/utils/request-util';

/**
 * 付款用户数量超限风控Api
 */
export class PayuserCountLimitApi {
  //分页列表
  static findPage(params) {
    return Request.postAndLoadData('/payuserCountLimit/page', params);
  }

  //添加
  static add(params) {
    return Request.post('/payuserCountLimit/add', params);
  }

  //详情
  static detail(params) {
    return Request.postAndLoadData('/payuserCountLimit/detail', params);
  }

  //编辑
  static edit(params) {
    return Request.post('/payuserCountLimit/edit', params);
  }

  //删除
  static delete(params) {
    return Request.post('/payuserCountLimit/delete', params);
  }

}
