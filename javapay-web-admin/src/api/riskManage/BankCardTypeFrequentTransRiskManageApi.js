import Request from '@/utils/request-util';

/**
 * 银行卡类型频繁交易风控
 */
export class BankCardTypeFrequentTransRiskManageApi {
    /**
     * 获取数据列表
     */
    static getCardOftenTransPages(params) {
        return Request.postAndLoadData('/cardOftenTrans/page', params);
    }

    /**
     * 新增
     */
    static add(params) {
        return Request.post('/cardOftenTrans/add', params);
    }

    /**
     * 编辑
     */
    static edit(params) {
        return Request.post('/cardOftenTrans/edit', params);
    }

    /**
     * 删除
     */
    static delete(params) {
        return Request.post('/cardOftenTrans/delete', params);
    }
}