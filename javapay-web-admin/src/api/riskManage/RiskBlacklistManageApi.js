import Request from '@/utils/request-util';

/**
 * 风控黑名单管理
 */
export class RiskBlacklistManageApi {

    /**
     * 查询列表
     */
    static getRiskBlacklistPages(params) {
        return Request.postAndLoadData('/riskBlack/page', params);
    }

    /**
     * 新增
     */
    static add(params) {
        return Request.post('/riskBlack/add', params);
    }

    /**
     * 修改
     */
    static edit(params) {
        return Request.post('/riskBlack/edit', params);
    }

    /**
     * 删除
     */
    static delete(params) {
        return Request.post('/riskBlack/delete', params);
    }

}