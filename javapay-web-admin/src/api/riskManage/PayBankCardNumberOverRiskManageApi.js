import Request from '@/utils/request-util';

/**
 * 支付银行卡数量超限风控
 */
export class PayBankCardNumberOverRiskManageApi {
    /**
     * 获取数据列表
     */
    static getCardCountLimitPages(params) {
        return Request.postAndLoadData('/cardCountLimit/page', params);
    }

    /**
     * 新增
     */
    static add(params) {
        return Request.post('/cardCountLimit/add', params);
    }

    /**
     * 编辑
     */
    static edit(params) {
        return Request.post('/cardCountLimit/edit', params);
    }

    /**
     * 删除
     */
    static delete(params) {
        return Request.post('/cardCountLimit/delete', params);
    }
}