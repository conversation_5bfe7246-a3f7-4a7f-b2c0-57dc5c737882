import Request from '@/utils/request-util';

/**
 * 交易限制管理Api
 */
export class TransactionLimitManageApi {

    /**
     * 获取列表数据
     */
    static getTransactionLimitManagePages(params) {
        return Request.postAndLoadData('/riskLimit/page', params);
    }

    /**
     * 新增
     */
    static add(params) {
        return Request.post('/riskLimit/add', params);
    }

    /**
     * 编辑
     */
    static edit(params) {
        return Request.post('/riskLimit/edit', params);
    }

    /**
     * 删除
     */
    static delete(params) {
        return Request.post('/riskLimit/delete', params);
    }
}

