import Request from '@/utils/request-util';

/**
 * 三方应用管理api
 */
export class TripartiteApplicationManageApi {
  /**
   * 获取列表
   */
  static findPage(params) {
    return Request.postAndLoadData('/thirdPlat/page', params);
  }

  /**
   * 新增
   */
  static add(params) {
    return Request.post('/thirdPlat/add', params);
  }

  /**
   * 编辑
   */
  static edit(params) {
    return Request.post('/thirdPlat/edit', params);
  }

  /**
   * 列表
   */
  static findList(params) {
    return Request.postAndLoadData('/thirdPlat/list', params);
  }
}
