import Request from '@/utils/request-util';

/**
 * 三方应用商户api
 */
export class TripartiteApplicationMerchApi {
  /**
   * 获取列表
   */
  static findPage(params) {
    return Request.postAndLoadData('/thirdPlatMerchRelation/page', params);
  }

  /**
   * 新增
   */
  static add(params) {
    return Request.post('/thirdPlatMerchRelation/add', params);
  }

  /**
   * 编辑
   */
  static edit(params) {
    return Request.post('/thirdPlatMerchRelation/edit', params);
  }

  /**
   * 详情
   */
  static detail(params) {
    return Request.post('/thirdPlatMerchRelation/detail', params);
  }

  /**
   * 列表
   */
  static findList(params) {
    return Request.postAndLoadData('/thirdPlatMerchRelation/list', params);
  }
}
