import Request from '@/utils/request-util';

/**
 * 清算流水api
 *
 * <AUTHOR>
 * @date 2023/07/13 10:29
 */
export class RemitFlowApi {
  /**
   * 获取分页列表
   *
   * <AUTHOR>
   * @date 2023/07/13 10:29
   */
  static findPage(params) {
    return Request.postAndLoadData('/remitFlow/page', params);
  }

  /**
   * 详情
   *
   * <AUTHOR>
   * @date 2023/07/13 10:29
   */
  static detail(params) {
    return Request.postAndLoadData('/remitFlow/detail', params);
  }
}
