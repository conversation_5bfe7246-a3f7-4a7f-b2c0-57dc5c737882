import Request from '@/utils/request-util';

/**
 * 钱包流水api
 *
 * <AUTHOR>
 * @date 2023/03/19 14:31
 */
export class WalletFlowApi {
  /**
   * 获取分页列表
   *
   * <AUTHOR>
   * @date 2023/03/19 14:31
   */
  static findPage(params) {
    return Request.postAndLoadData('/walletFlow/page', params);
  }

  /**
   * 详情
   *
   * <AUTHOR>
   * @date 2023/03/19 14:31
   */
  static detail(params) {
    return Request.postAndLoadData('/walletFlow/detail', params);
  }
}
