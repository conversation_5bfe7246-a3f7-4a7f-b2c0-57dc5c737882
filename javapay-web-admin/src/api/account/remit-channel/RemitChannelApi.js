import Request from '@/utils/request-util';

/**
 * 清算渠道api
 *
 * <AUTHOR>
 * @date 2023/06/27 10:17
 */
export class RemitChannelApi{
  /**
   * 获取清算渠道列表
   * <AUTHOR>
   * @date 2023/06/27 10:17
   */
  static findPage(params) {
    return Request.postAndLoadData('/remitChannel/page', params);
  }

  /**
   * 获取所有清算渠道列表
   * <AUTHOR>
   * @date 2023/07/03 16:17
   */
  static findAll(params) {
    return Request.postAndLoadData('/remitChannel/all', params);
  }

  /**
   * 新增
   *
   * <AUTHOR>
   * @date 2023/06/28 14:38
   */
  static add(params) {
    return Request.post('/remitChannel/add', params);
  }

  /**
   * 编辑
   *
   * <AUTHOR>
   * @date 2023/06/28 14:38
   */
  static edit(params) {
    return Request.post('/remitChannel/edit', params);
  }

  /**
   * 修改状态
   *
   * <AUTHOR>
   * @date 2023/06/28 14:38
   */
  static updateStatus(params) {
    return Request.post('/remitChannel/updateStatus', params);
  }
  
}