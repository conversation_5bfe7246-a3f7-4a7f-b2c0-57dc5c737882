import Request from '@/utils/request-util';

/**
 * 调账记录表api
 *
 * <AUTHOR>
 * @date 2023/06/05 14:28
 */
export class FinanceAdjustRecordApi {
  /**
   * 获取代理商分页列表
   *
   * <AUTHOR>
   * @date 2023/06/05 14:28
   */
  static findAgentPage(params) {
    return Request.postAndLoadData('/financeAdjustRecordByAgent/page', params);
  }

  /**
   * 新增
   *
   * <AUTHOR>
   * @date 2023/06/05 14:28
   */
  static addAgentRecord(params) {
    return Request.post('/financeAdjustRecordByAgent/add', params);
  }

  /**
   * 获取商户分页列表
   *
   * <AUTHOR>
   * @date 2023/06/05 14:28
   */
  static findMerchantPage(params) {
    return Request.postAndLoadData('/financeAdjustRecordByMerchant/page', params);
  }

  /**
   * 商户新增
   *
   * <AUTHOR>
   * @date 2023/06/05 14:28
   */
  static addMerchantRecord(params) {
    return Request.post('/financeAdjustRecordByMerchant/add', params);
  }

  /**
   * 修改
   *
   * <AUTHOR>
   * @date 2023/06/05 14:28
   */
  static check(params) {
    return Request.post('/financeAdjustRecord/check', params);
  }

  /**
   * 删除
   *
   * <AUTHOR>
   * @date 2023/06/05 14:28
   */
  static delete(params) {
    return Request.post('/financeAdjustRecord/delete', params);
  }

  /**
   * 详情
   *
   * <AUTHOR>
   * @date 2023/06/05 14:28
   */
  static detail(params) {
    return Request.postAndLoadData('/financeAdjustRecord/detail', params);
  }
}
