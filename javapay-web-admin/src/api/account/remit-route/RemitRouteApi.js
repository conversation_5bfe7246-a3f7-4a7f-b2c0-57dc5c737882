import Request from '@/utils/request-util';

/**
 * 清算路由api
 *
 * <AUTHOR>
 * @date 2023/06/27 10:17
 */
export class RemitRouteApi{
    /**
     * 获取清算路由列表
     * <AUTHOR>
     * @date 2023/06/27 10:17
     */
  static findPage(params) {
    return Request.postAndLoadData('/remitRoute/page', params);
  }

  /**
   * 新增
   *
   * <AUTHOR>
   * @date 2023/06/28 14:38
   */
   static add(params) {
    return Request.post('/remitRoute/add', params);
  }

  /**
   * 编辑
   *
   * <AUTHOR>
   * @date 2023/06/28 14:38
   */
  static edit(params) {
    return Request.post('/remitRoute/edit', params);
  }

  /**
   * 修改状态
   *
   * <AUTHOR>
   * @date 2023/06/28 14:38
   */
  static updateStatus(params) {
    return Request.post('/remitRoute/updateStatus', params);
  }
  
}