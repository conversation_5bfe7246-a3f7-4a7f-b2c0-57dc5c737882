import Request from '@/utils/request-util';

/**
 * 代理商账户api
 *
 * <AUTHOR>
 * @date 2023/05/16 14:31
 */
export class AgentAccountApi {
  /**
   * 获取商户账户列表
   *
   * <AUTHOR>
   * @date 2023/05/16 14:31
   */
  static findPage(params) {
    return Request.postAndLoadData('/agentAccount/page', params);
  }

  /**
   * 详情
   *
   * <AUTHOR>
   * @date 2023/06/15 14:31
   */
  static detail(params) {
    return Request.postAndLoadData('/agentAccount/detail', params);
  }

  /**
   * 状态编辑
   */
  static editWalletStatus(params) {
    return Request.post('/agentAccount/editWalletStatus', params);
  }
}
