import Request from '@/utils/request-util';

/**
 * 清算批次记录api
 *
 * <AUTHOR>
 * @date 2023/07/13 10:29
 */
export class RemitBatchApi {
  /**
   * 获取分页列表
   *
   * <AUTHOR>
   * @date 2023/07/13 10:29
   */
  static findPage(params) {
    return Request.postAndLoadData('/remitBatch/page', params);
  }

  /**
   * 详情
   *
   * <AUTHOR>
   * @date 2023/07/13 10:29
   */
  static detail(params) {
    return Request.postAndLoadData('/remitBatch/detail', params);
  }
}