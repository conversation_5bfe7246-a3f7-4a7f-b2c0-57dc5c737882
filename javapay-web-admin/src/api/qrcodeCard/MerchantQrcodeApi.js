import Request from '@/utils/request-util';

/**
 * 商户码牌记录
 */
export class MerchantQrcodeApi {
/**
   * 分页
*/
static findPages(params) {
    return Request.postAndLoadData('/merchantQrcode/page', params);
}

/**
 * 修改
   */
static edit(params) {
    return Request.post('/merchantQrcode/edit', params);
}

/**
 * 详情
 */
static detail(params) {
    return Request.postAndLoadData('/merchantQrcode/detail', params);
}

/**
   * 分页
*/
static findList(params) {
    return Request.postAndLoadData('/merchantQrcode/list', params);
}

}