import Request from '@/utils/request-util';

/**
 * 码牌管理Api
 */
export class QrcodeCardApi {
  /**
   * 码牌批次
   */

  //分页列表
  static findBatchPage(params) {
    return Request.postAndLoadData('/qrcodeBatch/page', params);
  }

  //批量添加
  static addBatch(params) {
    return Request.post('/qrcodeBatch/batchGenerate', params);
  }

  //下载码牌
  static downloadZip(data) {
    return Request.requestByConfig({
      url: '/qrcodeBatch/download',
      method: 'post',
      data,
      responseType: 'blob'
    });
  }

  //下载码牌编码
  static downloadExcel(data) {
    return Request.requestByConfig({
      url: '/qrcodeBatch/downloadQrcodeSn',
      method: 'post',
       data,
      responseType: 'blob'
    });
  }

  //修改制作状态
  static editStepStatus(params) {
    return Request.post('/qrcodeBatch/edit', params);
  }

  /**
   * 码牌信息
   */

  //分页列表
  static findInfoPage(params) {
    return Request.postAndLoadData('/qrcodeSn/page', params);
  }

  //修改商户码牌状态
  static changeQrcodeStatus(params) {
    return Request.post('/merchantQrcode/changeStatus', params);
  }

  //修改商户码牌信息
  static editQrcodeInfo(params) {
    return Request.post('/merchantQrcode/edit', params);
  }

  //绑定码牌信息
  static bindQrcode(params) {
    return Request.post('/qrcodeBatch/bindAgent', params);
  }
}
