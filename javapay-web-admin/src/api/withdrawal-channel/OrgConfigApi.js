import Request from '@/utils/request-util';

/**
 * 机构配置api
 */
export class OrgConfigApi {
  /**
   * 分页
   */
  static findPage(params) {
    return Request.postAndLoadData('/dlgOrgConfig/page', params);
  }

  /**
   * 添加
   */
  static add(params) {
    return Request.post('/dlgOrgConfig/add', params);
  }

  /**
   * 编辑
   */
  static edit(params) {
    return Request.post('/dlgOrgConfig/edit', params);
  }
}
