import Request from '@/utils/request-util';

/**
 * 通道商户费率变更Api
 */
export class AisleMerchRateChangeApi {
  /** 分页 */
  static findPage(params) {
    return Request.postAndLoadData('/channelMerchRateChange/page', params);
  }

  /** 详情 */
  static detail(params) {
    return Request.postAndLoadData('/channelMerchRateChange/detail', params);
  }

  /** 编辑 */
  static edit(params) {
    return Request.post('/channelMerchRateChange/edit', params);
  }
}
