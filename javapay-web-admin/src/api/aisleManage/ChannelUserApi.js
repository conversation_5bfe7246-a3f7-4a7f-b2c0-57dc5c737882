import Request from '@/utils/request-util';

/**
 * 通道用户Api
 */
export class ChannelUserApi {
  /*
   分页
  */
  static findPage(params) {
    return Request.postAndLoadData('/channelUser/page', params);
  }

  /*
   添加
  */
  static add(params) {
    return Request.post('/channelUser/add', params);
  }

  /*
   编辑
  */
  static edit(params) {
    return Request.post('/channelUser/edit', params);
  }

  /*
   详情
  */
  static detail(params) {
    return Request.postAndLoadData('/channelUser/detail', params);
  }


  /*
   删除
  */
  static delete(params) {
    return Request.post('/channelUser/delete', params);
  }
}
