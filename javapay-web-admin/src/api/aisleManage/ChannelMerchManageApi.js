import Request from '@/utils/request-util';

/**
 * 渠道商户管理api
 */
export class ChannelMerchManageApi {
  /**
   * 获取数据列表
   */
  static getChannelMerchManagePages(params) {
    return Request.postAndLoadData('/channelMerchant/page', params);
  }

  /**
   * 支付方式配置
   */
  static payMethodSwitchConfig(params) {
    return Request.post('/channelMerchant/payMethodSwitchConfig', params);
  }

  /**
   * 结算账户查询
   */
  static settleBankCardDetail(params) {
    return Request.postAndLoadData('/channelMerchant/settleBankCardDetail', params);
  }

  /**
   * 查看通道图片
   */
  static detailImage(params) {
    return Request.postAndLoadData('/merchantSignOrder/detailImage', params);
  }

  /**
   * 修改富友清算时间
   */
  static changeSettleTime(params) {
    return Request.post('/channelMerchant/changeSettleTime', params);
  }

  /**
   * 关闭富友商户
   */
  static closeChlMerch(params) {
    return Request.post('/channelMerchant/closeChlMerch', params);
  }

  /**
   * 通道商户交易累计详情
   */
  static transCumulateDetail(params) {
    return Request.postAndLoadData('/channelMerchantCumulate/detail', params);
  }

  /**
   * 通道商户修改地址
   */
  static uptAddress(params) {
    return Request.post('/chnMerch/uptAddress', params);
  }

  /**
   * 编辑通道商户状态
   */
  static editOpenStatus(params) {
    return Request.post('/channelMerchant/editOpenStatus', params);
  }

  /**
   * 补充图片上传
   */
  static supplyFileUpload(params) {
    return Request.post('/channelMerchant/supplyFileUpload', params);
  }
}
