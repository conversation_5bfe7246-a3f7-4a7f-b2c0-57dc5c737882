import Request from '@/utils/request-util';

/**
 * 通道费率Api
 */
export class AisleRateApi {
  //分页
  static findPage(params) {
    return Request.postAndLoadData('/channelRate/page', params);
  }

  //添加
  static add(params) {
    return Request.post('/channelRate/add', params);
  }

  //查看
  static detail(params) {
    return Request.postAndLoadData('/channelRate/detail', params);
  }

  //修改
  static edit(params) {
    return Request.post('/channelRate/edit', params);
  }

  //获取列表
  static list(params) {
    return Request.postAndLoadData('/channelRate/list', params);
  }
}
