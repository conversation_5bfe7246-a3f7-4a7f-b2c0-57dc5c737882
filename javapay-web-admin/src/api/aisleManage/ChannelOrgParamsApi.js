import Request from '@/utils/request-util';

/**
 * 机构通道密钥参数Api
 */
export class ChannelOrgParamsApi {
  /*
   分页
  */
  static findPage(params) {
    return Request.postAndLoadData('/channelOrgParams/page', params);
  }

  /*
   添加
  */
  static add(params) {
    return Request.post('/channelOrgParams/add', params);
  }

  /*
   编辑
  */
  static edit(params) {
    return Request.post('/channelOrgParams/edit', params);
  }

  /*
   详情
  */
  static detail(params) {
    return Request.postAndLoadData('/channelOrgParams/detail', params);
  }


  /*
   删除
  */
  static delete(params) {
    return Request.post('/channelOrgParams/delete', params);
  }
}
