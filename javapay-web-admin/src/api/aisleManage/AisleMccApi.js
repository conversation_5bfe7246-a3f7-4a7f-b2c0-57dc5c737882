import Request from '@/utils/request-util';

/**
 * 通道Mcc码Api
 */
export class AisleMccApi {
  //分页列表
  static findPage(params) {
    return Request.postAndLoadData('/channelMcc/page', params);
  }

  //添加
  static add(params) {
    return Request.post('/channelMcc/add', params);
  }

  //编辑
  static edit(params) {
    return Request.post('/channelMcc/edit', params);
  }

  //详情
  static detail(params) {
    return Request.post('/channelMcc/detail', params);
  }


  //删除
  static delete(params) {
    return Request.post('/channelMcc/delete', params);
  }

  //批量删除
  static batchDelete(params) {
    return Request.post('/channelMcc/batchDelete', params);
  }
}
