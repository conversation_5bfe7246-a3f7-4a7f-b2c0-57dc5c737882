import Request from '@/utils/request-util';

/**
 * 通道商户结算卡变更api
 */
export class AisleMerchPaymentCardChangeApi {
    /**
     * 
     * 获取列表
     * @returns 
     */
    static getMerchPaymentCardPage(params) {
        return Request.postAndLoadData('/channelMerchCardChange/page', params);
    }
    
    /**
     * 同步账户结算卡变更
     */
    static editAccountSettleCard(params) {
        return Request.post('/channelMerchCardChange/editAccountSettleCard', params);
    }

}