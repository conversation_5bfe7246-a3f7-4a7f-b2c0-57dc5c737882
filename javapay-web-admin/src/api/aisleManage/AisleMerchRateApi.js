import Request from '@/utils/request-util';

/**
 * 通道商户费率Api
 */
export class AisleMerchRateApi {
  //分页
  static findPage(params) {
    return Request.postAndLoadData('/channelMerchantRate/page', params);
  }

  //详情
  static detail(params) {
    return Request.postAndLoadData('/channelMerchantRate/detail', params);
  }

  //编辑
  static edit(params) {
    return Request.post('/channelMerchantRate/edit', params);
  }

  /** 费率文件记录下载 */
  static download(data) {
    return Request.requestByConfigNoData({
      url: '/channelMerchantRate/download',
      method: 'post',
      data,
      responseType: 'blob'
    });
  }
}
