import Request from '@/utils/request-util';

/**
 * 通道银行总行Api
 */
export class AisleBankTypeApi {
  //分页
  static findPage(params) {
    return Request.postAndLoadData('/channelBankType/page', params);
  }

  //添加
  static add(params) {
    return Request.post('/channelBankType/add', params);
  }

  //编辑
  static edit(params) {
    return Request.post('/channelBankType/edit', params);
  }

  //删除
  static delete(params) {
    return Request.post('/channelBankType/delete', params);
  }
}
