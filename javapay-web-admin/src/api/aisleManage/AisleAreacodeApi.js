import Request from '@/utils/request-util';

/**
 * 通道地区码Api
 */
export class AisleAreacodeApi {
  //分页列表
  static findPage(params) {
    return Request.postAndLoadData('/channelArea/page', params);
  }

  //添加
  static add(params) {
    return Request.post('/channelArea/add', params);
  }

  //编辑
  static edit(params) {
    return Request.post('/channelArea/edit', params);
  }


  //删除
  static delete(params) {
    return Request.post('/channelArea/delete', params);
  }

  //批量删除
  static batchDelete(params) {
    return Request.post('/channelArea/batchDelete', params);
  }
}