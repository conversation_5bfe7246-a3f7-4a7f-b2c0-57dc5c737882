import Request from '@/utils/request-util';

/**
 * 通道银行限额Api
 */
export class ChannelBankLimitApi {
  //分页列表
  static findPage(params) {
    return Request.postAndLoadData('/channelBankLimit/page', params);
  }

  //添加
  static add(params) {
    return Request.post('/channelBankLimit/add', params);
  }

  //编辑
  static edit(params) {
    return Request.post('/channelBankLimit/edit', params);
  }

  //详情
  static detail(params) {
    return Request.postAndLoadData('/channelBankLimit/detail', params);
  }

  //删除
  static delete(params) {
    return Request.post('/channelBankLimit/delete', params);
  }
}
