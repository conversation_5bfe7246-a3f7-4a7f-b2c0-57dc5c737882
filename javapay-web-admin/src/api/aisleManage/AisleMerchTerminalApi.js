import Request from '@/utils/request-util';

/**
 * 通道商户终端Api
 */
export class AisleMerchTerminalApi  {
  //分页
  static findPage(params) {
    return Request.postAndLoadData('/channelMerchTerm/page', params);
  }

  //分页
  static terminalTransInfo(params) {
    return Request.postAndLoadData('/terminalTransInfo/detail', params);
  }

  //银联终端报备(手动发起)
  static reReportUnionTerminalSn(params) {
    return Request.post('/terminalInfo/reReportUnionTerminalSn', params);
  }
}
