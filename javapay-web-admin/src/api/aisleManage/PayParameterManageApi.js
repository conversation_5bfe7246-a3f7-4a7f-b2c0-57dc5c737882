import Request from '@/utils/request-util';
/**
 * 支付参数管理api
 *
 */
export class PayParameterManageApi {
    /**
     * 获取分页数据
     */
    static getPlatformPayChannelPages(params) {
        return Request.postAndLoadData('/platformPayChannel/page', params);
    }

    /**
     * 添加
     */
    static add(params) {
        return Request.post('/platformPayChannel/add', params);
    }

    /**
     * 编辑
     */
    static edit(params) {
        return Request.post('/platformPayChannel/edit', params);
    }

    /**
     * 删除
     */
    static delete(params) {
        return Request.post('/platformPayChannel/delete', params);
    }

}