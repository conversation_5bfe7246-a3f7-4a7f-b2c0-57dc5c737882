import Request from '@/utils/request-util';

/**
 * 通道Mcc关联Api
 */
export class AisleMccRelationApi {
  //分页列表
  static findPage(params) {
    return Request.postAndLoadData('/mccAndChnMccRelation/page', params);
  }

  //添加
  static add(params) {
    return Request.post('/mccAndChnMccRelation/add', params);
  }

  //编辑
  static edit(params) {
    return Request.post('/mccAndChnMccRelation/edit', params);
  }

  //详情
  static detail(params) {
    return Request.postAndLoadData('/mccAndChnMccRelation/detail', params);
  }

  //删除
  static delete(params) {
    return Request.post('/mccAndChnMccRelation/delete', params);
  }

}
