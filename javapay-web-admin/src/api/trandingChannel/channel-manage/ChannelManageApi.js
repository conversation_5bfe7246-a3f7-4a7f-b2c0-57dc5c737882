import Request from '@/utils/request-util';

/**
 * 渠道Api
 */
export class ChannelManageApi {
  //渠道-列表查询
  static findChannelList(parmas) {
    return Request.postAndLoadData('/channel/page', parmas);
  }

  //渠道-新增接口
  static addChannel(params) {
    return Request.post('/channel/add', params);
  }

  //渠道-编辑接口
  static editChannel(parmas) {
    return Request.post('/channel/edit', parmas);
  }

  //渠道-列表
  static list(parmas) {
    return Request.postAndLoadData('/channel/list', parmas);
  }

  //渠道-查看通道参数
  static queryByChnCode(parmas) {
    return Request.postAndLoadData('/channel/queryByChnCode', parmas);
  }

  //渠道-通道二维码图片上传
  static uploadAuthQrcodeInfo(parmas) {
    return Request.post('/channel/uploadAuthQrcodeInfo', parmas);
  }

  //渠道-认证二维码查询
  static queryAuthQrcodeInfo(parmas) {
    return Request.postAndLoadData('/channel/queryAuthQrcodeInfo', parmas);
  }

  /**
   * 通道交易限制
   */

  //添加
  static addChannelTransLimit(parmas) {
    return Request.post('/channelTransLimit/add', parmas);
  }
  //编辑
  static editChannelTransLimit(parmas) {
    return Request.post('/channelTransLimit/edit', parmas);
  }
  //详情
  static detailChannelTransLimit(parmas) {
    return Request.postAndLoadData('/channelTransLimit/detail', parmas);
  }


  //详情
  static channekTransInfoDetail(parmas) {
    return Request.postAndLoadData('/channelTransInfo/detail', parmas);
  }

  //通道签到
  static channekTransInfoEdit(parmas) {
    return Request.post('/channelTransInfo/edit', parmas);
  }
}
