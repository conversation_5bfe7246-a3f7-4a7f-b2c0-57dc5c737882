import Request from '@/utils/request-util';

/**
 * 系统通知管理api
 *
 * <AUTHOR>
 * @date 2021/04/04 00:29
 */
export default class PublishApi {
  /**
   * 获取通知列表
   *
   * <AUTHOR>
   * @date 2021/4/1 16:07
   */
  static findPage(params) {
    return Request.getAndLoadData('/sysNotice/page', params);
  }

  /**
   * 新建系统通知
   *
   * <AUTHOR>
   * @date 2021/04/04 00:29
   */
  static addPublish(params) {
    return Request.post('/sysNotice/add', params);
  }

  /**
   * 编辑系统消息
   * <AUTHOR>
   * @date 2021/04/07 23:03
   */
  static editPublish(params) {
    return Request.post('/sysNotice/edit', params);
  }

  /**
   * 发送支付成功通知消息
   * @param {Object} params - 包含用户ID、支付信息和地址的参数
   * @param {string} params.userId - 用户ID
   * @param {string} params.paymentId - 支付ID
   * @param {string} params.address - 要发送的地址
   * @param {number} params.amount - 支付金额
   * <AUTHOR> @date 2024/08/28
   */
  static sendPaymentSuccessNotice(params) {
    const noticeData = {
      noticeTitle: '支付成功通知',
      noticeContent: `您的支付已成功完成！\n支付金额：¥${params.amount}\n支付单号：${params.paymentId}\n\n您的专属地址：${params.address}\n\n感谢您的使用！`,
      noticeScope: params.userId, // 发送给特定用户
      priorityLevel: 'M', // 中等优先级
      noticeType: 'PAYMENT_SUCCESS' // 支付成功类型
    };
    return Request.post('/sysNotice/add', noticeData);
  }

  /**
   * 批量发送支付成功通知
   * @param {Array} paymentList - 支付成功的用户列表
   * <AUTHOR> @date 2024/08/28
   */
  static batchSendPaymentNotice(paymentList) {
    return Request.post('/sysNotice/batchSendPaymentNotice', { paymentList });
  }
}
