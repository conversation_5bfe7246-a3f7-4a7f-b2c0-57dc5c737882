import Request from '@/utils/request-util';

/**
 * APP应用管理Api
 */
export class AppBrandApi {
  /** 分页 */
  static findPage(params) {
    return Request.postAndLoadData('/appBrand/page', params);
  }

  /** 列表 */
  static list(params) {
    return Request.postAndLoadData('/appBrand/list', params);
  }

  /** 添加 */
  static add(params) {
    return Request.post('/appBrand/add', params);
  }

  /** 详情 */
  static detail(params) {
    return Request.postAndLoadData('/appBrand/detail', params);
  }

  /** 编辑 */
  static edit(params) {
    return Request.post('/appBrand/edit', params);
  }

  /** 删除 */
  static delete(params) {
    return Request.post('/appBrand/delete', params);
  }
}
