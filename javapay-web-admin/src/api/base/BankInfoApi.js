import Request from '@/utils/request-util';

/**
 * 银行信息Api
 */
export class BankInfoApi {
  //分页
  static findPage(params) {
    return Request.postAndLoadData('/bankInfo/page', params);
  }

  //列表
  static list(params) {
    return Request.postAndLoadData('/bankInfo/list', params);
  }

  //添加
  static add(params) {
    return Request.post('/bankInfo/add', params);
  }

  //详情
  static detail(params) {
    return Request.postAndLoadData('/bankInfo/detail', params);
  }

  //编辑
  static edit(params) {
    return Request.post('/bankInfo/edit', params);
  }

  //修改状态
  static changeStatus(params) {
    return Request.post('/bankInfo/changeStatus', params);
  }

  //删除
  static delete(params) {
    return Request.post('/bankInfo/delete', params);
  }

  //批量删除
  static batchDelete(params) {
    return Request.post('/bankInfo/batchDelete', params);
  }
}