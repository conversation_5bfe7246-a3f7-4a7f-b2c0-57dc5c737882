import Request from '@/utils/request-util';

/**
 * 地区码Api
 */
export class AreaApi {
  //分页列表
  static findPage(params) {
    return Request.postAndLoadData('/area/page', params);
  }

  //列表
  static list(params) {
    return Request.postAndLoadData('/area/list', params);
  }

  //添加
  static add(params) {
    return Request.post('/area/add', params);
  }

  //详情
  static detail(params) {
    return Request.postAndLoadData('/area/detail', params);
  }

  //编辑
  static edit(params) {
    return Request.post('/area/edit', params);
  }

  //修改状态
  static changeStatus(params) {
    return Request.post('/area/changeStatus', params);
  }

  //删除
  static delete(params) {
    return Request.post('/area/delete', params);
  }

  //批量删除
  static batchDelete(params) {
    return Request.post('/area/batchDelete', params);
  }
}