import Request from '@/utils/request-util';

/**
 * 银行总行Api
 */
export class BankTypeApi {
  //分页
  static findPage(params) {
    return Request.postAndLoadData('/bankType/page', params);
  }

  //列表
  static list(params) {
    return Request.postAndLoadData('/bankType/list', params);
  }

  //添加
  static add(params) {
    return Request.post('/bankType/add', params);
  }

  //详情
  static detail(params) {
    return Request.postAndLoadData('/bankType/detail', params);
  }

  //编辑
  static edit(params) {
    return Request.post('/bankType/edit', params);
  }

  //修改状态
  static changeStatus(params) {
    return Request.post('/bankType/changeStatus', params);
  }

  //删除
  static delete(params) {
    return Request.post('/bankType/delete', params);
  }

  //批量删除
  static batchDelete(params) {
    return Request.post('/bankType/batchDelete', params);
  }
}
