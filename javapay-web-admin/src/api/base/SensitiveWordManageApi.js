import Request from '@/utils/request-util';

/**
 * 系统敏感词管理
 */
export class SensitiveWordManageApi {
  //分页列表
  static findPage(params) {
    return Request.postAndLoadData('/sensitiveWord/page', params);
  }

  //添加
  static add(params) {
    return Request.post('/sensitiveWord/add', params);
  }

  //详情
  static detail(params) {
    return Request.postAndLoadData('/sensitiveWord/detail', params);
  }

  //编辑
  static edit(params) {
    return Request.post('/sensitiveWord/edit', params);
  }

  //删除
  static delete(params) {
    return Request.post('/sensitiveWord/delete', params);
  }
}