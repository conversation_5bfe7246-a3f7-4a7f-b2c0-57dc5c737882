import Request from '@/utils/request-util';

/**
 * 卡BIN管理api
 */
export class CareerManageApi {

    /**
     * 获取列表数据
     */
    static getCareerManagePages(params) {
        return Request.postAndLoadData('/profession/page', params);
    }

    /**
     * 新增
     */
    static add(params) {
        return Request.post('/profession/add', params);
    }

    /**
     * 修改
     */
    static edit(params) {
        return Request.post('/profession/edit', params);
    }

    /**
     * 删除
     */
    static delete(params) {
        return Request.post('/profession/delete', params);
    }

    /**
     * 修改状态
     */
    static changeStatus(params) {
        return Request.post('/profession/changeStatus', params);
    }

    /**
     * 修改状态
     */
    static getProfessionList(params) {
        return Request.post('/profession/list', params);
    }

}