import Request from '@/utils/request-util';

/**
 * 序列生成Api
 */
export class SequenceApi {
  //分页列表
  static findPage(params) {
    return Request.postAndLoadData('/sequence/page', params);
  }

  //列表
  static list(params) {
    return Request.postAndLoadData('/sequence/list', params);
  }

  //添加
  static add(params) {
    return Request.post('/sequence/add', params);
  }

  //详情
  static detail(params) {
    return Request.postAndLoadData('/sequence/detail', params);
  }

  //编辑
  static edit(params) {
    return Request.post('/sequence/edit', params);
  }

  //删除
  static delete(params) {
    return Request.post('/sequence/delete', params);
  }

  //批量删除
  static batchDelete(params) {
    return Request.post('/sequence/batchDelete', params);
  }
}