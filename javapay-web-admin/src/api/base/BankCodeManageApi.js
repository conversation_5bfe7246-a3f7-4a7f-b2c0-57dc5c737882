import Request from '@/utils/request-util';

/**
 * 银行编码pi
 */
export class BankCodeManageApi {
    /**
     * 获取整体列表
     *
     */
    static list(params) {
      return Request.postAndLoadData('/bankTypeCode/list', params);
    }
  
    /**
     * 获取分页列表
     *
     */
    static getBankCodePages(params) {
      return Request.postAndLoadData('/bankTypeCode/page', params);
    }
  
    /**
     * 新增
     *
     */
    static add(params) {
      return Request.post('/bankTypeCode/add', params);
    }
  
    /**
     * 修改
     *
     */
    static edit(params) {
      return Request.post('/bankTypeCode/edit', params);
    }
    
    /**
     * 详情
     */
    static detail(params) {
      return Request.postAndLoadData('/bankTypeCode/detail', params);
    }
  
    /**
     * 删除卡BIN
     *
     */
    static delete(params) {
      return Request.post('/bankTypeCode/delete', params);
    }

  }