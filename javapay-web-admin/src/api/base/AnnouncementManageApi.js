import Request from '@/utils/request-util';

/**
 * 公告消息Api
 */
export class AnnouncementManageApi {
  //列表
  static list(params) {
    return Request.postAndLoadData('/messageAnnounce/page', params);
  }

  //添加
  static add(params) {
    return Request.post('/messageAnnounce/add', params);
  }

  //详情
  static detail(params) {
    return Request.postAndLoadData('/messageAnnounce/detail', params);
  }

  //编辑
  static edit(params) {
    return Request.post('/messageAnnounce/edit', params);
  }

  //删除
  static delete(params) {
    return Request.post('/messageAnnounce/delete', params);
  }
}
