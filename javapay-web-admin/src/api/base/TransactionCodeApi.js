import Request from '@/utils/request-util';
/**
 * 交易码管理api
 */

export class TransactionCodeApi {
    //获取分页数据
    static getTransCodePage(params) {
        return Request.postAndLoadData('/transCode/page',params);
    }
    //新增数据
    static addTransCode(params) {
        return Request.post('/transCode/add', params);
    }
    //编辑数据
    static editTransCode(params) {
        return Request.post('/transCode/edit', params);
    }
    //获取详情
    static getTransCodeDetail(params) {
        return Request.postAndLoadData('/transCode/detail', params);
    }
    //删除
    static deleteTransCode(params) {
        return Request.post('/transCode/delete', params);
    }
    //批量删除
    static batchDelete(params) {
        return Request.post('/transCode/batchDelete', params);
    }
}
