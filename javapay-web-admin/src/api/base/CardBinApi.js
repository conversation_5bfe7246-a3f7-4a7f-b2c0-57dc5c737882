import Request from '@/utils/request-util';

/**
 * 卡BIN管理api
 *
 * <AUTHOR>
 * @date 2021/4/1 16:06
 */
export class CardBinApi {
  /**
   * 获取卡BIN列表
   *
   * <AUTHOR>
   * @date 2021/4/1 16:07
   */
  static getCardBinPages(params) {
    return Request.postAndLoadData('/cardBin/page', params);
  }

  /**
   * 新增卡BIN
   *
   * <AUTHOR>
   * @date 2021/4/1 16:07
   */
  static add(params) {
    return Request.post('/cardBin/add', params);
  }

  /**
   * 修改卡BIN
   *
   * <AUTHOR>
   * @date 2021/4/1 16:07
   */
  static edit(params) {
    return Request.post('/cardBin/edit', params);
  }

  static detail(params) {
    return Request.postAndLoadData('/cardBin/detail', params);
  }


  /**
   * 删除卡BIN
   *
   * <AUTHOR>
   * @date 2021/4/1 16:07
   */
  static delete(params) {
    return Request.post('/cardBin/delete', params);
  }

  /**
   * 批量删除卡BIN
   *
   * <AUTHOR>
   * @date 2021/4/1 16:07
   */
  static batchDelete(params) {
    return Request.post('/cardBin/batchDelete', params);
  }

  static changeStatus(params) {
    return Request.post('/cardBin/changeStatus', params);
  }
}
