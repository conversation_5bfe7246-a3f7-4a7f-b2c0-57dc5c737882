import Request from '@/utils/request-util';

/**
 * Mcc码Api
 */
export class MccCodeApi {
  //分页列表
  static findPage(params) {
    return Request.postAndLoadData('/mcc/page', params);
  }

  //列表
  static list(params) {
    return Request.postAndLoadData('/mcc/list', params);
  }

  //添加
  static add(params) {
    return Request.post('/mcc/add', params);
  }

  //详情
  static detail(params) {
    return Request.postAndLoadData('/mcc/detail', params);
  }

  //编辑
  static edit(params) {
    return Request.post('/mcc/edit', params);
  }

  //修改状态
  static changeStatus(params) {
    return Request.post('/mcc/changeStatus', params);
  }

  //删除
  static delete(params) {
    return Request.post('/mcc/delete', params);
  }

  //批量删除
  static batchDelete(params) {
    return Request.post('/mcc/batchDelete', params);
  }
}