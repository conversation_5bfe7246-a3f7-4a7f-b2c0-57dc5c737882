import Request from '@/utils/request-util';

/**
 * APP版本管理Api
 */
export class AppVersionManageApi {
  //分页
  static findPage(params) {
    return Request.postAndLoadData('/appVersionRecord/page', params);
  }

  //添加
  static add(params) {
    return Request.post('/appVersionRecord/add', params);
  }

  //详情
  static detail(params) {
    return Request.postAndLoadData('/appVersionRecord/detail', params);
  }

  //编辑
  static edit(params) {
    return Request.post('/appVersionRecord/edit', params);
  }

  //删除
  static delete(params) {
    return Request.post('/appVersionRecord/delete', params);
  }
}
