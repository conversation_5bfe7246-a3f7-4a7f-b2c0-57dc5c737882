import Request from '@/utils/request-util';

/**
 * 交易码Api
 */
export class RespCodeApi {
  //分页列表
  static findPage(params) {
    return Request.postAndLoadData('/respCode/page', params);
  }

  //添加
  static add(params) {
    return Request.post('/respCode/add', params);
  }

  //详情
  static detail(params) {
    return Request.postAndLoadData('/respCode/detail', params);
  }

  //编辑
  static edit(params) {
    return Request.post('/respCode/edit', params);
  }

  //删除
  static delete(params) {
    return Request.post('/respCode/delete', params);
  }

  //批量删除
  static batchDelete(params) {
    return Request.post('/respCode/batchDelete', params);
  }
}