<template>
  <div class="payment-chat-box" v-if="visible">
    <div class="chat-header">
      <span class="chat-title">
        <message-outlined />
        支付通知
      </span>
      <span class="chat-close" @click="closeChat">
        <close-outlined />
      </span>
    </div>
    
    <div class="chat-content" ref="chatContent">
      <div 
        v-for="(message, index) in messages" 
        :key="index"
        class="chat-message"
        :class="{ 'system-message': message.type === 'system' }"
      >
        <div class="message-avatar" v-if="message.type === 'system'">
          <check-circle-outlined class="success-icon" />
        </div>
        
        <div class="message-content">
          <div class="message-header">
            <span class="message-sender">{{ message.sender }}</span>
            <span class="message-time">{{ formatTime(message.timestamp) }}</span>
          </div>
          
          <div class="message-body">
            <div class="message-text" v-html="message.content"></div>
            
            <!-- 地址信息特殊显示 -->
            <div v-if="message.address" class="address-info">
              <div class="address-label">🏠 您的专属地址：</div>
              <div class="address-content">
                <a-input 
                  :value="message.address" 
                  readonly 
                  class="address-input"
                >
                  <template #suffix>
                    <a-tooltip title="复制地址">
                      <copy-outlined 
                        class="copy-icon" 
                        @click="copyAddress(message.address)"
                      />
                    </a-tooltip>
                  </template>
                </a-input>
              </div>
              <div class="address-actions">
                <a-button 
                  type="primary" 
                  size="small" 
                  @click="openAddress(message.address)"
                >
                  访问地址
                </a-button>
                <a-button 
                  size="small" 
                  @click="shareAddress(message.address)"
                >
                  分享
                </a-button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-if="messages.length === 0" class="empty-state">
        <div class="empty-icon">💬</div>
        <div class="empty-text">暂无消息</div>
      </div>
    </div>
    
    <!-- 底部操作栏 -->
    <div class="chat-footer">
      <a-button size="small" @click="clearMessages">清空消息</a-button>
      <a-button size="small" @click="refreshMessages">刷新</a-button>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import { 
  MessageOutlined, 
  CloseOutlined, 
  CheckCircleOutlined,
  CopyOutlined 
} from '@ant-design/icons-vue';

export default {
  name: 'PaymentChatBox',
  components: {
    MessageOutlined,
    CloseOutlined,
    CheckCircleOutlined,
    CopyOutlined
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    autoShow: {
      type: Boolean,
      default: true
    }
  },
  emits: ['update:visible', 'message-received'],
  setup(props, { emit }) {
    const chatContent = ref(null);
    const messages = reactive([]);

    // 监听支付成功事件
    const handlePaymentSuccess = (event) => {
      const { detail } = event;
      addMessage({
        type: 'system',
        sender: '系统通知',
        content: detail.content,
        address: detail.address,
        timestamp: detail.timestamp || Date.now()
      });

      // 如果设置了自动显示，则显示聊天框
      if (props.autoShow) {
        emit('update:visible', true);
      }

      emit('message-received', detail);
    };

    // 添加消息
    const addMessage = (messageData) => {
      messages.push(messageData);
      
      // 滚动到底部
      nextTick(() => {
        scrollToBottom();
      });
    };

    // 滚动到底部
    const scrollToBottom = () => {
      if (chatContent.value) {
        chatContent.value.scrollTop = chatContent.value.scrollHeight;
      }
    };

    // 格式化时间
    const formatTime = (timestamp) => {
      const date = new Date(timestamp);
      return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    };

    // 复制地址
    const copyAddress = async (address) => {
      try {
        await navigator.clipboard.writeText(address);
        message.success('地址已复制到剪贴板');
      } catch (error) {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = address;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        message.success('地址已复制到剪贴板');
      }
    };

    // 打开地址
    const openAddress = (address) => {
      if (address.startsWith('http')) {
        window.open(address, '_blank');
      } else {
        message.info('地址格式不正确，无法直接访问');
      }
    };

    // 分享地址
    const shareAddress = (address) => {
      if (navigator.share) {
        navigator.share({
          title: '我的专属地址',
          text: '这是我的专属访问地址',
          url: address
        });
      } else {
        copyAddress(address);
        message.info('地址已复制，您可以手动分享');
      }
    };

    // 关闭聊天框
    const closeChat = () => {
      emit('update:visible', false);
    };

    // 清空消息
    const clearMessages = () => {
      messages.splice(0, messages.length);
      message.success('消息已清空');
    };

    // 刷新消息
    const refreshMessages = () => {
      // 这里可以调用API重新获取消息
      message.info('消息已刷新');
    };

    // 添加示例消息（用于测试）
    const addSampleMessage = () => {
      addMessage({
        type: 'system',
        sender: '系统通知',
        content: '🎉 支付成功！您的订单已处理完成。',
        address: 'https://example.com/user/12345/access/abc123',
        timestamp: Date.now()
      });
    };

    onMounted(() => {
      // 监听支付成功事件
      window.addEventListener('payment-success-chat', handlePaymentSuccess);
      
      // 添加示例消息（开发测试用）
      if (process.env.NODE_ENV === 'development') {
        setTimeout(() => {
          addSampleMessage();
        }, 1000);
      }
    });

    onUnmounted(() => {
      window.removeEventListener('payment-success-chat', handlePaymentSuccess);
    });

    return {
      chatContent,
      messages,
      formatTime,
      copyAddress,
      openAddress,
      shareAddress,
      closeChat,
      clearMessages,
      refreshMessages,
      addMessage
    };
  }
};
</script>

<style lang="less" scoped>
.payment-chat-box {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 350px;
  height: 400px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  z-index: 1000;
  border: 1px solid #e8e8e8;

  .chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f5f5f5;
    border-bottom: 1px solid #e8e8e8;
    border-radius: 8px 8px 0 0;

    .chat-title {
      font-weight: 500;
      color: #333;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .chat-close {
      cursor: pointer;
      color: #999;
      padding: 4px;
      border-radius: 4px;
      transition: all 0.2s;

      &:hover {
        color: #666;
        background: #e6e6e6;
      }
    }
  }

  .chat-content {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
    
    .chat-message {
      margin-bottom: 16px;
      display: flex;
      gap: 8px;

      &.system-message {
        .message-content {
          background: #f6ffed;
          border: 1px solid #b7eb8f;
        }
      }

      .message-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: #52c41a;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;

        .success-icon {
          color: #fff;
          font-size: 16px;
        }
      }

      .message-content {
        flex: 1;
        background: #f5f5f5;
        border-radius: 8px;
        padding: 8px 12px;

        .message-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 4px;

          .message-sender {
            font-weight: 500;
            font-size: 12px;
            color: #666;
          }

          .message-time {
            font-size: 11px;
            color: #999;
          }
        }

        .message-body {
          .message-text {
            font-size: 14px;
            line-height: 1.4;
            color: #333;
            margin-bottom: 8px;
          }

          .address-info {
            .address-label {
              font-size: 12px;
              color: #666;
              margin-bottom: 6px;
            }

            .address-content {
              margin-bottom: 8px;

              .address-input {
                font-size: 12px;
                
                :deep(.ant-input) {
                  background: #fff;
                  border: 1px solid #d9d9d9;
                }

                .copy-icon {
                  cursor: pointer;
                  color: #1890ff;
                  
                  &:hover {
                    color: #40a9ff;
                  }
                }
              }
            }

            .address-actions {
              display: flex;
              gap: 8px;
            }
          }
        }
      }
    }

    .empty-state {
      text-align: center;
      padding: 40px 20px;
      color: #999;

      .empty-icon {
        font-size: 32px;
        margin-bottom: 8px;
      }

      .empty-text {
        font-size: 14px;
      }
    }
  }

  .chat-footer {
    padding: 8px 12px;
    border-top: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    background: #fafafa;
    border-radius: 0 0 8px 8px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .payment-chat-box {
    width: 300px;
    height: 350px;
    bottom: 10px;
    right: 10px;
  }
}
</style>
