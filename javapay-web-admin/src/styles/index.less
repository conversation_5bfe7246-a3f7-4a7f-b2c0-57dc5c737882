/** 全局样式 */
@import 'ant-design-vue/es/message/style/index.less';
@import 'ele-admin-pro/es/style/nprogress.less';
@import 'ele-admin-pro/es/style/display.less';
@import 'ele-admin-pro/es/style/common.less';
@import './transition/index.less';

// 主题
@import 'ele-admin-pro/es/style/themes/dynamic.less';

@import url(//at.alicdn.com/t/font_3190923_g95c1a9wwwo.css);

/* 需要覆盖框架样式变量写最下面, 具体请到文档查看 */
/*搜索框与表格之间的间隙*/
.block-interval {
  margin-bottom: 10px;
}

.left-menu-height {
  min-height: calc(90vh - 17px);
}

.table-height {
  min-height: calc(90vh - 108px);
}

// 搜索框内元素间隔
.search-bar-margin-left {
  margin-left: 1em;
}

.ant-descriptions-item-label{
  color: grey !important;
}