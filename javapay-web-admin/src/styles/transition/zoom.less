/* 放大渐变 */
.zoom-in-enter-active,
.zoom-in-leave-active {
  transition: opacity 0.2s ease-out, transform 0.25s ease-out;
}

.zoom-in-enter-from {
  opacity: 0;
  transform: scale(0.9);
}

.zoom-in-leave-to {
  opacity: 0;
  transform: scale(1.1);
}

/* 缩小渐变 */
.zoom-out-leave-active,
.zoom-out-enter-active {
  transition: opacity 0.2s ease-out, transform 0.25s ease-out;
}

.zoom-out-enter-from {
  opacity: 0;
  transform: scale(1.2);
}

.zoom-out-leave-to {
  opacity: 0;
  transform: scale(0.8);
}
