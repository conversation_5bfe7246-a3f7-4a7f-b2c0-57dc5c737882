<template>
  <a-modal
    :width="800"
    :visible="visible"
    title="详情"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
    :footer="null"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="操作类型">
        <template v-for="({ label, value }, key) in operateType" :key="key">
          <a-tag v-if="form.operateType === value">{{ label }}</a-tag>
        </template>
      </a-descriptions-item>
      <a-descriptions-item label="操作状态">
        <a-tag v-if="form.operateStatus === 1" color="success">成功</a-tag>
        <a-tag v-else-if="form.operateStatus === 2" color="error">失败</a-tag>
        <a-tag v-else-if="form.operateStatus === 3" color="pink">其他异常</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="操作终端ID">{{ form.terminalId }}</a-descriptions-item>
      <a-descriptions-item label="操作终端SN">{{ form.terminalSn }}</a-descriptions-item>
      <a-descriptions-item label="操作终端编号" :span="2">{{ form.terminalNo }}</a-descriptions-item>
      <a-descriptions-item label="原始信息" :span="2">{{ form.origInfo }}</a-descriptions-item>
      <a-descriptions-item label="变更信息" :span="2">{{ form.targInfo }}</a-descriptions-item>
      <a-descriptions-item label="操作描述" :span="2">{{ form.operateDesc }}</a-descriptions-item>
      <a-descriptions-item label="操作用户编号">{{ form.operateNo }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
    </a-descriptions>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

const operateType = [
  { label: '入库', value: 1 },
  { label: '批量入库', value: 2 },
  { label: '删除', value: 3 },
  { label: '批量删除', value: 4 },
  { label: '修改基础信息', value: 5 },
  { label: '修改营销活动', value: 6 },
  { label: '修改费率信息', value: 7 },
  { label: '回收', value: 8 },
  { label: '批量回收', value: 9 },
  { label: '终端绑定', value: 10 },
  { label: '划拨', value: 11 },
  { label: '回拨', value: 12 },
  { label: '重置密钥', value: 13 },
  { label: '撤机', value: 14 },
  { label: '换绑', value: 15 }
];
export default {
  props: {
    visible: Boolean,
    detail: Object,
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {},
      operateType
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
