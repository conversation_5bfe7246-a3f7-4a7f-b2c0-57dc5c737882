<template>
  <div class="ele-body">
    <!-- 搜索框内容 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="操作终端SN">
              <a-input v-model:value.trim="where.terminalSn" placeholder="请输入终端SN" allow-clear />
            </a-form-item>
            <a-form-item label="操作终端编号">
              <a-input v-model:value.trim="where.terminalNo" placeholder="请输入操作终端编号" allow-clear />
            </a-form-item>
            <a-form-item label="操作类型">
              <a-select v-model:value="where.operateType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option v-for="({ label, value }, key) in operateType" :value="value" :key="key">{{ label }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="操作状态">
              <a-select v-model:value="where.operateStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">成功</a-select-option>
                <a-select-option :value="2">失败</a-select-option>
                <a-select-option :value="3">其他异常</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格内容 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: '1000' }">
          <!-- 表体的操作 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'operateStatus'">
              <a-tag v-if="record.operateStatus === 1" color="success">成功</a-tag>
              <a-tag v-else-if="record.operateStatus === 2" color="error">失败</a-tag>
              <a-tag v-else-if="record.operateStatus === 3" color="pink">其他异常</a-tag>
            </template>
            <template v-else-if="column.key === 'operateType'">
              <template v-for="({ label, value }, key) in operateType" :key="key">
                <a-tag v-if="record.operateType === value">{{ label }}</a-tag>
              </template>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <TerminalOperateDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { TerminalOperateRecordApi } from '@/api/terminal-manage/TerminalOperateRecordApi';
import TerminalOperateDetail from './TerminalOperateDetail.vue';

const operateType = [
  { label: '入库', value: 1 },
  { label: '批量入库', value: 2 },
  { label: '删除', value: 3 },
  { label: '批量删除', value: 4 },
  { label: '修改基础信息', value: 5 },
  { label: '修改营销活动', value: 6 },
  { label: '修改费率信息', value: 7 },
  { label: '回收', value: 8 },
  { label: '批量回收', value: 9 },
  { label: '终端绑定', value: 10 },
  { label: '划拨', value: 11 },
  { label: '回拨', value: 12 },
  { label: '重置密钥', value: 13 },
  { label: '撤机', value: 14 },
  { label: '换绑', value: 15 }
];
export default {
  name: 'TerminalOperateRecord',
  components: {
    TerminalOperateDetail
  },
  data() {
    return {
      //表格查询条件
      where: {},
      //展示编辑页面传的参数
      current: null,
      //是否展示详情页面
      showDetail: false,
      //表格列配置
      operateType,
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center'
        },
        {
          title: '操作类型',
          dataIndex: 'operateType',
          key: 'operateType',
          align: 'center',
          width: 180
        },
        {
          title: '操作终端ID',
          dataIndex: 'terminalId',
          align: 'center',
          width: 180
        },
        {
          title: '操作终端编号',
          dataIndex: 'terminalNo',
          align: 'center',
          width: 180
        },
        {
          title: '操作终端SN',
          dataIndex: 'terminalSn',
          align: 'center',
          width: 180
        },
        {
          title: '操作状态',
          dataIndex: 'operateStatus',
          key: 'operateStatus',
          align: 'center',
          width: 180
        },
        {
          title: '操作描述',
          dataIndex: 'operateDesc',
          width: '200px',
          align: 'center'
        },
        // {
        //   title: '原始信息',
        //   dataIndex: 'origInfo',
        //   align: 'center'
        // },
        // {
        //   title: '变更信息',
        //   dataIndex: 'targInfo',
        //   align: 'center'
        // },
        {
          title: '操作用户编号',
          dataIndex: 'operateNo',
          align: 'center',
          width: 180
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          width: 180
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 100,
          align: 'center'
        }
      ]
    };
  },
  methods: {
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where }) {
      return TerminalOperateRecordApi.findPage({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>
