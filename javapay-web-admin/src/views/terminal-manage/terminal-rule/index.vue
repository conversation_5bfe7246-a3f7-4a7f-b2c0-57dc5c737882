<template>
  <div class="ele-body">
    <!-- 搜索框内容 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="正则表达式">
              <a-input v-model:value.trim="where.regex" placeholder="请输入正则表达式" allow-clear />
            </a-form-item>
            <a-form-item label="终端厂商">
              <a-select v-model:value="where.factoryId" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option v-for="(item, key) in factorys" :key="key" :value="item.id">
                  {{ item.factoryName }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="终端型号">
              <a-select v-model:value="where.modelId" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option v-for="(item, key) in models" :key="key" :value="item.id">
                  {{ item.modelName }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="终端来源">
              <a-select v-model:value="where.source" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">全款机</a-select-option>
                <a-select-option :value="2">分期机</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="有效状态">
              <a-select v-model:value="where.validStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">有效</a-select-option>
                <a-select-option :value="0">无效</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格内容 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 1500 }">
          <!-- 表格上方的操作按钮 -->
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>

          <!-- 表体的操作 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'validStatus'">
              <a-tag v-if="record.validStatus === 1" color="success">有效</a-tag>
              <a-tag v-else>无效</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleEdit(record)">修改</a>
                <a-divider type="vertical" />
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 新增编辑 -->
    <TerminalRuleEdit v-if="showEdit" v-model:visible="showEdit" :data="current" @done="reload" :models="models" :factorys="factorys" />

    <!-- 详情 -->
    <TerminalRuleDetail v-model:visible="showDetail" :detail="current" :models="models" :factorys="factorys" />
  </div>
</template>

<script>
import { TerminalRuleApi } from '@/api/terminal-manage/TerminalRuleApi';
import { TerminalFactoryApi } from '@/api/terminal-manage/TerminalFactoryApi';
import { TerminalModelApi } from '@/api/terminal-manage/TerminalModelApi';
import TerminalRuleDetail from './TerminalRuleDetail.vue';
import TerminalRuleEdit from './TerminalRuleEdit.vue';

export default {
  name: 'TerminalRule',
  components: {
    TerminalRuleEdit,
    TerminalRuleDetail
  },
  data() {
    return {
      //表格查询条件
      where: {},
      //展示编辑页面传的参数
      current: null,
      //是否展示新增或者是编辑页面
      showEdit: false,
      //是否展示详情页面
      showDetail: false,
      models: [],
      factorys: [],
      //表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center'
        },
        {
          title: '正则表达式',
          dataIndex: 'regex',
          ellipsis: true,
          width: 300,
          align: 'center'
        },
        {
          title: '厂商名称',
          dataIndex: 'factoryName',
          align: 'center',
          width: 180
        },
        {
          title: '终端型号',
          dataIndex: 'modelName',
          align: 'center',
          width: 180
        },
        {
          title: '终端来源',
          dataIndex: 'source',
          align: 'center',
          width: 100,
          customRender: ({ text }) => {
            return text === 1 ? '全款机' : text === 2 ? '分期机' : '--';
          }
        },
        {
          title: '有效状态',
          dataIndex: 'validStatus',
          align: 'center',
          width: 100,
          key: 'validStatus'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId',
          width: 200,
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          width: 200,
          align: 'center'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId',
          width: 200,
          align: 'center'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime',
          width: 200,
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 160,
          align: 'center'
        }
      ]
    };
  },
  mounted() {
    this.getFactorys();
    this.getModels();
  },
  methods: {
    async getFactorys() {
      const data = await TerminalFactoryApi.list();
      this.factorys = data || [];
    },

    async getModels() {
      const data = await TerminalModelApi.list();
      this.models = data || [];
    },

    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where }) {
      return TerminalRuleApi.findPage({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>
