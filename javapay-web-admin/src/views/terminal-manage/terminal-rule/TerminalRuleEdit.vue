<template>
  <a-modal
    :width="800"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新增'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules" :label-col="{ style: { width: '150px' } }">
      <a-form-item label="终端厂商" name="factoryId">
        <a-select v-model:value="form.factoryId" placeholder="请选择" @change="form.modelId = null" :disabled="isUpdate">
          <a-select-option v-for="(item, key) in factoryList" :key="key" :value="item.id">
            {{ item.factoryName }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="终端型号" name="modelId">
        <a-select v-model:value="form.modelId" placeholder="请选择" :disabled="isUpdate || !form.factoryId">
          <a-select-option v-for="(item, key) in modelList" :key="key" :value="item.id">
            {{ item.modelName }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="终端来源" name="source">
        <a-select v-model:value="form.source" placeholder="请选择" :disabled="isUpdate">
          <a-select-option :value="1">全款机</a-select-option>
          <a-select-option :value="2">分期机</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="正则表达式" name="regex">
        <a-input v-model:value="form.regex" placeholder="请输入正则表达式" />
      </a-form-item>
      <a-form-item label="有效状态" name="validStatus">
        <a-switch
          v-model:checked="form.validStatus"
          checked-children="有效"
          un-checked-children="无效"
          :checkedValue="1"
          :unCheckedValue="0"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { TerminalRuleApi } from '@/api/terminal-manage/TerminalRuleApi';

export default {
  props: {
    visible: Boolean,
    data: Object,
    factorys: Array,
    models: Array
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {
        source: 1,
        validStatus: 1
      },
      // 表单验证规则
      rules: {
        factoryId: [{ required: true, message: '请选择' }],
        source: [{ required: true, message: '请选择' }],
        regex: [{ required: true, message: '请输入正则表达式' }],
        modelId: [{ required: true, message: '请选择' }],
        validStatus: [{ required: true, message: '请选择' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false
    };
  },
  computed: {
    factoryList() {
      if (this.isUpdate) {
        return this.factorys;
      }
      return this.factorys.filter(i => i.validStatus === 1);
    },
    modelList() {
      if (this.isUpdate) {
        return this.models;
      }
      return this.models.filter(i => i.validStatus === 1 && i.factoryId === this.form.factoryId);
    }
  },
  mounted() {
    if (this.data) {
      this.form = Object.assign({}, this.data);
      this.isUpdate = true;
    } else {
      this.isUpdate = false;
    }
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      // 执行编辑或修改方法
      if (this.isUpdate) {
        result = TerminalRuleApi.edit(this.form);
      } else {
        result = TerminalRuleApi.add(this.form);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
<style lang="less" scoped>
:deep(.ant-divider-horizontal.ant-divider-with-text) {
  margin: 0 0 16px;
}

:deep(.ant-form-item-label) {
  background-color: #f3f5f7;
  text-align: center;
}
</style>
