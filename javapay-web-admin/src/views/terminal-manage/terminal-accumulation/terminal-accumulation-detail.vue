<template>
  <a-modal :width="800" :visible="visible" title="详情" :body-style="{ paddingBottom: '8px' }" @update:visible="updateVisible">
    <a-descriptions :column="2">
      <a-descriptions-item label="终端序列号(SN)">{{ form.terminalSn }}</a-descriptions-item>
      <a-descriptions-item label="累计金额(刷卡交易)">{{ form.cumulateTradeVolume }}</a-descriptions-item>
      <a-descriptions-item label="用户编号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="通道商户编号">{{ form.chnMerchNo }}</a-descriptions-item>
      <a-descriptions-item label="绑定状态">
        <a-tag v-if="form.bindStatus === 1" color="green">已绑定</a-tag>
        <a-tag v-else-if="form.bindStatus === 0">未绑定</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="绑定时间">{{ form.bindTime }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
