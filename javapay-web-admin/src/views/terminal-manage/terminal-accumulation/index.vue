<template>
  <div class="ele-body">
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="通道商户编号">
              <a-input v-model:value.trim="where.chnMerchNo" placeholder="通道商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="用户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="用户编号" allow-clear />
            </a-form-item>
            <a-form-item label="终端序列号(SN)">
              <a-input v-model:value.trim="where.terminalSn" placeholder="终端序列号" allow-clear />
            </a-form-item>
            <a-form-item label="绑定状态">
              <a-select v-model:value="where.bindStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">已绑定</a-select-option>
                <a-select-option :value="0">未绑定</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'bindStatus'">
              <a-tag v-if="record.bindStatus === 1" color="green">已绑定</a-tag>
              <a-tag v-else-if="record.bindStatus === 0">未绑定</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <TerminalAccumulationDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { TerminalAccumulationApi } from '@/api/terminal-manage/TerminalAccumulationApi';
import TerminalAccumulationDetail from './terminal-accumulation-detail.vue';

export default {
  name: 'TerminalAccumulation',
  components: {
    TerminalAccumulationDetail
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          align: 'center',
          width: 80,
          fixed: 'left'
        },
        {
          title: '终端序列号(SN)',
          dataIndex: 'terminalSn',
          align: 'center'
        },
        {
          title: '累计金额(刷卡交易)',
          dataIndex: 'cumulateTradeVolume',
          align: 'center'
        },
        {
          title: '用户编号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '通道商户编号',
          dataIndex: 'chnMerchNo',
          align: 'center'
        },

        {
          title: '绑定状态',
          dataIndex: 'bindStatus',
          key: 'bindStatus',
          align: 'center'
        },
        {
          title: '绑定时间',
          dataIndex: 'bindTime',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 100,
          align: 'center'
        }
      ],
      // 表格搜索条件
      where: {},
      // 当前编辑数据
      current: null,
      // 是否显示编辑弹窗
      showDetail: false
    };
  },
  methods: {
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where, orders }) {
      return TerminalAccumulationApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
