<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="终端序列号(SN)">
              <a-input v-model:value.trim="where.terminalSn" placeholder="请输入序列号(SN)" allow-clear />
            </a-form-item>
            <a-form-item label="用户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入用户编号" allow-clear />
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <!-- table操作栏按钮 -->
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <TerminalTransCumulateDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { TerminalTransCumulateApi } from '@/api/terminal-manage/TerminalTransCumulateApi';
import TerminalTransCumulateDetail from './TerminalTransCumulateDetail.vue';

export default {
  name: 'TerminalTransCumulate',
  components: {
    TerminalTransCumulateDetail
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '终端序列号(SN)',
          dataIndex: 'terminalSn',
          align: 'center'
        },
        {
          title: '用户编号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '支付宝累计金额/元',
          dataIndex: 'alipayCumulate',
          align:'center'
        },
        {
          title: '支付宝累计金额/元(达标)',
          dataIndex: 'standardAlipayCumulate',
          align:'center'
        },
        {
          title: '微信累计金额/元',
          dataIndex: 'wechatCumulate',
          align:'center'
        },
        {
          title: '微信累计金额/元(达标)',
          dataIndex: 'standardWechatCumulate',
          align:'center'
        },
        {
          title: '云闪付累计金额/元',
          dataIndex: 'unionpayCumulate',
          align:'center'
        },
        {
          title: '云闪付累计金额/元(达标)',
          dataIndex: 'standardUnionpayCumulate',
          align:'center'
        },
        {
          title: 'EPOS累计金额/元',
          dataIndex: 'eposCumulate',
          align:'center'
        },
        {
          title: 'EPOS累计金额/元(达标)',
          dataIndex: 'standardEposCumulate',
          align:'center'
        },
        {
          title: '信用卡刷卡累计金额/元',
          dataIndex: 'posCreditCumulate',
          align:'center'
        },
        {
          title: '信用卡刷卡累计金额/元(达标)',
          dataIndex: 'standardPosCreditCumulate',
          align:'center'
        },
        {
          title: '借记卡刷卡累计金额/元',
          dataIndex: 'posDebitCumulate',
          align:'center'
        },
        {
          title: '借记卡刷卡累计金额/元(达标)',
          dataIndex: 'standardPosDebitCumulate',
          align:'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          align: 'center'
        }
      ],
      // 表格搜索条件
      where: {},
      current: null,
      showDetail: false
    };
  },
  methods: {
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where, orders }) {
      return TerminalTransCumulateApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
