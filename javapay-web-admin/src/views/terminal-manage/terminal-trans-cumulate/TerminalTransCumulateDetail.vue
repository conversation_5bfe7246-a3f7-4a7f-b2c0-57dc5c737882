<template>
  <a-modal
    :width="1000"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="终端序列号(SN)">{{ form.terminalSn }}</a-descriptions-item>
      <a-descriptions-item label="用户编号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="支付宝累计金额/元">{{ form.alipayCumulate }}</a-descriptions-item>
      <a-descriptions-item label="支付宝累计金额/元(达标)">{{ form.standardAlipayCumulate }}</a-descriptions-item>
      <a-descriptions-item label="微信累计金额/元">{{ form.wechatCumulate }}</a-descriptions-item>
      <a-descriptions-item label="微信累计金额/元(达标)">{{ form.standardWechatCumulate }}</a-descriptions-item>
      <a-descriptions-item label="云闪付累计金额/元">{{ form.unionpayCumulate }}</a-descriptions-item>
      <a-descriptions-item label="云闪付累计金额/元(达标)">{{ form.standardUnionpayCumulate }}</a-descriptions-item>
      <a-descriptions-item label="EPOS累计金额/元">{{ form.eposCumulate }}</a-descriptions-item>
      <a-descriptions-item label="EPOS累计金额/元(达标)">{{ form.standardEposCumulate }}</a-descriptions-item>
      <a-descriptions-item label="信用卡刷卡累计金额/元">{{ form.posCreditCumulate }}</a-descriptions-item>
      <a-descriptions-item label="信用卡刷卡累计金额/元(达标)">{{ form.standardPosCreditCumulate }}</a-descriptions-item>
      <a-descriptions-item label="借记卡刷卡累计金额/元">{{ form.posDebitCumulate }}</a-descriptions-item>
      <a-descriptions-item label="借记卡刷卡累计金额/元(达标)">{{ form.standardPosDebitCumulate }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
