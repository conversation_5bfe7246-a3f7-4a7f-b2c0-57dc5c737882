<template>
  <a-modal
    :width="800"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="所属大区编号" v-if="hasPurview(['0'])">{{ form.regionNo }}</a-descriptions-item>
      <a-descriptions-item label="所属运营中心编号" v-if="hasPurview(['0', '1'])"> {{ form.branchNo }}</a-descriptions-item>
      <a-descriptions-item label="所属一代代理商编号" v-if="hasPurview(['0', '1', '2'])">{{ form.oneLevelAgentNo }}</a-descriptions-item>
      <a-descriptions-item label="所属代理商编号">{{ form.agentNo }}</a-descriptions-item>
      <a-descriptions-item label="终端SN号">{{ form.terminalSn }}</a-descriptions-item>
      <a-descriptions-item label="终端使用状态">
        <span v-if="form.useStatus === 0" class="pink">闲置</span>
        <span v-else-if="form.useStatus === 1" class="blue">已分配</span>
        <span v-else-if="form.useStatus === 2" class="puple">已绑定</span>
      </a-descriptions-item>
      <a-descriptions-item label="商户号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="通道名称">{{ form.channelName }}</a-descriptions-item>



      <a-descriptions-item label="未激活扣款开关">
        <a-tag v-if="form.inactiveSwitch === 0" color="pink">关闭</a-tag>
        <a-tag v-else-if="form.inactiveSwitch === 1" color="blue">开启</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="未激活状态">
        <a-tag v-if="form.inactiveStatus === 0" color="pink">初始</a-tag>
        <a-tag v-else-if="form.inactiveStatus === 1" color="blue">已扣款</a-tag>
        <a-tag v-else-if="form.inactiveStatus === 2" color="purple">不再扣款</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="未激活配置ID">{{ form.inactiveConfId }}</a-descriptions-item>
      <a-descriptions-item label="未激活扣款金额(元)'">{{ form.inactiveDeductAmount }}</a-descriptions-item>
      <a-descriptions-item label="未激活周期-开始时间">{{ form.inactiveBeginTime }}</a-descriptions-item>
      <a-descriptions-item label="未激活周期-结束时间">{{ form.inactiveEndTime }}</a-descriptions-item>

      <a-descriptions-item label="伪激活扣款开关">
        <a-tag v-if="form.pseudoActiveSwitch === 0" color="pink">关闭</a-tag>
        <a-tag v-else-if="form.pseudoActiveSwitch === 1" color="blue">开启</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="伪激活状态">
        <a-tag v-if="form.pseudoActiveStatus === 0" color="pink">初始</a-tag>
        <a-tag v-else-if="form.pseudoActiveStatus === 1" color="blue">已扣款</a-tag>
        <a-tag v-else-if="form.pseudoActiveStatus === 2" color="purple">不再扣款</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="伪激活配置ID">{{ form.pseudoActiveConfId }}</a-descriptions-item>
      <a-descriptions-item label="伪激活扣款金额(元)">{{ form.pseudoActiveDeductAmount }}</a-descriptions-item>
      <a-descriptions-item label="伪激活标准金额(元)">{{ form.pseudoActiveTradeVolume }}</a-descriptions-item>
      <a-descriptions-item label="伪激活周期-开始时间">{{ form.pseudoActiveBeginTime }}</a-descriptions-item>
      <a-descriptions-item label="伪激活周期-结束时间">{{ form.pseudoActiveEndTime }}</a-descriptions-item>

      <a-descriptions-item label="激活奖励开关">
        <a-tag v-if="form.activeSwitch === 0" color="pink">关闭</a-tag>
        <a-tag v-else-if="form.activeSwitch === 1" color="blue">开启</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="激活奖励政策编号">{{ form.activePolicyNo }}</a-descriptions-item>
      <a-descriptions-item label="激活状态">
        <a-tag v-if="form.activeStatus === 0" color="pink">初始</a-tag>
        <a-tag v-else-if="form.activeStatus === 1" color="blue">达标</a-tag>
        <a-tag v-else-if="form.activeStatus === 2" color="purple">不再达标</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="达标奖励开关">
        <a-tag v-if="form.cashbackSwitch === 0" color="pink">关闭</a-tag>
        <a-tag v-else-if="form.cashbackSwitch === 1" color="blue">开启</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="达标奖励政策编号">{{ form.cashbackPolicyNo }}</a-descriptions-item>
      <a-descriptions-item label="达标计算方式">
        <a-tag v-if="form.cashCalculateType === 0" color="pink">交易量优先</a-tag>
        <a-tag v-else-if="form.cashCalculateType === 1" color="blue">时间优先</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="达标奖励交易类型">{{ form.payMethodsStr }}</a-descriptions-item>

      <a-descriptions-item label="创建时间">{{ toDateString(form.createTime) }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ toDateString(form.lastModifyTime) }}</a-descriptions-item>
    </a-descriptions>

    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';
import { toDateString } from 'ele-admin-pro';
import { ServiceFeePolicyApi } from '@/api/businessTeam/activity-Config/ServiceFeePolicyApi';
import { SimFeePolicyApi } from '@/api/businessTeam/activity-Config/SimFeePolicyApi';
import { hasPurview } from '@/utils/permission';

export default {
  name: 'TerminalInfoDetail',
  props: {
    visible: Boolean,
    detail: Object,
    channelCodes: Array
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {},
      serviceFeePolicyList: [],
      simFeePolicyList: []
    });

    const watch = watchEffect(() => {
      getServiceFeePolicyList();
      getSimFeePolicyList();
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
        const channelItem = props.channelCodes.find(m => m.channelCode === data.form.channelCode);
        channelItem && (data.form.channelName = channelItem.channelName);
      }
    });

    //获取服务费政策列表
    async function getServiceFeePolicyList() {
      const result = await ServiceFeePolicyApi.list({ orgNo: props.detail.agentNo, orgType: 3 });
      data.serviceFeePolicyList = result || [];

      const serviceFeeItem = data.serviceFeePolicyList.find(m => m.configId === data.form.serviceFeeId);
      serviceFeeItem && (data.form.serviceFeePolicyName = serviceFeeItem.policyName);
    }

    //获取流量费政策列表
    async function getSimFeePolicyList() {
      const result = await SimFeePolicyApi.list({ orgNo: props.detail.agentNo, orgType: 3 });
      data.simFeePolicyList = result || [];

      const simFeeItem = data.simFeePolicyList.find(m => m.configId === data.form.simFeeId);
      simFeeItem && (data.form.simFeePolicyName = simFeeItem.policyName);
    }

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible,
      toDateString,
      hasPurview
    };
  }
};
</script>

<style></style>
