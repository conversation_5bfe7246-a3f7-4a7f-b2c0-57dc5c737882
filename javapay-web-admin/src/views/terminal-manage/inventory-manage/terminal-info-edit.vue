<template>
  <a-modal
    :width="900"
    :visible="visible"
    :confirm-loading="loading"
    title="编辑"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @cancel="cancel"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 7 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 17 }, sm: { span: 24 } }"
    >
      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="设备厂商" name="factoryId">
            <a-select
              v-model:value="form.factoryId"
              placeholder="请选择"
              :disabled="!(form.useStatus === 1 || form.useStatus === 2)"
              allow-clear
            >
              <a-select-option v-for="(item, key) in factoryList" :key="key" :value="item.id">{{ item.factoryName }}</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="支付机构" name="channelCode">
            <a-select
              v-model:value="form.channelCode"
              placeholder="请选择"
              @change="channelSelectChanged"
              :disabled="!(form.useStatus === 1)"
              allow-clear
            >
              <a-select-option v-for="(item, key) in channelCodes" :key="key" :value="item.channelCode">{{
                item.channelName
              }}</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="终端序列号" name="terminalSn">
            <a-input v-model:value.trim="form.terminalSn" placeholder="请输入终端序列号" :disabled="true" allow-clear />
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="设备型号" name="modelId">
            <a-select
              v-model:value="form.modelId"
              placeholder="请选择"
              :disabled="!(form.useStatus === 1 || form.useStatus === 2)"
              allow-clear
            >
              <a-select-option v-for="(item, key) in terminalModelList" :key="key" :value="item.id">{{ item.modelName }}</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="代理商编号" name="agentNo">
            <a-input
              v-model:value="form.agentNo"
              placeholder="请选择"
              @click="agentSelectMethod"
              :readonly="true"
              :disabled="!(form.channelCode && form.useStatus === 1)"
              allow-clear
            />
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="押金" name="serviceFeeId">
            <a-select
              v-model:value="form.serviceFeeId"
              placeholder="请选择"
              :disabled="!(form.useStatus === 1 || form.useStatus === 2)"
              allow-clear
            >
              <a-select-option v-for="(item, key) in serviceFeeList" :key="key" :value="item.id">{{ item.serviceFee }}</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="流量费" name="simFeeId">
            <a-select
              v-model:value="form.simFeeId"
              placeholder="请选择"
              :disabled="!(form.useStatus === 1 || form.useStatus === 2)"
              allow-clear
            >
              <a-select-option v-for="(item, key) in simFeeList" :key="key" :value="item.id">{{ item.simFee }}</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="终端来源" name="terminalSource">
            <a-select v-model:value="form.terminalSource" placeholder="请选择">
              <a-select-option :value="1">全款机</a-select-option>
              <a-select-option :value="2">分期机</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="流量费收取开关" name="simSwitch">
            <a-radio-group v-model:value="form.simSwitch" name="simSwitch" :disabled="!(form.useStatus === 1 || form.useStatus === 2)">
              <a-radio :value="1">开启</a-radio>
              <a-radio :value="0">关闭</a-radio>
            </a-radio-group>
          </a-form-item>

          <a-form-item label="免费使用天数" name="simFreeDay">
            <a-input
              v-model:value.trim="form.simFreeDay"
              placeholder="请输入免费使用天数"
              :disabled="!(form.useStatus === 1 || form.useStatus === 2)"
              allow-clear
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-divider orientationMargin="0" orientation="left" dashed>流量费返现政策</a-divider>
      <a-row>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="商户结算政策" name="policyId">
            <a-select
              v-model:value="form.policyId"
              placeholder="请选择"
              @change="ratePolicyValueChanged($event)"
              allow-clear
              :disabled="!(form.channelCode != null && form.agentNo != null && form.useStatus === 1)"
            >
              <a-select-option v-for="(item, key) in rateAgentList" :key="key" :value="item.id">{{ item.policyDesc }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <a-form
        ref="rateForm"
        :model="rateInfoDTO"
        :label-col="{ md: { span: 17 }, sm: { span: 24 } }"
        :wrapper-col="{ md: { span: 7 }, sm: { span: 24 } }"
      >
        <!-- 费率相关 -->
        <RateModule :rate-item="rateInfoDTO" :bankList="[]" :disabled="!(form.useStatus === 1)" />
      </a-form>
    </a-form>
  </a-modal>

  <!-- 代理商选择弹窗 -->
  <AgentSelect v-if="showAgentList" v-model:visible="showAgentList" @done="setAgentInfo" />
</template>

<script>
import { InventoryManageApi } from '@/api/terminal-manage/InventoryManageApi';
import { message } from 'ant-design-vue';
import AgentSelect from '../../qrcodeCard/batch-manage/agent-select.vue';
import { RateTemplateApi } from '@/api/businessTeam/rate-template/RateTemplateApi';
import { RatePolicyApi } from '@/api/businessTeam/rate-policy/RatePolicyApi';
import { RateApi } from '@/api/businessTeam/rate/RateApi';
import RateModule from '../../business-team/_components/RateModule.vue';
import { hasPurview } from '@/utils/permission';
import { omit } from 'lodash-es';

function formDefaults() {
  return {
    simSwitch: 1,
    channelCode: null
  };
}

export default {
  name: 'TerminalInfoEdit',
  components: {
    AgentSelect,
    RateModule
  },
  props: {
    visible: Boolean,
    data: Object,
    factoryList: Array,
    terminalModelList: Array,
    channelCodes: Array,
    agentList: Array,
    serviceFeeList: Array,
    simFeeList: Array,
    policyList: Array
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: formDefaults(),
      // 表单验证规则
      rules: {
        factoryId: [{ required: true, message: '请选择' }],
        modelId: [{ required: true, message: '请选择' }],
        channelCode: [{ required: true, message: '请选择' }],
        agentNo: [{ required: true, message: '请选择' }],
        terminalSn: [{ required: true, message: '请输入开始序列号' }],
        serviceFeeId: [{ required: true, message: '请选择' }],
        simSwitch: [{ required: true, message: '请选择' }],
        simFeeId: [{ required: true, message: '请选择' }],
        simFreeDay: [{ required: true, message: '请输入免费使用天数' }],
        terminalSource: [{ required: true, message: '请选择' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false,
      //是否展示代理商选择页面
      showAgentList: false,
      //模版费率
      rateTemplateList: [],
      //代理上费率
      rateAgentList: [],
      //费率数据
      rateInfoDTO: {},
      //选择的代理商模版编号
      templateNo: '',
      hasPurview
    };
  },
  watch: {
    data() {
      if (this.data) {
        this.form = Object.assign(formDefaults(), this.data);
        this.rateInfoDTO = JSON.parse(this.form.rateInfo);
        this.isUpdate = true;

        //请求一下对应的费率政策列表
        this.getRateAgentList();
        //请求一下 获取代理商信息,取得模版编号
        this.getRateInfo();
      }
      if (this.$refs.form) {
        this.$refs.form.clearValidate();
      }
      //代理商登录的,默认显示代理商编号
      if (hasPurview('3')) {
        this.form.agentNo = localStorage.getItem('SASS_ORG_CODE');
      }
    }
  },
  methods: {
    //代理商选择点击
    agentSelectMethod() {
      this.showAgentList = true;
    },

    //从代理商列表返回，配置相关数据
    setAgentInfo(info) {
      this.showAgentList = false;
      this.form.agentNo = info.agentNo;

      //需要清空押金活动和流量费活动还有结算政策
      this.form.serviceFeeId = null;
      this.form.simFeeId = null;
      //选择过费率政策的才需要清空,默认模版的不需要清空
      if (!(this.form.policyId === undefined || this.form.policyId === null)) {
        this.form.policyId = null;
        this.rateInfoDTO = {};
        this.form.rateInfoDTO = {};
      }

      //选择完代理商后,请求一下对应的费率政策列表
      this.getRateAgentList();
      //选择完代理商后,请求一下 获取代理商信息,取得模版编号
      this.getRateInfo();
    },

    //获取代理商费率信息
    async getRateInfo() {
      const data = await RateApi.rateDetail({ channelCode: this.form.channelCode, userNo: this.form.agentNo, rateType: 1 });
      this.templateNo = data.templateNo || '';
    },

    //通道选择
    channelSelectChanged() {
      this.getRateTemplateList();
      //因为代理商的进来,默认显示代理商编号了,所以这里要请求一下
      if (hasPurview('3')) {
        if (this.form.policyId) {
          this.form.policyId = null;
          this.rateInfoDTO = {};
          this.form.rateInfoDTO = {};
        }

        //选择完后,请求一下对应的费率政策列表
        this.getRateAgentList();
        //选择完后,请求一下 获取代理商信息,取得模版编号
        this.getRateInfo();
      }
    },

    //费率政策选值了
    ratePolicyValueChanged(value) {
      const selectedPolicy = this.rateAgentList.find(item => item.id === value);
      this.rateInfoDTO = selectedPolicy.rateDTOList[0];
    },

    //模版费率列表
    async getRateTemplateList() {
      const data = await RateTemplateApi.list({ validStatus: 1, templateType: 2, channelCode: this.form.channelCode, rateType: 1 });
      this.rateTemplateList = data || [];
      this.rateInfoDTO = this.rateTemplateList[0];
    },

    //代理商费率列表
    async getRateAgentList() {
      const data = await RatePolicyApi.list({
        validStatus: 1,
        channelCode: this.form.channelCode,
        rateType: 1,
        userNo: this.form.agentNo,
        userType: 3
      });
      this.rateAgentList = data || [];
    },

    async save() {
      // 校验表单
      await this.$refs.form.validate();
      await this.$refs.rateForm.validate();

      // 修改加载框为正在加载
      this.loading = true;

      this.rateInfoDTO.templateNo = this.templateNo;
      this.rateInfoDTO.isSame = 1;
      this.form.rateInfo = this.rateInfoDTO;

      if (this.form.simSwitch !== 1) {
        this.form = omit(this.form, ['simFeeId', 'simFreeDay']);
      }

      // 执行编辑方法
      let result = InventoryManageApi.singleEdit(this.form);

      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 如果是新增，则form表单置空
          if (!this.isUpdate) {
            this.form = formDefaults();
          }

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    cancel() {
      this.form = Object.assign(formDefaults(), this.data);
      this.$refs.form.clearValidate();
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
