<template>
  <div class="ele-body">
    <!-- 搜索框内容 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="终端编号">
              <a-input v-model:value.trim="where.terminalNo" placeholder="请输入终端编号" allow-clear />
            </a-form-item>

            <a-form-item label="终端SN序列号">
              <a-input v-model:value.trim="where.terminalSn" placeholder="请输入终端SN序列号" allow-clear />
            </a-form-item>

            <a-form-item label="商户号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户号" allow-clear />
            </a-form-item>

            <a-form-item label="渠道商户号">
              <a-input v-model:value.trim="where.chnMerchNo" placeholder="请输入渠道商户号" allow-clear />
            </a-form-item>

            <a-form-item label="所属代理商编号">
              <a-input v-model:value.trim="where.agentNo" placeholder="请输入所属代理商编号" allow-clear />
            </a-form-item>

            <a-form-item label="所属一代代理商编号">
              <a-input v-model:value.trim="where.oneLevelAgentNo" placeholder="请输入所属一代代理商编号" allow-clear />
            </a-form-item>

            <a-form-item label="设备厂商">
              <a-select v-model:value="where.factoryId" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="item.id" v-for="item in factoryList" :key="item.id">{{ item.factoryName }}</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="设备型号">
              <a-select v-model:value="where.modelId" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="item.id" v-for="item in terminalModelList" :key="item.id">{{ item.modelName }}</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="支付通道">
              <a-select
                v-model:value="where.channelCode"
                style="width: 200px"
                placeholder="请选择"
                allow-clear
                @change="where.policyId = null"
              >
                <a-select-option :value="item.channelCode" v-for="item in channelCodes" :key="item.id">{{
                  item.channelName
                }}</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="商户结算政策" v-purview="['2', '3', '5']">
              <a-select
                v-model:value="where.policyId"
                placeholder="请选择"
                allow-clear
                :disabled="!where.channelCode"
                style="width: 200px"
                @dropdownVisibleChange="open => open && getRateMerchTypeList()"
              >
                <a-select-option v-for="(item, key) in merchRatePolicyList" :key="key" :value="item.id">{{
                  item.policyDesc
                }}</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="终端使用状态">
              <a-select v-model:value="where.useStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">闲置</a-select-option>
                <a-select-option :value="1">已分配</a-select-option>
                <a-select-option :value="2">已绑定</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="是否活动终端">
              <a-select v-model:value="where.termActiveSwitch" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">是</a-select-option>
                <a-select-option :value="0">否</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
                <a-button type="dashed" @click="handleDownloadMerchantRateExcel">查询并导出</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格内容 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table
          ref="table"
          row-key="id"
          :datasource="datasource"
          :columns="columns"
          :where="where"
          :scroll="{ x: 'max-content' }"
          v-model:selection="selection"
        >
          <!-- 表格上方的操作按钮 -->
          <template #toolbar>
            <a-row wrap class="toolbar">
              <a-button type="primary" @click="addTerminalToLibraryNumberType()" v-if="hasPurview(['1', '3'])">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>终端入库</span>
              </a-button>
              <a-button type="primary" @click="addTerminalToLibraryFileType()" v-if="hasPurview(['1', '3'])">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>批量入库</span>
              </a-button>

              <a-button type="danger" v-if="hasPurview(['0', '1'])" @click="showTerminalDeleteByFile = true">
                <span>批量删除终端</span>
              </a-button>

              <a-dropdown>
                <template #overlay>
                  <a-menu @click="({ key }) => handleTerminalTransferActivity(key, false)">
                    <a-menu-item key="number">区间下发</a-menu-item>
                    <a-menu-item key="file">文件下发</a-menu-item>
                  </a-menu>
                </template>
                <a-button v-if="hasPurview(['2'])" type="primary">
                  <span>终端下发</span>
                  <DownOutlined />
                </a-button>
              </a-dropdown>
              <a-dropdown>
                <template #overlay>
                  <a-menu @click="({ key }) => handleTerminalTransferActivity(key, true)">
                    <a-menu-item key="number">区间下发</a-menu-item>
                    <a-menu-item key="file">文件下发</a-menu-item>
                  </a-menu>
                </template>
                <a-button v-if="hasPurview(['2'])" type="primary">
                  <span>活动终端下发</span>
                  <DownOutlined />
                </a-button>
              </a-dropdown>

              <a-dropdown>
                <template #overlay>
                  <a-menu @click.stop>
                    <a-menu-item @click="terminalTransfer">区间下发</a-menu-item>
                    <a-menu-item @click="terminalTransferByfile">文件下发</a-menu-item>
                  </a-menu>
                </template>
                <a-button v-if="hasPurview(['3', '5'])" type="primary">
                  <span>终端下发</span>
                  <DownOutlined />
                </a-button>
              </a-dropdown>
              <a-dropdown>
                <template #overlay>
                  <a-menu @click="({ key }) => handleTerminalTransferActivity(key, true)">
                    <a-menu-item key="number">区间下发</a-menu-item>
                    <a-menu-item key="file">文件下发</a-menu-item>
                  </a-menu>
                </template>
                <a-button v-if="hasPurview(['3', '5'])" type="primary">
                  <span>活动终端下发</span>
                  <DownOutlined />
                </a-button>
              </a-dropdown>

              <a-button type="primary" @click="terminalCallback()" v-if="hasPurview(['2', '3', '5'])">
                <span>终端回拨</span>
              </a-button>
              <a-button type="primary" @click="terminalCallbackByFile()" v-if="hasPurview(['2', '3', '5'])">
                <span>终端文件回拨</span>
              </a-button>

              <a-button type="primary" @click="handleTerminalTransferToTarget" v-if="hasPurview(['0'])">
                <span>终端调拨</span>
              </a-button>

              <a-button type="primary" @click="showTerminalTransferToTargetByfile = true" v-if="hasPurview(['0'])">
                <span>终端文件调拨</span>
              </a-button>

              <a-button type="primary" @click="handleTerminalChangeBind">
                <span>终端换绑</span>
              </a-button>

              <a-button type="primary" @click="handleWeaning">
                <span>SN解绑</span>
              </a-button>

              <a-button v-if="hasPurview('0')" type="primary" @click="handleAdminUbind">
                <span>终端解绑(平台内部)</span>
              </a-button>

              <a-button type="primary" @click="handleChangeMerchPolicy" v-if="hasPurview(['3', '5'])">
                <span>变更终端商户费率</span>
              </a-button>

              <a-button type="primary" @click="handleBatchChangeMerchPolicy" v-if="hasPurview(['3', '5'])">
                <span>批量变更商户费率</span>
              </a-button>

              <a-button type="primary" @click="showBatchUpdateTerminalActiveConf = true" v-if="hasPurview(['1', '3'])">
                <span>批量变更营销活动配置</span>
              </a-button>
            </a-row>
          </template>

          <!-- 表体的操作 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'validStatus'">
              <a-switch :checked="record.validStatus === 1" @change="checked => editValidStatus(checked, record)" />
            </template>

            <template v-else-if="column.key === 'terminalSource'">
              <a-tag v-if="record.terminalSource === 1" color="pink">全款机</a-tag>
              <a-tag v-else-if="record.terminalSource === 2" color="blue">分期机</a-tag>
            </template>

            <template v-else-if="column.key === 'termActiveSwitch'">
              <a-tag v-if="record.termActiveSwitch === 1" color="green">是</a-tag>
              <a-tag v-else-if="record.termActiveSwitch === 0">否</a-tag>
            </template>

            <template v-else-if="column.key === 'useStatus'">
              <a-tag v-if="record.useStatus === 0" color="pink">闲置</a-tag>
              <a-tag v-else-if="record.useStatus === 1" color="blue">已分配</a-tag>
              <a-tag v-else-if="record.useStatus === 2" color="purple">已绑定</a-tag>
            </template>

            <template v-else-if="column.key === 'simSwitch'">
              <a-tag v-if="record.simSwitch === 0" color="pink">关闭</a-tag>
              <a-tag v-else-if="record.simSwitch === 1" color="blue">开启</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <!-- 闲置可以修改费率信息 -->
                <template v-if="hasPurview(['0', '1']) && (record.useStatus === 0 || record.useStatus === 1)">
                  <a @click="handleBaseInfoEdit(record)">修改基本信息</a>
                </template>

                <!-- 闲置可以修改营销活动信息 -->
                <template
                  v-if="
                    (record.useStatus === 0 || record.useStatus === 1) &&
                      (hasPurview(['1']) || (hasPurview(['3']) && record.termActiveSwitch === 0 && record.terminalSource === 1))
                  "
                >
                  <a @click="handleActivityInfoEdit(record)">修改营销活动信息</a>
                </template>

                <!-- 闲置可以修改基本信息 -->
                <!-- <template v-if="record.useStatus === 0 && hasPurview(['3', '5'])">
                  <a @click="handleRateInfoEdit(record)">修改费率信息</a>
                </template> -->

                <a-divider type="vertical" />
                <a @click="handleShowDetail(record)">详情</a>
                <!-- 闲置的才能删除 -->
                <template v-if="hasPurview(['0', '1']) && record.useStatus === 0">
                  <a-divider type="vertical" />
                  <a-popconfirm title="确定要删除此记录吗？" @confirm="singleRemove(record)">
                    <a class="ele-text-danger">删除</a>
                  </a-popconfirm>
                </template>
                <!-- 已分配的才能回收 -->
                <template v-if="record.useStatus === 1">
                  <a-divider type="vertical" />
                  <a-popconfirm title="确定要回收次终端吗？" @confirm="handleSingleRecycle(record)">
                    <a>回收</a>
                  </a-popconfirm>
                </template>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 单号/连号入库 -->
    <TerminalAddLibrary
      v-model:visible="showAddLibraryNumberType"
      v-if="showAddLibraryNumberType"
      :handle-type="addLibraryHandleType"
      :factory-list="factoryList"
      :channel-codes="channelCodes"
      @done="reload"
    />

    <!-- 活动终端入库 -->
    <TerminalAddLibraryActivity
      v-model:visible="showAddLibraryActivity"
      v-if="showAddLibraryActivity"
      :handle-type="addLibraryHandleType"
      :factory-list="factoryList"
      :channel-codes="channelCodes"
      @done="reload"
    />

    <!-- 终端下发 -->
    <TerminalTransfer
      v-model:visible="showTerminalTransfer"
      v-if="showTerminalTransfer"
      :data="current"
      :channel-codes="channelCodes"
      @done="reload"
    />

    <!-- 活动终端下发 -->
    <TerminalTransferActivity
      v-if="showTerminalTransferActivity"
      v-model:visible="showTerminalTransferActivity"
      :handle-type="transferHandleType"
      :channel-codes="channelCodes"
      :isActiveTer="isActiveTer"
      @done="reload"
    />

    <!-- 终端回拨 -->
    <TerminalCallback v-model:visible="showTerminalCallback" :data="current" @done="reload" :channel-codes="channelCodes" />

    <!-- 基本信息修改 -->
    <TerminalBaseInfoEdit
      v-model:visible="showBaseInfoEdit"
      :data="current"
      :factory-list="factoryList"
      :channel-codes="channelCodes"
      @done="reload"
    />

    <!-- 修改活动信息 -->
    <TerminalActivityInfoEdit v-if="showActivityInfoEdit" v-model:visible="showActivityInfoEdit" :data="current" @done="reload" />

    <!-- 修改费率信息 -->
    <TerminalRateInfoEdit
      v-model:visible="showRateInfoEdit"
      :data="current"
      @done="reload"
      v-if="showRateInfoEdit"
      :channel-codes="channelCodes"
    />

    <!-- 详情页面 -->
    <TerminalInfoDetail v-model:visible="showDetail" :channel-codes="channelCodes" :detail="current" v-if="showDetail" />

    <!-- 变更终端商户费率政策 -->
    <TerminalMerchRatePolicyEdit v-if="showChangeMerchPolicy" v-model:visible="showChangeMerchPolicy" :data="detail" @done="reload" />

    <TerminalTransferToTarget
      v-if="showTerminalTransferToTarget"
      v-model:visible="showTerminalTransferToTarget"
      :data="detail"
      @done="reload"
    />

    <TerminalTransferToTargetByFile
      v-if="showTerminalTransferToTargetByfile"
      v-model:visible="showTerminalTransferToTargetByfile"
      :channelCodes="channelCodes"
      @done="reload"
    />

    <TerminalModifyRateByFile
      v-if="showBatchChangeMerchPolicy"
      v-model:visible="showBatchChangeMerchPolicy"
      :channelCodes="channelCodes"
      @done="reload"
    />

    <TerminalAgentCallBackToTargetByFile
      v-if="showTerminalCallbackByFile"
      v-model:visible="showTerminalCallbackByFile"
      :channelCodes="channelCodes"
      @done="reload"
    />

    <TerminalAgentTransferToTargetByFile
      v-if="showTerminalTransferByFile"
      v-model:visible="showTerminalTransferByFile"
      :channelCodes="channelCodes"
      @done="reload"
    />

    <TerminalDeleteByFile
      v-if="showTerminalDeleteByFile"
      v-model:visible="showTerminalDeleteByFile"
      @done="reload"
      :channelCodes="channelCodes"
    />

    <TerminalChangeBind v-if="showTerminalChangeBind" v-model:visible="showTerminalChangeBind" :data="detail" @done="reload" />

    <BatchUpdateTerminalActiveConf
      v-if="showBatchUpdateTerminalActiveConf"
      v-model:visible="showBatchUpdateTerminalActiveConf"
      :channelCodes="channelCodes"
      @done="reload"
    />
  </div>
</template>

<script>
import { InventoryManageApi } from '@/api/terminal-manage/InventoryManageApi';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import { TerminalFactoryApi } from '@/api/qrcodeCard/TerminalFactoryApi';
import { TerminalModelApi } from '@/api/qrcodeCard/TerminalModelApi';
import { message, Modal } from 'ant-design-vue';
import TerminalAddLibraryActivity from './terminal-add-library-activity.vue';
import TerminalAddLibrary from './terminal-add-library.vue';
import TerminalInfoDetail from './terminal-info-detail.vue';
import TerminalBaseInfoEdit from './terminal-base-info-edit.vue';
import TerminalActivityInfoEdit from './terminal-activity-info-edit.vue';
import TerminalRateInfoEdit from './terminal-rate-info-edit.vue';
import TerminalTransfer from './terminal-transfer.vue';
import TerminalTransferActivity from './modules/terminal-transfer-activity.vue';
import TerminalMerchRatePolicyEdit from './modules/TerminalMerchRatePolicyEdit.vue';
import TerminalTransferToTarget from './modules/TerminalTransferToTarget.vue';
import TerminalTransferToTargetByFile from './modules/TerminalTransferToTargetByFile.vue';
import TerminalChangeBind from './modules/TerminalChangeBind.vue';
import TerminalModifyRateByFile from './modules/TerminalModifyRateByFile.vue';
import TerminalAgentCallBackToTargetByFile from './modules/TerminalAgentCallBackToTargetByFile.vue';
import TerminalAgentTransferToTargetByFile from './modules/TerminalAgentTransferToTargetByFile.vue';
import TerminalDeleteByFile from './modules/TerminalDeleteByFile.vue';
import BatchUpdateTerminalActiveConf from './modules/batch-update-terminal-active-conf.vue';
import { hasPurview } from '@/utils/permission';
import TerminalCallback from './terminal-callback.vue';
import { ServiceFeePolicyApi } from '@/api/businessTeam/activity-Config/ServiceFeePolicyApi';
import { SimFeePolicyApi } from '@/api/businessTeam/activity-Config/SimFeePolicyApi';
import { createVNode } from 'vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { RatePolicyApi } from '@/api/businessTeam/rate-policy/RatePolicyApi';
import { useUserStore } from '@/store/modules/user';
import { RegionManageApi } from '@/api/businessTeam/region-center/RegionManageApi';

export default {
  name: 'InbentoryManage',
  components: {
    TerminalAddLibrary,
    TerminalInfoDetail,
    TerminalBaseInfoEdit,
    TerminalActivityInfoEdit,
    TerminalRateInfoEdit,
    TerminalTransfer,
    TerminalTransferActivity,
    TerminalCallback,
    TerminalMerchRatePolicyEdit,
    TerminalTransferToTarget,
    TerminalChangeBind,
    TerminalTransferToTargetByFile,
    TerminalAgentCallBackToTargetByFile,
    TerminalAgentTransferToTargetByFile,
    TerminalModifyRateByFile,
    TerminalDeleteByFile,
    TerminalAddLibraryActivity,
    BatchUpdateTerminalActiveConf
  },
  data() {
    return {
      //表格查询条件
      where: {},
      //展示编辑页面传的参数
      current: null,
      //是否展示基础信息编辑页面
      showBaseInfoEdit: false,
      //是否展示营销活动信息编辑页面
      showActivityInfoEdit: false,
      //是否展示费率信息编辑页面
      showRateInfoEdit: false,
      //是否展示详情页面
      showDetail: false,
      //是否展示单号/连号入库
      showAddLibraryNumberType: false,
      showAddLibraryActivity: false,
      //是否展示终端下发
      showTerminalTransfer: false,
      transferHandleType: 'number',
      isActiveTer: false,
      showTerminalTransferActivity: false,
      //是否展示终端回拨
      showTerminalCallback: false,

      showChangeMerchPolicy: false,
      showTerminalTransferToTarget: false,
      showTerminalTransferToTargetByfile: false,
      showTerminalChangeBind: false,
      showTerminalTransferByFile: false,
      showTerminalCallbackByFile: false,
      showBatchChangeMerchPolicy: false,
      showTerminalDeleteByFile: false,
      showBatchUpdateTerminalActiveConf: false,

      selection: [],
      detail: null,

      //服务费政策数组
      serviceFeePolicyList: [],
      //流量费政策数组
      simFeePolicyList: [],
      //支付通道
      channelCodes: [],

      //厂商列表
      factoryList: [],
      //设备型号列表
      terminalModelList: [],
      merchRatePolicyList: [],
      //操作类型
      addLibraryHandleType: 'number', //number 默认数字类型 ,file为文件
      hasPurview,

      showRegionWarehouse: true,
      //表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center'
        },
        {
          title: '所属代理商编号',
          dataIndex: 'agentNo',
          align: 'center'
        },
        {
          title: '所属一级代理商编号',
          dataIndex: 'oneLevelAgentNo',
          align: 'center'
        },
        {
          title: '通道名称',
          dataIndex: 'channelCode',
          align: 'center',
          customRender: ({ text }) => {
            const item = this.channelCodes.find(c => c.channelCode === text);
            return item?.channelName || '--';
          }
        },
        {
          title: '终端编号',
          dataIndex: 'terminalNo',
          align: 'center'
        },
        {
          title: '终端SN序列号',
          dataIndex: 'terminalSn',
          align: 'center'
        },
        {
          title: '终端使用状态',
          dataIndex: 'useStatus',
          align: 'center',
          key: 'useStatus'
        },
        {
          title: '是否活动终端',
          dataIndex: 'termActiveSwitch',
          align: 'center',
          key: 'termActiveSwitch'
        },
        {
          title: '商户号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '渠道商户号',
          dataIndex: 'chnMerchNo',
          align: 'center'
        },
        {
          title: '厂商编号',
          dataIndex: 'factoryCode',
          align: 'center'
        },
        {
          title: '厂商名称',
          dataIndex: 'factoryName',
          align: 'center'
        },
        {
          title: '终端型号',
          dataIndex: 'modelName',
          align: 'center'
        },
        {
          title: '终端来源',
          dataIndex: 'terminalSource',
          align: 'center',
          key: 'terminalSource'
        },
        {
          title: '押金活动金额',
          dataIndex: 'serviceFee',
          align: 'center'
        },
        {
          title: '流量费收取开关',
          dataIndex: 'simSwitch',
          align: 'center',
          key: 'simSwitch'
        },
        {
          title: '流量费免费使用天数',
          dataIndex: 'simFreeDay',
          align: 'center',
          customRender: ({ text }) => {
            return text || '--';
          }
        },
        {
          title: '流量费(第一期)',
          dataIndex: 'firstSimFee',
          customRender: ({ record }) => {
            if (record.simSwitch !== 1) return '--';
            return `流量费:${record.firstSimFee}元, 权益天数:${record.firstSimPeriodDay}天`;
          }
        },
        {
          title: '流量费(第二期)',
          dataIndex: 'secondSimFee',
          customRender: ({ record }) => {
            if (record.simSwitch !== 1) return '--';
            return `流量费:${record.secondSimFee}元, 权益天数:${record.secondSimPeriodDay}天`;
          }
        },
        {
          title: '流量费(第三期)',
          dataIndex: 'thirdSimFee',
          customRender: ({ record }) => {
            if (record.simSwitch !== 1) return '--';
            return `流量费:${record.thirdSimFee}元, 权益天数:${record.thirdSimPeriodDay}天`;
          }
        },
        {
          title: '流量费(标准期)',
          dataIndex: 'fourthSimFee',
          customRender: ({ record }) => {
            if (record.simSwitch !== 1) return '--';
            return `流量费:${record.fourthSimFee}元, 权益天数:${record.fourthSimPeriodDay}天`;
          }
        },
        {
          title: '创建人',
          dataIndex: 'createUserId',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          align: 'center'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId',
          align: 'center'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime',
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          align: 'center'
        }
      ]
    };
  },
  computed: {
    // 当前登录用户信息
    loginUser() {
      const userStore = useUserStore();
      return userStore.$state.info;
    }
  },
  mounted() {
    this.getChannelCodes();
    //获取服务费、流量费自身的活动政策列表
    this.getServiceFeeSelfOpenList();
    this.getSimFeeSelfOpenList();
    this.getFactoryList();
    this.getTerminalModelList();

    if (hasPurview('1')) {
      // this.getRegionStatus();
    }
  },
  methods: {
    terminalTransferActivity() {
      this.showTerminalTransferActivity = true;
    },
    handleTerminalTransferActivity(key, isActiveTer) {
      this.transferHandleType = key;
      this.isActiveTer = isActiveTer;
      this.showTerminalTransferActivity = true;
    },
    async getRegionStatus() {
      const data = await RegionManageApi.getOneRegionStatus({ regionNo: localStorage.getItem('SASS_ORG_CODE') });
      const { snInboundSwitch } = data || {};
      this.showRegionWarehouse = snInboundSwitch === 1;
    },
    async getRateMerchTypeList() {
      this.merchRatePolicyList = [];
      const agentNo = localStorage.getItem('SASS_ORG_CODE');
      let data = await RatePolicyApi.listOfOrg({
        channelCode: this.where.channelCode,
        userNo: agentNo,
        userType: Number(this.loginUser.userType),
        policyType: 2
      });
      //对数据进行筛选,二次筛选,只有rateType === 1的项目才保留,只有有这个项目的data的项目才保留
      let ratePolicyData = data || [];
      ratePolicyData = ratePolicyData.filter(ratePolicy => {
        const filteredRatePolicy = ratePolicy.rateDTOList.filter(rateData => rateData.rateType === 1);
        return filteredRatePolicy.length > 0;
      });

      this.merchRatePolicyList = ratePolicyData || [];
    },
    handleTerminalChangeBind() {
      if (this.selection.length !== 1) return message.warn('请选择一条数据');
      if (this.selection.some(i => i.useStatus !== 2)) {
        return message.warn('只能选择已绑定终端');
      }
      this.detail = this.selection[0];
      this.showTerminalChangeBind = true;
    },
    async handleDownloadMerchantRateExcel() {
      this.reload();
      this.spinning = true;
      const res = await InventoryManageApi.terminalDownload(this.where).catch(() => {
        this.spinning = false;
      });
      this.spinning = false;
      const fileReader = new FileReader();
      fileReader.onload = function () {
        try {
          // 说明是普通对象数据，后台转换失败
          const jsonData = JSON.parse(fileReader.result);
          message.error(jsonData.message);
        } catch (err) {
          // 解析对象失败，说明是正常的文件流
          let blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.style.display = 'none';
          a.href = url;
          a.download = `终端导出.xlsx`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }
      };
      fileReader.readAsText(res?.data);
    },
    handleTerminalTransferToTarget() {
      if (!this.selection.length) return message.warn('请选择数据');
      // if (this.selection.some(i => i.useStatus !== 0)) {
      //   return message.warn('只能选择闲置终端');
      // }
      const isSameChannel = this.selection.every(i => i.channelCode === this.selection[0].channelCode);
      if (!isSameChannel) {
        return message.warn('只能选择通道相同终端');
      }
      const isSameTerminalSource = this.selection.every(i => i.terminalSource === this.selection[0].terminalSource);
      if (!isSameTerminalSource) {
        return message.warn('只能选择终端来源相同终端');
      }
      const terminalSnList = this.selection.map(i => i.terminalSn);
      this.detail = {
        channelCode: this.selection[0].channelCode,
        oneLevelAgentNo: this.selection[0].oneLevelAgentNo,
        terminalSource: this.selection[0].terminalSource,
        terminalSnList
      };
      this.showTerminalTransferToTarget = true;
    },
    handleChangeMerchPolicy() {
      if (!this.selection.length) return message.warn('请选择数据');
      if (this.selection.some(i => i.useStatus !== 0)) {
        return message.warn('只能选择闲置终端');
      }
      const isSameChannel = this.selection.every(i => i.channelCode === this.selection[0].channelCode);
      if (!isSameChannel) {
        return message.warn('只能选择通道相同终端');
      }
      const { policyNo, channelCode } = this.selection[0] || {};
      const terminalSnList = this.selection.map(i => i.terminalSn);
      this.detail = {
        channelCode,
        originPolicyNo: policyNo,
        terminalSnList
      };
      this.showChangeMerchPolicy = true;
    },

    handleAdminUbind() {
      if (this.selection.length !== 1) return message.warn('请选择一条数据');
      if (this.selection.some(i => i.useStatus !== 2)) {
        return message.warn('只能选择已绑定终端');
      }
      Modal.confirm({
        title: '提示',
        content: '确定要解绑操作吗?',
        icon: createVNode(ExclamationCircleOutlined),
        maskClosable: true,
        onOk: async () => {
          const result = await InventoryManageApi.unbindTermSn({ id: this.selection[0]?.id });
          message.success(result.message);
          this.reload();
        }
      });
    },
    handleWeaning() {
      if (this.selection.length !== 1) return message.warn('请选择一条数据');
      if (this.selection.some(i => i.useStatus !== 2)) {
        return message.warn('只能选择已绑定终端');
      }
      Modal.confirm({
        title: '提示',
        content: '确定要执行SN解绑操作吗?',
        icon: createVNode(ExclamationCircleOutlined),
        maskClosable: true,
        onOk: async () => {
          const result = await InventoryManageApi.abortTermSn({ id: this.selection[0]?.id });
          message.success(result.message);
          this.reload();
        }
      });
    },

    reload() {
      this.selection = [];
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.selection = [];
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    //单号/连号入库
    addTerminalToLibraryNumberType() {
      this.addLibraryHandleType = 'number';
      if (hasPurview('3')) {
        this.showAddLibraryNumberType = true;
      } else if (hasPurview('1')) {
        this.showAddLibraryActivity = true;
      }
    },

    //批量入库
    addTerminalToLibraryFileType() {
      this.addLibraryHandleType = 'file';
      if (hasPurview('3')) {
        this.showAddLibraryNumberType = true;
      } else if (hasPurview('1')) {
        this.showAddLibraryActivity = true;
      }
    },

    //终端下发
    terminalTransfer() {
      this.showTerminalTransfer = true;
    },

    handleBatchChangeMerchPolicy() {
      this.showBatchChangeMerchPolicy = true;
    },

    //终端文件下发
    terminalTransferByfile() {
      this.showTerminalTransferByFile = true;
    },

    //终端回拨
    terminalCallback() {
      this.showTerminalCallback = true;
    },

    //终端文件回拨
    terminalCallbackByFile() {
      this.showTerminalCallbackByFile = true;
    },

    //基本信息编辑
    handleBaseInfoEdit(row) {
      this.showBaseInfoEdit = true;
      this.current = row;
    },

    //营销活动编辑
    handleActivityInfoEdit(row) {
      this.showActivityInfoEdit = true;
      this.current = row;
    },

    //费率信息修改
    handleRateInfoEdit(row) {
      this.showRateInfoEdit = true;
      this.current = row;
    },

    //展示详情
    handleShowDetail(row) {
      this.showDetail = true;
      this.current = row;
    },

    //删除
    async singleRemove(row) {
      const result = await InventoryManageApi.singleDelete({ id: row.id });
      message.success(result.message);
      this.reload();
    },
    //回收
    async handleSingleRecycle(row) {
      const result = await InventoryManageApi.singleRecycle({ id: row.id });
      message.success(result.message);
      this.reload();
    },

    //获取通道编号列表
    async getChannelCodes() {
      const data = await ChannelManageApi.list({ validStatus: 1 });
      this.channelCodes = data || [];
    },

    //获取服务费政策列表
    async getServiceFeeSelfOpenList() {
      const data = await ServiceFeePolicyApi.selfOpenList();
      this.serviceFeePolicyList = data || [];
    },

    //获取流量费政策列表
    async getSimFeeSelfOpenList() {
      const data = await SimFeePolicyApi.selfOpenList();
      this.simFeePolicyList = data || [];
    },

    //厂商列表
    async getFactoryList() {
      const data = await TerminalFactoryApi.list({ validStatus: 1 });
      this.factoryList = data || [];
    },

    //设备型号列表
    async getTerminalModelList() {
      const data = await TerminalModelApi.list({ validStatus: 1 });
      this.terminalModelList = data || [];
    },

    //获取数据方法
    datasource({ page, limit, where }) {
      return InventoryManageApi.findPages({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>

<style scoped lang="less">
::v-deep {
  .toolbar {
    .ant-btn {
      margin: 0 8px 8px 0;
    }
  }
}
</style>
