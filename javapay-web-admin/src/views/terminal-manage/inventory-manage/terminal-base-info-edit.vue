<template>
  <a-modal
    :width="900"
    :visible="visible"
    :confirm-loading="loading"
    title="基本信息编辑"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @cancel="cancel"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 7 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 17 }, sm: { span: 24 } }"
    >
      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="设备厂商" name="factoryId">
            <a-select v-model:value="form.factoryId" placeholder="请选择" @change="factorySelectChanged()" allow-clear>
              <a-select-option v-for="(item, key) in factoryList" :key="key" :value="item.id">{{ item.factoryName }}</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="支付机构" name="channelCode" v-if="form.useStatus === 0">
            <a-select v-model:value="form.channelCode" placeholder="请选择" allow-clear disabled>
              <a-select-option v-for="(item, key) in channelCodes" :key="key" :value="item.channelCode">{{
                item.channelName
              }}</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="终端来源" name="terminalSource">
            <a-select v-model:value="form.terminalSource" placeholder="请选择" disabled>
              <a-select-option :value="1">全款机</a-select-option>
              <a-select-option :value="2">分期机</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="设备型号" name="modelId">
            <a-select v-model:value="form.modelId" placeholder="请选择" :disabled="!form.factoryId" allow-clear>
              <a-select-option v-for="(item, key) in terminalModelList" :key="key" :value="item.id">{{ item.modelName }}</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="终端序列号" name="terminalSn" v-if="form.useStatus === 0">
            <a-input v-model:value.trim="form.terminalSn" placeholder="请输入终端序列号" allow-clear />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script>
import { InventoryManageApi } from '@/api/terminal-manage/InventoryManageApi';
import { message } from 'ant-design-vue';
import { TerminalModelApi } from '@/api/qrcodeCard/TerminalModelApi';

function formDefaults() {
  return {
    channelCode: null
  };
}

export default {
  name: 'TerminalBaseInfoEdit',
  props: {
    visible: Boolean,
    data: Object,
    factoryList: Array,
    channelCodes: Array
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: formDefaults(),
      //设备型号数组
      terminalModelList: [],
      // 表单验证规则
      rules: {
        factoryId: [{ required: true, message: '请选择' }],
        modelId: [{ required: true, message: '请选择' }],
        channelCode: [{ required: true, message: '请选择' }],
        terminalSn: [{ required: true, message: '请输入终端序列号' }],
        terminalSource: [{ required: true, message: '请选择' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false
    };
  },
  watch: {
    data() {
      if (this.data) {
        this.form = Object.assign(formDefaults(), this.data);
        this.isUpdate = true;

        this.getTerminalModelList();
      } else {
        this.form = formDefaults();
        this.isUpdate = false;
      }
      if (this.$refs.form) {
        this.$refs.form.clearValidate();
      }
    }
  },
  methods: {
    //厂商选择之后
    factorySelectChanged() {
      this.form.modelId = null;
      this.getTerminalModelList();
    },

    //设备型号列表
    async getTerminalModelList() {
      const data = await TerminalModelApi.list({ factoryId: this.form.factoryId, validStatus: 1 });
      this.terminalModelList = data || [];
    },

    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;
      // 执行编辑或修改方法
      let result = InventoryManageApi.editBaseInfo({
        id: this.form.id,
        factoryId: this.form.factoryId,
        modelId: this.form.modelId,
        channelCode: this.form.channelCode,
        terminalSn: this.form.terminalSn,
        terminalSource: this.form.terminalSource
      });

      result
        .then(result => {
          // 移除加载框
          this.loading = false;
          // 提示添加成功
          message.success(result.message);

          // 如果是新增，则form表单置空
          if (!this.isUpdate) {
            this.form = formDefaults();
          }

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    cancel() {
      this.form = Object.assign(formDefaults(), this.data);
      this.$refs.form.clearValidate();
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
