<template>
  <a-modal
    :width="1000"
    :visible="visible"
    title="终端入库"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
  >
    <a-form ref="form" :model="form" :rules="rules" :label-col="{ style: { width: '135px' } }">
      <a-tabs v-model:activeKey="activeTabKey" type="card" destroyInactiveTabPane>
        <!-- 基本配置 -->
        <a-tab-pane key="1" tab="基本配置 ">
          <a-row :gutter="16">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="支付通道" name="channelCode">
                <a-select v-model:value="form.channelCode" class="ele-fluid" placeholder="请选择" allow-clear @change="onChangeChannel">
                  <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
                    {{ channelName }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="是否活动终端" name="termActiveSwitch">
                <a-radio-group v-model:value="form.termActiveSwitch" @change="onChangeTermActiveSwitch">
                  <a-radio :value="1">是</a-radio>
                  <a-radio :value="0">否</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="设备厂商" name="factoryId">
                <a-select v-model:value="form.factoryId" placeholder="请选择" @change="factorySelectChanged" allow-clear>
                  <a-select-option v-for="(item, key) in factoryList" :key="key" :value="item.id">{{ item.factoryName }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="设备型号" name="modelId">
                <a-select v-model:value="form.modelId" placeholder="请选择" :disabled="!form.factoryId" allow-clear>
                  <a-select-option v-for="(item, key) in terminalModelList" :key="key" :value="item.id">
                    {{ item.modelName }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :md="12" :sm="24" :xs="24" :span="12">
              <a-form-item label="入库机构类型">
                <a-radio-group v-model:value="orgType" @change="onChangeOrgType" :disabled="form.termActiveSwitch === 1">
                  <a-radio-button :value="2">运营中心</a-radio-button>
                  <a-radio-button :value="3">代理商</a-radio-button>
                </a-radio-group>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24" :span="12">
              <a-form-item label="运营中心编号" name="agentNo" v-if="orgType === 2">
                <a-input v-model:value="form.agentNo" placeholder="请选择" readonly @click="onSelectBranch" :disabled="!form.channelCode" />
              </a-form-item>

              <a-form-item label="代理商编号" name="agentNo" v-if="orgType === 3">
                <a-input
                  v-model:value="form.agentNo"
                  placeholder="请选择"
                  readonly
                  @click="showTargAgentList = true"
                  :disabled="!form.channelCode"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="押金" name="serviceFeeId">
                <a-select v-model:value="form.serviceFeeId" placeholder="请选择" :disabled="!form.agentNo" allow-clear>
                  <a-select-option v-for="(item, key) in serviceFeePolicyList" :key="key" :value="item.configId">{{
                    item.policyName
                  }}</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="流量费收取开关" name="simSwitch">
                <a-radio-group v-model:value="form.simSwitch" name="simSwitch">
                  <a-radio :value="1">开启</a-radio>
                  <a-radio :value="0">关闭</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="终端来源" name="terminalSource">
                <a-select
                  v-model:value="form.terminalSource"
                  placeholder="请选择"
                  :disabled="!form.agentNo"
                  @change="onChangeTerminalSource"
                >
                  <a-select-option :value="1">全款机</a-select-option>
                  <a-select-option :value="2">分期机</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="免费使用天数" name="simFreeDay" v-if="form.simSwitch === 1">
                <a-input v-model:value.trim="form.simFreeDay" placeholder="请输入免费使用天数" allow-clear />
              </a-form-item>
            </a-col>
          </a-row>

          <template v-if="form.simSwitch === 1">
            <a-row :gutter="16" v-for="(item, key) in form.simFeePeriodConfigDTO || []" :key="key">
              <a-col :md="12" :sm="24" :xs="24">
                <a-form-item
                  :label="`${item.periodName}流量费金额`"
                  :name="['simFeePeriodConfigDTO', key, item.periodIdField]"
                  :rules="rules.simFeeId"
                >
                  <a-select v-model:value="item[item.periodIdField]" placeholder="请选择" :disabled="!form.agentNo">
                    <a-select-option
                      v-for="(option, keyo) in simFeePolicyMap[item.periodIdOptionsField] || []"
                      :key="keyo"
                      :value="option.configId"
                    >
                      {{ option.policyName }}</a-select-option
                    >
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24" :xs="24">
                <a-form-item
                  :label="`${item.periodName}权益天数`"
                  :name="['simFeePeriodConfigDTO', key, item.periodDayField]"
                  :rules="rules.simFeeDay"
                >
                  <a-select v-model:value="item[item.periodDayField]" placeholder="请选择" :disabled="!form.agentNo">
                    <a-select-option v-for="(day, keyd) in [180, 210, 240, 270, 300, 360]" :key="keyd" :value="day"
                    >{{ day }}天</a-select-option
                    >
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </template>

          <a-form-item label="指定终端方式">
            <a-radio-group v-model:value="terminalSelectType">
              <a-radio-button value="region">区间选择</a-radio-button>
              <a-radio-button value="file">文件上传</a-radio-button>
            </a-radio-group>
          </a-form-item>

          <a-row :gutter="16" v-if="terminalSelectType === 'region'">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="开始序列号" name="terminalSnStart">
                <a-input v-model:value.trim="form.terminalSnStart" placeholder="请输入开始序列号" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="结束序列号" name="terminalSnEnd">
                <a-input v-model:value.trim="form.terminalSnEnd" placeholder="请输入结束序列号" allow-clear />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16" v-if="terminalSelectType === 'file'">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="终端文件" name="file">
                <a-upload-dragger accept=".xlsx,.xls" :maxCount="1" :before-upload="uploadTerminalFile" @remove="deleteTerminalFile">
                  <p class="ant-upload-drag-icon">
                    <cloud-upload-outlined />
                  </p>
                  <p class="ant-upload-hint" style="margin-bottom: 10px">将文件拖到此处，或点击上传</p>
                  <div class="ele-text-center">
                    <a @click.stop="downloadTemplateFile">下载模板文件 </a>
                  </div>
                </a-upload-dragger>
              </a-form-item>
            </a-col>
          </a-row>
        </a-tab-pane>

        <!-- 活动配置 -->
        <a-tab-pane key="2" tab="活动配置" class="tabpane__background" disabled v-if="form.termActiveSwitch === 1">
          <!-- 激活返现配置 -->
          <a-card title="激活返现配置" :bordered="false">
            <a-row :gutter="16">
              <a-col :md="12" :sm="24" :xs="24">
                <a-form-item label="激活返现开关" name="activateSwitch">
                  <a-switch :checkedValue="1" :un-checked-value="0" v-model:checked="form.activateSwitch" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16" v-if="form.activateSwitch === 1">
              <a-col :md="12" :sm="24" :xs="24">
                <a-form-item label="激活返现政策" name="activePolicyNo">
                  <a-select
                    v-model:value="form.activePolicyNo"
                    style="width: 100%"
                    placeholder="请选择"
                    :options="activatePolicyOptions"
                    :fieldNames="{ label: 'policyName', value: 'policyNo' }"
                    @change="onConfirmActivePolicy"
                  />
                </a-form-item>
              </a-col>
              <!-- <a-col :md="12" :sm="24" :xs="24">
                <a-form-item label="激活周期偏移量" name="activeCycleOffset">
                  <a-input-number v-model:value="form.activeCycleOffset" placeholder="自激活多少日起" style="width: 100%" />
                </a-form-item>
              </a-col> -->
            </a-row>

            <div v-if="form.activateSwitch === 1 && form.activePolicyNo">
              <a-typography-text keyboard>
                统计周期:从
                <a-typography-text underline>终端绑定后</a-typography-text>
                算起
              </a-typography-text>
            </div>

            <!-- 规则列表 -->
            <a-table
              v-if="form.activateSwitch === 1 && form.activePolicyNo"
              :columns="columns"
              :data-source="form.activePolicyDataSource"
              bordered
              :pagination="false"
              size="middle"
            >
              <template #bodyCell="{ column, text, record }">
                <!-- 可编辑列 -->
                <template v-if="tableEditKeys.includes(column.dataIndex)">
                  <div>
                    <a-form-item
                      :name="['activePolicyDataSource', record.key, column.dataIndex]"
                      :rules="
                        column.dataIndex === 'cashbackAmount'
                          ? cashbackAmtRules(record.parentCashbackAmount)
                          : rules[column.dataIndex] || []
                      "
                      class="table-edit-form__item"
                    >
                      <a-input-number
                        v-if="activeEditableData[record.key]"
                        v-model:value="activeEditableData[record.key][column.dataIndex]"
                        placeholder="必填"
                        :min="0"
                        style="margin: -5px 0; width: 100%"
                      />
                      <template v-else>
                        {{ text }}
                      </template>
                      <template #help v-if="activeEditableData[record.key]">
                        <a-typography-text type="warning" style="font-size: 12px"
                        >注: 下级不能大于{{ record.parentCashbackAmount }}</a-typography-text
                        >
                      </template>
                    </a-form-item>
                  </div>
                </template>
                <!-- 操作栏 -->
                <template v-else-if="column.dataIndex === 'operation'">
                  <div class="editable-row-operations">
                    <span v-if="activeEditableData[record.key]">
                      <a @click="updateActiveRow(record.key)" class="ele-text-danger">保存</a>
                    </span>
                    <span v-else>
                      <a @click="editActiveRow(record.key)">编辑</a>
                    </span>
                  </div>
                </template>
              </template>
            </a-table>
          </a-card>

          <!-- 达标返现配置 -->
          <a-card title="达标返现配置" :bordered="false">
            <a-row :gutter="16">
              <a-col :md="12" :sm="24" :xs="24">
                <a-form-item label="达标返现开关" name="cashbackSwitch">
                  <a-switch :checkedValue="1" :un-checked-value="0" v-model:checked="form.cashbackSwitch" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16" v-if="form.cashbackSwitch === 1">
              <a-col :md="12" :sm="24" :xs="24">
                <a-form-item label="达标返现政策" name="cashbackPolicyNo">
                  <a-select
                    v-model:value="form.cashbackPolicyNo"
                    style="width: 100%"
                    placeholder="请选择"
                    :options="cashbackPolicyOptions"
                    :fieldNames="{ label: 'policyName', value: 'policyNo' }"
                    @change="onConfirmCashbackPolicy"
                  />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24" :xs="24">
                <a-form-item label="达标交易类型" name="payMethods">
                  <a-select v-model:value="form.payMethods" placeholder="请选择" mode="multiple">
                    <a-select-option :value="1">银联云闪付</a-select-option>
                    <a-select-option :value="2">微信支付</a-select-option>
                    <a-select-option :value="3">支付宝支付</a-select-option>
                    <a-select-option :value="4">EPOS支付</a-select-option>
                    <a-select-option :value="5">POS刷卡</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>

            <div v-if="form.cashbackSwitch === 1 && form.cashbackPolicyNo">
              <a-typography-text keyboard>
                统计周期:从
                <a-typography-text underline>终端绑定后</a-typography-text>
                算起
              </a-typography-text>
            </div>

            <!-- 规则表格 -->
            <a-table
              v-if="form.cashbackSwitch === 1 && form.cashbackPolicyNo"
              :columns="columns"
              :data-source="form.cashbackPolicyDataSource"
              bordered
              :pagination="false"
              size="middle"
            >
              <template #bodyCell="{ column, text, record }">
                <!-- 可编辑列 -->
                <template v-if="tableEditKeys.includes(column.dataIndex)">
                  <div>
                    <a-form-item
                      :name="['cashbackPolicyDataSource', record.key, column.dataIndex]"
                      :rules="
                        column.dataIndex === 'cashbackAmount'
                          ? cashbackAmtRules(record.parentCashbackAmount)
                          : rules[column.dataIndex] || []
                      "
                      class="table-edit-form__item"
                    >
                      <a-input-number
                        v-if="cashbackEditableData[record.key]"
                        v-model:value="cashbackEditableData[record.key][column.dataIndex]"
                        placeholder="必填"
                        :min="0"
                        style="margin: -5px 0; width: 100%"
                      />
                      <template v-else>
                        {{ text }}
                      </template>
                      <template #help v-if="cashbackEditableData[record.key]">
                        <a-typography-text type="warning" style="font-size: 12px"
                        >注: 下级不能大于{{ record.parentCashbackAmount }}</a-typography-text
                        >
                      </template>
                    </a-form-item>
                  </div>
                </template>
                <!-- 操作栏 -->
                <template v-else-if="column.dataIndex === 'operation'">
                  <div class="editable-row-operations">
                    <span v-if="cashbackEditableData[record.key]">
                      <a @click="updateCashbackRow(record.key)" class="ele-text-danger">保存</a>
                    </span>
                    <span v-else>
                      <a @click="editCashbackRow(record.key)">编辑</a>
                    </span>
                  </div>
                </template>
              </template>
            </a-table>
          </a-card>

          <!-- 未/伪激活配置 -->
          <a-card title="未/伪激活配置" :bordered="false">
            <a-row :gutter="16">
              <a-col :md="12" :sm="24" :xs="24">
                <a-form-item label="未激活开关" name="inactiveSwitch">
                  <a-switch :checkedValue="1" :un-checked-value="0" v-model:checked="form.inactiveSwitch" />
                </a-form-item>
                <a-form-item label="未激活配置规则" name="inactiveConfId" v-if="form.inactiveSwitch === 1">
                  <a-select
                    v-model:value="form.inactiveConfId"
                    style="width: 100%"
                    placeholder="请选择"
                    @dropdownVisibleChange="open => open && getInactiveConfs()"
                  >
                    <a-select-option disabled>
                      <a-row>
                        <a-col :span="12"> 时间周期 </a-col>
                        <a-col :span="12"> 扣款金额 </a-col>
                      </a-row>
                    </a-select-option>
                    <a-select-option v-for="(item, key) in inactiveConfs" :key="key" :value="item.id">
                      <a-row>
                        <a-col :span="12"> {{ item.timeCycle }}天 </a-col>
                        <a-col :span="12"> {{ item.deductAmount }}元 </a-col>
                      </a-row>
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="16">
              <a-col :md="12" :sm="24" :xs="24">
                <a-form-item label="伪激活开关" name="pseudoActiveSwitch">
                  <a-switch :checkedValue="1" :un-checked-value="0" v-model:checked="form.pseudoActiveSwitch" />
                </a-form-item>
                <a-form-item label="伪激活配置规则" name="pseudoActiveRuleId" v-if="form.pseudoActiveSwitch === 1">
                  <a-select
                    v-model:value="form.pseudoActiveRuleId"
                    style="width: 100%"
                    placeholder="请选择"
                    @dropdownVisibleChange="open => open && getPseudoActives()"
                  >
                    <a-select-option disabled>
                      <a-row>
                        <a-col :span="8"> 时间周期 </a-col>
                        <a-col :span="8"> 扣款金额 </a-col>
                        <a-col :span="8"> 交易标准金额 </a-col>
                      </a-row>
                    </a-select-option>
                    <a-select-option v-for="(item, key) in pseudoActives" :key="key" :value="item.id">
                      <a-row>
                        <a-col :span="8"> {{ item.timeCycle }}天 </a-col>
                        <a-col :span="8"> {{ item.deductAmount }}元 </a-col>
                        <a-col :span="8"> {{ item.tradeVolume }}元 </a-col>
                      </a-row>
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>
        </a-tab-pane>
      </a-tabs>
    </a-form>

    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
      <template v-if="form.termActiveSwitch === 1">
        <a-button v-if="activeTabKey === '1'" type="primary" :loading="loading" @click="handleNext">下一步</a-button>
        <a-button v-else-if="activeTabKey === '2'" type="primary" :loading="loading" @click="save">提交</a-button>
      </template>

      <template v-else>
        <a-button type="primary" :loading="loading" @click="save">提交</a-button>
      </template>
    </template>
  </a-modal>

  <BranchSelect v-if="showSelectBranch" v-model:visible="showSelectBranch" @done="onSelectBranchFinish" />

  <TargAgentSelect v-if="showTargAgentList" v-model:visible="showTargAgentList" @done="setTargAgentInfo" :agentWhere="agentWhere" />
</template>

<script>
import { message } from 'ant-design-vue';
import { cloneDeep, isEmpty, omit } from 'lodash-es';
import { useUserStore } from '@/store/modules/user';
import { deepCopy } from '@/utils/util';
import { hasPurview } from '@/utils/permission';
import { InventoryManageApi } from '@/api/terminal-manage/InventoryManageApi';
import { TerminalModelApi } from '@/api/qrcodeCard/TerminalModelApi';
import { ActiveRuleConfigApi } from '@/api/businessTeam/active-rule-config/ActiveRuleConfigApi';
import BranchSelect from './targ-branch-select.vue';
import { ServiceFeePolicyApi } from '@/api/businessTeam/activity-Config/ServiceFeePolicyApi';
import { SimFeePolicyApi } from '@/api/businessTeam/activity-Config/SimFeePolicyApi';
import TargAgentSelect from './targ-agent-select.vue';

const simFeePeriodConfigDef = [
  {
    periodName: '一期',
    periodIdField: 'firstSimFeeId',
    periodFeeField: 'firstSimFee',
    periodDayField: 'firstSimPeriodDay',
    periodIdOptionsField: 'firstPeriodList',
    firstSimFeeId: null,
    firstSimPeriodDay: null
  },
  {
    periodName: '二期',
    periodIdField: 'secondSimFeeId',
    periodFeeField: 'secondSimFee',
    periodDayField: 'secondSimPeriodDay',
    periodIdOptionsField: 'secondPeriodList',
    secondSimFeeId: null,
    secondSimPeriodDay: null
  },
  {
    periodName: '三期',
    periodIdField: 'thirdSimFeeId',
    periodFeeField: 'thirdSimFee',
    periodDayField: 'thirdSimPeriodDay',
    periodIdOptionsField: 'thirdPeriodList',
    thirdSimFeeId: null,
    thirdSimPeriodDay: null
  },
  {
    periodName: '标准期',
    periodIdField: 'fourthSimFeeId',
    periodFeeField: 'fourthSimFee',
    periodDayField: 'fourthSimPeriodDay',
    periodIdOptionsField: 'fourthPeriodList',
    fourthSimFeeId: null,
    fourthSimPeriodDay: null
  }
];

function formDefaults() {
  return {
    channelCode: null,

    terminalSource: 1,

    termActiveSwitch: 0,
    activateSwitch: 0,
    cashbackSwitch: 0,
    pseudoActiveSwitch: 0,
    inactiveSwitch: 0,

    simSwitch: 1,

    activePolicyDataSource: [],
    cashbackPolicyDataSource: [],

    simFeePeriodConfigDTO: deepCopy(simFeePeriodConfigDef)
  };
}

export default {
  components: {
    BranchSelect,
    TargAgentSelect
  },
  props: {
    visible: Boolean,
    factoryList: Array,
    channelCodes: Array
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      terminalSelectType: 'region',
      showTargAgentList: false,
      agentWhere: {
        regionNo: localStorage.getItem('SASS_ORG_CODE'),
        orgType: 1
      },
      orgType: 2,
      activeTabKey: '1',
      // 表单数据
      form: formDefaults(),
      //设备型号数组
      terminalModelList: [],
      // 表单验证规则
      rules: {
        factoryId: [{ required: true, message: '请选择' }],
        modelId: [{ required: true, message: '请选择' }],
        channelCode: [{ required: true, message: '请选择' }],
        agentNo: [{ required: true, message: '请选择' }],
        terminalSnStart: [{ required: true, message: '请输入开始序列号' }],
        terminalSnEnd: [{ required: true, message: '请输入结束序列号' }],
        terminalSource: [{ required: true, message: '请选择' }],
        termActiveSwitch: [{ required: true, message: '请选择' }],

        activateSwitch: [{ required: true, message: '请选择' }],
        activePolicyNo: [{ required: true, message: '请选择' }],
        cashbackSwitch: [{ required: true, message: '请选择' }],
        cashbackPolicyNo: [{ required: true, message: '请选择' }],
        payMethods: [{ required: true, message: '请选择' }],
        inactiveSwitch: [{ required: true, message: '请选择' }],
        pseudoActiveSwitch: [{ required: true, message: '请选择' }],
        inactiveConfId: [{ required: true, message: '请选择' }],
        pseudoActiveRuleId: [{ required: true, message: '请选择' }],
        // activeCycleOffset: [{ required: true, message: '请填写' }],

        timeCycleStart: [{ required: true, message: '请填写' }],
        timeCycleEnd: [{ required: true, message: '请填写' }],
        tradeVolume: [{ required: true, message: '请填写' }],

        file: [{ required: true, message: '请上传文件' }],

        serviceFeeId: [{ required: true, message: '请选择' }],
        simSwitch: [{ required: true, message: '请选择' }],
        simFreeDay: [{ required: true, message: '请输入免费使用天数' }],
        simFeeId: [{ required: true, message: '请选择' }],
        simFeeDay: [{ required: true, message: '请选择' }]
      },
      // 提交状态
      loading: false,
      showSelectBranch: false,

      hasPurview,
      activatePolicyOptions: [],
      cashbackPolicyOptions: [],
      inactiveConfs: [],
      pseudoActives: [],

      serviceFeePolicyList: [],
      simFeePolicyMap: {},

      // 表格可编辑键
      tableEditKeys: ['cashbackAmount'],
      // 表格列
      columns: [
        {
          title: '统计周期-开始天数',
          dataIndex: 'timeCycleStart',
          width: '20%',
          align: 'center'
        },
        {
          title: '统计周期-结束天数',
          dataIndex: 'timeCycleEnd',
          width: '20%',
          align: 'center'
        },
        {
          title: '交易标准金额(元)',
          dataIndex: 'tradeVolume',
          width: '20%',
          align: 'center'
        },
        {
          title: '返现金额(元)',
          dataIndex: 'cashbackAmount',
          width: '20%',
          align: 'center',
          defaultFilteredValue: [0],
          onFilter: (value, record) => {
            if (Number(record.parentCashbackAmount) > value) return true;
            else {
              record.cashbackAmount = 0;
              return false;
            }
          }
        },
        {
          title: '操作',
          dataIndex: 'operation',
          align: 'center'
        }
      ],
      // 编辑中的行
      activeEditableData: {},
      cashbackEditableData: {}
    };
  },
  computed: {
    // 当前登录用户信息
    loginUser() {
      const userStore = useUserStore();
      return userStore.$state.info;
    }
  },
  methods: {
    onChangeChannel() {
      if (this.form.agentNo) {
        this.form.serviceFeeId = null;

        this.form.simFeePeriodConfigDTO.forEach(item => {
          item[item.periodIdField] = null;
          item[item.periodDayField] = null;
        });

        this.getServiceFeePolicyList();
        this.getSimFeePolicyList();
      }
    },

    onChangeTerminalSource() {
      this.form.serviceFeeId = null;

      this.form.simFeePeriodConfigDTO.forEach(item => {
        item[item.periodIdField] = null;
        item[item.periodDayField] = null;
      });

      this.getServiceFeePolicyList();
      this.getSimFeePolicyList();
    },

    onChangeTermActiveSwitch() {
      if (this.form.termActiveSwitch === 1) {
        if (this.orgType !== 2) {
          this.orgType = 2;
          this.onChangeOrgType();
        }
      }
    },
    cashbackAmtRules(maxsCashbackAmt) {
      if (!maxsCashbackAmt && maxsCashbackAmt !== 0) {
        return [{ required: true, message: '请填写', trigger: 'submit' }];
      }
      return [
        { required: true, message: '请填写', trigger: 'submit' },
        {
          validator: async (rule, value) => {
            if (Number(value) > Number(maxsCashbackAmt)) {
              return Promise.reject(`注：下级不能大于${maxsCashbackAmt}`);
            }
            return Promise.resolve();
          },
          trigger: 'submit'
        }
      ];
    },

    /** 选中激活政策 */
    onConfirmActivePolicy() {
      this.getActivePolicyDataSource();
    },

    /** 获取激活规则列表 */
    async getActivePolicyDataSource() {
      this.form.activePolicyDataSource = [];
      this.activeEditableData = {};

      let data = await InventoryManageApi.fetchTargetOrgCashPolicyInfo({
        cashbackType: 0,
        targAgentNo: this.form.agentNo,
        policyNo: this.form.activePolicyNo
      });
      data = data || [];
      data.forEach((item, index) => {
        item.key = index;
      });

      this.form.activePolicyDataSource = data;
    },

    /** 选中达标政策 */
    onConfirmCashbackPolicy() {
      this.getCashbackPolicyDataSource();
    },

    onChangeOrgType() {
      this.form.agentNo = null;
      this.form.serviceFeeId = null;

      this.form.simFeePeriodConfigDTO.forEach(item => {
        item[item.periodIdField] = null;
        item[item.periodDayField] = null;
      });
    },

    setTargAgentInfo(info) {
      this.showTargAgentList = false;
      this.form.agentNo = info.agentNo;

      this.form.serviceFeeId = null;

      this.form.simFeePeriodConfigDTO.forEach(item => {
        item[item.periodIdField] = null;
        item[item.periodDayField] = null;
      });

      this.getServiceFeePolicyList();
      this.getSimFeePolicyList();
    },

    /** 获取达标规则列表 */
    async getCashbackPolicyDataSource() {
      this.form.cashbackPolicyDataSource = [];
      this.cashbackEditableData = {};

      let data = await InventoryManageApi.fetchTargetOrgCashPolicyInfo({
        cashbackType: 1,
        targAgentNo: this.form.agentNo,
        policyNo: this.form.cashbackPolicyNo
      });
      data = data || [];
      data.forEach((item, index) => {
        item.key = index;
      });

      this.form.cashbackPolicyDataSource = data;
    },

    /** 选中表格行 */
    editActiveRow(key) {
      this.activeEditableData[key] = cloneDeep(this.form.activePolicyDataSource.filter(item => key === item.key)[0]);
    },

    /** 更新表格行 */
    async updateActiveRow(key) {
      // 先赋值
      Object.assign(this.form.activePolicyDataSource.filter(item => key === item.key)[0], this.activeEditableData[key]);
      // 后校验
      const validateFields = this.tableEditKeys.map(field => {
        return ['activePolicyDataSource', key, field];
      });
      await this.$refs.form.validateFields(validateFields).catch(() => {
        return Promise.reject('激活返现规则校验失败');
      });

      delete this.activeEditableData[key];
    },

    /** 选中表格行 */
    editCashbackRow(key) {
      this.cashbackEditableData[key] = cloneDeep(this.form.cashbackPolicyDataSource.filter(item => key === item.key)[0]);
    },

    /** 更新表格行 */
    async updateCashbackRow(key) {
      // 先赋值
      Object.assign(this.form.cashbackPolicyDataSource.filter(item => key === item.key)[0], this.cashbackEditableData[key]);
      // 后校验
      const validateFields = this.tableEditKeys.map(field => {
        return ['cashbackPolicyDataSource', key, field];
      });
      await this.$refs.form.validateFields(validateFields).catch(() => {
        return Promise.reject('达标返现规则校验失败');
      });

      delete this.cashbackEditableData[key];
    },

    async getInactiveConfs() {
      const data = await ActiveRuleConfigApi.list({ ruleType: 1, orgNo: this.loginUser.orgCode });
      this.inactiveConfs = data || [];
    },

    async getPseudoActives() {
      const data = await ActiveRuleConfigApi.list({ ruleType: 2, orgNo: this.loginUser.orgCode });
      this.pseudoActives = data || [];
    },

    /** 获取终端型号列表 */
    async getTerminalModelList() {
      const data = await TerminalModelApi.list({ factoryId: this.form.factoryId, validStatus: 1 });
      this.terminalModelList = data || [];
    },

    /** 选择终端厂商 */
    factorySelectChanged() {
      this.form.modelId = null;
      this.getTerminalModelList();
    },

    onSelectBranch() {
      this.showSelectBranch = true;
    },

    onSelectBranchFinish(info) {
      if (!info) return;
      this.form.agentNo = info.branchNo;

      this.form.serviceFeeId = null;

      this.form.simFeePeriodConfigDTO.forEach(item => {
        item[item.periodIdField] = null;
        item[item.periodDayField] = null;
      });

      this.getServiceFeePolicyList();
      this.getSimFeePolicyList();
    },

    /** 活动配置前置 */
    async handleNext() {
      // 校验表单
      await this.$refs.form.validate();
      // 修改加载框为正在加载
      this.loading = true;

      InventoryManageApi.fetchOwnerCashPolicyInfo({})
        .then(result => {
          this.loading = false;

          const { activeCashRuleList, reachCashRuleList } = result;

          this.form.activePolicyNo = null;
          this.form.cashbackPolicyNo = null;

          this.activatePolicyOptions = activeCashRuleList || [];
          this.cashbackPolicyOptions = reachCashRuleList || [];

          this.activeTabKey = '2';
        })
        .catch(() => {
          this.loading = false;
        });
    },

    /** 提交 */
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      if (this.form.termActiveSwitch === 1) {
        if (!(isEmpty(this.activeEditableData) && isEmpty(this.cashbackEditableData))) {
          return message.warn(`存在未保存的返现规则`);
        }
      }

      // 修改加载框为正在加载
      this.loading = true;

      let params = deepCopy(this.form);

      if (params.activateSwitch === 1) {
        params.activeRuleList = params.activePolicyDataSource;
      }
      if (params.cashbackSwitch === 1) {
        params.cashbackRuleList = params.cashbackPolicyDataSource;
      }
      params = omit(params, ['activePolicyDataSource', 'cashbackPolicyDataSource']);

      params.payMethods = params.payMethods ? params.payMethods.join(',') : '';

      const serviceFeeItem = this.serviceFeePolicyList.find(item => item.configId === params.serviceFeeId);
      params.serviceFee = serviceFeeItem?.amount

      if (params.simSwitch === 1) {
        const simFeePeriodConfigDTO = params.simFeePeriodConfigDTO;
        params = omit(params, ['simFeePeriodConfigDTO']);
        simFeePeriodConfigDTO.forEach(item => {
          params[item.periodIdField] = item[item.periodIdField];
          params[item.periodDayField] = item[item.periodDayField];

          const simFeeItem = this.simFeePolicyMap[item.periodIdOptionsField].find(option => option.configId === item[item.periodIdField]);
          params[item.periodFeeField] = simFeeItem?.simFeeAmt;
        });
      } else {
        params = omit(params, ['simFeePeriodConfigDTO']);
      }

      // 区间入库
      if (this.terminalSelectType === 'region') {
        const subMethod = this.orgType === 2 ? InventoryManageApi.addTerminalActiveInfo(params) : InventoryManageApi.addTermToAgent(params);
        subMethod
          .then(result => {
            this.loading = false;

            message.success(result.message);

            this.updateVisible(false);

            this.$emit('done');
          })
          .catch(() => {
            this.loading = false;
          });
      }
      // 文件入库
      else if (this.terminalSelectType === 'file') {
        this.addByFile(params);
      }
    },

    async getServiceFeePolicyList() {
      this.serviceFeePolicyList = [];
      let data = await ServiceFeePolicyApi.list({ orgNo: this.form.agentNo, orgType: this.orgType });
      data = data || [];
      this.serviceFeePolicyList = data.filter(
        item => item.terminalSource === this.form.terminalSource && item.channelCode === this.form.channelCode
      );
    },

    async getSimFeePolicyList() {
      this.simFeePolicyMap = {};
      let data = await SimFeePolicyApi.list({ orgNo: this.form.agentNo, orgType: this.orgType });
      data = data || {};
      Object.keys(data).forEach(key => {
        data[key] =
          data[key]?.filter(item => item.terminalSource === this.form.terminalSource && item.channelCode === this.form.channelCode) || [];
      });
      this.simFeePolicyMap = data || {};
    },

    uploadTerminalFile(file) {
      this.form.file = file;
      return false;
    },

    deleteTerminalFile() {
      this.form.file = null;
    },

    /** 文件入库 */
    addByFile(params) {
      const formData = new FormData();

      Object.keys(params).forEach(key => {
        if (!Array.isArray(params[key]) && params[key] !== null) {
          formData.append(key, params[key]);
        }
      });

      formData.append('file', this.form.file);

      const ruleFields = ['ruleNo', 'timeCycleStart', 'timeCycleEnd', 'tradeVolume', 'cashbackAmount'];
      if (params.activeRuleList?.length) {
        params.activeRuleList.forEach((item, index) => {
          ruleFields.forEach(field => {
            formData.append(`activeRuleList[${index}].${field}`, item[field]);
          });
        });
      }
      if (params.cashbackRuleList?.length) {
        params.cashbackRuleList.forEach((item, index) => {
          ruleFields.forEach(field => {
            formData.append(`cashbackRuleList[${index}].${field}`, item[field]);
          });
        });
      }

      const submitMethods =
        this.orgType === 2 ? InventoryManageApi.uploadActuvity(formData) : InventoryManageApi.batchSaveTermToAgentByFile(formData);

      submitMethods
        .then(result => {
          this.loading = false;

          message.success(result.message);

          this.updateVisible(false);

          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    /** 下载模版 */
    async downloadTemplateFile() {
      const res = await InventoryManageApi.download({}).catch(error => {
        message.destroy();

        const reader = new FileReader(); //创建一个FileReader实例
        reader.readAsText(error, 'utf-8'); //读取文件,结果用字符串形式表示
        reader.onload = function () {
          const { message: errorMsg } = JSON.parse(reader.result);
          message.error(errorMsg || '下载失败');
        };
      });

      const fileReader = new FileReader();
      fileReader.onload = () => {
        try {
          // 说明是普通对象数据，后台转换失败
          const jsonData = JSON.parse(fileReader.result);
          message.error(jsonData.message);
        } catch (err) {
          const contentDisposition = res.headers['content-disposition'];
          let fileName = decodeURIComponent(contentDisposition.substring(contentDisposition.indexOf('=') + 1));
          fileName = fileName ? fileName.replace("utf-8''", '') + '.xlsx' : '模板下载.xlsx';

          // 解析对象失败，说明是正常的文件流
          let blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.style.display = 'none';
          a.href = url;
          a.download = fileName;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }
      };
      fileReader.readAsText(res?.data);
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
<style lang="less" scoped>
.editable-row-operations a {
  margin-right: 8px;
}

:deep(.table-edit-form__item) {
  margin-bottom: 0;
}

.tabpane__background {
  background-color: #ececec;
  padding: 15px;

  :deep(.ant-card) {
    border-radius: 8px;
    &:not(:last-child) {
      margin-bottom: 15px;
    }
  }
}
</style>
