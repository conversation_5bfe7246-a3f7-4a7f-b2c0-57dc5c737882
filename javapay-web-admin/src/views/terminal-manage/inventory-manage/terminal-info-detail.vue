<template>
  <a-modal
    :width="800"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
    :footer="null"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="所属代理商编号">{{ form.agentNo }}</a-descriptions-item>
      <a-descriptions-item label="所属一代代理商编号">{{ form.oneLevelAgentNo }}</a-descriptions-item>
      <a-descriptions-item label="终端编号">{{ form.terminalNo }}</a-descriptions-item>

      <a-descriptions-item label="终端SN序列号">{{ form.terminalSn }}</a-descriptions-item>
      <a-descriptions-item label="终端使用状态">
        <span v-if="form.useStatus === 0" class="pink">闲置</span>
        <span v-else-if="form.useStatus === 1" class="blue">已分配</span>
        <span v-else-if="form.useStatus === 2" class="puple">已绑定</span>
      </a-descriptions-item>
      <a-descriptions-item label="是否活动终端">
        <a-tag v-if="form.termActiveSwitch === 1" color="green">是</a-tag>
        <a-tag v-else-if="form.termActiveSwitch === 0">否</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="商户号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="渠道商户号">{{ form.chnMerchNo }}</a-descriptions-item>
      <a-descriptions-item label="厂商编号">{{ form.factoryCode }}</a-descriptions-item>
      <a-descriptions-item label="厂商名称">{{ form.factoryName }}</a-descriptions-item>
      <a-descriptions-item label="终端型号">{{ form.modelName }}</a-descriptions-item>
      <a-descriptions-item label="终端来源">
        <span v-if="form.terminalSource === 1" class="pink">全款机</span>
        <span v-else-if="form.terminalSource === 2" class="blue">分期机</span>
      </a-descriptions-item>

      <a-descriptions-item label="通道名称">{{ form.channelName }}</a-descriptions-item>
      <a-descriptions-item label="押金活动金额">{{ form.serviceFeePolicyName }}</a-descriptions-item>

      <a-descriptions-item label="流量费免费使用天数">{{ form.simFreeDay }}</a-descriptions-item>
      <a-descriptions-item label="流量费收取开关">
        <span v-if="form.simSwitch === 1" class="ele-text-success">开启</span>
        <span v-else class="ele-text-danger">关闭</span>
      </a-descriptions-item>
      <template v-if="form.simSwitch === 1">
        <a-descriptions-item label="一期流量费金额"> {{ form.firstSimFee }} </a-descriptions-item>
        <a-descriptions-item label="一期流量费权益天数"> {{ form.firstSimPeriodDay }} </a-descriptions-item>
        <a-descriptions-item label="二期流量费金额"> {{ form.secondSimFee }} </a-descriptions-item>
        <a-descriptions-item label="二期流量费权益天数"> {{ form.secondSimPeriodDay }} </a-descriptions-item>
        <a-descriptions-item label="三期流量费金额"> {{ form.thirdSimFee }} </a-descriptions-item>
        <a-descriptions-item label="三期流量费权益天数"> {{ form.thirdSimPeriodDay }} </a-descriptions-item>
        <a-descriptions-item label="标准期流量费金额"> {{ form.fourthSimFee }} </a-descriptions-item>
        <a-descriptions-item label="标准期流量费权益天数"> {{ form.fourthSimPeriodDay }} </a-descriptions-item>
      </template>
      <a-descriptions-item label="创建人">{{ form.createUserId }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ toDateString(form.createTime) }}</a-descriptions-item>
      <a-descriptions-item label="最后修改人">{{ form.lastModifyUserId }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ toDateString(form.lastModifyTime) }}</a-descriptions-item>
    </a-descriptions>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';
import { toDateString } from 'ele-admin-pro';
import { ServiceFeePolicyApi } from '@/api/businessTeam/activity-Config/ServiceFeePolicyApi';
import { SimFeePolicyApi } from '@/api/businessTeam/activity-Config/SimFeePolicyApi';

export default {
  name: 'TerminalInfoDetail',
  props: {
    visible: Boolean,
    detail: Object,
    channelCodes: Array
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {},
      serviceFeePolicyList: [],
      simFeePolicyList: []
    });

    const watch = watchEffect(() => {
      getServiceFeePolicyList();
      getSimFeePolicyList();
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
        const channelItem = props.channelCodes.find(m => m.channelCode === data.form.channelCode);
        channelItem && (data.form.channelName = channelItem.channelName);
      }
    });

    //获取服务费政策列表
    async function getServiceFeePolicyList() {
      const result = await ServiceFeePolicyApi.list({ orgNo: props.detail.agentNo, orgType: 3 });
      data.serviceFeePolicyList = result || [];

      const serviceFeeItem = data.serviceFeePolicyList.find(m => m.configId === data.form.serviceFeeId);
      serviceFeeItem && (data.form.serviceFeePolicyName = serviceFeeItem.policyName);
    }

    //获取流量费政策列表
    async function getSimFeePolicyList() {
      const result = await SimFeePolicyApi.list({ orgNo: props.detail.agentNo, orgType: 3 });
      data.simFeePolicyList = result || [];

      const simFeeItem = data.simFeePolicyList.find(m => m.configId === data.form.simFeeId);
      simFeeItem && (data.form.simFeePolicyName = simFeeItem.policyName);
    }

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible,
      toDateString
    };
  }
};
</script>
