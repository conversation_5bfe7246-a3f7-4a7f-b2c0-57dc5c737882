<template>
  <a-modal
    :width="900"
    :visible="visible"
    :confirm-loading="loading"
    title="费率修改"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @cancel="cancel"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 7 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 17 }, sm: { span: 24 } }"
    >
      <a-divider orientationMargin="0" orientation="left" dashed>商户结算费率信息</a-divider>
      <a-row :gutter="24">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="支付机构" name="channelCode">
            <a-select v-model:value="form.channelCode" placeholder="请选择" @change="channelChanged">
              <a-select-option v-for="(item, key) in channelCodes" :key="key" :value="item.channelCode">{{
                item.channelName
              }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="商户结算政策" name="policyId">
            <a-select
              v-model:value="form.policyId"
              placeholder="请选择"
              @change="ratePolicyValueChanged"
              :disabled="!(form.channelCode != null && form.agentNo != null)"
            >
              <a-select-option v-for="(item, key) in merchRatePolicyList" :key="key" :value="item.id">{{
                item.policyDesc
              }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <a-form
        ref="rateForm"
        :model="rateInfoDTO"
        :label-col="{ md: { span: 17 }, sm: { span: 24 } }"
        :wrapper-col="{ md: { span: 7 }, sm: { span: 24 } }"
      >
        <!-- 费率相关 -->
        <RateModule :rate-item="rateInfoDTO" :bankList="[]" :disabled="false" />
      </a-form>
    </a-form>
  </a-modal>
</template>

<script>
import { InventoryManageApi } from '@/api/terminal-manage/InventoryManageApi';
import { message } from 'ant-design-vue';
import { RatePolicyApi } from '@/api/businessTeam/rate-policy/RatePolicyApi';
import { RateApi } from '@/api/businessTeam/rate/RateApi';
import RateModule from '../../business-team/_components/RateModule.vue';
import { deepCopy } from '@/utils/util';

function formDefaults() {
  return {
    channelCode: null
  };
}

export default {
  name: 'TerminalRateInfoEdit',
  components: {
    RateModule
  },
  props: {
    visible: Boolean,
    data: Object,
    policyList: Array,
    channelCodes: Array
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: formDefaults(),
      // 表单验证规则
      rules: {
        policyId: [{ required: true, message: '请选择' }],
        channelCode: [{ required: true, message: '请选择' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false,

      //费率政策列表
      merchRatePolicyList: [],
      //固定的选择的费率
      fixedRateInfoDTO: {},
      //费率数据
      rateInfoDTO: {},
      //选择的代理商模版编号
      templateNo: ''
    };
  },
  watch: {
    'form.channelCode'(newVal, oldVal) {
      if (oldVal) {
        this.form.policyId = '';
      }
      this.merchRatePolicyList = [];
      this.getAgentRateList();
    }
  },
  // watch: {
  //   data() {
  //     if (this.data) {
  //       this.form = Object.assign({}, this.data);
  //       this.rateInfoDTO = JSON.parse(this.form.rateInfo);
  //       this.isUpdate = true;

  //       //获取代理商信息,得到templateNo
  //       this.getRateInfo();
  //       //获取代理商费率列表
  //       this.getAgentRateList();
  //     } else {
  //       this.form = formDefaults();
  //       this.rateInfoDTO = {};
  //       this.isUpdate = false;
  //     }
  //     if (this.$refs.form) {
  //       this.$refs.form.clearValidate();
  //     }
  //   }
  // },
  mounted() {
    if (this.data) {
      this.form = Object.assign(formDefaults(), this.data);
      this.rateInfoDTO = JSON.parse(this.form.rateInfo) || {};

      //记录一个固定的值,用来提交时候的比较判断
      this.fixedRateInfoDTO = deepCopy(this.rateInfoDTO);
      this.isUpdate = true;

      //获取代理商信息,得到templateNo
      this.getRateInfo();
      //获取代理商费率列表
      // this.getAgentRateList();
    } else {
      this.form = formDefaults();
      this.rateInfoDTO = {};
      this.isUpdate = false;
    }
    if (this.$refs.form) {
      this.$refs.form.clearValidate();
    }

    this.merchRatePolicyList = [];
    this.getAgentRateList();
  },
  methods: {
    //支付机构修改了
    channelChanged() {
      //换了支付机构,重新获取templateNo
      this.getRateInfo();
    },

    //费率政策选值了
    ratePolicyValueChanged(value, { key }) {
      const selectedPolicy = this.merchRatePolicyList.find(item => item.id === value);
      //设置一个固定的值,用来提交时候的比较判断
      this.fixedRateInfoDTO = deepCopy(selectedPolicy.rateDTOList[0]);
      //换了一个费率之后,默认为1 相同的
      this.fixedRateInfoDTO.isSame = 1;
      this.rateInfoDTO = selectedPolicy.rateDTOList[0];

      this.form.policyNo = this.merchRatePolicyList[key].policyNo;
    },

    //获取代理商费率信息
    async getRateInfo() {
      const data = await RateApi.rateDetail({ channelCode: this.form.channelCode, userNo: this.form.agentNo, rateType: 1 });
      this.templateNo = data.templateNo || '';
    },

    //代理商费率政策列表,这个只是判断代理商是否开通了他自身的政策
    async getAgentRateList() {
      let data = await RateApi.list({
        channelCode: this.form.channelCode,
        userNo: this.form.agentNo,
        validStatus: 1
      });
      //对数据进行筛选,只有rateType === 1的项目才保留
      let rateAgentData = data || [];
      rateAgentData = rateAgentData.filter(rateData => rateData.rateType === 1);

      if (rateAgentData.length === 0) {
        message.error('该代理商没有开通所选通道的业务!');
        return;
      }
      //如果开通了,再去请求商户费率政策列表
      this.getRateMerchTypeList();
    },

    //商户费率政策列表,这个是用来下面的商户结算政策选择的
    async getRateMerchTypeList() {
      //因为只有一代才有商户结算政策,所以都用一代的
      let data = await RatePolicyApi.listOfOrg({
        channelCode: this.form.channelCode,
        userNo: this.form.oneLevelAgentNo,
        userType: 3,
        policyType: 2
      });
      //对数据进行筛选,二次筛选,只有rateType === 1的项目才保留,只有有这个项目的data的项目才保留
      let ratePolicyData = data || [];
      ratePolicyData = ratePolicyData.filter(ratePolicy => {
        const filteredRatePolicy = ratePolicy.rateDTOList.filter(rateData => rateData.rateType === 1);
        return filteredRatePolicy.length > 0;
      });

      this.merchRatePolicyList = ratePolicyData || [];
      //没有商户费率政策的话,要去引导开通商户费率政策
      if (this.merchRatePolicyList.length === 0) {
        this.showAddMerchRateBtn = true;
      }
    },

    // 检查两个对象的 rateInfoDTO 字段是否相同
    compareRateInfoDTO(obj1, obj2) {
      // 获取两个对象的 rateInfoDTO 字段
      const rateInfoDTO1 = obj1.rateInfoDTO;
      const rateInfoDTO2 = obj2.rateInfoDTO;

      // 比较 rateInfoDTO 字段中的所有值是否相同
      for (let key in rateInfoDTO1) {
        if (rateInfoDTO1[key] !== rateInfoDTO2[key]) {
          return false; // 有不相同的值，返回 false
        }
      }
      return true; // 所有值都相同，返回 true
    },

    async save() {
      // 校验表单
      await this.$refs.rateForm.validate();

      // 修改加载框为正在加载
      this.loading = true;

      this.rateInfoDTO.templateNo = this.templateNo;

      //只有之前就相同的才用去比较,意思是如果是不同的那么就继续是不同,不用比较
      if (this.fixedRateInfoDTO.isSame === 1) {
        if (Object.keys(this.fixedRateInfoDTO).length !== 0) {
          //判断费率值有没有修改
          const isSame = this.compareRateInfoDTO(this.rateInfoDTO, this.fixedRateInfoDTO);
          isSame === false ? (this.rateInfoDTO.isSame = 0) : (this.rateInfoDTO.isSame = 1);
        }
      }

      this.form.rateInfo = this.rateInfoDTO;
      // 执行编辑或修改方法
      let result = InventoryManageApi.editRateInfo({
        id: this.form.id,
        policyNo: this.form.policyNo,
        policyId: this.form.policyId,
        channelCode: this.form.channelCode,
        rateInfo: this.form.rateInfo
      });

      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 如果是新增，则form表单置空
          if (!this.isUpdate) {
            this.form = formDefaults();
            this.rateInfoDTO = {};
          }

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    cancel() {
      this.form = Object.assign(formDefaults(), this.data);
      this.rateInfoDTO = JSON.parse(this.form.rateInfo);
      this.$refs.form.clearValidate();
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
