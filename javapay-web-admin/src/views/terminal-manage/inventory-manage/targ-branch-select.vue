<template>
  <a-modal
    :width="1000"
    :visible="visible"
    title="运营中心选择"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <div class="ele-body">
      <!-- 搜索框内容 -->
      <div>
        <a-card :bordered="false">
          <a-form layout="inline" :model="where">
            <a-row :gutter="[0, 16]">
              <a-form-item label="运营中心名称">
                <a-input v-model:value.trim="where.agentName" placeholder="请输入运营中心名称" allow-clear />
              </a-form-item>
              <a-form-item label="运营中心编号">
                <a-input v-model:value.trim="where.agentNo" placeholder="请输入运营中心编号" allow-clear />
              </a-form-item>

              <a-form-item class="ele-text-center">
                <a-space>
                  <a-button type="primary" @click="reload">查询</a-button>
                  <a-button @click="reset">重置</a-button>
                </a-space>
              </a-form-item>
            </a-row>
          </a-form>
        </a-card>
      </div>

      <!-- 表格内容 -->
      <div>
        <a-card :bordered="false">
          <ele-pro-table
            ref="table"
            row-key="id"
            :datasource="datasource"
            :columns="columns"
            :where="where"
            v-model:current="selectedRow"
            :scroll="{ x: 'max-content' }"
          >
            <!-- 表体的操作 -->
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'remitType'">
                <a-tag v-if="record.remitType === 1" color="cyan">平台清算</a-tag>
                <a-tag v-else-if="record.remitType === 2" color="pink">自行提现</a-tag>
              </template>
              <template v-else-if="column.key === 'settleMode'">
                <a-tag v-if="record.settleMode === 1" color="cyan">日结</a-tag>
                <a-tag v-else-if="record.settleMode === 2" color="blue">月结</a-tag>
              </template>
            </template>
          </ele-pro-table>
        </a-card>
      </div>
    </div>
  </a-modal>
</template>

<script>
import { OperationCenterApi } from '@/api/businessTeam/operation-center/OperationCenterApi';

export default {
  props: {
    // 弹窗是否打开
    visible: Boolean
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      //表格查询条件
      where: {},
      //选择数据
      selectedRow: null,
      // 表格列配置
      columns: [
        {
          title: '运营中心编号',
          dataIndex: 'branchNo',
          align: 'center'
        },
        {
          title: '运营中心名称',
          dataIndex: 'branchName'
        },
        {
          title: '运营中心简称',
          dataIndex: 'branchSname'
        },
        {
          title: '费率政策',
          dataIndex: 'policyDesc'
        },
        {
          title: '法人姓名',
          dataIndex: 'legalName',
          align: 'center'
        },
        {
          title: '法人电话号码',
          dataIndex: 'legalTelMask',
          align: 'center'
        },
        {
          title: '联系人姓名',
          dataIndex: 'contactsName',
          align: 'center'
        },
        {
          title: '联系人电话号码',
          dataIndex: 'contactsTelMask',
          align: 'center'
        },
        {
          title: '清算类型',
          dataIndex: 'remitType',
          key: 'remitType',
          align: 'center',
          width: 100
        },
        {
          title: '结算方式',
          dataIndex: 'settleMode',
          key: 'settleMode',
          align: 'center',
          width: 100
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        }
      ]
    };
  },
  methods: {
    save() {
      this.$emit('done', this.selectedRow);
      this.updateVisible(false);
    },

    reset() {
      this.where = {};
      this.selectedRow = null;
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    reload() {
      this.selectedRow = null;
      this.$refs.table.reload({ page: 1 });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    },

    datasource({ page, limit, where }) {
      const regionNo = localStorage.getItem('SASS_ORG_CODE');
      return OperationCenterApi.findBranchPage({ ...where, pageNo: page, pageSize: limit, regionNo });
    }
  }
};
</script>

<style></style>
