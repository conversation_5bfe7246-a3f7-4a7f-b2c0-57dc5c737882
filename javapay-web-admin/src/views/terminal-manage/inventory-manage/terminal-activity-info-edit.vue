<template>
  <a-modal
    :width="1000"
    :visible="visible"
    :confirm-loading="loading"
    title="修改营销活动信息"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 7 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 17 }, sm: { span: 24 } }"
    >
      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="终端来源" name="terminalSource">
            <a-select v-model:value="form.terminalSource" placeholder="请选择" disabled>
              <a-select-option :value="1">全款机</a-select-option>
              <a-select-option :value="2">分期机</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="流量费收取开关" name="simSwitch">
            <a-radio-group v-model:value="form.simSwitch" name="simSwitch" :disabled="isSubAgent">
              <a-radio :value="1">开启</a-radio>
              <a-radio :value="0">关闭</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="押金" name="serviceFeeId">
            <a-select v-model:value="form.serviceFeeId" placeholder="请选择" :disabled="!form.agentNo" allow-clear>
              <a-select-option v-for="(item, key) in serviceFeePolicyList" :key="key" :value="item.configId">{{
                item.policyName
              }}</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="免费使用天数" name="simFreeDay" v-if="form.simSwitch === 1">
            <a-input v-model:value.trim="form.simFreeDay" placeholder="请输入免费使用天数" allow-clear :disabled="isSubAgent" />
          </a-form-item>
        </a-col>
      </a-row>

      <template v-if="form.simSwitch === 1">
        <a-row :gutter="16" v-for="(item, key) in form.simFeePeriodConfigDTO || []" :key="key">
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item
              :label="`${item.periodName}流量费金额`"
              :name="['simFeePeriodConfigDTO', key, item.periodIdField]"
              :rules="rules.simFeeId"
            >
              <a-select v-model:value="item[item.periodIdField]" placeholder="请选择" :disabled="isSubAgent">
                <a-select-option
                  v-for="(option, keyo) in simFeePolicyMap[item.periodIdOptionsField] || []"
                  :key="keyo"
                  :value="option.configId"
                >
                  {{ option.policyName }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item
              :label="`${item.periodName}权益天数`"
              :name="['simFeePeriodConfigDTO', key, item.periodDayField]"
              :rules="rules.simFeeDay"
            >
              <a-select v-model:value="item[item.periodDayField]" placeholder="请选择" :disabled="isSubAgent">
                <a-select-option v-for="(day, keyd) in [180, 210, 240, 270, 300, 360]" :key="keyd" :value="day"
                >{{ day }}天</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </template>
    </a-form>
  </a-modal>
</template>

<script>
import { InventoryManageApi } from '@/api/terminal-manage/InventoryManageApi';
import { message } from 'ant-design-vue';
import { ServiceFeePolicyApi } from '@/api/businessTeam/activity-Config/ServiceFeePolicyApi';
import { SimFeePolicyApi } from '@/api/businessTeam/activity-Config/SimFeePolicyApi';
import { hasPurview } from '@/utils/permission';
import { deepCopy } from '@/utils/util';

const simFeePeriodConfigDef = [
  {
    periodName: '一期',
    periodIdField: 'firstSimFeeId',
    periodFeeField: 'firstSimFee',
    periodDayField: 'firstSimPeriodDay',
    periodIdOptionsField: 'firstPeriodList',
    firstSimFeeId: null,
    firstSimPeriodDay: null
  },
  {
    periodName: '二期',
    periodIdField: 'secondSimFeeId',
    periodFeeField: 'secondSimFee',
    periodDayField: 'secondSimPeriodDay',
    periodIdOptionsField: 'secondPeriodList',
    secondSimFeeId: null,
    secondSimPeriodDay: null
  },
  {
    periodName: '三期',
    periodIdField: 'thirdSimFeeId',
    periodFeeField: 'thirdSimFee',
    periodDayField: 'thirdSimPeriodDay',
    periodIdOptionsField: 'thirdPeriodList',
    thirdSimFeeId: null,
    thirdSimPeriodDay: null
  },
  {
    periodName: '标准期',
    periodIdField: 'fourthSimFeeId',
    periodFeeField: 'fourthSimFee',
    periodDayField: 'fourthSimPeriodDay',
    periodIdOptionsField: 'fourthPeriodList',
    fourthSimFeeId: null,
    fourthSimPeriodDay: null
  }
];

function formDefaults() {
  return {};
}

export default {
  name: 'TerminalActivityInfoEdit',
  props: {
    visible: Boolean,
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: formDefaults(),
      //服务费政策数组
      serviceFeePolicyList: [],
      //流量费政策数组
      simFeePolicyMap: {},
      // 表单验证规则
      rules: {
        serviceFeeId: [{ required: true, message: '请选择' }],
        simSwitch: [{ required: true, message: '请选择' }],
        simFeeId: [{ required: true, message: '请选择' }],
        simFeeDay: [{ required: true, message: '请选择' }],
        simFreeDay: [{ required: true, message: '请输入免费使用天数' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false,
      isSubAgent: hasPurview('5')
    };
  },
  async created() {
    if (this.data) {
      this.form = Object.assign({}, this.data);

      await Promise.all([this.getServiceFeePolicyList(), this.getSimFeePolicyList()]);

      const serviceItem = this.serviceFeePolicyList.find(item => Number(item.amount) === Number(this.form.serviceFee));
      this.form.serviceFeeId = serviceItem?.configId || null;

      const simFeePeriodConfigDTO = deepCopy(simFeePeriodConfigDef);
      simFeePeriodConfigDTO.forEach(item => {
        const simFeeItem = this.simFeePolicyMap[item.periodIdOptionsField].find(
          option => Number(option.simFeeAmt) === Number(this.form[item.periodFeeField])
        );
        this.form[item.periodIdField] = simFeeItem?.configId || null;

        item[item.periodIdField] = this.form[item.periodIdField] || null;
        item[item.periodDayField] = this.form[item.periodDayField] || null;
      });
      this.form.simFeePeriodConfigDTO = simFeePeriodConfigDTO;

      this.isUpdate = true;
    } else {
      this.form = formDefaults();
      this.isUpdate = false;
    }
  },
  methods: {
    //获取服务费政策列表
    async getServiceFeePolicyList() {
      let data = await ServiceFeePolicyApi.list({ orgNo: this.form.agentNo, orgType: 3 });
      data = data || [];
      this.serviceFeePolicyList = data.filter(
        item => item.terminalSource === this.form.terminalSource && item.channelCode === this.form.channelCode
      );
    },

    //获取流量费政策列表
    async getSimFeePolicyList() {
      let data = await SimFeePolicyApi.list({ orgNo: this.form.agentNo, orgType: 3 });
      data = data || {};
      Object.keys(data).forEach(key => {
        data[key] =
          data[key]?.filter(item => item.terminalSource === this.form.terminalSource && item.channelCode === this.form.channelCode) || [];
      });
      this.simFeePolicyMap = data || {};
    },

    async save() {
      // 校验表单
      await this.$refs.form.validate();
      // 修改加载框为正在加载
      this.loading = true;
      let submitForm = {};
      const serviceFeeItem = this.serviceFeePolicyList.find(item => item.configId === this.form.serviceFeeId);
      if (this.form.simSwitch) {
        //打开的就全都传
        submitForm = {
          id: this.form.id,
          serviceFeeId: this.form.serviceFeeId,
          serviceFee: serviceFeeItem?.amount,
          simSwitch: this.form.simSwitch,
          simFreeDay: this.form.simFreeDay,
          channelCode: this.form.channelCode
        };
        this.form.simFeePeriodConfigDTO.forEach(item => {
          submitForm[item.periodIdField] = item[item.periodIdField];
          submitForm[item.periodDayField] = item[item.periodDayField];

          const simFeeItem = this.simFeePolicyMap[item.periodIdOptionsField].find(option => option.configId === item[item.periodIdField]);
          submitForm[item.periodFeeField] = simFeeItem?.simFeeAmt;
        });
      } else {
        submitForm = {
          id: this.form.id,
          serviceFeeId: this.form.serviceFeeId,
          serviceFee: serviceFeeItem?.amount,
          simSwitch: this.form.simSwitch,
          channelCode: this.form.channelCode
        };
      }

      // 执行编辑或修改方法
      let result = InventoryManageApi.editActivityInfo(submitForm);

      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
