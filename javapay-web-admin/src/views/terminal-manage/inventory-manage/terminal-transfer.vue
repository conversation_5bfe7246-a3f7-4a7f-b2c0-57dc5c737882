<template>
  <a-modal
    :width="900"
    :visible="visible"
    :confirm-loading="loading"
    title="终端下发"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 7 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 17 }, sm: { span: 24 } }"
    >
      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="支付通道" name="channelCode">
            <a-select v-model:value="form.channelCode" class="ele-fluid" placeholder="请选择" allow-clear @change="onChangeChannel">
              <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
                {{ channelName }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <!-- 运营中心和大区需要选择这个 -->
        <a-col :md="12" :sm="24" :xs="24" v-if="hasPurview(['1', '2'])">
          <a-form-item label="所属代理商" name="agentNo">
            <a-input v-model:value="form.agentNo" placeholder="请选择" @click="agentSelectMethod" :readonly="true" allow-clear />
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="指定代理商" name="targAgentNo">
            <a-input
              v-model:value="form.targAgentNo"
              placeholder="请选择"
              @click="targAgentSelectMethod"
              :readonly="true"
              :disabled="!form.channelCode"
              allow-clear
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="开始序列号" name="terminalSnStart">
            <a-input v-model:value.trim="form.terminalSnStart" placeholder="请输入开始序列号" allow-clear />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="结束序列号" name="terminalSnEnd">
            <a-input v-model:value.trim="form.terminalSnEnd" placeholder="请输入结束序列号" allow-clear />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>

  <!-- 代理商选择弹窗 -->
  <AgentSelect v-if="showAgentList" v-model:visible="showAgentList" @done="setAgentInfo" />
  <TargAgentSelect v-if="showTargAgentList" v-model:visible="showTargAgentList" @done="setTargAgentInfo" />
</template>

<script>
import { hasPurview } from '@/utils/permission';
import AgentSelect from '../../qrcodeCard/batch-manage/agent-select.vue';
import TargAgentSelect from './targ-agent-select.vue';
import { InventoryManageApi } from '@/api/terminal-manage/InventoryManageApi';
import { message } from 'ant-design-vue';
import { deepCopy } from '@/utils/util';
import { RateApi } from '@/api/businessTeam/rate/RateApi';
import { WHITE_BOARD_CHANNEL_CODE } from '@/config/setting';

export default {
  components: {
    AgentSelect,
    TargAgentSelect,
  },
  props: {
    visible: Boolean,
    data: Object,
    channelCodes: Array
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      form: {
        channelCode: null,
        termActiveSwitch: 0
      },
      // 表单验证规则
      rules: {
        agentNo: [{ required: true, message: '请选择所属代理商' }],
        targAgentNo: [{ required: true, message: '请选择指定代理商' }],
        terminalSnStart: [{ required: true, message: '请输入开始序列号' }],
        terminalSnEnd: [{ required: true, message: '请输入结束序列号' }],
        channelCode: [{ required: true, message: '请选择' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false,
      //是否展示所属代理商页面
      showAgentList: false,
      //是否展示指定代理商页面
      showTargAgentList: false,
      hasPurview,
    };
  },
  methods: {
    onChangeChannel() {
      this.form.targAgentNo = null;
    },
    //所属代理商选择完
    agentSelectMethod() {
      this.showAgentList = true;
    },

    //从所属代理商列表返回，配置相关数据
    setAgentInfo(info) {
      this.showAgentList = false;
      this.form.targAgentNo = info.agentNo;
    },

    //指定代理商选择完
    targAgentSelectMethod() {
      this.showTargAgentList = true;
      // if (hasPurview('5')) {
      //   //代理商的,选择的是直属下级
      //   this.showTargAgentList = true;
      // } else {
      //   //其它的选择下级就行
      //   this.showAgentList = true;
      // }
    },

    //从指定代理商列表返回，配置相关数据
    setTargAgentInfo(info) {
      this.showTargAgentList = false;
      this.form.targAgentNo = info.agentNo;

      if (hasPurview('3')) {
        //选择完代理商后,请求一下对应的费率政策列表
        this.getAgentRateList();
      }
    },

    async getAgentRateList() {
     // 白板机通道不判断
      if (this.form.channelCode === WHITE_BOARD_CHANNEL_CODE) {
        return;
      }

      let data = await RateApi.list({
        channelCode: this.form.channelCode,
        userNo: this.form.targAgentNo,
        validStatus: 1
      });
      //对数据进行筛选,只有rateType === 1的项目才保留
      let rateAgentData = data || [];
      rateAgentData = rateAgentData.filter(rateData => rateData.rateType === 1);

      if (rateAgentData.length === 0) {
        message.error('该代理商没有开通所选通道的业务!');
      }
    },

    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;
      let result = null;

      let params = deepCopy(this.form);

      // 执行调拨
      result = InventoryManageApi.transferTerminal(params);
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);
          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    /**
     * 更新编辑界面的弹框是否显示
     *
     * @param value true-显示，false-隐藏
     * <AUTHOR>
     * @date 2022/11/03 17:53
     */
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
