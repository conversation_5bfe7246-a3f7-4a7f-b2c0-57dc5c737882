<template>
  <div class="ele-body">
    <!-- 搜索框内容 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="终端编号">
              <a-input v-model:value.trim="where.terminalNo" placeholder="请输入终端编号" allow-clear />
            </a-form-item>

            <a-form-item label="终端SN序列号">
              <a-input v-model:value.trim="where.terminalSn" placeholder="请输入终端SN序列号" allow-clear />
            </a-form-item>

            <a-form-item label="商户号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户号" allow-clear />
            </a-form-item>

            <a-form-item label="渠道商户号">
              <a-input v-model:value.trim="where.chnMerchNo" placeholder="请输入渠道商户号" allow-clear />
            </a-form-item>

            <a-form-item label="所属大区编号" v-if="hasPurview(['0'])">
              <a-input v-model:value.trim="where.regionNo" placeholder="请输入所属大区编号" allow-clear />
            </a-form-item>
            <a-form-item label="所属运营中心编号" v-if="hasPurview(['0', '1'])">
              <a-input v-model:value.trim="where.branchNo" placeholder="请输入所属运营中心编号" allow-clear />
            </a-form-item>
            <a-form-item label="所属一代代理商编号" v-if="hasPurview(['0', '1', '2'])">
              <a-input v-model:value.trim="where.oneLevelAgentNo" placeholder="请输入所属一代代理商编号" allow-clear />
            </a-form-item>

            <a-form-item label="所属代理商编号">
              <a-input v-model:value.trim="where.agentNo" placeholder="请输入所属代理商编号" allow-clear />
            </a-form-item>

            <a-form-item label="设备厂商">
              <a-select v-model:value="where.factoryId" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="item.id" v-for="item in factoryList" :key="item.id">{{ item.factoryName }}</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="设备型号">
              <a-select v-model:value="where.modelId" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="item.id" v-for="item in terminalModelList" :key="item.id">{{ item.modelName }}</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="支付通道">
              <a-select
                v-model:value="where.channelCode"
                style="width: 200px"
                placeholder="请选择"
                allow-clear
                @change="where.policyId = null"
              >
                <a-select-option :value="item.channelCode" v-for="item in channelCodes" :key="item.id">{{
                  item.channelName
                }}</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="商户结算政策" v-purview="['2', '3', '5']">
              <a-select
                v-model:value="where.policyId"
                placeholder="请选择"
                allow-clear
                :disabled="!where.channelCode"
                style="width: 200px"
                @dropdownVisibleChange="open => open && getRateMerchTypeList()"
              >
                <a-select-option v-for="(item, key) in merchRatePolicyList" :key="key" :value="item.id">{{
                  item.policyDesc
                }}</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="终端使用状态">
              <a-select v-model:value="where.useStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">闲置</a-select-option>
                <a-select-option :value="1">已分配</a-select-option>
                <a-select-option :value="2">已绑定</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="流量费收取开关">
              <a-select v-model:value="where.simSwitch" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">关闭</a-select-option>
                <a-select-option :value="1">打开</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="激活奖励政策编号">
              <a-input v-model:value.trim="where.activePolicyNo" placeholder="请输入激活奖励政策编号" allow-clear />
            </a-form-item>
            <a-form-item label="达标奖励政策编号">
              <a-input v-model:value.trim="where.cashbackPolicyNo" placeholder="请输入达标奖励政策编号" allow-clear />
            </a-form-item>
            <a-form-item label="激活奖励开关">
              <a-select v-model:value="where.activeSwitch" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">关闭</a-select-option>
                <a-select-option :value="1">打开</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="激活状态">
              <a-select v-model:value="where.activeStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">初始</a-select-option>
                <a-select-option :value="1">达标</a-select-option>
                <a-select-option :value="2">不再达标</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="达标奖励开关">
              <a-select v-model:value="where.cashbackSwitch" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">关闭</a-select-option>
                <a-select-option :value="1">打开</a-select-option>
              </a-select>
            </a-form-item>
            <!-- <a-form-item label="达标奖励状态">
              <a-select v-model:value="where.cashbackStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">初始</a-select-option>
                <a-select-option :value="1">达标</a-select-option>
                <a-select-option :value="2">不再达标</a-select-option>
              </a-select>
            </a-form-item> -->
            <a-form-item label="达标计算方式">
              <a-select v-model:value="where.cashCalculateType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">交易量优先</a-select-option>
                <a-select-option :value="1">时间优先</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="未激活扣款开关">
              <a-select v-model:value="where.inactiveSwitch" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">关闭</a-select-option>
                <a-select-option :value="1">打开</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="未激活状态">
              <a-select v-model:value="where.inactiveStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">初始</a-select-option>
                <a-select-option :value="1">已扣款</a-select-option>
                <a-select-option :value="2">不再扣款</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="伪激活扣款开关">
              <a-select v-model:value="where.pseudoActiveSwitch" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">关闭</a-select-option>
                <a-select-option :value="1">打开</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="伪激活状态">
              <a-select v-model:value="where.pseudoActiveStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">初始</a-select-option>
                <a-select-option :value="1">已扣款</a-select-option>
                <a-select-option :value="2">不再扣款</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格内容 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- 表体的操作 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'validStatus'">
              <a-switch :checked="record.validStatus === 1" @change="checked => editValidStatus(checked, record)" />
            </template>

            <template v-else-if="column.key === 'terminalSource'">
              <a-tag v-if="record.terminalSource === 1" color="pink">自备机</a-tag>
              <a-tag v-else-if="record.terminalSource === 2" color="blue">定制机</a-tag>
            </template>

            <template v-else-if="column.key === 'useStatus'">
              <a-tag v-if="record.useStatus === 0" color="pink">闲置</a-tag>
              <a-tag v-else-if="record.useStatus === 1" color="blue">已分配</a-tag>
              <a-tag v-else-if="record.useStatus === 2" color="purple">已绑定</a-tag>
            </template>

            <template v-else-if="column.key === 'activeStatus'">
              <a-tag v-if="record.activeStatus === 0" color="pink">初始</a-tag>
              <a-tag v-else-if="record.activeStatus === 1" color="blue">达标</a-tag>
              <a-tag v-else-if="record.activeStatus === 2" color="purple">不再达标</a-tag>
            </template>
            <template v-else-if="column.key === 'inactiveStatus'">
              <a-tag v-if="record.inactiveStatus === 0" color="pink">初始</a-tag>
              <a-tag v-else-if="record.inactiveStatus === 1" color="blue">已扣款</a-tag>
              <a-tag v-else-if="record.inactiveStatus === 2" color="purple">不再扣款</a-tag>
            </template>
            <template v-else-if="column.key === 'pseudoActiveStatus'">
              <a-tag v-if="record.pseudoActiveStatus === 0" color="pink">初始</a-tag>
              <a-tag v-else-if="record.pseudoActiveStatus === 1" color="blue">已扣款</a-tag>
              <a-tag v-else-if="record.pseudoActiveStatus === 2" color="purple">不再扣款</a-tag>
            </template>

            <template v-else-if="column.key === 'simSwitch'">
              <a-tag v-if="record.simSwitch === 0" color="pink">关闭</a-tag>
              <a-tag v-else-if="record.simSwitch === 1" color="blue">开启</a-tag>
            </template>

            <template v-else-if="column.key === 'cashCalculateType'">
              <a-tag v-if="record.cashCalculateType === 0" color="pink">交易量优先</a-tag>
              <a-tag v-else-if="record.cashCalculateType === 1" color="blue">时间优先</a-tag>
            </template>

            <template v-else-if="['activeSwitch', 'cashbackSwitch', 'inactiveSwitch', 'pseudoActiveSwitch'].includes(column.key)">
              <a-tag v-if="record[column.key] === 0" color="pink">关闭</a-tag>
              <a-tag v-else-if="record[column.key] === 1" color="blue">开启</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleShowDetail(record)">详情</a>
                <a-dropdown>
                  <template #overlay>
                    <a-menu @click="({ key }) => handleShowRule(key, record)">
                      <a-menu-item v-if="record.activeSwitch === 1" :key="1">激活奖励规则</a-menu-item>
                      <a-menu-item v-if="record.cashbackSwitch === 1" :key="2">达标奖励规则</a-menu-item>
                    </a-menu>
                  </template>
                  <a v-if="[record.activeSwitch, record.cashbackSwitch].some(v => v === 1)">
                    <span>奖励规则</span>
                    <DownOutlined />
                  </a>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情页面 -->
    <TerminalInfoDetailActivity v-model:visible="showDetail" :channel-codes="channelCodes" :detail="current" v-if="showDetail" />

    <BonusRules v-model:visible="showRules" :detail="current" v-if="showRules" :ruleType="ruleType" />
  </div>
</template>

<script>
import { InventoryManageApi } from '@/api/terminal-manage/InventoryManageApi';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import { TerminalFactoryApi } from '@/api/qrcodeCard/TerminalFactoryApi';
import { TerminalModelApi } from '@/api/qrcodeCard/TerminalModelApi';
import { hasPurview } from '@/utils/permission';
import { ServiceFeePolicyApi } from '@/api/businessTeam/activity-Config/ServiceFeePolicyApi';
import { SimFeePolicyApi } from '@/api/businessTeam/activity-Config/SimFeePolicyApi';
import { RatePolicyApi } from '@/api/businessTeam/rate-policy/RatePolicyApi';
import { useUserStore } from '@/store/modules/user';
import { ActiveRuleConfigApi } from '@/api/businessTeam/active-rule-config/ActiveRuleConfigApi';
import TerminalInfoDetailActivity from './terminal-info-detail-activity.vue';
import BonusRules from './modules/bonus-rules.vue';

export default {
  name: 'InbentoryManageActivity',
  components: {
    TerminalInfoDetailActivity,
    BonusRules
  },
  data() {
    return {
      //表格查询条件
      where: {},
      //展示编辑页面传的参数
      current: null,
      //是否展示详情页面
      showDetail: false,
      showRules: false,
      ruleType: 1,

      selection: [],
      detail: null,

      //服务费政策数组
      serviceFeePolicyList: [],
      //流量费政策数组
      simFeePolicyList: [],
      //支付通道
      channelCodes: [],

      //厂商列表
      factoryList: [],
      //设备型号列表
      terminalModelList: [],
      merchRatePolicyList: [],

      hasPurview,
      //表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          hideCol: !hasPurview(['0']),
          width: 80,
          align: 'center'
        },
        {
          title: '所属大区编号',
          dataIndex: 'regionNo',
          align: 'center',
          hideCol: !hasPurview(['0'])
        },
        {
          title: '所属运营中心编号',
          dataIndex: 'branchNo',
          align: 'center',
          hideCol: !hasPurview(['0', '1'])
        },
        {
          title: '所属一级代理商编号',
          dataIndex: 'oneLevelAgentNo',
          align: 'center',
          hideCol: !hasPurview(['0', '1', '2'])
        },
        {
          title: '所属代理商编号',
          dataIndex: 'agentNo',
          align: 'center'
        },
        {
          title: '通道名称',
          dataIndex: 'channelCode',
          align: 'center',
          customRender: ({ text }) => {
            const item = this.channelCodes.find(c => c.channelCode === text);
            return item?.channelName || '--';
          }
        },
        {
          title: '终端SN号',
          dataIndex: 'terminalSn',
          align: 'center'
        },
        {
          title: '终端使用状态',
          dataIndex: 'useStatus',
          align: 'center',
          key: 'useStatus'
        },
        {
          title: '商户号',
          dataIndex: 'merchantNo',
          align: 'center'
        },

        {
          title: '未激活扣款开关',
          dataIndex: 'inactiveSwitch',
          key: 'inactiveSwitch',
          align: 'center'
        },
        {
          title: '未激活状态',
          dataIndex: 'inactiveStatus',
          key: 'inactiveStatus',
          align: 'center'
        },
        {
          title: '未激活配置ID',
          dataIndex: 'inactiveConfId',
          align: 'center'
        },
        {
          title: '未激活扣款金额(元)',
          dataIndex: 'inactiveDeductAmount',
          align: 'center'
        },
        {
          title: '未激活周期-开始时间',
          dataIndex: 'inactiveBeginTime',
          align: 'center'
        },
        {
          title: '未激活周期-结束时间',
          dataIndex: 'inactiveEndTime',
          align: 'center'
        },

        {
          title: '伪激活扣款开关',
          dataIndex: 'pseudoActiveSwitch',
          key: 'pseudoActiveSwitch',
          align: 'center'
        },
        {
          title: '伪激活状态',
          dataIndex: 'pseudoActiveStatus',
          key: 'pseudoActiveStatus',
          align: 'center'
        },
        {
          title: '伪激活配置ID',
          dataIndex: 'pseudoActiveConfId',
          align: 'center'
        },
        {
          title: '伪激活扣款金额(元)',
          dataIndex: 'pseudoActiveDeductAmount',
          align: 'center'
        },
        {
          title: '伪激活标准金额(元)',
          dataIndex: 'pseudoActiveTradeVolume',
          align: 'center'
        },
        {
          title: '伪激活周期-开始时间',
          dataIndex: 'pseudoActiveBeginTime',
          align: 'center'
        },
        {
          title: '伪激活周期-结束时间',
          dataIndex: 'pseudoActiveEndTime',
          align: 'center'
        },
        {
          title: '激活奖励开关',
          dataIndex: 'activeSwitch',
          key: 'activeSwitch',
          align: 'center'
        },
        {
          title: '激活状态',
          dataIndex: 'activeStatus',
          key: 'activeStatus',
          align: 'center'
        },
        {
          title: '激活奖励政策编号',
          dataIndex: 'activePolicyNo',
          align: 'center'
        },
        {
          title: '达标返现开关',
          dataIndex: 'cashbackSwitch',
          key: 'cashbackSwitch',
          align: 'center'
        },
        {
          title: '达标奖励政策编号',
          dataIndex: 'cashbackPolicyNo',
          align: 'center'
        },
        {
          title: '达标计算方式',
          dataIndex: 'cashCalculateType',
          key: 'cashCalculateType',
          align: 'center'
        },
        {
          title: '达标奖励交易类型',
          dataIndex: 'payMethods',
          key: 'payMethods',
          align: 'center',
          width: 200,
          customRender: ({ text, record }) => {
            if (text) {
              const text2arr = text.split(',');
              text2arr.forEach((item, index) => {
                text2arr[index] = payMethods[item];
              });
              record.payMethodsStr = text2arr.join(',');
              return record.payMethodsStr;
            }
            return '--';
          }
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          align: 'center'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime',
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          align: 'center',
          width: 160
        }
      ].filter(i => !i.hideCol)
    };
  },
  computed: {
    // 当前登录用户信息
    loginUser() {
      const userStore = useUserStore();
      return userStore.$state.info;
    }
  },
  mounted() {
    this.getChannelCodes();
    //获取服务费、流量费自身的活动政策列表
    this.getServiceFeeSelfOpenList();
    this.getSimFeeSelfOpenList();
    this.getFactoryList();
    this.getTerminalModelList();
  },
  methods: {
    handleShowRule(key, row) {
      this.ruleType = key;
      this.current = row;
      this.showRules = true;
    },
    async getRateMerchTypeList() {
      this.merchRatePolicyList = [];
      const agentNo = localStorage.getItem('SASS_ORG_CODE');
      let data = await RatePolicyApi.listOfOrg({
        channelCode: this.where.channelCode,
        userNo: agentNo,
        userType: Number(this.loginUser.userType),
        policyType: 2
      });
      //对数据进行筛选,二次筛选,只有rateType === 1的项目才保留,只有有这个项目的data的项目才保留
      let ratePolicyData = data || [];
      ratePolicyData = ratePolicyData.filter(ratePolicy => {
        const filteredRatePolicy = ratePolicy.rateDTOList.filter(rateData => rateData.rateType === 1);
        return filteredRatePolicy.length > 0;
      });

      this.merchRatePolicyList = ratePolicyData || [];
    },

    reload() {
      this.selection = [];
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.selection = [];
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    //展示详情
    handleShowDetail(row) {
      this.showDetail = true;
      this.current = row;
    },

    //获取通道编号列表
    async getChannelCodes() {
      const data = await ChannelManageApi.list({ validStatus: 1 });
      this.channelCodes = data || [];
    },

    //获取服务费政策列表
    async getServiceFeeSelfOpenList() {
      const data = await ServiceFeePolicyApi.selfOpenList();
      this.serviceFeePolicyList = data || [];
    },

    //获取流量费政策列表
    async getSimFeeSelfOpenList() {
      const data = await SimFeePolicyApi.selfOpenList();
      this.simFeePolicyList = data || [];
    },

    //厂商列表
    async getFactoryList() {
      const data = await TerminalFactoryApi.list({ validStatus: 1 });
      this.factoryList = data || [];
    },

    //设备型号列表
    async getTerminalModelList() {
      const data = await TerminalModelApi.list({ validStatus: 1 });
      this.terminalModelList = data || [];
    },

    //设备型号列表
    async getTerminalModelList2() {
      const data = await ActiveRuleConfigApi.list({ ruleType: 1, orgNo: '00000000' });
      this.terminalModelList = data || [];
    },

    //获取数据方法
    datasource({ page, limit, where }) {
      return InventoryManageApi.findPagesActive({ ...where, pageNo: page, pageSize: limit });
    }
  }
};

const payMethods = {
  1: '银联云闪付',
  2: '微信支付',
  3: '支付宝支付',
  4: 'EPOS支付',
  5: 'POS刷卡'
};
</script>

<style scoped lang="less">
::v-deep {
  .toolbar {
    .ant-btn {
      margin: 0 8px 8px 0;
    }
  }
}
</style>
