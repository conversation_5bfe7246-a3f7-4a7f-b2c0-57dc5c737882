<template>
  <a-modal
    :width="600"
    :visible="visible"
    :confirm-loading="loading"
    title="终端换绑"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules" :label-col="{ style: { width: '100px' } }">
      <a-form-item label="新终端SN" name="newTerminalSn">
        <a-input v-model:value="form.newTerminalSn" placeholder="请输入新终端SN" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { InventoryManageApi } from '@/api/terminal-manage/InventoryManageApi';

export default {
  props: {
    visible: Boolean,
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {},
      merchRatePolicyList: [],
      // 表单验证规则
      rules: {
        newTerminalSn: [{ required: true, message: '请输入新终端SN' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false,
      showAgentList: false
    };
  },
  mounted() {
    if (this.data) {
      this.form.id = this.data.id;
      this.form.channelCode = this.data.channelCode;
    }
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      InventoryManageApi.changeBindTermSn(this.form)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
