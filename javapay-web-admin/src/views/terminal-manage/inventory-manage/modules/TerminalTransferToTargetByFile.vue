<template>
  <a-modal
    :width="860"
    :visible="visible"
    :confirm-loading="loading"
    title="终端调拨"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules" :label-col="{ style: { width: '135px' } }">
      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="支付通道" name="channelCode">
            <a-select v-model:value="form.channelCode" class="ele-fluid" placeholder="请选择" allow-clear @change="onChangeChannel">
              <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">{{
                channelName
              }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="调拨文件" name="file">
        <a-upload-dragger
          :file-list="form.file ? [form.file] : []"
          accept=".xlsx,.xls"
          :maxCount="1"
          :before-upload="beforeUpload"
          @remove="handleRemove"
          style="padding: 24px 0"
        >
          <p class="ant-upload-drag-icon">
            <cloud-upload-outlined />
          </p>
          <p class="ant-upload-hint" style="margin-bottom: 10px">将文件拖到此处，或点击上传</p>
          <div class="ele-text-center">
            <a @click.stop="downloadTemplateFile">下载模板文件 </a>
          </div>
        </a-upload-dragger>
      </a-form-item>

      <a-form-item label="指定代理商" name="targetAgentNo">
        <a-input
          v-model:value="form.targetAgentNo"
          placeholder="请选择"
          @click="showAgentList = true"
          readonly
          :disabled="!form.channelCode"
        />
      </a-form-item>

      <a-form-item label="终端来源" name="terminalSource">
        <a-select v-model:value="form.terminalSource" placeholder="请选择" @change="onChangeTerminalSource" :disabled="!form.targetAgentNo">
          <a-select-option :value="1">全款机</a-select-option>
          <a-select-option :value="2">分期机</a-select-option>
        </a-select>
      </a-form-item>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="押金" name="serviceFeeId">
            <a-select v-model:value="form.serviceFeeId" placeholder="请选择" :disabled="!form.targetAgentNo" allow-clear>
              <a-select-option v-for="(item, key) in serviceFeePolicyList" :key="key" :value="item.configId">
                {{ item.policyName }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="免费使用天数" name="simFreeDay" v-if="form.simSwitch === 1">
            <a-input v-model:value.trim="form.simFreeDay" placeholder="请输入免费使用天数" allow-clear />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="流量费收取开关" name="simSwitch">
            <a-radio-group v-model:value="form.simSwitch" name="simSwitch">
              <a-radio :value="1">开启</a-radio>
              <a-radio :value="0">关闭</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
      </a-row>

      <template v-if="form.simSwitch === 1">
        <a-row :gutter="16" v-for="(item, key) in form.simFeePeriodConfigDTO || []" :key="key">
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item
              :label="`${item.periodName}流量费金额`"
              :name="['simFeePeriodConfigDTO', key, item.periodIdField]"
              :rules="rules.simFeeId"
            >
              <a-select v-model:value="item[item.periodIdField]" placeholder="请选择" :disabled="!form.targetAgentNo">
                <a-select-option
                  v-for="(option, keyo) in simFeePolicyMap[item.periodIdOptionsField] || []"
                  :key="keyo"
                  :value="option.configId"
                >
                  {{ option.policyName }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item
              :label="`${item.periodName}权益天数`"
              :name="['simFeePeriodConfigDTO', key, item.periodDayField]"
              :rules="rules.simFeeDay"
            >
              <a-select v-model:value="item[item.periodDayField]" placeholder="请选择" :disabled="!form.targetAgentNo">
                <a-select-option v-for="(day, keyd) in [180, 210, 240, 270, 300, 360]" :key="keyd" :value="day"
                >{{ day }}天</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </template>
    </a-form>

    <AgentSelect v-if="showAgentList" v-model:visible="showAgentList" @done="setAgentInfo" />
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { InventoryManageApi } from '@/api/terminal-manage/InventoryManageApi';
import AgentSelect from '../../../qrcodeCard/batch-manage/agent-select.vue';
import { deepCopy } from '@/utils/util';
import { ServiceFeePolicyApi } from '@/api/businessTeam/activity-Config/ServiceFeePolicyApi';
import { SimFeePolicyApi } from '@/api/businessTeam/activity-Config/SimFeePolicyApi';

const simFeePeriodConfigDef = [
  {
    periodName: '一期',
    periodIdField: 'firstSimFeeId',
    periodFeeField: 'firstSimFee',
    periodDayField: 'firstSimPeriodDay',
    periodIdOptionsField: 'firstPeriodList',
    firstSimFeeId: null,
    firstSimPeriodDay: null
  },
  {
    periodName: '二期',
    periodIdField: 'secondSimFeeId',
    periodFeeField: 'secondSimFee',
    periodDayField: 'secondSimPeriodDay',
    periodIdOptionsField: 'secondPeriodList',
    secondSimFeeId: null,
    secondSimPeriodDay: null
  },
  {
    periodName: '三期',
    periodIdField: 'thirdSimFeeId',
    periodFeeField: 'thirdSimFee',
    periodDayField: 'thirdSimPeriodDay',
    periodIdOptionsField: 'thirdPeriodList',
    thirdSimFeeId: null,
    thirdSimPeriodDay: null
  },
  {
    periodName: '标准期',
    periodIdField: 'fourthSimFeeId',
    periodFeeField: 'fourthSimFee',
    periodDayField: 'fourthSimPeriodDay',
    periodIdOptionsField: 'fourthPeriodList',
    fourthSimFeeId: null,
    fourthSimPeriodDay: null
  }
];

export default {
  components: { AgentSelect,  },
  props: {
    visible: Boolean,
    channelCodes: Array,
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {
        simSwitch: 1,
        terminalSource: 1,
        channelCode: null,
        simFeePeriodConfigDTO: deepCopy(simFeePeriodConfigDef)
      },
      // 表单验证规则
      rules: {
        file: [{ required: true, message: '请上传文件' }],
        targetAgentNo: [{ required: true, message: '请选择' }],
        serviceFeeId: [{ required: true, message: '请选择' }],
        simSwitch: [{ required: true, message: '请选择' }],
        simFeeId: [{ required: true, message: '请选择' }],
        simFeeDay: [{ required: true, message: '请选择' }],
        simFreeDay: [{ required: true, message: '请输入免费使用天数' }],
        channelCode: [{ required: true, message: '请选择' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false,
      showAgentList: false,
      serviceFeePolicyList: [],
      simFeePolicyMap: {}
    };
  },
  methods: {
    onChangeChannel() {
      this.form.policyId = null;

      if (this.form.targetAgentNo) {
        this.form.serviceFeeId = null;

        this.form.simFeePeriodConfigDTO.forEach(item => {
          item[item.periodIdField] = null;
          item[item.periodDayField] = null;
        });

        this.getServiceFeePolicyList();
        this.getSimFeePolicyList();
      }
    },

    onChangeTerminalSource() {
      this.form.serviceFeeId = null;

      this.form.simFeePeriodConfigDTO.forEach(item => {
        item[item.periodIdField] = null;
        item[item.periodDayField] = null;
      });

      this.getServiceFeePolicyList();
      this.getSimFeePolicyList();
    },

    async downloadTemplateFile() {
      const res = await InventoryManageApi.download({}).catch(error => {
        message.destroy();
        const reader = new FileReader(); //创建一个FileReader实例
        reader.readAsText(error, 'utf-8'); //读取文件,结果用字符串形式表示
        reader.onload = function () {
          const { message: errorMsg } = JSON.parse(reader.result);
          message.error(errorMsg || '下载失败');
        };
      });

      const fileReader = new FileReader();
      fileReader.onload = () => {
        try {
          // 说明是普通对象数据，后台转换失败
          const jsonData = JSON.parse(fileReader.result);
          message.error(jsonData.message);
        } catch (err) {
          const fileName = '终端文件调拨模板.xlsx';

          // 解析对象失败，说明是正常的文件流
          let blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.style.display = 'none';
          a.href = url;
          a.download = fileName;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }
      };
      fileReader.readAsText(res?.data);
    },
    beforeUpload(file) {
      this.form.file = file;
      return false;
    },

    handleRemove() {
      this.form.file = null;
    },
    //获取服务费政策列表
    async getServiceFeePolicyList() {
      this.serviceFeePolicyList = [];
      let data = await ServiceFeePolicyApi.list({ orgNo: this.form.targetAgentNo, orgType: 3 });
      data = data || [];
      this.serviceFeePolicyList = data.filter(
        item => item.terminalSource === this.form.terminalSource && item.channelCode === this.form.channelCode
      );
    },

    //获取流量费政策列表
    async getSimFeePolicyList() {
      let data = await SimFeePolicyApi.list({ orgNo: this.form.targetAgentNo, orgType: 3 });
      data = data || {};
      Object.keys(data).forEach(key => {
        data[key] =
          data[key]?.filter(item => item.terminalSource === this.form.terminalSource && item.channelCode === this.form.channelCode) || [];
      });
      this.simFeePolicyMap = data || {};
    },

    setAgentInfo(info) {
      this.showAgentList = false;
      this.form.targetAgentNo = info.agentNo;
      this.form.oneLevelAgentNo = info.oneLevelAgentNo;

      this.form.serviceFeeId = null;

      this.form.simFeePeriodConfigDTO.forEach(item => {
        item[item.periodIdField] = null;
        item[item.periodDayField] = null;
      });

      this.getServiceFeePolicyList();
      this.getSimFeePolicyList();
    },

    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let params = deepCopy(this.form);

      const serviceFeeItem = this.serviceFeePolicyList.find(item => item.configId === params.serviceFeeId);
      params.serviceFee = serviceFeeItem?.amount;

      if (!params.simSwitch) {
        params.simFreeDay = null;
        delete params.simFeePeriodConfigDTO;
      } else {
        const simFeePeriodConfigDTO = params.simFeePeriodConfigDTO;
        delete params.simFeePeriodConfigDTO;
        simFeePeriodConfigDTO.forEach(item => {
          params[item.periodIdField] = item[item.periodIdField];
          params[item.periodDayField] = item[item.periodDayField];

          const simFeeItem = this.simFeePolicyMap[item.periodIdOptionsField].find(feeItem => feeItem.configId === item[item.periodIdField]);
          params[item.periodFeeField] = simFeeItem?.simFeeAmt;
        });
      }

      const formData = new FormData();

      Object.keys(params).forEach(key => {
        params[key] !== null && formData.append(key, params[key]);
      });
      formData.append('file', this.form.file);

      InventoryManageApi.terminalTransferToTargetByFile(formData)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
