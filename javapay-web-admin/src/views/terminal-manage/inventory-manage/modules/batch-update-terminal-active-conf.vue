<template>
  <a-modal
    :width="1000"
    :visible="visible"
    title="批量更新营销活动配置"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules" :label-col="{ style: { width: '130px' } }">
      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24" :span="12">
          <a-form-item label="支付机构" name="channelCode">
            <a-select v-model:value="form.channelCode" placeholder="请选择" @change="onChangeChannel" allow-clear>
              <a-select-option v-for="(item, key) in channelCodes" :key="key" :value="item.channelCode">
                {{ item.channelName }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <!-- <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="指定代理商" name="agentNo">
            <a-input v-model:value="form.agentNo" placeholder="请选择" @click="onSelectAgent" readonly :disabled="hasPurview('5')" />
          </a-form-item>
        </a-col>
      </a-row> -->

      <div style="margin-bottom: 24px">
        <a-divider dashed />
      </div>

      <a-form-item label="指定终端方式">
        <a-radio-group v-model:value="terminalSelectType">
          <a-radio-button value="region">区间选择</a-radio-button>
          <a-radio-button value="file">文件上传</a-radio-button>
        </a-radio-group>
      </a-form-item>

      <!-- 区间 -->
      <a-row :gutter="16" v-if="terminalSelectType === 'region'">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="开始序列号" name="terminalSnStart">
            <a-input v-model:value.trim="form.terminalSnStart" placeholder="请输入开始序列号" allow-clear />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="结束序列号" name="terminalSnEnd">
            <a-input v-model:value.trim="form.terminalSnEnd" placeholder="请输入结束序列号" allow-clear />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 文件 -->
      <a-row :gutter="16" v-if="terminalSelectType === 'file'">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="终端文件" name="file">
            <a-upload-dragger
              :file-list="form.file ? [form.file] : []"
              accept=".xlsx,.xls"
              :maxCount="1"
              :before-upload="beforeUpload"
              @remove="handleRemove"
            >
              <p class="ant-upload-drag-icon">
                <cloud-upload-outlined />
              </p>
              <p class="ant-upload-hint" style="margin-bottom: 10px">将文件拖到此处，或点击上传</p>
              <div class="ele-text-center">
                <a @click.stop="downloadTemplateFile">下载模板文件 </a>
              </div>
            </a-upload-dragger>
          </a-form-item>
        </a-col>
      </a-row>

      <div style="margin-bottom: 24px">
        <a-divider dashed />
      </div>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="终端来源" name="terminalSource">
            <a-select
              v-model:value="form.terminalSource"
              placeholder="请选择"
              @change="onChangeTerminalSource"
              :disabled="hasPurview(['3'])"
            >
              <a-select-option :value="1">全款机</a-select-option>
              <a-select-option :value="2">分期机</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="修改类型" name="modifyTypeList">
            <a-select v-model:value="form.modifyTypeList" placeholder="可多选" mode="multiple" :disabled="!form.channelCode">
              <a-select-option :value="1">服务费</a-select-option>
              <a-select-option v-if="!hasPurview('5')" :value="2">流量费</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 押金流量费相关 -->
      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24" v-if="form.modifyTypeList?.includes(1)">
          <a-form-item label="押金" name="serviceFeeId">
            <a-select v-model:value="form.serviceFeeId" placeholder="请选择" allow-clear>
              <a-select-option v-for="(item, key) in serviceFeePolicyList" :key="key" :value="item.configId">
                {{ item.policyName }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24" v-if="form.modifyTypeList?.includes(2)">
          <a-form-item label="流量费收取开关" name="simSwitch">
            <a-radio-group v-model:value="form.simSwitch" name="simSwitch">
              <a-radio :value="1">开启</a-radio>
              <a-radio :value="0">关闭</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16" v-if="form.simSwitch === 1 && form.modifyTypeList?.includes(2)">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="免费使用天数" name="simFreeDay">
            <a-input-number v-model:value="form.simFreeDay" :min="0" placeholder="请输入免费使用天数" style="width: 100%" />
          </a-form-item>
        </a-col>
      </a-row>

      <template v-if="form.simSwitch === 1 && form.modifyTypeList?.includes(2)">
        <a-row :gutter="16" v-for="(item, key) in form.simFeePeriodConfigDTO || []" :key="key">
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item
              :label="`${item.periodName}流量费金额`"
              :name="['simFeePeriodConfigDTO', key, item.periodIdField]"
              :rules="rules.simFeeId"
            >
              <a-select v-model:value="item[item.periodIdField]" placeholder="请选择">
                <a-select-option
                  v-for="(option, keyo) in simFeePolicyMap[item.periodIdOptionsField] || []"
                  :key="keyo"
                  :value="option.configId"
                >
                  {{ option.policyName }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item
              :label="`${item.periodName}权益天数`"
              :name="['simFeePeriodConfigDTO', key, item.periodDayField]"
              :rules="rules.simFeeDay"
            >
              <a-select v-model:value="item[item.periodDayField]" placeholder="请选择">
                <a-select-option v-for="(day, keyd) in [180, 210, 240, 270, 300, 360]" :key="keyd" :value="day"
                >{{ day }}天</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </template>
    </a-form>
  </a-modal>

  <AgentSelect v-if="showSelectAgent" v-model:visible="showSelectAgent" @done="onSelectAgentFinish" />
</template>

<script>
import { message } from 'ant-design-vue';
import { omit } from 'lodash-es';
import { deepCopy } from '@/utils/util';
import { hasPurview } from '@/utils/permission';
import { InventoryManageApi } from '@/api/terminal-manage/InventoryManageApi';
import AgentSelect from '../targ-agent-select.vue';
import { ServiceFeePolicyApi } from '@/api/businessTeam/activity-Config/ServiceFeePolicyApi';
import { SimFeePolicyApi } from '@/api/businessTeam/activity-Config/SimFeePolicyApi';
import { RateApi } from '@/api/businessTeam/rate/RateApi';
import { WHITE_BOARD_CHANNEL_CODE } from '@/config/setting';

const simFeePeriodConfigDef = [
  {
    periodName: '一期',
    periodIdField: 'firstSimFeeId',
    periodFeeField: 'firstSimFee',
    periodDayField: 'firstSimPeriodDay',
    periodIdOptionsField: 'firstPeriodList',
    firstSimFeeId: null,
    firstSimPeriodDay: null
  },
  {
    periodName: '二期',
    periodIdField: 'secondSimFeeId',
    periodFeeField: 'secondSimFee',
    periodDayField: 'secondSimPeriodDay',
    periodIdOptionsField: 'secondPeriodList',
    secondSimFeeId: null,
    secondSimPeriodDay: null
  },
  {
    periodName: '三期',
    periodIdField: 'thirdSimFeeId',
    periodFeeField: 'thirdSimFee',
    periodDayField: 'thirdSimPeriodDay',
    periodIdOptionsField: 'thirdPeriodList',
    thirdSimFeeId: null,
    thirdSimPeriodDay: null
  },
  {
    periodName: '标准期',
    periodIdField: 'fourthSimFeeId',
    periodFeeField: 'fourthSimFee',
    periodDayField: 'fourthSimPeriodDay',
    periodIdOptionsField: 'fourthPeriodList',
    fourthSimFeeId: null,
    fourthSimPeriodDay: null
  }
];

function formDefaults() {
  return {
    terminalSource: 1,
    channelCode: null,
    simFeePeriodConfigDTO: deepCopy(simFeePeriodConfigDef)
  };
}

export default {
  components: {
    AgentSelect,
  },
  props: {
    visible: Boolean,
    channelCodes: Array
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      terminalSelectType: 'region',
      // 表单数据
      form: formDefaults(),
      // 表单验证规则
      rules: {
        agentNo: [{ required: true, message: '请选择' }],
        channelCode: [{ required: true, message: '请选择' }],
        modifyTypeList: [{ required: true, message: '请选择' }],
        terminalSnStart: [{ required: true, message: '请输入开始序列号' }],
        terminalSnEnd: [{ required: true, message: '请输入结束序列号' }],
        file: [{ required: true, message: '请上传文件' }],

        serviceFeeId: [{ required: true, message: '请选择' }],
        simSwitch: [{ required: true, message: '请选择' }],
        simFeeId: [{ required: true, message: '请选择' }],
        simFeeDay: [{ required: true, message: '请选择' }],
        simFreeDay: [{ required: true, message: '请输入免费使用天数' }],
        terminalSource: [{ required: true, message: '请选择' }],
      },
      // 提交状态
      loading: false,
      showSelectAgent: false,

      hasPurview,

      serviceFeePolicyList: [],
      simFeePolicyMap: [],
    };
  },
  methods: {
    onChangeChannel() {
      const agentNo = localStorage.getItem('SASS_ORG_CODE');
      this.onSelectAgentFinish({ agentNo });
    },
    onChangeTerminalSource() {
      this.form.serviceFeeId = null;

      this.form.simFeePeriodConfigDTO.forEach(item => {
        item[item.periodIdField] = null;
        item[item.periodDayField] = null;
      });

      this.getServiceFeePolicyList();
      this.getSimFeePolicyList();
    },

    onSelectAgent() {
      this.showSelectAgent = true;
    },

    onSelectAgentFinish(info) {
      if (!info) return;
      this.form.agentNo = info.agentNo;

      this.form.serviceFeeId = null;
      this.form.policyId = null;

      this.form.simFeePeriodConfigDTO.forEach(item => {
        item[item.periodIdField] = null;
        item[item.periodDayField] = null;
      });

      if (!hasPurview('1')) {
        //选择完代理商后,请求一下对应的费率政策列表
        this.getAgentRateList();
      }

      //选择完代理商后,请求一下服务费政策列表
      this.getServiceFeePolicyList();
      //选择完代理商后,请求一下流量费政策列表
      this.getSimFeePolicyList();
    },


    // 代理商费率政策列表,这个只是判断代理商是否开通了他自身的政策
    async getAgentRateList() {
       // 白板机通道不判断
      if (this.form.channelCode === WHITE_BOARD_CHANNEL_CODE) {
        return;
      }

      let data = await RateApi.list({
        channelCode: this.form.channelCode,
        userNo: this.form.agentNo,
        validStatus: 1
      });
      //对数据进行筛选,只有rateType === 1的项目才保留
      let rateAgentData = data || [];
      rateAgentData = rateAgentData.filter(rateData => rateData.rateType === 1);

      if (rateAgentData.length === 0) {
        message.error('该代理商没有开通相关业务!');
      }
    },

    async getServiceFeePolicyList() {
      this.serviceFeePolicyList = [];
      let data = await ServiceFeePolicyApi.list({ orgNo: this.form.agentNo, orgType: 3 });
      data = data || [];
      this.serviceFeePolicyList = data.filter(
        item => item.terminalSource === this.form.terminalSource && item.channelCode === this.form.channelCode
      );
    },

    async getSimFeePolicyList() {
      let data = await SimFeePolicyApi.list({ orgNo: this.form.agentNo, orgType: 3 });
      data = data || {};
      Object.keys(data).forEach(key => {
        data[key] =
          data[key]?.filter(item => item.terminalSource === this.form.terminalSource && item.channelCode === this.form.channelCode) || [];
      });
      this.simFeePolicyMap = data || {};
    },

    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let params = deepCopy(this.form);

      const serviceFeeItem = this.serviceFeePolicyList.find(item => item.configId === params.serviceFeeId);
      params.serviceFee = serviceFeeItem?.amount

      let formData = new FormData();

      params = omit(params, ['agentNo']);

      if (params.simSwitch !== 1) {
        params = omit(params, ['simFreeDay', 'simFeePeriodConfigDTO']);
      } else {
        const simFeePeriodConfigDTO = params.simFeePeriodConfigDTO;
        params = omit(params, ['simFeePeriodConfigDTO']);
        simFeePeriodConfigDTO.forEach(item => {
          params[item.periodIdField] = item[item.periodIdField];
          params[item.periodDayField] = item[item.periodDayField];

          const simFeeItem = this.simFeePolicyMap[item.periodIdOptionsField].find(feeItem => feeItem.configId === item[item.periodIdField]);
          params[item.periodFeeField] = simFeeItem?.simFeeAmt
        });
      }

      let submitMethod;

      // 区间
      if (this.terminalSelectType === 'region') {
        submitMethod = InventoryManageApi.batchUpdateTerminalActiveConf(params);
      }
      // 文件
      else if (this.terminalSelectType === 'file') {
        Object.keys(params).forEach(key => {
          if (params[key] !== null && key !== 'file') {
            formData.append(key, params[key]);
          }
        });

        formData.append('file', this.form.file);

        submitMethod = InventoryManageApi.batchUpdateTerminalActiveConfByFile(formData);
      }

      submitMethod
        .then(result => {
          this.loading = false;
          message.success(result.message);
          this.updateVisible(false);
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    beforeUpload(file) {
      this.form.file = file;
      return false;
    },

    handleRemove() {
      this.form.file = null;
    },

    async downloadTemplateFile() {
      const res = await InventoryManageApi.agentTransferTerminalTemplate({}).catch(error => {
        message.destroy();
        const reader = new FileReader(); //创建一个FileReader实例
        reader.readAsText(error, 'utf-8'); //读取文件,结果用字符串形式表示
        reader.onload = function () {
          const { message: errorMsg } = JSON.parse(reader.result);
          message.error(errorMsg || '下载失败');
        };
      });

      const fileReader = new FileReader();
      fileReader.onload = () => {
        try {
          // 说明是普通对象数据，后台转换失败
          const jsonData = JSON.parse(fileReader.result);
          message.error(jsonData.message);
        } catch (err) {
          const fileName = '终端文件模板.xlsx';

          // 解析对象失败，说明是正常的文件流
          let blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.style.display = 'none';
          a.href = url;
          a.download = fileName;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }
      };
      fileReader.readAsText(res?.data);
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
<style lang="less" scoped>
.editable-row-operations a {
  margin-right: 8px;
}

:deep(.table-edit-form__item) {
  margin-bottom: 0;
}

.tabpane__background {
  background-color: #ececec;
  padding: 15px;

  :deep(.ant-card) {
    border-radius: 8px;
    &:not(:last-child) {
      margin-bottom: 15px;
    }
  }
}
</style>
