<template>
  <a-modal
    :width="800"
    :visible="visible"
    :title="modalTitle[ruleType]"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2" bordered>
      <a-descriptions-item label="终端SN序列号">{{ form.terminalSn }}</a-descriptions-item>
    </a-descriptions>
    <!-- 规则 -->
    <a-table
      v-if="ruleType === 1"
      :columns="activeColumns"
      :data-source="form.policyDataSource || []"
      bordered
      :pagination="false"
      size="middle"
      :scroll="{ x: 'max-content' }"
    />

    <a-collapse v-else v-model:activeKey="activeCollapseKey" :bordered="false">
      <a-collapse-panel :key="String(key)" v-for="(item,key) in form.policyDataSource">
        <template #header>
          <a-typography-text keyboard>机构编号: {{ item.orgNo }}</a-typography-text>
        </template>
        <a-table
          :columns="cashColumns"
          :data-source="item.rules || []"
          bordered
          :pagination="false"
          size="middle"
          :scroll="{ x: 'max-content' }"
        />
      </a-collapse-panel>
    </a-collapse>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { onMounted, reactive, toRefs } from 'vue';
import { hasPurview } from '@/utils/permission';
import { InventoryManageApi } from '@/api/terminal-manage/InventoryManageApi';

export default {
  props: {
    visible: Boolean,
    ruleType: Number,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      activeCollapseKey: [],
      form: {
        policyDataSource: []
      },
      modalTitle: ['', '激活奖励规则', '达标奖励规则'],
      activeColumns: [
        {
          title: '机构编号',
          dataIndex: 'orgNo',
          align: 'center'
        },
        {
          title: '统计周期-开始天数',
          dataIndex: 'timeCycleStart',
          align: 'center'
        },
        {
          title: '统计周期-结束天数',
          dataIndex: 'timeCycleEnd',
          align: 'center'
        },
        {
          title: '交易标准金额(元)',
          dataIndex: 'tradeVolume',
          align: 'center'
        },
        {
          title: '返现金额(元)',
          dataIndex: 'cashbackAmount',
          align: 'center',
          defaultFilteredValue: [0],
          onFilter: (value, record) => {
            return Number(record.cashbackAmount) > value;
          }
        }
      ],
      cashColumns: [
        {
          title: '规则序号',
          dataIndex: 'ruleNo',
          align: 'center'
        },
        {
          title: '统计周期-开始天数',
          dataIndex: 'timeCycleStart',
          align: 'center'
        },
        {
          title: '统计周期-结束天数',
          dataIndex: 'timeCycleEnd',
          align: 'center'
        },
        {
          title: '交易标准金额(元)',
          dataIndex: 'tradeVolume',
          align: 'center'
        },
        {
          title: '返现金额(元)',
          dataIndex: 'cashbackAmount',
          align: 'center',
          defaultFilteredValue: [0],
          onFilter: (value, record) => {
            return Number(record.cashbackAmount) > value;
          }
        },
        {
          title: '活动开始时间',
          dataIndex: 'activityStartTime',
          align: 'center'
        },
        {
          title: '活动结束时间',
          dataIndex: 'activityEndTime',
          align: 'center'
        },
      ]
    });

    onMounted(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);

        getPolicyDataSource();
      }
    });

    /** 获取返现规则列表 */
    async function getPolicyDataSource() {
      const submitmethod = props.ruleType === 1 ? InventoryManageApi.activeRuleList : InventoryManageApi.standardRuleList;
      let res = await submitmethod({ id: props.detail.id });
      const { terminalActiveCashbackRuleList, terminalReachCashbackRuleList } = res || {};

      if (props.ruleType === 1) {
        res = terminalActiveCashbackRuleList || [];
      } else {
        if (terminalReachCashbackRuleList?.length) {
          const ruleMap = []
          const orgs = terminalReachCashbackRuleList.map(item => item.orgNo);
          const allOrgs = Array.from(new Set(orgs));

          allOrgs.forEach((org,key) => {
            const arrByOrg = terminalReachCashbackRuleList.filter(item => item.orgNo === org);
            arrByOrg.sort((a, b) => a.ruleNo - b.ruleNo);
            ruleMap.push({
              orgNo: org,
              rules: arrByOrg
            });

            data.activeCollapseKey.push(String(key));
          });

          res = ruleMap;
        } else {
          res = [];
        }
      }

      data.form.policyDataSource = res;
    }

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      updateVisible,
      hasPurview
    };
  }
};
</script>
