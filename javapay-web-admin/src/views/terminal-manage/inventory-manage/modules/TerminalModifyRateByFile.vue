<template>
  <a-modal
    :width="1000"
    :visible="visible"
    :confirm-loading="loading"
    title="批量变更终端商户费率"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules" :label-col="{ style: { width: '120px' } }">
      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="支付通道" name="channelCode">
            <a-select v-model:value="form.channelCode" class="ele-fluid" placeholder="请选择" allow-clear @change="form.policyId = null">
              <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode"
              >{{ channelName }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="指定终端方式">
            <a-radio-group v-model:value="terminalSelectType">
              <a-radio-button value="region">区间选择</a-radio-button>
              <a-radio-button value="file">文件上传</a-radio-button>
            </a-radio-group>
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 区间 -->
      <a-row :gutter="16" v-if="terminalSelectType === 'region'">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="开始序列号" name="terminalSnStart">
            <a-input v-model:value.trim="form.terminalSnStart" placeholder="请输入开始序列号" allow-clear />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="结束序列号" name="terminalSnEnd">
            <a-input v-model:value.trim="form.terminalSnEnd" placeholder="请输入结束序列号" allow-clear />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16" v-if="terminalSelectType === 'file'">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="上传文件" name="file">
            <a-upload-dragger
              :file-list="form.file ? [form.file] : []"
              accept=".xlsx,.xls"
              :maxCount="1"
              :before-upload="beforeUpload"
              @remove="handleRemove"
              style="padding: 24px 0"
            >
              <p class="ant-upload-drag-icon">
                <cloud-upload-outlined />
              </p>
              <p class="ant-upload-hint" style="margin-bottom: 10px">将文件拖到此处，或点击上传</p>
              <div class="ele-text-center">
                <a @click.stop="downloadTemplateFile">下载模板文件 </a>
              </div>
            </a-upload-dragger>
          </a-form-item>
        </a-col>
      </a-row>

      <a-divider orientationMargin="0" orientation="left" dashed>商户结算费率信息</a-divider>
      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="商户结算政策" name="policyId">
            <a-select
              v-model:value="form.policyId"
              placeholder="请选择"
              @change="ratePolicyValueChanged"
              allow-clear
              @dropdownVisibleChange="open => open && getRateMerchTypeList()"
              :disabled="!form.channelCode"
            >
              <a-select-option v-for="(item, key) in merchRatePolicyList" :key="key" :value="item.id"
              >{{ item.policyDesc }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col>
          <a-form-item class="ele-text-center">
            <a-button type="danger" @click="jumpToOpenMerchRatePolicy">开通商户费率政策</a-button>
          </a-form-item>
        </a-col>
      </a-row>
      <a-form
        ref="rateForm"
        :model="rateInfoDTO"
        :label-col="{ md: { span: 17 }, sm: { span: 24 } }"
        :wrapper-col="{ md: { span: 7 }, sm: { span: 24 } }"
      >
        <!-- 费率相关 -->
        <RateModule :rate-item="rateInfoDTO" :disabled="false" />
      </a-form>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { InventoryManageApi } from '@/api/terminal-manage/InventoryManageApi';
import { deepCopy } from '@/utils/util';
import { RatePolicyApi } from '@/api/businessTeam/rate-policy/RatePolicyApi';
import RateModule from '../../../business-team/_components/RateModule.vue';

export default {
  components: { RateModule },
  props: {
    visible: Boolean,
    channelCodes: Array,
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {
        channelCode: null
      },
      terminalSelectType: 'region',
      merchRatePolicyList: [],
      // 表单验证规则
      rules: {
        terminalSnStart: [{ required: true, message: '请输入开始序列号' }],
        terminalSnEnd: [{ required: true, message: '请输入结束序列号' }],
        file: [{ required: true, message: '请上传文件' }],
        policyId: [{ required: true, message: '请选择' }],
        channelCode: [{ required: true, message: '请选择' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false,
      showAgentList: false,
      //固定的选择的费率
      fixedRateInfoDTO: {},
      //费率数据
      rateInfoDTO: {},
    };
  },
  methods: {
    async downloadTemplateFile() {
      const res = await InventoryManageApi.agentModifyRateTerminalTemplate({}).catch(error => {
        message.destroy();
        const reader = new FileReader(); //创建一个FileReader实例
        reader.readAsText(error, 'utf-8'); //读取文件,结果用字符串形式表示
        reader.onload = function () {
          const { message: errorMsg } = JSON.parse(reader.result);
          message.error(errorMsg || '下载失败');
        };
      });

      const fileReader = new FileReader();
      fileReader.onload = () => {
        try {
          // 说明是普通对象数据，后台转换失败
          const jsonData = JSON.parse(fileReader.result);
          message.error(jsonData.message);
        } catch (err) {
          const fileName = '文件模板.xlsx';

          // 解析对象失败，说明是正常的文件流
          let blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.style.display = 'none';
          a.href = url;
          a.download = fileName;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }
      };
      fileReader.readAsText(res?.data);
    },
    beforeUpload(file) {
      this.form.file = file;
      return false;
    },

    handleRemove() {
      this.form.file = null;
    },

    //开通商户费率政策
    jumpToOpenMerchRatePolicy() {
      //跳转到商户费率政策页面
      this.$router.push('/rate-policy/index-merch');
      // 关闭弹框，通过控制visible的值，传递给父组件
      this.updateVisible(false);
    },

    //商户费率政策列表,这个是用来下面的商户结算政策选择的
    async getRateMerchTypeList() {
      //因为只有一代才有商户结算政策,所以都用一代的
      let data = await RatePolicyApi.listOfOrg({
        channelCode: this.form.channelCode,
        userNo: localStorage.getItem('SASS_ORG_CODE'),
        userType: localStorage.getItem('SASS_USER_TYPE'),
        policyType: 2
      });
      //对数据进行筛选,二次筛选,只有rateType === 1的项目才保留,只有有这个项目的data的项目才保留
      let ratePolicyData = data || [];
      ratePolicyData = ratePolicyData.filter(ratePolicy => {
        const filteredRatePolicy = ratePolicy.rateDTOList.filter(rateData => rateData.rateType === 1);
        return filteredRatePolicy.length > 0;
      });

      this.merchRatePolicyList = ratePolicyData || [];
    },
    //费率政策选值了
    ratePolicyValueChanged(value) {
      const selectedPolicy = this.merchRatePolicyList.find(item => item.id === value);
      //选择完政策之后,将政策的id和policyNo赋值
      this.form.policyId = selectedPolicy.id;
      this.form.policyNo = selectedPolicy.policyNo;
      //设置一个固定的值,用来提交时候的比较判断
      this.fixedRateInfoDTO = deepCopy(selectedPolicy.rateDTOList[0]);
      this.rateInfoDTO = selectedPolicy.rateDTOList[0];
    },

    // 检查两个对象的 rateInfoDTO 字段是否相同
    compareRateInfoDTO(obj1, obj2) {
      // 获取两个对象的 rateInfoDTO 字段
      const rateInfoDTO1 = obj1.rateInfoDTO;
      const rateInfoDTO2 = obj2.rateInfoDTO;

      // 比较 rateInfoDTO 字段中的所有值是否相同
      for (let key in rateInfoDTO1) {
        if (rateInfoDTO1[key] !== rateInfoDTO2[key]) {
          return false; // 有不相同的值，返回 false
        }
      }
      return true; // 所有值都相同，返回 true
    },
    async save() {
      // 校验表单
      await this.$refs.form.validate();
      await this.$refs.rateForm.validate();

      // 修改加载框为正在加载
      this.loading = true;

      const isSame = this.compareRateInfoDTO(this.rateInfoDTO, this.fixedRateInfoDTO);
      isSame === false ? (this.rateInfoDTO.isSame = '0') : (this.rateInfoDTO.isSame = '1');

      let submitMethod;
      const formData = new FormData();

      let params = deepCopy(this.form);

      // 区间
      if (this.terminalSelectType === 'region') {
        params.rateInfo = this.rateInfoDTO;
        submitMethod = InventoryManageApi.terminalModifyRate(params);
      }
      // 文件
      else if (this.terminalSelectType === 'file') {
        Object.keys(this.form).forEach(key => {
          this.form[key] !== null && formData.append(key, this.form[key]);
        });
        if (this.rateInfoDTO) {
          Object.keys(this.rateInfoDTO).forEach(rKey => {
            if (rKey !== 'rateInfoDTO') {
              this.rateInfoDTO[rKey] !== null && formData.append(`rateInfo.${rKey}`, this.rateInfoDTO[rKey] || '');
            }
          });
          if (this.rateInfoDTO.rateInfoDTO) {
            Object.keys(this.rateInfoDTO.rateInfoDTO).forEach(i => {
              this.rateInfoDTO.rateInfoDTO[i] !== null &&
                formData.append(`rateInfo.rateInfoDTO.${i}`, this.rateInfoDTO.rateInfoDTO[i] || '');
            });
          }
        }

        submitMethod = InventoryManageApi.terminalModifyRateByFile(formData);
      }

      submitMethod
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
