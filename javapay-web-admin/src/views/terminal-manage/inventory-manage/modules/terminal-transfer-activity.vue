<template>
  <a-modal
    :width="1000"
    :visible="visible"
    title="终端下发"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
  >
    <a-form ref="form" :model="form" :rules="rules" :label-col="{ style: { width: '130px' } }">
      <a-tabs v-model:activeKey="activeTabKey" type="card" destroyInactiveTabPane>
        <!-- 基本配置 -->
        <a-tab-pane key="1" tab="基本配置">
          <a-row :gutter="16">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="支付机构" name="channelCode">
                <a-select v-model:value="form.channelCode" placeholder="请选择" @change="channelSelectChanged">
                  <a-select-option v-for="(item, key) in channelCodes" :key="key" :value="item.channelCode">
                    {{ item.channelName }}</a-select-option
                  >
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <!-- 大区下发 -->
              <a-form-item v-if="hasPurview('1')" label="指定运营中心" name="agentNo">
                <a-input v-model:value="form.agentNo" placeholder="请选择" @click="onSelectBranch" readonly />
              </a-form-item>
              <!-- 运营、代理商下发 -->
              <a-form-item v-else label="指定代理商" name="agentNo">
                <a-input v-model:value="form.agentNo" placeholder="请选择" @click="onSelectAgent" readonly :disabled="!form.channelCode" />
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 押金流量费相关 -->
          <!-- <a-row :gutter="16" v-if="hasPurview(['3'])">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="押金" name="serviceFeeId">
                <a-select v-model:value="form.serviceFeeId" placeholder="请选择" :disabled="!form.agentNo" allow-clear>
                  <a-select-option v-for="(item, key) in serviceFeePolicyList" :key="key" :value="item.configId">
                    {{ item.policyName }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="流量费" name="simFeeId" v-if="form.simSwitch === 1">
                <a-select v-model:value="form.simFeeId" placeholder="请选择" :disabled="!form.agentNo">
                  <a-select-option v-for="(item, key) in simFeePolicyList" :key="key" :value="item.configId">
                    {{ item.policyName }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="流量费收取开关" name="simSwitch">
                <a-radio-group v-model:value="form.simSwitch" name="simSwitch">
                  <a-radio :value="1">开启</a-radio>
                  <a-radio :value="0">关闭</a-radio>
                </a-radio-group>
              </a-form-item>
              <a-form-item label="免费使用天数" name="simFreeDay" v-if="form.simSwitch === 1">
                <a-input v-model:value.trim="form.simFreeDay" placeholder="请输入免费使用天数" allow-clear />
              </a-form-item>
            </a-col>
          </a-row> -->

          <!-- 区间下发 -->
          <a-row :gutter="16" v-if="handleType === 'number'">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="开始序列号" name="terminalSnStart">
                <a-input v-model:value.trim="form.terminalSnStart" placeholder="请输入开始序列号" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="结束序列号" name="terminalSnEnd">
                <a-input v-model:value.trim="form.terminalSnEnd" placeholder="请输入结束序列号" allow-clear />
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 文件下发 -->
          <a-row :gutter="16" v-if="handleType === 'file'">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="上传文件" name="file">
                <a-upload-dragger
                  :file-list="form.file ? [form.file] : []"
                  accept=".xlsx,.xls"
                  :maxCount="1"
                  :before-upload="beforeUpload"
                  @remove="handleRemove"
                >
                  <p class="ant-upload-drag-icon">
                    <cloud-upload-outlined />
                  </p>
                  <p class="ant-upload-hint" style="margin-bottom: 10px">将文件拖到此处，或点击上传</p>
                  <div class="ele-text-center">
                    <a @click.stop="downloadTemplateFile">下载模板文件 </a>
                  </div>
                </a-upload-dragger>
              </a-form-item>
            </a-col>
          </a-row>
        </a-tab-pane>

        <!-- 活动配置 -->
        <a-tab-pane key="2" tab="活动配置" class="tabpane__background" disabled v-if="form.termActiveSwitch === 1">
          <!-- 激活返现配置 -->
          <a-card title="激活返现配置" :bordered="false">
            <tamplate v-if="form.activePolicyDataSource?.length">
              <div>
                <a-typography-text keyboard>
                  统计周期:从
                  <a-typography-text underline>终端绑定后</a-typography-text>
                  算起
                </a-typography-text>
              </div>

              <!-- 规则列表 -->
              <a-table :columns="columns" :data-source="form.activePolicyDataSource" bordered :pagination="false" size="middle">
                <template #bodyCell="{ column, text, record }">
                  <!-- 可编辑列 -->
                  <template v-if="tableEditKeys.includes(column.dataIndex)">
                    <div>
                      <a-form-item
                        :name="['activePolicyDataSource', record.key, column.dataIndex]"
                        :rules="
                          column.dataIndex === 'cashbackAmount'
                            ? cashbackAmtRules(record.parentCashbackAmount)
                            : rules[column.dataIndex] || []
                        "
                        class="table-edit-form__item"
                      >
                        <a-input-number
                          v-if="activeEditableData[record.key]"
                          v-model:value="activeEditableData[record.key][column.dataIndex]"
                          placeholder="必填"
                          :min="0"
                          style="margin: -5px 0; width: 100%"
                        />
                        <template v-else>
                          {{ text }}
                        </template>
                        <template #help v-if="activeEditableData[record.key]">
                          <a-typography-text type="warning" style="font-size: 12px"
                          >注: 下级不能大于{{ record.parentCashbackAmount }}</a-typography-text
                          >
                        </template>
                      </a-form-item>
                    </div>
                  </template>
                  <!-- 操作栏 -->
                  <template v-else-if="column.dataIndex === 'operation'">
                    <div class="editable-row-operations">
                      <span v-if="activeEditableData[record.key]">
                        <a @click="updateActiveRow(record.key)" class="ele-text-danger">保存</a>
                      </span>
                      <span v-else>
                        <a @click="editActiveRow(record.key)">编辑</a>
                      </span>
                    </div>
                  </template>
                </template>
              </a-table>
            </tamplate>
            <tamplate v-else>
              <a-alert message="无需配置" type="info" />
            </tamplate>
          </a-card>

          <!-- 达标返现配置 -->
          <a-card title="达标返现配置" :bordered="false">
            <tamplate v-if="form.cashbackPolicyDataSource?.length">
              <div>
                <a-typography-text keyboard>
                  统计周期:从
                  <a-typography-text underline>终端绑定后</a-typography-text>
                  算起
                </a-typography-text>
              </div>

              <!-- 规则表格 -->
              <a-table :columns="columns" :data-source="form.cashbackPolicyDataSource" bordered :pagination="false" size="middle">
                <template #bodyCell="{ column, text, record }">
                  <!-- 可编辑列 -->
                  <template v-if="tableEditKeys.includes(column.dataIndex)">
                    <div>
                      <a-form-item
                        :name="['cashbackPolicyDataSource', record.key, column.dataIndex]"
                        :rules="
                          column.dataIndex === 'cashbackAmount'
                            ? cashbackAmtRules(record.parentCashbackAmount)
                            : rules[column.dataIndex] || []
                        "
                        class="table-edit-form__item"
                      >
                        <a-input-number
                          v-if="cashbackEditableData[record.key]"
                          v-model:value="cashbackEditableData[record.key][column.dataIndex]"
                          placeholder="必填"
                          :min="0"
                          style="margin: -5px 0; width: 100%"
                        />
                        <template v-else>
                          {{ text }}
                        </template>
                        <template #help v-if="cashbackEditableData[record.key]">
                          <a-typography-text type="warning" style="font-size: 12px"
                          >注: 下级不能大于{{ record.parentCashbackAmount }}</a-typography-text
                          >
                        </template>
                      </a-form-item>
                    </div>
                  </template>
                  <!-- 操作栏 -->
                  <template v-else-if="column.dataIndex === 'operation'">
                    <div class="editable-row-operations">
                      <span v-if="cashbackEditableData[record.key]">
                        <a @click="updateCashbackRow(record.key)" class="ele-text-danger">保存</a>
                      </span>
                      <span v-else>
                        <a @click="editCashbackRow(record.key)">编辑</a>
                      </span>
                    </div>
                  </template>
                </template>
              </a-table>
            </tamplate>
            <tamplate v-else>
              <a-alert message="无需配置" type="info" />
            </tamplate>
          </a-card>
        </a-tab-pane>
      </a-tabs>
    </a-form>

    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>

      <template v-if="form.termActiveSwitch === 1">
        <a-button v-if="activeTabKey === '1'" type="primary" :loading="loading" @click="handleNext">下一步</a-button>
        <a-button v-else-if="activeTabKey === '2'" type="primary" :loading="loading" @click="save">提交</a-button>
      </template>

      <template v-else>
        <a-button type="primary" :loading="loading" @click="save">提交</a-button>
      </template>
    </template>
  </a-modal>

  <BranchSelect v-if="showSelectBranch" v-model:visible="showSelectBranch" @done="onSelectBranchFinish" />
  <AgentSelect v-if="showSelectAgent" v-model:visible="showSelectAgent" @done="onSelectAgentFinish" />
</template>

<script>
import { message } from 'ant-design-vue';
import { cloneDeep, isEmpty, omit } from 'lodash-es';
import { useUserStore } from '@/store/modules/user';
import { deepCopy } from '@/utils/util';
import { hasPurview } from '@/utils/permission';
import { InventoryManageApi } from '@/api/terminal-manage/InventoryManageApi';
import { CashbackRulePolicyApi } from '@/api/businessTeam/cashback-rule-policy/CashbackRulePolicyApi';
import BranchSelect from '../targ-branch-select.vue';
import AgentSelect from '../targ-agent-select.vue';
import { ServiceFeePolicyApi } from '@/api/businessTeam/activity-Config/ServiceFeePolicyApi';
import { SimFeePolicyApi } from '@/api/businessTeam/activity-Config/SimFeePolicyApi';
import { RateApi } from '@/api/businessTeam/rate/RateApi';
import { WHITE_BOARD_CHANNEL_CODE } from '@/config/setting';

function formDefaults() {
  return {
    channelCode: null,
    simSwitch: 1,
    termActiveSwitch: 0,
    activePolicyDataSource: [],
    cashbackPolicyDataSource: []
  };
}

export default {
  components: {
    BranchSelect,
    AgentSelect
  },
  props: {
    visible: Boolean,
    isActiveTer: Boolean,
    handleType: String, // number:区间下发 file:文件批量下发
    factoryList: Array,
    channelCodes: Array
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      activeTabKey: '1',
      // 表单数据
      form: formDefaults(),
      // 表单验证规则
      rules: {
        agentNo: [{ required: true, message: '请选择' }],
        terminalSnStart: [{ required: true, message: '请输入开始序列号' }],
        terminalSnEnd: [{ required: true, message: '请输入结束序列号' }],
        file: [{ required: true, message: '请上传文件' }],

        serviceFeeId: [{ required: true, message: '请选择' }],
        simSwitch: [{ required: true, message: '请选择' }],
        simFeeId: [{ required: true, message: '请选择' }],
        simFreeDay: [{ required: true, message: '请输入免费使用天数' }],

        activePolicyNo: [{ required: true, message: '暂无可用政策' }],
        cashbackPolicyNo: [{ required: true, message: '暂无可用政策' }],
        timeCycleStart: [{ required: true, message: '请填写' }],
        timeCycleEnd: [{ required: true, message: '请填写' }],
        tradeVolume: [{ required: true, message: '请填写' }]
      },
      // 提交状态
      loading: false,
      showSelectBranch: false,
      showSelectAgent: false,

      hasPurview,
      activatePolicyOptions: [],
      cashbackPolicyOptions: [],

      serviceFeePolicyList: [],
      simFeePolicyList: [],

      // 表格可编辑键
      tableEditKeys: ['cashbackAmount'],
      // 表格列
      columns: [
        {
          title: '统计周期-开始天数',
          dataIndex: 'timeCycleStart',
          width: '20%',
          align: 'center'
        },
        {
          title: '统计周期-结束天数',
          dataIndex: 'timeCycleEnd',
          width: '20%',
          align: 'center'
        },
        {
          title: '交易标准金额(元)',
          dataIndex: 'tradeVolume',
          width: '20%',
          align: 'center'
        },
        {
          title: '返现金额(元)',
          dataIndex: 'cashbackAmount',
          width: '20%',
          align: 'center',
          defaultFilteredValue: [0],
          onFilter: (value, record) => {
            if (Number(record.parentCashbackAmount) > value) return true;
            else {
              record.cashbackAmount = 0;
              return false;
            }
          }
        },
        {
          title: '操作',
          dataIndex: 'operation',
          align: 'center'
        }
      ],
      // 编辑中的行
      activeEditableData: {},
      cashbackEditableData: {}
    };
  },
  computed: {
    // 当前登录用户信息
    loginUser() {
      const userStore = useUserStore();
      return userStore.$state.info;
    }
  },
  created() {
    if (this.isActiveTer) {
      this.form.termActiveSwitch = 1;
    } else {
      this.form.termActiveSwitch = 0;
    }
  },
  methods: {
    /** 活动配置前置 */
    async handleNext() {
      // 校验表单
      await this.$refs.form.validate();
      // 修改加载框为正在加载
      this.loading = true;

      const { agentNo, terminalSnStart, terminalSnEnd, file } = this.form;

      let rusult;
      if (this.handleType === 'number') {
        rusult = InventoryManageApi.fetchScreenActiveTerminalRules({
          targAgentNo: agentNo,
          terminalSnStart,
          terminalSnEnd
        });
      } else if (this.handleType === 'file') {
        const formData = new FormData();
        formData.append('targAgentNo', agentNo);
        formData.append('file', file);
        rusult = InventoryManageApi.fetchScreenActiveTerminalRulesByFile(formData);
      }

      rusult
        .then(result => {
          this.loading = false;

          const { activePolicyRuleList, cashPolicyRuleList, activePolicyNo, activePolicyName, reachPolicyNo, reachPolicyName } =
            result?.data || result;

          this.form.activePolicyNo = activePolicyNo;
          this.form.cashbackPolicyNo = reachPolicyNo;

          this.activatePolicyOptions = [{ policyName: activePolicyName, policyNo: activePolicyNo }];
          this.cashbackPolicyOptions = [{ policyName: reachPolicyName, policyNo: reachPolicyNo }];

          this.initActivePolicyDataSource(activePolicyRuleList);
          this.initCashbackPolicyDataSource(cashPolicyRuleList);

          this.activeTabKey = '2';
        })
        .catch(() => {
          this.loading = false;
        });
    },

    /** 获取激活规则列表 */
    async initActivePolicyDataSource(data) {
      this.form.activePolicyDataSource = [];
      this.activeEditableData = {};

      data = data || [];
      data.forEach((item, index) => {
        item.key = index;
      });

      this.form.activePolicyDataSource = data;
    },

    /** 获取达标规则列表 */
    async initCashbackPolicyDataSource(data) {
      this.form.cashbackPolicyDataSource = [];
      this.cashbackEditableData = {};

      data = data || [];
      data.forEach((item, index) => {
        item.key = index;
      });

      this.form.cashbackPolicyDataSource = data;
    },

    cashbackAmtRules(maxsCashbackAmt) {
      if (!maxsCashbackAmt && maxsCashbackAmt !== 0) {
        return [{ required: true, message: '请填写', trigger: 'submit' }];
      }
      return [
        { required: true, message: '请填写', trigger: 'submit' },
        {
          validator: async (rule, value) => {
            if (Number(value) > Number(maxsCashbackAmt)) {
              return Promise.reject(`注：下级不能大于${maxsCashbackAmt}`);
            }
            return Promise.resolve();
          },
          trigger: 'submit'
        }
      ];
    },

    //代理商费率政策列表,这个只是判断代理商是否开通了他自身的政策
    async getAgentRateList() {
      // 白板机通道不判断
      if (this.form.channelCode === WHITE_BOARD_CHANNEL_CODE) {
        return;
      }

      let data = await RateApi.list({
        channelCode: this.form.channelCode,
        userNo: this.form.agentNo,
        validStatus: 1
      });
      //对数据进行筛选,只有rateType === 1的项目才保留
      let rateAgentData = data || [];
      rateAgentData = rateAgentData.filter(rateData => rateData.rateType === 1);

      if (rateAgentData.length === 0) {
        message.error('该代理商没有开通所选通道的业务!');
      }
    },

    channelSelectChanged() {
      this.form.agentNo = null;
    },

    async getServiceFeePolicyList() {
      this.serviceFeePolicyList = [];
      const data = await ServiceFeePolicyApi.list({ orgNo: this.form.agentNo, orgType: 3 });
      this.serviceFeePolicyList = data || [];
    },

    async getSimFeePolicyList() {
      this.simFeePolicyList = [];
      const data = await SimFeePolicyApi.list({ orgNo: this.form.agentNo, orgType: 3 });
      this.simFeePolicyList = data || [];
    },

    onSelectBranch() {
      this.showSelectBranch = true;
    },

    onSelectBranchFinish(info) {
      if (!info) return;
      this.form.agentNo = info.branchNo;
    },

    onSelectAgent() {
      this.showSelectAgent = true;
    },

    onSelectAgentFinish(info) {
      if (!info) return;
      this.form.agentNo = info.agentNo;

      if (hasPurview('3')) {
        // this.form.serviceFeeId = null;
        // this.form.simFeeId = null;

        //选择完代理商后,请求一下对应的费率政策列表
        this.getAgentRateList();
        //选择完代理商后,请求一下服务费政策列表
        // this.getServiceFeePolicyList();
        //选择完代理商后,请求一下流量费政策列表
        // this.getSimFeePolicyList();
      }
    },

    /** 选中激活政策 */
    onConfirmActivePolicy() {
      this.getActivePolicyDataSource();
    },

    /** 获取激活规则列表 */
    async getActivePolicyDataSource() {
      this.form.activePolicyDataSource = [];
      this.activeEditableData = {};

      let data = await CashbackRulePolicyApi.list({
        cashbackType: 0,
        orgNo: this.loginUser.orgCode,
        policyNo: this.form.activePolicyNo
      });
      data = data || [];
      data.forEach((item, index) => {
        item.key = -(index + 1);
      });

      this.form.activePolicyDataSource = data;
    },

    /** 选中达标政策 */
    onConfirmCashbackPolicy() {
      this.getCashbackPolicyDataSource();
    },

    /** 获取达标规则列表 */
    async getCashbackPolicyDataSource() {
      this.form.cashbackPolicyDataSource = [];
      this.cashbackEditableData = {};

      let data = await CashbackRulePolicyApi.list({
        cashbackType: 1,
        orgNo: this.loginUser.orgCode,
        policyNo: this.form.cashbackPolicyNo
      });
      data = data || [];
      data.forEach((item, index) => {
        item.key = -(index + 1);
      });

      this.form.cashbackPolicyDataSource = data;
    },

    /** 选中表格行 */
    editActiveRow(key) {
      this.activeEditableData[key] = cloneDeep(this.form.activePolicyDataSource.filter(item => key === item.key)[0]);
    },

    /** 更新表格行 */
    async updateActiveRow(key) {
      // 先赋值
      Object.assign(this.form.activePolicyDataSource.filter(item => key === item.key)[0], this.activeEditableData[key]);
      // 后校验
      const validateFields = this.tableEditKeys.map(field => {
        return ['activePolicyDataSource', key, field];
      });
      await this.$refs.form.validateFields(validateFields).catch(() => {
        return Promise.reject('激活返现规则校验失败');
      });

      delete this.activeEditableData[key];
    },

    /** 选中表格行 */
    editCashbackRow(key) {
      this.cashbackEditableData[key] = cloneDeep(this.form.cashbackPolicyDataSource.filter(item => key === item.key)[0]);
    },

    /** 更新表格行 */
    async updateCashbackRow(key) {
      // 先赋值
      Object.assign(this.form.cashbackPolicyDataSource.filter(item => key === item.key)[0], this.cashbackEditableData[key]);
      // 后校验
      const validateFields = this.tableEditKeys.map(field => {
        return ['cashbackPolicyDataSource', key, field];
      });
      await this.$refs.form.validateFields(validateFields).catch(() => {
        return Promise.reject('达标返现规则校验失败');
      });

      delete this.cashbackEditableData[key];
    },

    async getCashbackPolicys() {
      const data = await CashbackRulePolicyApi.policyNameList({ cashbackType: 1, orgNo: this.loginUser.orgCode });
      this.cashbackPolicyOptions = data || [];
    },

    async getActivatePolicys() {
      const data = await CashbackRulePolicyApi.policyNameList({ cashbackType: 0, orgNo: this.loginUser.orgCode });
      this.activatePolicyOptions = data || [];
    },

    async save() {
      // 校验表单
      await this.$refs.form.validate();

      if (this.form.termActiveSwitch === 1) {
        if (!(isEmpty(this.activeEditableData) && isEmpty(this.cashbackEditableData))) {
          return message.warn(`存在未保存的返现规则`);
        }
      }
      // 修改加载框为正在加载
      this.loading = true;

      let params = deepCopy(this.form);
      let formData = new FormData();

      params.activeRuleList = params.activePolicyDataSource;
      params.cashbackRuleList = params.cashbackPolicyDataSource;
      params.targAgentNo = params.agentNo;
      params = omit(params, ['activePolicyDataSource', 'cashbackPolicyDataSource', 'agentNo']);

      if (!hasPurview('3')) {
        params = omit(params, ['simSwitch']);
      }

      let submitMethod;

      // 区间
      if (this.handleType === 'number') {
        if (hasPurview(['1', '2'])) {
          submitMethod = InventoryManageApi.transferActiveTerminal(params);
        } else {
          submitMethod = InventoryManageApi.transferTerminal(params);
        }
      }
      // 文件
      else if (this.handleType === 'file') {
        Object.keys(params).forEach(key => {
          if (!Array.isArray(params[key]) && params[key] !== null && key !== 'file') {
            formData.append(key, params[key]);
          }
        });

        formData.append('file', this.form.file);

        const ruleFields = ['ruleNo', 'timeCycleStart', 'timeCycleEnd', 'tradeVolume', 'cashbackAmount'];
        if (params.activeRuleList?.length) {
          params.activeRuleList.forEach((item, index) => {
            ruleFields.forEach(field => {
              formData.append(`activeRuleList[${index}].${field}`, item[field]);
            });
          });
        }
        if (params.cashbackRuleList?.length) {
          params.cashbackRuleList.forEach((item, index) => {
            ruleFields.forEach(field => {
              formData.append(`cashbackRuleList[${index}].${field}`, item[field]);
            });
          });
        }

        if (hasPurview(['1', '2'])) {
          submitMethod = InventoryManageApi.transferActiveTerminalByFile(formData);
        } else {
          submitMethod = InventoryManageApi.terminalAgentTransferToTargetByFile(formData);
        }
      }

      submitMethod
        .then(result => {
          this.loading = false;
          message.success(result.message);
          this.updateVisible(false);
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    beforeUpload(file) {
      this.form.file = file;
      return false;
    },

    handleRemove() {
      this.form.file = null;
    },

    async downloadTemplateFile() {
      const res = await InventoryManageApi.agentTransferTerminalTemplate({}).catch(error => {
        message.destroy();
        const reader = new FileReader(); //创建一个FileReader实例
        reader.readAsText(error, 'utf-8'); //读取文件,结果用字符串形式表示
        reader.onload = function () {
          const { message: errorMsg } = JSON.parse(reader.result);
          message.error(errorMsg || '下载失败');
        };
      });

      const fileReader = new FileReader();
      fileReader.onload = () => {
        try {
          // 说明是普通对象数据，后台转换失败
          const jsonData = JSON.parse(fileReader.result);
          message.error(jsonData.message);
        } catch (err) {
          const fileName = '终端下发文件模板.xlsx';

          // 解析对象失败，说明是正常的文件流
          let blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.style.display = 'none';
          a.href = url;
          a.download = fileName;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }
      };
      fileReader.readAsText(res?.data);
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
<style lang="less" scoped>
.editable-row-operations a {
  margin-right: 8px;
}

:deep(.table-edit-form__item) {
  margin-bottom: 0;
  // .ant-form-item-explain {
  //   display: none;
  // }
}

.tabpane__background {
  background-color: #ececec;
  padding: 15px;

  :deep(.ant-card) {
    border-radius: 8px;
    &:not(:last-child) {
      margin-bottom: 15px;
    }
  }
}
</style>
