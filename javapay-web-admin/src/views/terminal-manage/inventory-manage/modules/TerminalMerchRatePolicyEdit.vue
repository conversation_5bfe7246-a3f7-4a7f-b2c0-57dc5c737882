<template>
  <a-modal
    :width="800"
    :visible="visible"
    :confirm-loading="loading"
    title="变更终端商户费率"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules" :label-col="{ style: { width: '100px' } }">
      <a-form-item label="已选终端SN" name="terminalSnList">
        <a-select v-model:value="form.terminalSnList" mode="tags" style="width: 100%" placeholder="SN" :showSearch="false" :open="false" />
      </a-form-item>
      <a-form-item label="原费率政策" name="originPolicyNo">
        <a-select v-model:value="form.originPolicyNo" style="width: 100%" placeholder="请选择" disabled>
          <a-select-option v-for="(item, key) in merchRatePolicyList" :key="key" :value="item.policyNo">{{
            item.policyDesc
          }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="变更政策为" name="targetPolicyNo">
        <a-select v-model:value="form.targetPolicyNo" style="width: 100%" placeholder="请选择" @change="ratePolicyValueChanged">
          <a-select-option v-for="(item, key) in merchRatePolicyList" :key="key" :value="item.policyNo">{{
            item.policyDesc
          }}</a-select-option>
        </a-select>
      </a-form-item>

      <a-form
        ref="rateForm"
        :model="rateInfoDTO"
        :label-col="{ md: { span: 17 }, sm: { span: 24 } }"
        :wrapper-col="{ md: { span: 7 }, sm: { span: 24 } }"
      >
        <!-- 费率相关 -->
        <RateModule :rate-item="rateInfoDTO" disabled />
      </a-form>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { InventoryManageApi } from '@/api/terminal-manage/InventoryManageApi';
import { RatePolicyApi } from '@/api/businessTeam/rate-policy/RatePolicyApi';
import RateModule from '../../../business-team/_components/RateModule.vue';

export default {
  components: { RateModule },
  props: {
    visible: Boolean,
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {},
      merchRatePolicyList: [],
      // 表单验证规则
      rules: {
        terminalSnList: [{ required: true, message: '请选择' }],
        originPolicyNo: [{ required: true, message: '请选择' }],
        targetPolicyNo: [{ required: true, message: '请选择' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false,
      rateInfoDTO: {}
    };
  },
  mounted() {
    if (this.data) {
      this.form.terminalSnList = this.data.terminalSnList;
      this.form.originPolicyNo = this.data.originPolicyNo;
      this.form.channelCode = this.data.channelCode;
      this.getMerchPolicyNoList();
    }
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      InventoryManageApi.batchSnModifyMerchRatePolicy(this.form)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    async getMerchPolicyNoList() {
      let data = await RatePolicyApi.listOfOrg({
        channelCode: this.data.channelCode,
        userNo: localStorage.getItem('SASS_ORG_CODE'),
        userType: localStorage.getItem('SASS_USER_TYPE'),
        policyType: 2
      });

      let ratePolicyData = data || [];

      ratePolicyData = ratePolicyData.filter(ratePolicy => {
        return ratePolicy.rateDTOList?.some(rateData => rateData.rateType === 1);
      });
      this.merchRatePolicyList = ratePolicyData;
    },

    //费率政策选值了
    ratePolicyValueChanged(value) {
      const selectedPolicy = this.merchRatePolicyList.find(item => item.policyNo === value);
      this.rateInfoDTO = selectedPolicy.rateDTOList[0];
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
