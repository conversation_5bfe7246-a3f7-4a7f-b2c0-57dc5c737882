<template>
  <a-modal
    :width="860"
    :visible="visible"
    :confirm-loading="loading"
    title="终端调拨"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules" :label-col="{ style: { width: '120px' } }">
      <a-form-item label="已选终端SN" name="terminalSnList">
        <a-select v-model:value="form.terminalSnList" mode="tags" style="width: 100%" placeholder="SN" :showSearch="false" :open="false" />
      </a-form-item>
      <a-form-item label="指定代理商" name="targAgentNo">
        <a-input v-model:value="form.targAgentNo" placeholder="请选择" @click="showAgentList = true" readonly />
      </a-form-item>
      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="押金" name="serviceFeeId">
            <a-select v-model:value="form.serviceFeeId" placeholder="请选择" :disabled="!form.targAgentNo" allow-clear>
              <a-select-option v-for="(item, key) in serviceFeePolicyList" :key="key" :value="item.configId">
                {{ item.policyName }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="免费使用天数" name="simFreeDay" v-if="form.simSwitch === 1">
            <a-input v-model:value.trim="form.simFreeDay" placeholder="请输入免费使用天数" allow-clear />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="流量费收取开关" name="simSwitch">
            <a-radio-group v-model:value="form.simSwitch" name="simSwitch">
              <a-radio :value="1">开启</a-radio>
              <a-radio :value="0">关闭</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
      </a-row>
      <template v-if="form.simSwitch === 1">
        <a-row :gutter="16" v-for="(item, key) in form.simFeePeriodConfigDTO || []" :key="key">
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item
              :label="`${item.periodName}流量费金额`"
              :name="['simFeePeriodConfigDTO', key, item.periodIdField]"
              :rules="rules.simFeeId"
            >
              <a-select v-model:value="item[item.periodIdField]" placeholder="请选择" :disabled="!form.targAgentNo">
                <a-select-option
                  v-for="(option, keyo) in simFeePolicyMap[item.periodIdOptionsField] || []"
                  :key="keyo"
                  :value="option.configId"
                >
                  {{ option.policyName }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item
              :label="`${item.periodName}权益天数`"
              :name="['simFeePeriodConfigDTO', key, item.periodDayField]"
              :rules="rules.simFeeDay"
            >
              <a-select v-model:value="item[item.periodDayField]" placeholder="请选择" :disabled="!form.targAgentNo">
                <a-select-option v-for="(day, keyd) in [180, 210, 240, 270, 300, 360]" :key="keyd" :value="day"
                >{{ day }}天</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </template>
    </a-form>

    <AgentSelect v-if="showAgentList" v-model:visible="showAgentList" @done="setAgentInfo" />
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { InventoryManageApi } from '@/api/terminal-manage/InventoryManageApi';
import AgentSelect from '../../../qrcodeCard/batch-manage/agent-select.vue';
import { deepCopy } from '@/utils/util';
import { ServiceFeePolicyApi } from '@/api/businessTeam/activity-Config/ServiceFeePolicyApi';
import { SimFeePolicyApi } from '@/api/businessTeam/activity-Config/SimFeePolicyApi';
import { omit } from 'lodash-es';

const simFeePeriodConfigDef = [
  {
    periodName: '一期',
    periodIdField: 'firstSimFeeId',
    periodFeeField: 'firstSimFee',
    periodDayField: 'firstSimPeriodDay',
    periodIdOptionsField: 'firstPeriodList',
    firstSimFeeId: null,
    firstSimPeriodDay: null
  },
  {
    periodName: '二期',
    periodIdField: 'secondSimFeeId',
    periodFeeField: 'secondSimFee',
    periodDayField: 'secondSimPeriodDay',
    periodIdOptionsField: 'secondPeriodList',
    secondSimFeeId: null,
    secondSimPeriodDay: null
  },
  {
    periodName: '三期',
    periodIdField: 'thirdSimFeeId',
    periodFeeField: 'thirdSimFee',
    periodDayField: 'thirdSimPeriodDay',
    periodIdOptionsField: 'thirdPeriodList',
    thirdSimFeeId: null,
    thirdSimPeriodDay: null
  },
  {
    periodName: '标准期',
    periodIdField: 'fourthSimFeeId',
    periodFeeField: 'fourthSimFee',
    periodDayField: 'fourthSimPeriodDay',
    periodIdOptionsField: 'fourthPeriodList',
    fourthSimFeeId: null,
    fourthSimPeriodDay: null
  }
];

export default {
  components: { AgentSelect },
  props: {
    visible: Boolean,
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {},
      // 表单验证规则
      rules: {
        terminalSnList: [{ required: true, message: '请选择' }],
        targAgentNo: [{ required: true, message: '请选择' }],
        serviceFeeId: [{ required: true, message: '请选择' }],
        simSwitch: [{ required: true, message: '请选择' }],
        simFeeId: [{ required: true, message: '请选择' }],
        simFeeDay: [{ required: true, message: '请选择' }],
        simFreeDay: [{ required: true, message: '请输入免费使用天数' }],
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false,
      showAgentList: false,
      serviceFeePolicyList: [],
      simFeePolicyMap: {}
    };
  },
  mounted() {
    if (this.data) {
      this.form = Object.assign({ simSwitch: 1 }, this.data);

      const simFeePeriodConfigDTO = deepCopy(simFeePeriodConfigDef);
      simFeePeriodConfigDTO.forEach(item => {
        item[item.periodIdField] = this.form[item.periodIdField] || null;
        item[item.periodDayField] = this.form[item.periodDayField] || null;
      });
      this.form.simFeePeriodConfigDTO = simFeePeriodConfigDTO;
    }
  },
  methods: {
    //获取服务费政策列表
    async getServiceFeePolicyList() {
      this.serviceFeePolicyList = [];
      let data = await ServiceFeePolicyApi.list({ orgNo: this.form.targAgentNo, orgType: 3 });
      data = data || [];
      this.serviceFeePolicyList = data.filter(
        item => item.terminalSource === this.form.terminalSource && item.channelCode === this.form.channelCode
      );
    },

    //获取流量费政策列表
    async getSimFeePolicyList() {
      let data = await SimFeePolicyApi.list({ orgNo: this.form.targAgentNo, orgType: 3 });
      data = data || {};
      Object.keys(data).forEach(key => {
        data[key] =
          data[key]?.filter(item => item.terminalSource === this.form.terminalSource && item.channelCode === this.form.channelCode) || [];
      });
      this.simFeePolicyMap = data || {};
    },
    setAgentInfo(info) {
      this.showAgentList = false;
      this.form.targAgentNo = info.agentNo;
      this.form.oneLevelAgentNo = info.oneLevelAgentNo;

      this.form.serviceFeeId = null;

      this.form.simFeePeriodConfigDTO.forEach(item => {
        item[item.periodIdField] = null;
        item[item.periodDayField] = null;
      });

      this.getServiceFeePolicyList();
      this.getSimFeePolicyList();
    },

    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let params = deepCopy(this.form);

      const serviceFeeItem = this.serviceFeePolicyList.find(item => item.configId === params.serviceFeeId);
      params.serviceFee = serviceFeeItem?.amount;

      if (!params.simSwitch) {
        delete params.simFreeDay;
        delete params.simFeePeriodConfigDTO;
      } else {
        const simFeePeriodConfigDTO = params.simFeePeriodConfigDTO;
        params = omit(params, ['simFeePeriodConfigDTO']);
        simFeePeriodConfigDTO.forEach(item => {
          params[item.periodIdField] = item[item.periodIdField];
          params[item.periodDayField] = item[item.periodDayField];

          const simFeeItem = this.simFeePolicyMap[item.periodIdOptionsField].find(feeItem => feeItem.configId === item[item.periodIdField]);
          params[item.periodFeeField] = simFeeItem?.simFeeAmt;
        });
      }

      InventoryManageApi.terminalTransferToTarget(params)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
