<template>
  <a-modal
    :width="1000"
    :visible="visible"
    :confirm-loading="loading"
    title="终端入库"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 7 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 17 }, sm: { span: 24 } }"
    >
      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="设备厂商" name="factoryId">
            <a-select v-model:value="form.factoryId" placeholder="请选择" @change="factorySelectChanged()" allow-clear>
              <a-select-option v-for="(item, key) in factoryList" :key="key" :value="item.id">{{ item.factoryName }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="设备型号" name="modelId">
            <a-select v-model:value="form.modelId" placeholder="请选择" :disabled="!form.factoryId" allow-clear>
              <a-select-option v-for="(item, key) in terminalModelList" :key="key" :value="item.id">{{ item.modelName }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24" :span="12">
          <a-form-item label="支付机构" name="channelCode">
            <a-select v-model:value="form.channelCode" placeholder="请选择" @change="channelSelectChanged" allow-clear>
              <a-select-option v-for="(item, key) in channelCodes" :key="key" :value="item.channelCode">{{
                item.channelName
              }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24" :span="12">
          <a-row>
            <a-col :md="20" :sm="24" :xs="24">
              <a-form-item
                label="代理商编号"
                name="agentNo"
                style="display: flex; align-items: center"
                :label-col="{ md: { span: 8 }, sm: { span: 24 } }"
              >
                <a-input
                  v-model:value="form.agentNo"
                  placeholder="请选择"
                  @click="agentSelectMethod"
                  :readonly="true"
                  :disabled="!form.channelCode"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :md="4" :sm="24" :xs="24">
              <a-button type="primary" @click="resetAgentNo" style="margin-left: 8px" :disabled="!form.channelCode">重置</a-button>
            </a-col>
          </a-row>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="开始序列号" name="terminalSnStart" v-if="handleType === 'number'">
            <a-input v-model:value.trim="form.terminalSnStart" placeholder="请输入开始序列号" allow-clear />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="结束序列号" name="terminalSnEnd" v-if="handleType === 'number'">
            <a-input v-model:value.trim="form.terminalSnEnd" placeholder="请输入结束序列号" allow-clear />
          </a-form-item>
        </a-col>

        <a-col v-if="!hasPurview(['3', '5'])" :md="12" :sm="24" :xs="24">
          <a-form-item label="终端来源" name="terminalSource">
            <a-select v-model:value="form.terminalSource" placeholder="请选择" @change="onChangeTerminalSource">
              <a-select-option :value="1">全款机</a-select-option>
              <a-select-option :value="2">分期机</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="押金" name="serviceFeeId">
            <a-select v-model:value="form.serviceFeeId" placeholder="请选择" :disabled="!form.agentNo" allow-clear>
              <a-select-option v-for="(item, key) in serviceFeePolicyList" :key="key" :value="item.configId">{{
                item.policyName
              }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="流量费收取开关" name="simSwitch">
            <a-radio-group v-model:value="form.simSwitch" name="simSwitch">
              <a-radio :value="1">开启</a-radio>
              <a-radio :value="0">关闭</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="免费使用天数" name="simFreeDay" v-if="form.simSwitch === 1">
            <a-input v-model:value.trim="form.simFreeDay" placeholder="请输入免费使用天数" allow-clear />
          </a-form-item>
        </a-col>
      </a-row>

      <template v-if="form.simSwitch === 1">
        <a-row :gutter="16" v-for="(item, key) in form.simFeePeriodConfigDTO || []" :key="key">
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item
              :label="`${item.periodName}流量费金额`"
              :name="['simFeePeriodConfigDTO', key, item.periodIdField]"
              :rules="rules.simFeeId"
            >
              <a-select v-model:value="item[item.periodIdField]" placeholder="请选择" :disabled="!form.agentNo">
                <a-select-option
                  v-for="(option, keyo) in simFeePolicyMap[item.periodIdOptionsField] || []"
                  :key="keyo"
                  :value="option.configId"
                >
                  {{ option.policyName }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item
              :label="`${item.periodName}权益天数`"
              :name="['simFeePeriodConfigDTO', key, item.periodDayField]"
              :rules="rules.simFeeDay"
            >
              <a-select v-model:value="item[item.periodDayField]" placeholder="请选择" :disabled="!form.agentNo">
                <a-select-option v-for="(day, keyd) in [180, 210, 240, 270, 300, 360]" :key="keyd" :value="day"
                >{{ day }}天</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </template>
    </a-form>

    <template #footer v-if="handleType === 'file'">
      <a-button @click="downloadTemplateFile">下载模版</a-button>
      <span style="margin: 0 8px"></span>
      <a-upload name="file" accept=".xlsx" :maxCount="1" :before-upload="beforeUpload" :showUploadList="false">
        <a-button type="primary" :disabled="false">
          <template #icon>
            <CloudUploadOutlined />
          </template>
          <span>上传文件(.xlsx)</span>
        </a-button>
      </a-upload>
    </template>
  </a-modal>

  <!-- 代理商选择弹窗 -->
  <AgentSelect v-if="showAgentList" v-model:visible="showAgentList" @done="setAgentInfo" />
</template>

<script>
import { InventoryManageApi } from '@/api/terminal-manage/InventoryManageApi';
import { message } from 'ant-design-vue';
import { RateApi } from '@/api/businessTeam/rate/RateApi';
import { hasPurview } from '@/utils/permission';
import { ServiceFeePolicyApi } from '@/api/businessTeam/activity-Config/ServiceFeePolicyApi';
import { SimFeePolicyApi } from '@/api/businessTeam/activity-Config/SimFeePolicyApi';
import { TerminalModelApi } from '@/api/qrcodeCard/TerminalModelApi';
import { deepCopy } from '@/utils/util';
import AgentSelect from './targ-agent-select.vue';
import { omit } from 'lodash-es';
import { WHITE_BOARD_CHANNEL_CODE } from '@/config/setting';

const simFeePeriodConfigDef = [
  {
    periodName: '一期',
    periodIdField: 'firstSimFeeId',
    periodFeeField: 'firstSimFee',
    periodDayField: 'firstSimPeriodDay',
    periodIdOptionsField: 'firstPeriodList',
    firstSimFeeId: null,
    firstSimPeriodDay: null
  },
  {
    periodName: '二期',
    periodIdField: 'secondSimFeeId',
    periodFeeField: 'secondSimFee',
    periodDayField: 'secondSimPeriodDay',
    periodIdOptionsField: 'secondPeriodList',
    secondSimFeeId: null,
    secondSimPeriodDay: null
  },
  {
    periodName: '三期',
    periodIdField: 'thirdSimFeeId',
    periodFeeField: 'thirdSimFee',
    periodDayField: 'thirdSimPeriodDay',
    periodIdOptionsField: 'thirdPeriodList',
    thirdSimFeeId: null,
    thirdSimPeriodDay: null
  },
  {
    periodName: '标准期',
    periodIdField: 'fourthSimFeeId',
    periodFeeField: 'fourthSimFee',
    periodDayField: 'fourthSimPeriodDay',
    periodIdOptionsField: 'fourthPeriodList',
    fourthSimFeeId: null,
    fourthSimPeriodDay: null
  }
];

function formDefaults() {
  return {
    channelCode: null,
    simSwitch: 1,
    terminalSource: 1,
    simFeePeriodConfigDTO: deepCopy(simFeePeriodConfigDef)
  };
}

export default {
  components: {
    AgentSelect
  },
  props: {
    visible: Boolean,
    handleType: String, //number:单号/连号入库的类型 file:批量入库的类型
    factoryList: Array,
    channelCodes: Array,
    agentList: Array
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: formDefaults(),
      //设备型号数组
      terminalModelList: [],
      //服务费政策数组
      serviceFeePolicyList: [],
      //流量费政策数组
      simFeePolicyMap: [],
      // 表单验证规则
      rules: {
        factoryId: [{ required: true, message: '请选择' }],
        modelId: [{ required: true, message: '请选择' }],
        channelCode: [{ required: true, message: '请选择' }],
        agentNo: [{ required: true, message: '请选择' }],

        terminalSnStart: [{ required: true, message: '请输入开始序列号' }],
        terminalSnEnd: [{ required: true, message: '请输入结束序列号' }],
        serviceFeeId: [{ required: true, message: '请选择' }],
        simSwitch: [{ required: true, message: '请选择' }],
        simFreeDay: [{ required: true, message: '请输入免费使用天数' }],
        terminalSource: [{ required: true, message: '请选择' }],
        simFeeId: [{ required: true, message: '请选择' }],
        simFeeDay: [{ required: true, message: '请选择' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false,
      //是否展示代理商选择页面
      showAgentList: false,
      hasPurview,
      activateRules: [],
      cashbackRules: [],
      inactiveConfs: [],
      pseudoActives: []
    };
  },
  mounted() {
    //代理商登录的,默认显示代理商编号
    if (hasPurview('3')) {
      this.form.agentNo = localStorage.getItem('SASS_ORG_CODE');
      //选择完代理商后,请求一下服务费政策列表
      // this.getServiceFeePolicyList();
      // //选择完代理商后,请求一下流量费政策列表
      // this.getSimFeePolicyList();

      // this.channelSelectChanged();
    }
  },
  methods: {
    onChangeTerminalSource() {
      this.form.serviceFeeId = null;

      this.form.simFeePeriodConfigDTO.forEach(item => {
        item[item.periodIdField] = null;
        item[item.periodDayField] = null;
      });

      this.getServiceFeePolicyList();
      this.getSimFeePolicyList();
    },

    //厂商选择之后
    factorySelectChanged() {
      this.form.modelId = null;
      this.getTerminalModelList();
    },

    //通道选择
    channelSelectChanged() {
      if (this.form.agentNo) {
        //需要清空押金活动和流量费活动还有结算政策
        this.form.serviceFeeId = null;

        this.form.simFeePeriodConfigDTO.forEach(item => {
          item[item.periodIdField] = null;
          item[item.periodDayField] = null;
        });

        //选择完代理商后,请求一下服务费政策列表
        this.getServiceFeePolicyList();
        //选择完代理商后,请求一下流量费政策列表
        this.getSimFeePolicyList();
      }
      //因为代理商的进来,默认显示代理商编号了,所以这里要请求一下
      if (hasPurview('3')) {
        //选择完后,请求一下对应的费率政策列表
        this.getAgentRateList();
      }
    },

    //代理商选择点击
    agentSelectMethod() {
      this.showAgentList = true;
    },

    //从代理商列表返回，配置相关数据
    setAgentInfo(info) {
      this.showAgentList = false;
      this.form.agentNo = info.agentNo;

      //需要清空押金活动和流量费活动还有结算政策
      this.form.serviceFeeId = null;

      this.form.simFeePeriodConfigDTO.forEach(item => {
        item[item.periodIdField] = null;
        item[item.periodDayField] = null;
      });

      //选择完代理商后,请求一下对应的费率政策列表
      this.getAgentRateList();
      //选择完代理商后,请求一下 获取代理商信息,取得模版编号
      this.getRateInfo();

      //选择完代理商后,请求一下服务费政策列表
      this.getServiceFeePolicyList();
      //选择完代理商后,请求一下流量费政策列表
      this.getSimFeePolicyList();
    },

    //重置-代理商编号重置
    resetAgentNo() {
      this.form.agentNo = localStorage.getItem('SASS_ORG_CODE');
      //选择完代理商后,请求一下对应的费率政策列表
      this.getAgentRateList();

      //选择完代理商后,请求一下服务费政策列表
      this.getServiceFeePolicyList();
      //选择完代理商后,请求一下流量费政策列表
      this.getSimFeePolicyList();
    },

    //设备型号列表
    async getTerminalModelList() {
      const data = await TerminalModelApi.list({ factoryId: this.form.factoryId, validStatus: 1 });
      this.terminalModelList = data || [];
    },

    //获取服务费政策列表
    async getServiceFeePolicyList() {
      let data = await ServiceFeePolicyApi.list({ orgNo: this.form.agentNo, orgType: 3 });
      data = data || [];
      this.serviceFeePolicyList = data.filter(
        item => item.terminalSource === this.form.terminalSource && item.channelCode === this.form.channelCode
      );
    },

    //获取流量费政策列表
    async getSimFeePolicyList() {
      let data = await SimFeePolicyApi.list({ orgNo: this.form.agentNo, orgType: 3 });
      data = data || {};
      Object.keys(data).forEach(key => {
        data[key] =
          data[key]?.filter(item => item.terminalSource === this.form.terminalSource && item.channelCode === this.form.channelCode) || [];
      });
      this.simFeePolicyMap = data || {};
    },

    //模版费率列表,只有选择的代理商有哪个通道的,这个才去显示默认值
    // async getRateTemplateList() {
    //   const data = await RateTemplateApi.list({ validStatus: 1, templateType: 2, channelCode: this.form.channelCode, rateType: 1 });
    //   this.rateTemplateList = data || [];

    //   this.rateInfoDTO = this.rateTemplateList[0];
    // },

    /**
     * 判断代理商是否开通了他自身的政策
     */
    async getAgentRateList() {
      // 白板机通道不判断
      if (this.form.channelCode === WHITE_BOARD_CHANNEL_CODE) {
        return;
      }

      let data = await RateApi.list({
        channelCode: this.form.channelCode,
        userNo: this.form.agentNo,
        validStatus: 1
      });
      //对数据进行筛选,只有rateType === 1的项目才保留
      let rateAgentData = data || [];
      rateAgentData = rateAgentData.filter(rateData => rateData.rateType === 1);

      if (rateAgentData.length === 0) {
        message.error('该代理商没有开通所选通道的业务!');
      }
    },

    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let params = deepCopy(this.form);

      const serviceFeeItem = this.serviceFeePolicyList.find(item => item.configId === params.serviceFeeId);
      params.serviceFee = serviceFeeItem?.amount;

      if (params.simSwitch !== 1) {
        params = omit(params, ['simFreeDay', 'simFeePeriodConfigDTO']);
      } else {
        const simFeePeriodConfigDTO = params.simFeePeriodConfigDTO;
        params = omit(params, ['simFeePeriodConfigDTO']);
        simFeePeriodConfigDTO.forEach(item => {
          params[item.periodIdField] = item[item.periodIdField];
          params[item.periodDayField] = item[item.periodDayField];

          const simFeeItem = this.simFeePolicyMap[item.periodIdOptionsField].find(feeItem => feeItem.configId === item[item.periodIdField]);
          params[item.periodFeeField] = simFeeItem?.simFeeAmt;
        });
      }

      params.payMethods = params.payMethods ? params.payMethods.join(',') : '';

      InventoryManageApi.addToLibraryNumberType(params)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 如果是新增，则form表单置空
          if (!this.isUpdate) {
            this.form = formDefaults();
          }

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    //下载模版
    async downloadTemplateFile() {
      // 修改加载框为正在加载
      this.loading = true;

      const res = await InventoryManageApi.download({}).catch(error => {
        this.loading = false;

        message.destroy();

        const reader = new FileReader(); //创建一个FileReader实例
        reader.readAsText(error, 'utf-8'); //读取文件,结果用字符串形式表示
        reader.onload = function () {
          const { message: errorMsg } = JSON.parse(reader.result);
          message.error(errorMsg || '下载失败');
        };
      });

      this.loading = false;

      const fileReader = new FileReader();
      fileReader.onload = () => {
        try {
          // 说明是普通对象数据，后台转换失败
          const jsonData = JSON.parse(fileReader.result);
          message.error(jsonData.message);
        } catch (err) {
          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          const contentDisposition = res.headers['content-disposition'];
          let fileName = decodeURIComponent(contentDisposition.substring(contentDisposition.indexOf('=') + 1));
          fileName = fileName ? fileName.replace("utf-8''", '') + '.xlsx' : '模板文件-下载.xlsx';

          // 解析对象失败，说明是正常的文件流
          let blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.style.display = 'none';
          a.href = url;
          a.download = fileName;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }
      };
      fileReader.readAsText(res?.data);
    },

    //上传文件
    async beforeUpload(file) {
      // 校验表单
      await this.$refs.form.validate();

      let params = deepCopy(this.form);

      const serviceFeeItem = this.serviceFeePolicyList.find(item => item.configId === params.serviceFeeId);
      params.serviceFee = serviceFeeItem?.amount;

      if (params.simSwitch !== 1) {
        params = omit(params, ['simFreeDay', 'simFeePeriodConfigDTO']);
      } else {
        const simFeePeriodConfigDTO = params.simFeePeriodConfigDTO;
        params = omit(params, ['simFeePeriodConfigDTO']);
        simFeePeriodConfigDTO.forEach(item => {
          params[item.periodIdField] = item[item.periodIdField];
          params[item.periodDayField] = item[item.periodDayField];

          const simFeeItem = this.simFeePolicyMap[item.periodIdOptionsField].find(feeItem => feeItem.configId === item[item.periodIdField]);
          params[item.periodFeeField] = simFeeItem?.simFeeAmt;
        });
      }

      params.payMethods = params.payMethods ? params.payMethods.join(',') : '';

      const formData = new FormData();

      Object.keys(params).forEach(key => {
        formData.append(key, params[key]);
      });
      formData.append('file', file);

      InventoryManageApi.upload(formData)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 如果是新增，则form表单置空
          if (!this.isUpdate) {
            this.form = formDefaults();
          }

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });

      return false;
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
