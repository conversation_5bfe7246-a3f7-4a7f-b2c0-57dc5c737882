<template>
  <a-modal
    :width="800"
    :visible="visible"
    title="详情"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
    :footer="null"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="批次号">{{ form.batchNo }}</a-descriptions-item>
      <a-descriptions-item label="批次状态">
        <a-tag v-if="form.batchStatus === 1" color="success">成功</a-tag>
        <a-tag v-else-if="form.batchStatus === 2" color="error">失败</a-tag>
        <a-tag v-else-if="form.batchStatus === 3" color="pink">部分成功</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="操作数量">{{ form.count }}</a-descriptions-item>
      <a-descriptions-item label="失败数量">{{ form.failedCount }}</a-descriptions-item>
      <a-descriptions-item label="回拨状态">
        <a-tag v-if="form.isCallback === 1" color="success">可以</a-tag>
        <a-tag v-else>不可以</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="调拨类型">
        <a-tag v-if="form.optType === 1" color="blue">回拨</a-tag>
        <a-tag v-else-if="form.optType === 0" color="purple">下发</a-tag>
        <a-tag v-else-if="form.optType === 2" color="purple">划拨</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="原所属代理商编号">{{ form.origAgentNo }}</a-descriptions-item>
      <a-descriptions-item label="接收代理商编号">{{ form.targAgentNo }}</a-descriptions-item>
      <a-descriptions-item label="操作人名称">{{ form.userName }}</a-descriptions-item>
      <a-descriptions-item label="操作人编号">{{ form.userNo }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
    </a-descriptions>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
