<template>
  <div class="ele-body">
    <!-- 搜索框内容 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="批次号">
              <a-input v-model:value.trim="where.batchNo" placeholder="请输入批次号" allow-clear />
            </a-form-item>
            <a-form-item label="原所属代理商编号">
              <a-input v-model:value.trim="where.origAgentNo" placeholder="请输入原所属代理商编号" allow-clear />
            </a-form-item>
            <a-form-item label="接收代理商编号">
              <a-input v-model:value.trim="where.targAgentNo" placeholder="请输入接收代理商编号" allow-clear />
            </a-form-item>
            <a-form-item label="操作人编号">
              <a-input v-model:value.trim="where.userNo" placeholder="请输入操作人编号" allow-clear />
            </a-form-item>
            <a-form-item label="批次状态">
              <a-select v-model:value="where.batchStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">成功</a-select-option>
                <a-select-option :value="2">失败</a-select-option>
                <a-select-option :value="3">部分成功</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="回拨状态">
              <a-select v-model:value="where.isCallback" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">可以</a-select-option>
                <a-select-option :value="0">不可以</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="调拨类型">
              <a-select v-model:value="where.optType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">下发</a-select-option>
                <a-select-option :value="1">回拨</a-select-option>
                <a-select-option :value="2">划拨</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格内容 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- 表体的操作 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'batchStatus'">
              <a-tag v-if="record.batchStatus === 1" color="success">成功</a-tag>
              <a-tag v-else-if="record.batchStatus === 2" color="error">失败</a-tag>
              <a-tag v-else-if="record.batchStatus === 3" color="pink">部分成功</a-tag>
            </template>
            <template v-else-if="column.key === 'isCallback'">
              <a-tag v-if="record.isCallback === 1" color="success">可以</a-tag>
              <a-tag v-else>不可以</a-tag>
            </template>
            <template v-else-if="column.key === 'optType'">
              <a-tag v-if="record.optType === 1" color="blue">回拨</a-tag>
              <a-tag v-else-if="record.optType === 0" color="purple">下发</a-tag>
              <a-tag v-else-if="record.optType === 2" color="orange">划拨</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
                <template v-if="record.isCallback === 1">
                  <a-divider type="vertical" />
                  <a-popconfirm :title="`确定要回拨 “${record.batchNo}” 批次的终端吗？`" @confirm="handleTransFer(record)">
                    <a class="ele-text-danger">终端回拨</a>
                  </a-popconfirm>
                </template>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <TerminalTransferBatchDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { TerminalTransferBatchApi } from '@/api/terminal-manage/TerminalTransferBatchApi';
import TerminalTransferBatchDetail from './terminal-transfer-batch-detail.vue';
import { message } from 'ant-design-vue';

export default {
  name: 'TerminalTransferBatch',
  components: {
    TerminalTransferBatchDetail
  },
  data() {
    return {
      //表格查询条件
      where: {},
      //展示编辑页面传的参数
      current: null,
      //是否展示详情页面
      showDetail: false,
      //表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center'
        },
        {
          title: '批次号',
          dataIndex: 'batchNo',
          align: 'center'
        },
        {
          title: ' 批次状态',
          dataIndex: 'batchStatus',
          key: 'batchStatus',
          align: 'center'
        },
        {
          title: '操作数量',
          dataIndex: 'count',
          align: 'center'
        },
        {
          title: '失败数量',
          dataIndex: 'failedCount',
          align: 'center'
        },
        {
          title: '回拨状态',
          dataIndex: 'isCallback',
          key: 'isCallback',
          align: 'center'
        },
        {
          title: '调拨类型',
          dataIndex: 'optType',
          key: 'optType',
          align: 'center'
        },
        {
          title: '原所属代理商编号',
          dataIndex: 'origAgentNo',
          align: 'center'
        },
        {
          title: '接收代理商编号',
          dataIndex: 'targAgentNo',
          align: 'center'
        },
        {
          title: '操作人名称',
          dataIndex: 'userName',
          align: 'center'
        },
        {
          title: '操作人编号',
          dataIndex: 'userNo',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 180,
          align: 'center'
        }
      ]
    };
  },
  methods: {
    async handleTransFer(row) {
      const result = await TerminalTransferBatchApi.callbackTermByBatchNo({ batchNo: row.batchNo });
      message.success(result.message);
      this.reload();
    },

    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where }) {
      return TerminalTransferBatchApi.findPage({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>
