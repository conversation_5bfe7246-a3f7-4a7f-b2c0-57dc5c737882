<template>
  <a-modal
    :width="888"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新增'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules" layout="vertical">
      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="支付通道" name="channelCode">
            <a-select v-model:value="form.channelCode" style="width: 100%" placeholder="请选择">
              <a-select-option :value="item.channelCode" v-for="item in channelCodes" :key="item.id"
              >{{ item.channelName }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="厂商编号" name="factoryCode">
            <a-input v-model:value="form.factoryCode" placeholder="厂商编号" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="厂商名称" name="factoryName">
            <a-input v-model:value="form.factoryName" placeholder="厂商名称" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="终端品牌编号" name="termFactoryBrandCode">
            <a-input v-model:value="form.termFactoryBrandCode" placeholder="终端品牌编号" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="终端报备机构编号" name="termFactoryOrgCode">
            <a-input v-model:value="form.termFactoryOrgCode" placeholder="终端报备机构编号" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="有效状态" name="validStatus">
            <a-select v-model:value="form.validStatus" style="width: 100%" placeholder="请选择">
              <a-select-option :value="1">有效</a-select-option>
              <a-select-option :value="0">无效</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { TerminalFactoryParamsApi } from '@/api/terminal-manage/TerminalFactoryParamsApi';

export default {
  props: {
    visible: Boolean,
    data: Object,
    channelCodes: Array
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {
        validStatus: 1
      },
      // 表单验证规则
      rules: {
        channelCode: [{ required: true, message: '请选择' }],
        factoryCode: [{ required: true, message: '请输入厂商编号' }],
        factoryName: [{ required: true, message: '请输入厂商名称' }],
        termFactoryBrandCode: [{ required: true, message: '请输入终端品牌编号' }],
        termFactoryOrgCode: [{ required: true, message: '请输入终端报备机构编号' }],
        validStatus: [{ required: true, message: '请选择' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false
    };
  },
  created() {
    if (this.data) {
      this.isUpdate = true;
      this.form = Object.assign(this.form, this.data);
    }
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      // 执行编辑或修改方法
      if (this.isUpdate) {
        result = TerminalFactoryParamsApi.edit(this.form);
      } else {
        result = TerminalFactoryParamsApi.add(this.form);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
