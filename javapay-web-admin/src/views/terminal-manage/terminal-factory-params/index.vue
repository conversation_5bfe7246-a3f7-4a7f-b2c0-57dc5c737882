<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="支付通道">
              <a-select v-model:value="where.channelCode" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
                  {{ channelName }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="厂商编号">
              <a-input v-model:value.trim="where.factoryCode" placeholder="请输入厂商编号" allow-clear />
            </a-form-item>
            <a-form-item label="厂商名称">
              <a-input v-model:value.trim="where.factoryName" placeholder="请输入厂商名称" allow-clear />
            </a-form-item>
            <a-form-item label="终端品牌编号">
              <a-input v-model:value.trim="where.termFactoryBrandCode" placeholder="请输入终端品牌编号" allow-clear />
            </a-form-item>
            <a-form-item label="终端报备机构编号">
              <a-input v-model:value.trim="where.termFactoryOrgCode" placeholder="请输入终端报备机构编号" allow-clear />
            </a-form-item>
            <a-form-item label="有效状态">
              <a-select v-model:value="where.validStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">有效</a-select-option>
                <a-select-option :value="0">无效</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'validStatus'">
              <a-tag v-if="record.validStatus === 0" color="pink">无效</a-tag>
              <a-tag v-else-if="record.validStatus === 1" color="cyan">有效</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleEdit(record)">修改</a>
                <a-divider type="vertical" />
                <a-popconfirm title="确定要删除此记录吗？" @confirm="remove(record)">
                  <a class="ele-text-danger">删除</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <TerminalFactoryParamsEdit v-if="showEdit" v-model:visible="showEdit" :data="current" :channel-codes="channelCodes" @done="reload" />
  </div>
</template>

<script>
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import TerminalFactoryParamsEdit from './modules/TerminalFactoryParamsEdit.vue';
import { message } from 'ant-design-vue';
import { TerminalFactoryParamsApi } from '@/api/terminal-manage/TerminalFactoryParamsApi';

export default {
  name: 'TerminalFactoryParams',
  components: {
    TerminalFactoryParamsEdit
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '支付通道',
          dataIndex: 'channelCode',
          align: 'center',
          customRender: ({ text }) => {
            const item = this.channelCodes.find(c => c.channelCode === text);
            return item?.channelName || '--';
          }
        },
        {
          title: '厂商编号',
          dataIndex: 'factoryCode',
          align: 'center'
        },
        {
          title: '厂商名称',
          dataIndex: 'factoryName',
          align: 'center'
        },
        {
          title: '终端品牌编号',
          dataIndex: 'termFactoryBrandCode',
          align: 'center'
        },
        {
          title: '终端报备机构编号',
          dataIndex: 'termFactoryOrgCode',
          align: 'center'
        },
        {
          title: '有效状态',
          dataIndex: 'validStatus',
          key: 'validStatus',
          align: 'center',
          width: 120
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          align: 'center'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime',
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          width: 150,
          fixed: 'right'
        }
      ],
      // 表格搜索条件
      where: {},
      channelCodes: [],
      showEdit: false,
      current: null
    };
  },
  mounted() {
    this.getChannelCodes();
  },
  methods: {
    async remove(row) {
      const result = await TerminalFactoryParamsApi.delete({ id: row.id });
      message.success(result.message);
      this.reload();
    },

    handleEdit(record) {
      this.current = record;
      this.showEdit = true;
    },

    async getChannelCodes() {
      const data = await ChannelManageApi.list({ validStatus: 1 });
      this.channelCodes = data || [];
    },

    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    datasource({ page, limit, where }) {
      return TerminalFactoryParamsApi.findPage({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>
