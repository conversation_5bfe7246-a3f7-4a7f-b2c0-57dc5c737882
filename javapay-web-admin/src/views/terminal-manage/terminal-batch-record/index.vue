<template>
  <div class="ele-body">
    <!-- 搜索框内容 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="批次号">
              <a-input v-model:value.trim="where.batchNo" placeholder="请输入批次号" allow-clear />
            </a-form-item>

            <a-form-item label="所属代理商编号">
              <a-input v-model:value.trim="where.agentNo" placeholder="请输入所属代理商编号" allow-clear />
            </a-form-item>

            <a-form-item label="支付通道">
              <a-select v-model:value="where.channelCode" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="item.channelCode" v-for="item in channelCodes" :key="item.id">{{
                  item.channelName
                }}</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="存储类型">
              <a-select v-model:value="where.storeType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">入库</a-select-option>
                <a-select-option :value="2">调拨</a-select-option>
                <a-select-option :value="3">批量修改终端费率</a-select-option>
                <a-select-option :value="4">批量删除终端</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="文件状态">
              <a-select v-model:value="where.fileStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">未生成</a-select-option>
                <a-select-option :value="2">已生成</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格内容 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- 表体的操作 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'storeType'">
              <a-tag v-if="record.storeType === 1" color="cyan">入库</a-tag>
              <a-tag v-else-if="record.storeType === 2" color="blue">调拨</a-tag>
              <a-tag v-else-if="record.storeType === 3" color="purple">批量修改终端费率</a-tag>
              <a-tag v-else-if="record.storeType === 4" color="red">批量删除终端</a-tag>
            </template>

            <template v-else-if="column.key === 'fileStatus'">
              <a-tag v-if="record.fileStatus === 2" color="success">已生成</a-tag>
              <a-tag v-else-if="record.fileStatus === 1">未生成</a-tag>
            </template>

            <template v-else-if="column.key === 'terminalSource'">
              <a-tag v-if="record.terminalSource === 1" color="pink">全款机</a-tag>
              <a-tag v-else-if="record.terminalSource === 2" color="blue">分期机</a-tag>
            </template>

            <template v-else-if="column.key === 'simSwitch'">
              <a-tag v-if="record.simSwitch === 0" color="pink">关闭</a-tag>
              <a-tag v-else-if="record.simSwitch === 1" color="blue">开启</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <!-- <a @click="handleDetail(record)">详情</a> -->
                <a
                  v-if="record.fileStatus === 2"
                  :href="`/api/sysFileInfo/previewByObjectName?fileBucket=${record.bucketName}&fileObjectName=${record.fileName}`"
                  :download="`${record.batchNo}-失败反馈.xlsx`"
                >失败反馈.xlsx</a
                >
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>
  </div>
</template>

<script>
import { TerminalBatchRecordApi } from '@/api/terminal-manage/TerminalBatchRecordApi';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import { ServiceFeePolicyApi } from '@/api/businessTeam/activity-Config/ServiceFeePolicyApi';
import { SimFeePolicyApi } from '@/api/businessTeam/activity-Config/SimFeePolicyApi';
import { TerminalFactoryApi } from '@/api/qrcodeCard/TerminalFactoryApi';
import { TerminalModelApi } from '@/api/qrcodeCard/TerminalModelApi';

export default {
  name: 'TerminalBatchRecord',
  data() {
    return {
      //表格查询条件
      where: {},
      //展示编辑页面传的参数
      current: null,
      //是否展示详情页面
      showDetail: false,

      channelCodes: [],
      serviceFeePolicyList: [],
      simFeePolicyList: [],
      factoryList: [],
      terminalModelList: [],
      //表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center'
        },
        {
          title: '批次号',
          dataIndex: 'batchNo',
          align: 'center'
        },
        {
          title: '支付通道',
          dataIndex: 'channelCode',
          key: 'channelCode',
          customRender: ({ text }) => {
            const item = this.channelCodes.find(c => c.channelCode === text);
            return item?.channelName || '--';
          },
          align: 'center'
        },
        {
          title: '终端厂商',
          dataIndex: 'factoryId',
          customRender: ({ text }) => {
            const item = this.factoryList.find(c => c.id === text);
            return item?.factoryName || '--';
          },
          align: 'center'
        },
        {
          title: '终端型号',
          dataIndex: 'modelId',
          customRender: ({ text }) => {
            const item = this.terminalModelList.find(c => c.id === text);
            return item?.modelName || '--';
          },
          align: 'center'
        },
        {
          title: '所属代理商编号',
          dataIndex: 'agentNo',
          align: 'center'
        },
        {
          title: '文件状态',
          dataIndex: 'fileStatus',
          key: 'fileStatus',
          align: 'center'
        },
        {
          title: '操作数量',
          dataIndex: 'operateCount',
          align: 'center'
        },
        {
          title: '操作失败数量',
          dataIndex: 'operateFailCount',
          align: 'center'
        },
        {
          title: '存储类型',
          dataIndex: 'storeType',
          key: 'storeType',
          align: 'center'
        },
        {
          title: '终端来源',
          dataIndex: 'terminalSource',
          key: 'terminalSource',
          align: 'center'
        },
        {
          title: '押金活动金额',
          dataIndex: 'serviceFeeId',
          align: 'center',
          customRender: ({ text }) => {
            const item = this.serviceFeePolicyList.find(c => c.configId === text);
            return item?.policyName || '--';
          }
        },
        {
          title: '流量费收取开关',
          dataIndex: 'simSwitch',
          align: 'center',
          key: 'simSwitch'
        },
        {
          title: '流量费免费使用天数',
          dataIndex: 'simFreeDay',
          align: 'center',
          customRender: ({ text }) => {
            return text || '--';
          }
        },
        {
          title: '流量费(第一期)',
          dataIndex: 'firstSimFee',
          customRender: ({ record }) => {
            if (record.simSwitch !== 1) return '--';
            return `流量费:${record.firstSimFee}元, 权益天数:${record.firstSimPeriodDay}天`;
          }
        },
        {
          title: '流量费(第二期)',
          dataIndex: 'secondSimFee',
          customRender: ({ record }) => {
            if (record.simSwitch !== 1) return '--';
            return `流量费:${record.secondSimFee}元, 权益天数:${record.secondSimPeriodDay}天`;
          }
        },
        {
          title: '流量费(第三期)',
          dataIndex: 'thirdSimFee',
          customRender: ({ record }) => {
            if (record.simSwitch !== 1) return '--';
            return `流量费:${record.thirdSimFee}元, 权益天数:${record.thirdSimPeriodDay}天`;
          }
        },
        {
          title: '流量费(标准期)',
          dataIndex: 'fourthSimFee',
          customRender: ({ record }) => {
            if (record.simSwitch !== 1) return '--';
            return `流量费:${record.fourthSimFee}元, 权益天数:${record.fourthSimPeriodDay}天`;
          }
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 180,
          align: 'center'
        }
      ]
    };
  },
  mounted() {
    this.getChannelList();
    this.getServiceFeeSelfOpenList();
    this.getSimFeeSelfOpenList();
    this.getFactoryList();
    this.getTerminalModelList();
  },
  methods: {
    //查询方法
    reload() {
      this.$refs.table.reload({ page: 1 });
    },
    //重置
    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    //获取通道列表
    async getChannelList() {
      const data = await ChannelManageApi.list({ validStatus: 1 });
      this.channelCodes = data || [];
    },

    //获取数据方法
    datasource({ page, limit, where }) {
      return TerminalBatchRecordApi.findPage({ ...where, pageNo: page, pageSize: limit });
    },

    //获取服务费政策列表
    async getServiceFeeSelfOpenList() {
      const data = await ServiceFeePolicyApi.selfOpenList();
      this.serviceFeePolicyList = data || [];
    },

    //获取流量费政策列表
    async getSimFeeSelfOpenList() {
      const data = await SimFeePolicyApi.selfOpenList();
      this.simFeePolicyList = data || [];
    },

    //厂商列表
    async getFactoryList() {
      const data = await TerminalFactoryApi.list({ validStatus: 1 });
      this.factoryList = data || [];
    },

    //设备型号列表
    async getTerminalModelList() {
      const data = await TerminalModelApi.list({ validStatus: 1 });
      this.terminalModelList = data || [];
    }
  }
};
</script>
