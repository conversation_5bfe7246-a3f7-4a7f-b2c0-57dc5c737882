<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="通道商户名称">
              <a-input v-model:value.trim="where.chnMerchName" placeholder="请输入通道商户名称" allow-clear />
            </a-form-item>
            <a-form-item label="通道商户编号">
              <a-input v-model:value.trim="where.chnMerchNo" placeholder="请输入通道商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="终端SN">
              <a-input v-model:value.trim="where.termSn" placeholder="请输入终端SN" allow-clear />
            </a-form-item>
            <a-form-item label="原支付通道">
              <a-select v-model:value="where.channelCode" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
                  {{ channelName }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="目标支付通道">
              <a-select v-model:value="where.targetChannelCode" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
                  {{ channelName }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="处理完成状态">
              <a-select v-model:value="where.processCompleteStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">是</a-select-option>
                <a-select-option :value="0">否</a-select-option>
                <a-select-option :value="-1">已终止</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="终端绑定商户状态">
              <a-select v-model:value="where.termSnBindStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">未处理</a-select-option>
                <a-select-option :value="1">处理中</a-select-option>
                <a-select-option :value="2">处理成功</a-select-option>
                <a-select-option :value="3">处理失败</a-select-option>
                <a-select-option :value="4">无需处理</a-select-option>
                <a-select-option :value="-1">已终止</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="终端切换品牌状态">
              <a-select v-model:value="where.termSwitchBrandStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">未处理</a-select-option>
                <a-select-option :value="1">处理中</a-select-option>
                <a-select-option :value="2">处理成功</a-select-option>
                <a-select-option :value="3">处理失败</a-select-option>
                <a-select-option :value="4">无需处理</a-select-option>
                <a-select-option :value="-1">已终止</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="终端银联机构报备状态">
              <a-select v-model:value="where.termUnionReportStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">未处理</a-select-option>
                <a-select-option :value="1">处理中</a-select-option>
                <a-select-option :value="2">处理成功</a-select-option>
                <a-select-option :value="3">处理失败</a-select-option>
                <a-select-option :value="4">无需处理</a-select-option>
                <a-select-option :value="-1">已终止</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'processCompleteStatus'">
              <a-tag v-if="record.processCompleteStatus === 0" color="pink">否</a-tag>
              <a-tag v-else-if="record.processCompleteStatus === 1" color="cyan">是</a-tag>
              <a-tag v-else-if="record.processCompleteStatus === -1" color="red">已终止</a-tag>
            </template>

            <template v-if="column.key === 'termSnBindStatus'">
              <a-tag v-if="record.termSnBindStatus === 0">未处理</a-tag>
              <a-tag v-else-if="record.termSnBindStatus === 1" color="cyan">处理中</a-tag>
              <a-tag v-else-if="record.termSnBindStatus === 2" color="green">处理成功</a-tag>
              <a-tag v-else-if="record.termSnBindStatus === 3" color="red">处理失败</a-tag>
              <a-tag v-else-if="record.termSnBindStatus === 4" color="blue">无需处理</a-tag>
              <a-tag v-else-if="record.termSnBindStatus === -1" color="red">已终止</a-tag>
            </template>

            <template v-if="column.key === 'termSwitchBrandStatus'">
              <a-tag v-if="record.termSwitchBrandStatus === 0">未处理</a-tag>
              <a-tag v-else-if="record.termSwitchBrandStatus === 1" color="cyan">处理中</a-tag>
              <a-tag v-else-if="record.termSwitchBrandStatus === 2" color="green">处理成功</a-tag>
              <a-tag v-else-if="record.termSwitchBrandStatus === 3" color="red">处理失败</a-tag>
              <a-tag v-else-if="record.termSwitchBrandStatus === 4" color="blue">无需处理</a-tag>
              <a-tag v-else-if="record.termSwitchBrandStatus === -1" color="red">已终止</a-tag>
            </template>

            <template v-if="column.key === 'termUnionReportStatus'">
              <a-tag v-if="record.termUnionReportStatus === 0">未处理</a-tag>
              <a-tag v-else-if="record.termUnionReportStatus === 1" color="cyan">处理中</a-tag>
              <a-tag v-else-if="record.termUnionReportStatus === 2" color="green">处理成功</a-tag>
              <a-tag v-else-if="record.termUnionReportStatus === 3" color="red">处理失败</a-tag>
              <a-tag v-else-if="record.termUnionReportStatus === 4" color="blue">无需处理</a-tag>
              <a-tag v-else-if="record.termUnionReportStatus === -1" color="red">已终止</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
                <a-divider type="vertical" />
                <a @click="handleEdit(record)">编辑</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <TerminalBlankReportDetail v-model:visible="showDetail" :detail="current" />

    <TerminalBlankReportEdit v-if="showEdit" v-model:visible="showEdit" :data="current" :channel-codes="channelCodes" @done="reload" />
  </div>
</template>

<script>
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import { TerminalBlankReportApi } from '@/api/terminal-manage/TerminalBlankReportApi';
import TerminalBlankReportEdit from './modules/TerminalBlankReportEdit.vue';
import TerminalBlankReportDetail from './modules/TerminalBlankReportDetail.vue';

export default {
  name: 'TerminalBlankReport',
  components: {
    TerminalBlankReportEdit,
    TerminalBlankReportDetail
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '原支付通道',
          dataIndex: 'channelCode',
          align: 'center',
          customRender: ({ text, record }) => {
            const item = this.channelCodes.find(c => c.channelCode === text);
            record.channelName = item?.channelName || '--';
            return record.channelName;
          }
        },
        {
          title: '目标支付通道',
          dataIndex: 'targetChannelCode',
          align: 'center',
          customRender: ({ text, record }) => {
            const item = this.channelCodes.find(c => c.channelCode === text);
            record.targetChannelName = item?.channelName || '--';
            return record.targetChannelName;
          }
        },
        {
          title: '通道商户名称',
          dataIndex: 'chnMerchName',
          align: 'center'
        },
        {
          title: '通道商户编号',
          dataIndex: 'chnMerchNo',
          align: 'center'
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '终端SN',
          dataIndex: 'termSn',
          align: 'center'
        },
        {
          title: '处理完成状态',
          dataIndex: 'processCompleteStatus',
          key: 'processCompleteStatus',
          align: 'center'
        },
        {
          title: '终端绑定商户状态',
          dataIndex: 'termSnBindStatus',
          key: 'termSnBindStatus',
          align: 'center'
        },
        {
          title: '终端绑定商户描述',
          dataIndex: 'termSnBindMsg',
          width: 180
        },
        {
          title: '终端绑定商户完成时间',
          dataIndex: 'termSnBindCompleteTime',
          align: 'center'
        },
        {
          title: '终端绑定商户发起次数',
          dataIndex: 'termSnBindCount',
          align: 'center'
        },
        {
          title: '终端切换品牌状态',
          dataIndex: 'termSwitchBrandStatus',
          key: 'termSwitchBrandStatus',
          align: 'center'
        },
        {
          title: '终端切换品牌描述',
          dataIndex: 'termSwitchBrandMsg',
          width: 180
        },
        {
          title: '终端切换品牌完成时间',
          dataIndex: 'termSwitchBrandCompleteTime',
          align: 'center'
        },
        {
          title: '终端切换品牌发起次数',
          dataIndex: 'termSwitchBrandCount',
          align: 'center'
        },
        {
          title: '终端银联机构报备状态',
          dataIndex: 'termUnionReportStatus',
          key: 'termUnionReportStatus',
          align: 'center'
        },
        {
          title: '终端银联机构报备描述',
          dataIndex: 'termUnionReportMsg',
          width: 180
        },
        {
          title: '终端银联机构报备完成时间',
          dataIndex: 'termUnionReportCompleteTime',
          align: 'center'
        },
        {
          title: '终端银联机构报备发起次数',
          dataIndex: 'termUnionReportCount',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          align: 'center'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime',
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          width: 160,
          fixed: 'right'
        }
      ],
      // 表格搜索条件
      where: {},
      channelCodes: [],
      showEdit: false,
      showDetail: false,
      current: null
    };
  },
  mounted() {
    this.getChannelCodes();
  },
  methods: {
    handleDetail(record) {
      this.current = record;
      this.showDetail = true;
    },

    handleEdit(record) {
      this.current = record;
      this.showEdit = true;
    },

    async getChannelCodes() {
      const data = await ChannelManageApi.list({ validStatus: 1 });
      this.channelCodes = data || [];
    },

    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    datasource({ page, limit, where }) {
      return TerminalBlankReportApi.findPage({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>
