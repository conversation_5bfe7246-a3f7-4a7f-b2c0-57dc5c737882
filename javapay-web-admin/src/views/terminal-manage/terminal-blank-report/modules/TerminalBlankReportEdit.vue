<template>
  <a-modal
    :width="888"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新增'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules" layout="vertical">
      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="终端绑定商户状态" name="termSnBindStatus">
            <a-select v-model:value="form.termSnBindStatus" style="width: 100%" placeholder="请选择">
              <a-select-option :value="0">未处理</a-select-option>
              <a-select-option :value="1">处理中</a-select-option>
              <a-select-option :value="2">处理成功</a-select-option>
              <a-select-option :value="3">处理失败</a-select-option>
              <a-select-option :value="4">无需处理</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="终端绑定商户发起次数" name="termSnBindCount">
            <a-input-number
              v-model:value="form.termSnBindCount"
              placeholder="终端绑定商户发起次数"
              autocomplete="off"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="终端切换品牌状态" name="termSwitchBrandStatus">
            <a-select v-model:value="form.termSwitchBrandStatus" style="width: 100%" placeholder="请选择">
              <a-select-option :value="0">未处理</a-select-option>
              <a-select-option :value="1">处理中</a-select-option>
              <a-select-option :value="2">处理成功</a-select-option>
              <a-select-option :value="3">处理失败</a-select-option>
              <a-select-option :value="4">无需处理</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="终端切换品牌发起次数" name="termSwitchBrandCount">
            <a-input-number
              v-model:value="form.termSwitchBrandCount"
              placeholder="终端切换品牌发起次数"
              autocomplete="off"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="终端银联机构报备状态" name="termUnionReportStatus">
            <a-select v-model:value="form.termUnionReportStatus" style="width: 100%" placeholder="请选择">
              <a-select-option :value="0">未处理</a-select-option>
              <a-select-option :value="1">处理中</a-select-option>
              <a-select-option :value="2">处理成功</a-select-option>
              <a-select-option :value="3">处理失败</a-select-option>
              <a-select-option :value="4">无需处理</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="终端银联机构报备发起次数" name="termUnionReportCount">
            <a-input-number
              v-model:value="form.termUnionReportCount"
              placeholder="终端银联机构报备发起次数"
              autocomplete="off"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { TerminalBlankReportApi } from '@/api/terminal-manage/TerminalBlankReportApi';

export default {
  props: {
    visible: Boolean,
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {},
      // 表单验证规则
      rules: {
        termSnBindStatus: [{ required: true, message: '请选择' }],
        termSnBindCount: [{ required: true, message: '请输入' }],
        termSwitchBrandStatus: [{ required: true, message: '请选择' }],
        termSwitchBrandCount: [{ required: true, message: '请输入' }],
        termUnionReportStatus: [{ required: true, message: '请选择' }],
        termUnionReportCount: [{ required: true, message: '请输入' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false
    };
  },
  created() {
    if (this.data) {
      this.isUpdate = true;
      this.form = Object.assign(this.form, this.data);
    }
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      TerminalBlankReportApi.edit(this.form)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
