<template>
  <a-modal
    :width="800"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
    :footer="null"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="原支付通道">{{ form.channelName }}</a-descriptions-item>
      <a-descriptions-item label="目标支付通道">{{ form.targetChannelName }}</a-descriptions-item>
      <a-descriptions-item label="通道商户名称">{{ form.chnMerchName }}</a-descriptions-item>
      <a-descriptions-item label="通道商户编号">{{ form.chnMerchNo }}</a-descriptions-item>
      <a-descriptions-item label="商户编号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="终端SN">{{ form.termSn }}</a-descriptions-item>
      <a-descriptions-item label="处理完成状态">
        <a-tag v-if="form.processCompleteStatus === 0" color="pink">否</a-tag>
        <a-tag v-else-if="form.processCompleteStatus === 1" color="cyan">是</a-tag>
        <a-tag v-else-if="form.processCompleteStatus === -1" color="red">已终止</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="终端绑定商户状态">
        <a-tag v-if="form.termSnBindStatus === 0">未处理</a-tag>
        <a-tag v-else-if="form.termSnBindStatus === 1" color="cyan">处理中</a-tag>
        <a-tag v-else-if="form.termSnBindStatus === 2" color="green">处理成功</a-tag>
        <a-tag v-else-if="form.termSnBindStatus === 3" color="red">处理失败</a-tag>
        <a-tag v-else-if="form.termSnBindStatus === 4" color="blue">无需处理</a-tag>
        <a-tag v-else-if="form.termSnBindStatus === -1" color="red">已终止</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="终端绑定商户描述">{{ form.termSnBindMsg }}</a-descriptions-item>
      <a-descriptions-item label="终端绑定商户完成时间">{{ form.termSnBindCompleteTime }}</a-descriptions-item>
      <a-descriptions-item label="终端绑定商户发起次数">{{ form.termSnBindCount }}</a-descriptions-item>
      <a-descriptions-item label="终端切换品牌状态">
        <a-tag v-if="form.termSwitchBrandStatus === 0">未处理</a-tag>
        <a-tag v-else-if="form.termSwitchBrandStatus === 1" color="cyan">处理中</a-tag>
        <a-tag v-else-if="form.termSwitchBrandStatus === 2" color="green">处理成功</a-tag>
        <a-tag v-else-if="form.termSwitchBrandStatus === 3" color="red">处理失败</a-tag>
        <a-tag v-else-if="form.termSwitchBrandStatus === 4" color="blue">无需处理</a-tag>
        <a-tag v-else-if="form.termSwitchBrandStatus === -1" color="red">已终止</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="终端切换品牌描述">{{ form.termSwitchBrandMsg }}</a-descriptions-item>
      <a-descriptions-item label="终端切换品牌完成时间">{{ form.termSwitchBrandCompleteTime }}</a-descriptions-item>
      <a-descriptions-item label="终端切换品牌发起次数">{{ form.termSwitchBrandCount }}</a-descriptions-item>
      <a-descriptions-item label="终端银联机构报备状态">
        <a-tag v-if="form.termUnionReportStatus === 0">未处理</a-tag>
        <a-tag v-else-if="form.termUnionReportStatus === 1" color="cyan">处理中</a-tag>
        <a-tag v-else-if="form.termUnionReportStatus === 2" color="green">处理成功</a-tag>
        <a-tag v-else-if="form.termUnionReportStatus === 3" color="red">处理失败</a-tag>
        <a-tag v-else-if="form.termUnionReportStatus === 4" color="blue">无需处理</a-tag>
        <a-tag v-else-if="form.termUnionReportStatus === -1" color="red">已终止</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="终端银联机构报备描述">{{ form.termUnionReportMsg }}</a-descriptions-item>
      <a-descriptions-item label="终端银联机构报备完成时间">{{ form.termUnionReportCompleteTime }}</a-descriptions-item>
      <a-descriptions-item label="终端银联机构报备发起次数">{{ form.termUnionReportCount }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
