<template>
  <a-modal
    :width="800"
    :visible="visible"
    title="详情"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
    :footer="null"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="终端厂商">{{ form.factoryName }}</a-descriptions-item>
      <a-descriptions-item label="终端类型">
        <a-tag v-if="form.terminalType === 1" color="pink">传统POS(有线)</a-tag>
        <a-tag v-else-if="form.terminalType === 2" color="blue">传统POS(无线)</a-tag>
        <a-tag v-else-if="form.terminalType === 3" color="purple">MPOS</a-tag>
        <a-tag v-else-if="form.terminalType === 4" color="pink">智能POS</a-tag>
        <a-tag v-else-if="form.terminalType === 5" color="blue">条码支付受理终端</a-tag>
        <a-tag v-else-if="form.terminalType === 6" color="purple">条码支付辅助终端</a-tag>
        <a-tag v-else-if="form.terminalType === 7" color="pink">人脸识别终端</a-tag>
        <a-tag v-else-if="form.terminalType === 8" color="blue">手机POS</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="终端型号编码">{{ form.modelCode }}</a-descriptions-item>
      <a-descriptions-item label="终端型号名称">{{ form.modelName }}</a-descriptions-item>
      <a-descriptions-item label="单价(元)">{{ form.price }}</a-descriptions-item>
      <a-descriptions-item label="类型编号">{{ form.typeCode }}</a-descriptions-item>
      <a-descriptions-item label="是否有底座">{{ filterWhether(form.baseFlag) }}</a-descriptions-item>
      <a-descriptions-item label="是否有电池">{{ filterWhether(form.batteryFlag) }}</a-descriptions-item>
      <a-descriptions-item label="是否有电源">{{ filterWhether(form.powerFlag) }}</a-descriptions-item>
      <a-descriptions-item label="是否有打印纸">{{ filterWhether(form.printFlag) }}</a-descriptions-item>
      <a-descriptions-item label="是否有密码键盘">{{ filterWhether(form.pwdFlag) }}</a-descriptions-item>
      <a-descriptions-item label="免密标志">{{ form.freeFlag === 1 ? '已更新' : '待更新' }}</a-descriptions-item>
      <a-descriptions-item label="是否支持接触式IC卡">{{ filterWhether(form.icFlag) }}</a-descriptions-item>
      <a-descriptions-item label="是否支持非接IC">{{ filterWhether(form.icNonFla) }}</a-descriptions-item>
      <a-descriptions-item label="是否有扫描头">{{ filterWhether(form.scanFlag) }}</a-descriptions-item>
      <a-descriptions-item label="是否支持磁卡">{{ filterWhether(form.trackFlag) }}</a-descriptions-item>
      <a-descriptions-item label="备注" :span="2">{{ form.remark }}</a-descriptions-item>
      <a-descriptions-item label="有效状态" :span="2">
        <a-tag v-if="form.validStatus === 1" color="success">有效</a-tag>
        <a-tag v-else>无效</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="创建人">{{ form.createUserId }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改人">{{ form.lastModifyUserId }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

export default {
  props: {
    visible: Boolean,
    detail: Object,
    factorys: Array
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const filterWhether = value => {
      if (value === 1) return '是';
      return '否';
    };

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible,
      filterWhether
    };
  }
};
</script>
