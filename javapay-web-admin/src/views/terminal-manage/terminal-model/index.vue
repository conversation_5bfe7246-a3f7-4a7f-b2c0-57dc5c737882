<template>
  <div class="ele-body">
    <!-- 搜索框内容 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="型号名称">
              <a-input v-model:value.trim="where.modelName" placeholder="请输入型号名称" allow-clear />
            </a-form-item>
            <a-form-item label="终端厂商">
              <a-select v-model:value="where.factoryId" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option v-for="(item, key) in factorys" :key="key" :value="item.id">{{ item.factoryName }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="终端类型">
              <a-select v-model:value="where.terminalType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">传统POS(有线)</a-select-option>
                <a-select-option :value="2">传统POS(无线)</a-select-option>
                <a-select-option :value="3">MPOS</a-select-option>
                <a-select-option :value="4">智能POS</a-select-option>
                <a-select-option :value="5">条码支付受理终端</a-select-option>
                <a-select-option :value="6">条码支付辅助终端</a-select-option>
                <a-select-option :value="7">人脸识别终端</a-select-option>
                <a-select-option :value="8">手机POS</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="有效状态">
              <a-select v-model:value="where.validStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">有效</a-select-option>
                <a-select-option :value="0">无效</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格内容 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- 表格上方的操作按钮 -->
          <template #toolbar>
            <a-space>
              <a-button v-if="hasPurview(['0'])" type="primary" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>

          <!-- 表体的操作 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'validStatus'">
              <a-tag v-if="record.validStatus === 1" color="success">有效</a-tag>
              <a-tag v-else>无效</a-tag>
            </template>
            <template v-else-if="column.key === 'freeFlag'">
              <span v-if="record.freeFlag === 1">已更新</span>
              <span v-else>待更新</span>
            </template>
            <template v-else-if="column.key === 'terminalType'">
              <a-tag v-if="record.terminalType === 1" color="pink">传统POS(有线)</a-tag>
              <a-tag v-else-if="record.terminalType === 2" color="blue">传统POS(无线)</a-tag>
              <a-tag v-else-if="record.terminalType === 3" color="purple">MPOS</a-tag>
              <a-tag v-else-if="record.terminalType === 4" color="pink">智能POS</a-tag>
              <a-tag v-else-if="record.terminalType === 5" color="blue">条码支付受理终端</a-tag>
              <a-tag v-else-if="record.terminalType === 6" color="purple">条码支付辅助终端</a-tag>
              <a-tag v-else-if="record.terminalType === 7" color="pink">人脸识别终端</a-tag>
              <a-tag v-else-if="record.terminalType === 8" color="blue">手机POS</a-tag>
            </template>
            <template
              v-else-if="
                ['baseFlag', 'batteryFlag', 'icFlag', 'icNonFlag', 'powerFlag', 'printFlag', 'pwdFlag', 'scanFlag', 'trackFlag'].includes(
                  column.key
                )
              "
            >
              <span v-if="record[column.key] === 1">是</span>
              <span v-else>否</span>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <template v-if="hasPurview(['0'])">
                  <a @click="handleEdit(record)">修改</a>
                  <a-divider type="vertical" />
                </template>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 新增编辑 -->
    <TerminalModelEdit v-if="showEdit" v-model:visible="showEdit" :data="current" @done="reload" :factorys="factorys" />

    <!-- 详情 -->
    <TerminalModelDetail v-model:visible="showDetail" :detail="current" :factorys="factorys" />
  </div>
</template>

<script>
import { TerminalModelApi } from '@/api/terminal-manage/TerminalModelApi';
import { TerminalFactoryApi } from '@/api/terminal-manage/TerminalFactoryApi';
import TerminalModelEdit from './TerminalModelEdit.vue';
import TerminalModelDetail from './TerminalModelDetail.vue';
import { hasPurview } from '@/utils/permission';

export default {
  name: 'TerminalModel',
  components: {
    TerminalModelEdit,
    TerminalModelDetail
  },
  data() {
    return {
      //表格查询条件
      where: {},
      //展示编辑页面传的参数
      current: null,
      //是否展示新增或者是编辑页面
      showEdit: false,
      //是否展示详情页面
      showDetail: false,
      //表格列配置
      factorys: [],
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center'
        },
        {
          title: '型号名称',
          dataIndex: 'modelName',
          align: 'center'
        },
        {
          title: '型号编码',
          dataIndex: 'modelCode',
          align: 'center'
        },
        {
          title: '终端厂商',
          dataIndex: 'factoryName',
          align: 'center'
        },
        {
          title: '类型编号',
          dataIndex: 'typeCode',
          align: 'center'
        },
        {
          title: '终端类型',
          dataIndex: 'terminalType',
          align: 'center',
          key: 'terminalType'
        },
        {
          title: '有效状态',
          dataIndex: 'validStatus',
          align: 'center',
          key: 'validStatus'
        },
        {
          title: '单价',
          dataIndex: 'price',
          align: 'center',
          key: 'price'
        },
        {
          title: '备注',
          dataIndex: 'remark'
        },
        {
          title: '是否有底座',
          dataIndex: 'baseFlag',
          align: 'center',
          key: 'baseFlag'
        },
        {
          title: '是否有电池',
          dataIndex: 'batteryFlag',
          align: 'center',
          key: 'batteryFlag'
        },
        {
          title: '免密标志',
          dataIndex: 'freeFlag',
          align: 'center',
          key: 'freeFlag'
        },
        {
          title: '是否支持接触式IC卡',
          dataIndex: 'icFlag',
          align: 'center',
          key: 'icFlag'
        },
        {
          title: '是否支持非接IC',
          dataIndex: 'icNonFlag',
          align: 'center',
          key: 'icNonFlag'
        },
        {
          title: '是否有电源',
          dataIndex: 'powerFlag',
          align: 'center',
          key: 'powerFlag'
        },

        {
          title: '是否有打印纸',
          dataIndex: 'printFlag',
          align: 'center',
          key: 'printFlag'
        },
        {
          title: '是否有密码键盘',
          dataIndex: 'pwdFlag',
          align: 'center',
          key: 'pwdFlag'
        },
        {
          title: '是否有扫描头',
          dataIndex: 'scanFlag',
          align: 'center',
          key: 'scanFlag'
        },
        {
          title: '是否支持磁卡',
          dataIndex: 'trackFlag',
          align: 'center',
          key: 'trackFlag'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 150,
          align: 'center'
        }
      ]
    };
  },
  mounted() {
    this.getFactorys();
  },
  methods: {
    hasPurview,

    async getFactorys() {
      const data = await TerminalFactoryApi.list();
      this.factorys = data || [];
    },
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where }) {
      return TerminalModelApi.findPage({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>

<style></style>
