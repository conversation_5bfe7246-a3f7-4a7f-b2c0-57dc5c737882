<template>
  <a-modal
    :width="880"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新增'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules" :label-col="{ style: { width: '150px' } }">
      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="终端厂商" name="factoryId">
            <a-select v-model:value="form.factoryId" placeholder="请选择">
              <a-select-option v-for="(item, key) in factoryList" :key="key" :value="item.id">
                {{ item.factoryName }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="终端型号编码" name="modelCode">
            <a-input v-model:value="form.modelCode" placeholder="请输入型号编码" />
          </a-form-item>
          <a-form-item label="终端类型" name="terminalType">
            <a-select v-model:value="form.terminalType" placeholder="请选择">
              <a-select-option :value="1">传统POS(有线)</a-select-option>
              <a-select-option :value="2">传统POS(无线)</a-select-option>
              <a-select-option :value="3">MPOS</a-select-option>
              <a-select-option :value="4">智能POS</a-select-option>
              <a-select-option :value="5">条码支付受理终端</a-select-option>
              <a-select-option :value="6">条码支付辅助终端</a-select-option>
              <a-select-option :value="7">人脸识别终端</a-select-option>
              <a-select-option :value="8">手机POS</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="是否有底座" name="baseFlag">
            <a-select v-model:value="form.baseFlag" placeholder="请选择">
              <a-select-option :value="1">是</a-select-option>
              <a-select-option :value="0">否</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="是否有电池" name="batteryFlag">
            <a-select v-model:value="form.batteryFlag" placeholder="请选择">
              <a-select-option :value="1">是</a-select-option>
              <a-select-option :value="0">否</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="免密标志" name="freeFlag">
            <a-select v-model:value="form.freeFlag" placeholder="请选择">
              <a-select-option :value="1">已更新</a-select-option>
              <a-select-option :value="0">待更新</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="是否有打印纸" name="printFlag">
            <a-select v-model:value="form.printFlag" placeholder="请选择">
              <a-select-option :value="1">是</a-select-option>
              <a-select-option :value="0">否</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="是否有密码键盘" name="pwdFlag">
            <a-select v-model:value="form.pwdFlag" placeholder="请选择">
              <a-select-option :value="1">是</a-select-option>
              <a-select-option :value="0">否</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="终端型号名称" name="modelName">
            <a-input v-model:value="form.modelName" placeholder="请输入型号名称" />
          </a-form-item>
          <a-form-item label="类型编号" name="typeCode">
            <a-input v-model:value="form.typeCode" placeholder="请输入类型编号" />
          </a-form-item>
          <a-form-item label="单价(元)" name="price">
            <a-input v-model:value="form.price" placeholder="请输入单价" />
          </a-form-item>
          <a-form-item label="是否支持接触式IC卡" name="icFlag">
            <a-select v-model:value="form.icFlag" placeholder="请选择">
              <a-select-option :value="1">是</a-select-option>
              <a-select-option :value="0">否</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="是否支持非接IC" name="icNonFlag">
            <a-select v-model:value="form.icNonFlag" placeholder="请选择">
              <a-select-option :value="1">是</a-select-option>
              <a-select-option :value="0">否</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="是否有电源" name="powerFlag">
            <a-select v-model:value="form.powerFlag" placeholder="请选择">
              <a-select-option :value="1">是</a-select-option>
              <a-select-option :value="0">否</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="是否有扫描头" name="scanFlag">
            <a-select v-model:value="form.scanFlag" placeholder="请选择">
              <a-select-option :value="1">是</a-select-option>
              <a-select-option :value="0">否</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="是否支持磁卡" name="trackFlag">
            <a-select v-model:value="form.trackFlag" placeholder="请选择">
              <a-select-option :value="1">是</a-select-option>
              <a-select-option :value="0">否</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <a-form-item label="备注" name="remark">
        <a-textarea v-model:value="form.remark" placeholder="请输入备注" :auto-size="{ minRows: 2, maxRows: 5 }" />
      </a-form-item>
      <a-form-item label="有效状态" name="validStatus">
        <a-switch
          v-model:checked="form.validStatus"
          checked-children="有效"
          un-checked-children="无效"
          :checkedValue="1"
          :unCheckedValue="0"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { TerminalModelApi } from '@/api/terminal-manage/TerminalModelApi';

export default {
  props: {
    visible: Boolean,
    data: Object,
    factorys: Array
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {
        pwdFlag: 1,
        trackFlag: 1,
        scanFlag: 1,
        powerFlag: 1,
        freeFlag: 0,
        printFlag: 0,
        baseFlag: 0,
        icFlag: 1,
        batteryFlag: 1,
        icNonFlag: 1,
        terminalType: 3,
        validStatus: 1
      },
      // 表单验证规则
      rules: {
        factoryId: [{ required: true, message: '请选择' }],
        // modelCode: [{ required: true, message: '请输入型号编码' }],
        terminalType: [{ required: true, message: '请选择' }],
        baseFlag: [{ required: true, message: '请选择' }],
        batteryFlag: [{ required: true, message: '请选择' }],
        freeFlag: [{ required: true, message: '请选择' }],
        printFlag: [{ required: true, message: '请选择' }],
        pwdFlag: [{ required: true, message: '请选择' }],
        modelName: [{ required: true, message: '请输入型号名称' }],
        // price: [{ required: true, message: '请输入单价' }],
        icFlag: [{ required: true, message: '请选择' }],
        icNonFlag: [{ required: true, message: '请选择' }],
        powerFlag: [{ required: true, message: '请选择' }],
        scanFlag: [{ required: true, message: '请选择' }],
        trackFlag: [{ required: true, message: '请选择' }],
        validStatus: [{ required: true, message: '请选择' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false,
      factoryList: []
    };
  },
  mounted() {
    if (this.data) {
      this.form = Object.assign({}, this.data);
      this.isUpdate = true;
    } else {
      this.isUpdate = false;
    }

    this.factoryList = [...this.factorys];
    if (!this.isUpdate) {
      this.factoryList = this.factoryList.filter(i => i.validStatus === 1);
    }
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      // 执行编辑或修改方法
      if (this.isUpdate) {
        result = TerminalModelApi.edit(this.form);
      } else {
        result = TerminalModelApi.add(this.form);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
<style lang="less" scoped>
:deep(.ant-divider-horizontal.ant-divider-with-text) {
  margin: 0 0 16px;
}

:deep(.ant-form-item-label) {
  background-color: #f3f5f7;
  text-align: center;
}
</style>
