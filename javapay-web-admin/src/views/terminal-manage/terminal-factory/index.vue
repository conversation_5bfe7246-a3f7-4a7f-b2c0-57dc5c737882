<template>
  <div class="ele-body">
    <!-- 搜索框内容 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="厂商编号">
              <a-input v-model:value.trim="where.factoryCode" placeholder="请输入厂商编号" allow-clear />
            </a-form-item>
            <a-form-item label="厂商名称">
              <a-input v-model:value.trim="where.factoryName" placeholder="请输入厂商名称" allow-clear />
            </a-form-item>
            <a-form-item label="有效状态">
              <a-select v-model:value="where.validStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">有效</a-select-option>
                <a-select-option :value="0">无效</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格内容 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- 表格上方的操作按钮 -->
          <template #toolbar>
            <a-space>
              <a-button v-if="hasPurview(['0'])" type="primary" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>

          <!-- 表体的操作 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'validStatus'">
              <a-tag v-if="record.validStatus === 1" color="success">有效</a-tag>
              <a-tag v-else>无效</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a v-if="hasPurview(['0'])" @click="handleEdit(record)">修改</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 新增编辑 -->
    <TerminalFactoryEdit v-if="showEdit" v-model:visible="showEdit" :data="current" @done="reload" />
  </div>
</template>

<script>
import { TerminalFactoryApi } from '@/api/terminal-manage/TerminalFactoryApi';
import TerminalFactoryEdit from './TerminalFactoryEdit.vue';
import { hasPurview } from '@/utils/permission';

export default {
  name: 'TerminalFactory',
  components: {
    TerminalFactoryEdit
  },
  data() {
    return {
      //表格查询条件
      where: {},
      //展示编辑页面传的参数
      current: null,
      //是否展示新增或者是编辑页面
      showEdit: false,
      //是否展示详情页面
      showDetail: false,
      //表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center'
        },
        {
          title: '厂商编号',
          dataIndex: 'factoryCode',
          align: 'center'
        },
        {
          title: '厂商名称',
          dataIndex: 'factoryName',
          align: 'center'
        },
        {
          title: '有效状态',
          dataIndex: 'validStatus',
          align: 'center',
          key: 'validStatus'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          align: 'center'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId',
          align: 'center'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime',
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 120,
          align: 'center'
        }
      ]
    };
  },
  methods: {
    hasPurview,

    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where }) {
      return TerminalFactoryApi.findPage({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>
