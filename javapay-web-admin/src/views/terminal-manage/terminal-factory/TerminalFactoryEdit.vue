<template>
  <a-modal
    :width="800"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新增'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules" :label-col="{ style: { width: '150px' } }">
      <a-form-item label="厂商编号" name="factoryCode">
        <a-input v-model:value="form.factoryCode" placeholder="请输入厂商编号" />
      </a-form-item>
      <a-form-item label="厂商名称" name="factoryName">
        <a-input v-model:value="form.factoryName" placeholder="请输入厂商名称" />
      </a-form-item>
      <a-form-item label="有效状态" name="validStatus">
        <a-switch
          v-model:checked="form.validStatus"
          checked-children="有效"
          un-checked-children="无效"
          :checkedValue="1"
          :unCheckedValue="0"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { TerminalFactoryApi } from '@/api/terminal-manage/TerminalFactoryApi';

export default {
  props: {
    visible: Boolean,
    data: Object,
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {
        validStatus: 1
      },
      // 表单验证规则
      rules: {
        factoryCode: [{ required: true, message: '请输入厂商编号' }],
        factoryName: [{ required: true, message: '请输入厂商名称' }],
        validStatus: [{ required: true, message: '请选择' }],
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false
    };
  },
  mounted() {
    if (this.data) {
      this.form = Object.assign({}, this.data);
      this.isUpdate = true;
    } else {
      this.isUpdate = false;
    }
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      // 执行编辑或修改方法
      if (this.isUpdate) {
        result = TerminalFactoryApi.edit(this.form);
      } else {
        result = TerminalFactoryApi.add(this.form);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
<style lang="less" scoped>
:deep(.ant-divider-horizontal.ant-divider-with-text) {
  margin: 0 0 16px;
}

:deep(.ant-form-item-label) {
  background-color: #f3f5f7;
  text-align: center;
}
</style>
