<template>
  <div class="ele-body">
    <a-spin :spinning="spinning" tip="下载中, 请稍候...">
      <!-- 搜索框内容 -->
      <div class="block-interval">
        <a-card :bordered="false">
          <a-form layout="inline" :model="where">
            <a-row :gutter="[0, 16]">
              <a-form-item label="订单号">
                <a-input v-model:value.trim="where.orderNo" placeholder="订单号" allow-clear />
              </a-form-item>
              <a-form-item label="批次号">
                <a-input v-model:value.trim="where.batchNo" placeholder="批次号" allow-clear />
              </a-form-item>
              <a-form-item label="直属代理商编号">
                <a-input v-model:value.trim="where.agentNo" placeholder="请输入直属代理商编号" allow-clear />
              </a-form-item>
              <a-form-item label="渠道商户编号">
                <a-input v-model:value.trim="where.chnMerchNo" placeholder="请输入渠道商户编号" allow-clear />
              </a-form-item>
              <a-form-item label="商户编号">
                <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
              </a-form-item>
              <a-form-item label="终端编号">
                <a-input v-model:value.trim="where.terminalNo" placeholder="请输入终端编号" allow-clear />
              </a-form-item>
              <a-form-item label="终端SN">
                <a-input v-model:value.trim="where.terminalSn" placeholder="请输入终端SN" allow-clear />
              </a-form-item>
              <a-form-item label="法人证件号码">
                <a-input v-model:value.trim="where.legalCertNo" placeholder="请输入法人证件号码" allow-clear />
              </a-form-item>
              <a-form-item label="是否需要意愿核身">
                <a-select v-model:value="where.isNeedIdentityVert" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option :value="1">是</a-select-option>
                  <a-select-option :value="0">否</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="订单类型">
                <a-select v-model:value="where.orderType" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option :value="1">服务费</a-select-option>
                  <a-select-option :value="2">Sim流量费</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="支付状态">
                <a-select v-model:value="where.payStatus" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option :value="1">未支付</a-select-option>
                  <a-select-option :value="2">已支付</a-select-option>
                  <a-select-option :value="3">支付失败</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="续约状态">
                <a-select v-model:value="where.renewalStatus" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option :value="1">是</a-select-option>
                  <a-select-option :value="0">否</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="结算状态">
                <a-select v-model:value="where.settleStatus" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option :value="1">未结算</a-select-option>
                  <a-select-option :value="2">已结算</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="有效状态">
                <a-select v-model:value="where.validStatus" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option :value="1">有效</a-select-option>
                  <a-select-option :value="0">无效</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="支付开始日期">
                <a-date-picker v-model:value="where.searchBeginPayTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
              </a-form-item>
              <a-form-item label="支付结束日期">
                <a-date-picker v-model:value="where.searchEndPayTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
              </a-form-item>
              <a-form-item class="ele-text-center">
                <a-space>
                  <a-button type="primary" @click="reload">查询</a-button>
                  <a-button @click="reset">重置</a-button>
                </a-space>
              </a-form-item>
            </a-row>
          </a-form>
        </a-card>
      </div>

      <!-- 表格内容 -->
      <div>
        <a-card :bordered="false" class="table-height">
          <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
            <template #toolbar>
              <div style="margin-bottom: 10px">
                <a-space>
                  <span style="margin-right: 20px">
                    <span style="color: red">应收总金额:</span>
                    <span style="color: black">{{ transFeeSummary.originPayAmount }}</span>
                  </span>
                  <span style="margin-right: 20px" v-if="hasPurview('0')">
                    <span style="color: red">实收总金额:</span>
                    <span style="color: black">{{ transFeeSummary.actualPayAmount }}</span>
                  </span>
                </a-space>
              </div>
              <a-space>
                <a-button @click="handleExportExcel">
                  <template #icon>
                    <download-outlined />
                  </template>
                  <span>导出excel</span>
                </a-button>
              </a-space>
            </template>
            <!-- 表体的操作 -->
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'validStatus'">
                <a-tag v-if="record.validStatus === 1" color="success">有效</a-tag>
                <a-tag v-else>无效</a-tag>
              </template>
              <template v-else-if="column.key === 'isNeedIdentityVert'">
                <a-tag v-if="record.isNeedIdentityVert === 1" color="success">是</a-tag>
                <a-tag v-else>否</a-tag>
              </template>
              <template v-else-if="column.key === 'orderType'">
                <a-tag v-if="record.orderType === 1" color="pink">服务费</a-tag>
                <a-tag v-else-if="record.orderType === 2" color="purple">Sim流量费</a-tag>
              </template>
              <template v-else-if="column.key === 'payStatus'">
                <a-tag v-if="record.payStatus === 1">未支付</a-tag>
                <a-tag v-else-if="record.payStatus === 2" color="success">已支付</a-tag>
                <a-tag v-else-if="record.payStatus === 3" color="error">支付失败</a-tag>
              </template>
              <template v-else-if="column.key === 'renewalStatus'">
                <a-tag v-if="record.renewalStatus === 1" color="success">是</a-tag>
                <a-tag v-else>否</a-tag>
              </template>
              <template v-else-if="column.key === 'settleStatus'">
                <a-tag v-if="record.settleStatus === 1">未结算</a-tag>
                <a-tag v-else-if="record.settleStatus === 2" color="success">已结算</a-tag>
              </template>

              <!-- table操作栏按钮 -->
              <template v-else-if="column.key === 'action'">
                <a-space>
                  <a @click="handleDetail(record)">详情</a>
                </a-space>
              </template>
            </template>
          </ele-pro-table>
        </a-card>
      </div>
    </a-spin>

    <TerminalActivePayOrderDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { TerminalActivePayOrderApi } from '@/api/terminal-manage/TerminalActivePayOrderApi';
import { hasPurview } from '@/utils/permission';
import TerminalActivePayOrderDetail from './terminal-active-pay-order-detail.vue';
import { message } from 'ant-design-vue';

export default {
  name: 'TerminalActivePayOrder',
  components: {
    TerminalActivePayOrderDetail
  },
  data() {
    return {
      spinning: false,
      //表格查询条件
      where: {},
      //展示编辑页面传的参数
      current: null,
      //是否展示详情页面
      showDetail: false,
      transFeeSummary: {},
      //表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center'
        },
        {
          title: '终端编号',
          dataIndex: 'terminalNo',
          align: 'center'
        },
        {
          title: '终端SN',
          dataIndex: 'terminalSn',
          align: 'center'
        },
        {
          title: '实收金额(元)',
          dataIndex: 'actualPayAmt',
          align: 'center',
          hideCol: !hasPurview('0')
        },
        {
          title: '应收金额(元)',
          dataIndex: 'originPayAmt',
          align: 'center'
        },
        {
          title: '直属代理商编号',
          dataIndex: 'agentNo',
          align: 'center'
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '通道活动编号',
          dataIndex: 'chlActiveNo',
          align: 'center'
        },
        {
          title: '通道商户编号',
          dataIndex: 'chnMerchNo',
          align: 'center'
        },
        {
          title: '期数',
          dataIndex: 'cycleDay',
          align: 'center'
        },
        {
          title: '是否需要意愿核身',
          dataIndex: 'isNeedIdentityVert',
          key: 'isNeedIdentityVert',
          align: 'center'
        },
        {
          title: '订单类型',
          dataIndex: 'orderType',
          key: 'orderType',
          align: 'center'
        },
        {
          title: '支付原因',
          dataIndex: 'payReason',
          align: 'center'
        },
        {
          title: '支付状态',
          dataIndex: 'payStatus',
          key: 'payStatus',
          align: 'center'
        },
        {
          title: '支付时间',
          dataIndex: 'payTime',
          align: 'center'
        },
        {
          title: '续约状态',
          dataIndex: 'renewalStatus',
          key: 'renewalStatus',
          align: 'center'
        },
        {
          title: '结算状态',
          dataIndex: 'settleStatus',
          key: 'settleStatus',
          align: 'center'
        },
        {
          title: '订单号',
          dataIndex: 'orderNo',
          align: 'center'
        },
        {
          title: '批次号',
          dataIndex: 'batchNo',
          align: 'center'
        },
        {
          title: '结算时间',
          dataIndex: 'settleTime',
          align: 'center'
        },
        {
          title: '有效开始时间',
          dataIndex: 'validBeginTime',
          align: 'center'
        },
        {
          title: '有效结束时间',
          dataIndex: 'validEndTime',
          align: 'center'
        },
        {
          title: '有效状态',
          dataIndex: 'validStatus',
          align: 'center',
          key: 'validStatus'
        },
        {
          title: '法人证件号码（掩码）',
          dataIndex: 'legalCertNoMask',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          align: 'center'
        },
        {
          title: '最后更新时间',
          dataIndex: 'lastModifyTime',
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 120,
          align: 'center'
        }
      ].filter(i => !i.hideCol)
    };
  },
  mounted() {
    this.getTransFeeSummaryData();
  },
  methods: {
    async handleExportExcel() {
      this.spinning = true;
      const res = await TerminalActivePayOrderApi.downloadPayorder(this.where).catch(() => {
        this.spinning = false;
      });
      this.spinning = false;
      const fileReader = new FileReader();
      fileReader.onload = function () {
        try {
          // 说明是普通对象数据，后台转换失败
          const jsonData = JSON.parse(fileReader.result);
          message.error(jsonData.message);
        } catch (err) {
          const contentDisposition = res.headers['content-disposition'];
          let fileName = decodeURIComponent(contentDisposition.substring(contentDisposition.indexOf('=') + 1));
          fileName = '终端权益订单-下载.xlsx';
          // 解析对象失败，说明是正常的文件流
          let blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.style.display = 'none';
          a.href = url;
          a.download = fileName;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }
      };
      fileReader.readAsText(res?.data);
    },
    hasPurview,

    reload() {
      this.$refs.table.reload({ page: 1 });
      this.getTransFeeSummaryData();
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
      this.getTransFeeSummaryData();
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    //列表查询统计
    async getTransFeeSummaryData() {
      this.transFeeSummary = (await TerminalActivePayOrderApi.sum(this.where)) || {};
    },

    datasource({ page, limit, where }) {
      return TerminalActivePayOrderApi.findPage({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>
