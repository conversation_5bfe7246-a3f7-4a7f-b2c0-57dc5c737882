<template>
  <a-modal
    :width="800"
    :visible="visible"
    title="详情"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
    :footer="null"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="终端编号">{{ form.terminalNo }}</a-descriptions-item>
      <a-descriptions-item label="终端SN">{{ form.terminalSn }}</a-descriptions-item>
      <a-descriptions-item label="实收金额(元)" v-if="hasPurview('0')">{{ form.actualPayAmt }}</a-descriptions-item>
      <a-descriptions-item label="应收金额(元)">{{ form.originPayAmt }}</a-descriptions-item>
      <a-descriptions-item label="直属代理商编号">{{ form.agentNo }}</a-descriptions-item>
      <a-descriptions-item label="商户编号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="通道活动编号">{{ form.chlActiveNo }}</a-descriptions-item>
      <a-descriptions-item label="通道商户编号">{{ form.chnMerchNo }}</a-descriptions-item>
      <a-descriptions-item label="期数">{{ form.cycleDay }}</a-descriptions-item>
      <a-descriptions-item label="是否需要意愿核身">{{ form.isNeedIdentityVert }}</a-descriptions-item>
      <a-descriptions-item label="订单类型">
        <a-tag v-if="form.orderType === 1" color="pink">服务费</a-tag>
        <a-tag v-else-if="form.orderType === 2" color="purple">Sim流量费</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="支付原因">{{ form.payReason }}</a-descriptions-item>
      <a-descriptions-item label="支付状态">
        <a-tag v-if="form.payStatus === 1">未支付</a-tag>
        <a-tag v-else-if="form.payStatus === 2" color="success">已支付</a-tag>
        <a-tag v-else-if="form.payStatus === 3" color="error">支付失败</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="支付时间">
        {{ form.payTime }}
      </a-descriptions-item>
      <a-descriptions-item label="续约状态">
        <a-tag v-if="form.renewalStatus === 1" color="success">是</a-tag>
        <a-tag v-else>否</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="结算状态">
        <a-tag v-if="form.settleStatus === 1">未结算</a-tag>
        <a-tag v-else-if="form.settleStatus === 2" color="success">已结算</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="订单号">{{ form.orderNo }}</a-descriptions-item>
      <a-descriptions-item label="批次号">{{ form.batchNo }}</a-descriptions-item>
      <a-descriptions-item label="结算时间">{{ form.settleTime }}</a-descriptions-item>
      <a-descriptions-item label="有效开始时间">{{ form.validBeginTime }}</a-descriptions-item>
      <a-descriptions-item label="有效结束时间">{{ form.validEndTime }}</a-descriptions-item>
      <a-descriptions-item label="有效状态">
        <a-tag v-if="form.validStatus === 1" color="success">有效</a-tag>
        <a-tag v-else>无效</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="法人证件号码（掩码）">{{ form.legalCertNoMask }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后更新时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>
  </a-modal>
</template>

<script>
import { hasPurview } from '@/utils/permission';
import { reactive, toRefs, watchEffect } from 'vue';

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const filterWhether = value => {
      if (value === 1) return '是';
      return '否';
    };

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible,
      filterWhether,
      hasPurview
    };
  }
};
</script>
