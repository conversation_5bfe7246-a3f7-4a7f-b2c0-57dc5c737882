<template>
  <a-modal
    :width="800"
    :visible="visible"
    title="详情"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
    :footer="null"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="终端编号">{{ form.terminalNo }}</a-descriptions-item>
      <a-descriptions-item label="终端SN">{{ form.terminalSn }}</a-descriptions-item>
      <a-descriptions-item label="批次id">{{ form.batchId }}</a-descriptions-item>
      <a-descriptions-item label="批次号">{{ form.batchNo }}</a-descriptions-item>
      <a-descriptions-item label="调拨类型">
        <a-tag v-if="form.optType === 1" color="blue">回拨</a-tag>
        <a-tag v-else-if="form.optType === 0" color="purple">下发</a-tag>
        <a-tag v-else-if="form.optType === 2" color="purple">划拨</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="调拨状态">
        <a-tag v-if="form.status === 1" color="success">成功</a-tag>
        <a-tag v-else-if="form.status === 0" color="error">失败</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="原机构编号">{{ form.origAgentNo }}</a-descriptions-item>
      <a-descriptions-item label="目标机构编号">{{ form.targAgentNo }}</a-descriptions-item>
      <a-descriptions-item label="失败原因">{{ form.failedDesc }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
    </a-descriptions>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
