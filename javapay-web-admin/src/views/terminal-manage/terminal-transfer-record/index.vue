<template>
  <div class="ele-body">
    <!-- 搜索框内容 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="批次号">
              <a-input v-model:value.trim="where.batchNo" placeholder="请输入批次号" allow-clear />
            </a-form-item>
            <a-form-item label="终端编号">
              <a-input v-model:value.trim="where.terminalNo" placeholder="请输入终端编号" allow-clear />
            </a-form-item>
            <a-form-item label="终端SN">
              <a-input v-model:value.trim="where.terminalSn" placeholder="请输入终端SN" allow-clear />
            </a-form-item>
            <a-form-item label="原机构编号">
              <a-input v-model:value.trim="where.sourceOrgCode" placeholder="请输入原机构编号" allow-clear />
            </a-form-item>
            <a-form-item label="目标机构编号">
              <a-input v-model:value.trim="where.targetOrgCode" placeholder="请输入目标机构编号" allow-clear />
            </a-form-item>
            <a-form-item label="调拨状态">
              <a-select v-model:value="where.status" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">成功</a-select-option>
                <a-select-option :value="0">失败</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="调拨类型">
              <a-select v-model:value="where.optType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">下发</a-select-option>
                <a-select-option :value="1">回拨</a-select-option>
                <a-select-option :value="2">划拨</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格内容 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- 表体的操作 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag v-if="record.status === 1" color="success">成功</a-tag>
              <a-tag v-else-if="record.status === 0" color="error">失败</a-tag>
            </template>
            <template v-else-if="column.key === 'optType'">
              <a-tag v-if="record.optType === 1" color="blue">回拨</a-tag>
              <a-tag v-else-if="record.optType === 0" color="purple">下发</a-tag>
              <a-tag v-else-if="record.optType === 2" color="orange">划拨</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <TerminalTransferRetailDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { TerminalTransferRecordApi } from '@/api/terminal-manage/TerminalTransferRecordApi';
import TerminalTransferRetailDetail from './terminal-transfer-record-detail.vue';

export default {
  name: 'TerminalTransferRecord',
  components: {
    TerminalTransferRetailDetail
  },
  data() {
    return {
      //表格查询条件
      where: {},
      //展示编辑页面传的参数
      current: null,
      //是否展示详情页面
      showDetail: false,
      //表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center'
        },
        {
          title: '终端编号',
          dataIndex: 'terminalNo',
          align: 'center'
        },
        {
          title: '终端SN',
          dataIndex: 'terminalSn',
          align: 'center'
        },
        {
          title: '批次id',
          dataIndex: 'batchId',
          align: 'center'
        },
        {
          title: '批次号',
          dataIndex: 'batchNo',
          align: 'center'
        },

        {
          title: '调拨类型',
          dataIndex: 'optType',
          key: 'optType',
          align: 'center'
        },
        {
          title: '调拨状态',
          dataIndex: 'status',
          key: 'status',
          align: 'center'
        },
        {
          title: '原机构编号',
          dataIndex: 'origAgentNo',
          align: 'center'
        },
        {
          title: '目标机构编号',
          dataIndex: 'targAgentNo',
          align: 'center'
        },
        {
          title: '失败原因',
          dataIndex: 'failedDesc',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 100,
          align: 'center'
        }
      ]
    };
  },
  methods: {
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where }) {
      return TerminalTransferRecordApi.findPage({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>
