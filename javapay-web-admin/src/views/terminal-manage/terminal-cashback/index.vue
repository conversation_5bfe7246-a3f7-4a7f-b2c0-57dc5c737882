<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="终端SN">
              <a-input v-model:value.trim="where.terminalSn" placeholder="请输入终端SN" allow-clear />
            </a-form-item>
            <a-form-item label="规则序号">
              <a-input v-model:value.trim="where.ruleNo" placeholder="请输入规则序号" allow-clear />
            </a-form-item>
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="通道商户编号">
              <a-input v-model:value.trim="where.chnMerchNo" placeholder="请输入通道商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="返现活动类型">
              <a-select v-model:value="where.cashbackType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">激活</a-select-option>
                <a-select-option :value="1">达标</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="活动状态">
              <a-select v-model:value="where.standardStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">未达标</a-select-option>
                <a-select-option :value="1">已达标</a-select-option>
                <a-select-option :value="2">不再达标</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="结算状态">
              <a-select v-model:value="where.settleStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">未结算</a-select-option>
                <a-select-option :value="2">已结算</a-select-option>
                <a-select-option :value="3">不结算</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'cashbackType'">
              <a-tag v-if="record.cashbackType === 0" color="purple">激活</a-tag>
              <a-tag v-else-if="record.cashbackType === 1" color="blue">达标</a-tag>
            </template>
            <template v-else-if="column.key === 'standardStatus'">
              <a-tag v-if="record.standardStatus === 0">未达标</a-tag>
              <a-tag v-else-if="record.standardStatus === 1" color="success">已达标</a-tag>
              <a-tag v-else-if="record.standardStatus === 2" color="orange">不再达标</a-tag>
            </template>
            <template v-else-if="column.key === 'settleStatus'">
              <a-tag v-if="record.settleStatus === 1">未结算</a-tag>
              <a-tag v-else-if="record.settleStatus === 2" color="success">已结算</a-tag>
              <a-tag v-else-if="record.settleStatus === 3" color="warning">不结算</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <TerminalCashbackDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { TerminalCashbackApi } from '@/api/terminal-manage/TerminalCashbackApi';
import TerminalCashbackDetail from './TerminalCashbackDetail.vue';

export default {
  name: 'TerminalCashback',
  components: {
    TerminalCashbackDetail
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '终端SN',
          dataIndex: 'terminalSn',
          align:'center'
        },
        {
          title: '规则序号',
          dataIndex: 'ruleNo',
          align:'center'
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo',
          align:'center'
        },
        {
          title: '通道商户编号',
          dataIndex: 'chnMerchNo',
          align:'center'
        },
        {
          title: '返现活动类型',
          dataIndex: 'cashbackType',
          key: 'cashbackType',
          align:'center'
        },
        {
          title: '活动状态',
          dataIndex: 'standardStatus',
          key: 'standardStatus',
          align:'center'
        },
        {
          title: '结算状态',
          dataIndex: 'settleStatus',
          key: 'settleStatus',
          align:'center'
        },
        {
          title: '结算时间',
          dataIndex: 'settleTime',
          align:'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          align: 'center'
        }
      ],
      // 表格搜索条件
      where: {},
      current: null,
      showDetail: false
    };
  },
  methods: {
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where, orders }) {
      return TerminalCashbackApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
