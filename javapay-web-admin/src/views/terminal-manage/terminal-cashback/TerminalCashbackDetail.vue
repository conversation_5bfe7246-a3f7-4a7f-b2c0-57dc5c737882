<template>
  <a-modal
    :width="1000"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="终端SN">{{ form.terminalSn }}</a-descriptions-item>
      <a-descriptions-item label="规则序号">{{ form.ruleNo }}</a-descriptions-item>
      <a-descriptions-item label="商户编号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="通道商户编号">{{ form.chnMerchNo }}</a-descriptions-item>
      <a-descriptions-item label="返现活动类型">
        <a-tag v-if="form.cashbackType === 0" color="purple">激活</a-tag>
        <a-tag v-else-if="form.cashbackType === 1" color="blue">达标</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="活动状态">
        <a-tag v-if="form.standardStatus === 0">未达标</a-tag>
        <a-tag v-else-if="form.standardStatus === 1" color="success">已达标</a-tag>
        <a-tag v-else-if="form.standardStatus === 2" color="orange">不再达标</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="结算状态">
        <a-tag v-if="form.settleStatus === 1">未结算</a-tag>
        <a-tag v-else-if="form.settleStatus === 2" color="success">已结算</a-tag>
        <a-tag v-else-if="form.settleStatus === 3" color="warning">不结算</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="结算时间">{{ form.settleTime }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
