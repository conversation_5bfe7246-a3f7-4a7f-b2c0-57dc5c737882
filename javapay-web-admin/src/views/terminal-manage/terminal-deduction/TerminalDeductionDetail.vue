<template>
  <a-modal
    :width="1000"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="终端SN">{{ form.terminalSn }}</a-descriptions-item>
      <a-descriptions-item label="商户编号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="通道商户编号">{{ form.chnMerchNo }}</a-descriptions-item>
      <a-descriptions-item label="机构编号">{{ form.orgNo }}</a-descriptions-item>
      <a-descriptions-item label="机构类型">
        <a-badge v-if="form.orgType === 1" color="pink" text="大区" />
        <a-badge v-else-if="form.orgType === 2" color="blue" text="运营中心" />
        <a-badge v-else-if="form.orgType === 3" color="orange" text="代理商" />
        <a-badge v-else-if="form.orgType === 5" color="orange" text="子级代理商" />
      </a-descriptions-item>
      <a-descriptions-item label="机构等级">{{ form.orgLevel }}</a-descriptions-item>
      <a-descriptions-item label="总扣款金额(元)">{{ form.totalDeductAmt }}</a-descriptions-item>
      <a-descriptions-item label="已扣款金额(元)">{{ form.deductedAmt }}</a-descriptions-item>
      <a-descriptions-item label="剩余扣款金额(元)">{{ form.residueDeductAmt }}</a-descriptions-item>
      <a-descriptions-item label="扣款类型">
        <a-tag v-if="form.deductionType === 1" color="purple">未激活</a-tag>
        <a-tag v-else-if="form.deductionType === 2" color="blue">伪激活</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="扣款状态">
        <a-tag v-if="form.deductionStatus === 0">未扣款</a-tag>
        <a-tag v-else-if="form.deductionStatus === 1" color="orange">部分扣款</a-tag>
        <a-tag v-else-if="form.deductionStatus === 2" color="success">扣款成功</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
