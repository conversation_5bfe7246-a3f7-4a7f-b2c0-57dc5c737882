<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="终端SN">
              <a-input v-model:value.trim="where.terminalSn" placeholder="请输入终端SN" allow-clear />
            </a-form-item>
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="通道商户编号">
              <a-input v-model:value.trim="where.chnMerchNo" placeholder="请输入通道商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="机构编号">
              <a-input v-model:value.trim="where.orgNo" placeholder="请输入机构编号" allow-clear />
            </a-form-item>
            <a-form-item label="机构角色">
              <a-select v-model:value="where.orgType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">大区</a-select-option>
                <a-select-option :value="2">运营中心</a-select-option>
                <a-select-option :value="3">代理商</a-select-option>
                <a-select-option :value="5">子级代理商</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="扣款类型">
              <a-select v-model:value="where.deductionType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">未激活</a-select-option>
                <a-select-option :value="2">伪激活</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="扣款状态">
              <a-select v-model:value="where.deductionStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">未扣款</a-select-option>
                <a-select-option :value="1">部分扣款</a-select-option>
                <a-select-option :value="2">扣款成功</a-select-option>
              </a-select>
            </a-form-item>


            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'deductionType'">
              <a-tag v-if="record.deductionType === 1" color="purple">未激活</a-tag>
              <a-tag v-else-if="record.deductionType === 2" color="blue">伪激活</a-tag>
            </template>
            <template v-else-if="column.key === 'deductionStatus'">
              <a-tag v-if="record.deductionStatus === 0">未扣款</a-tag>
              <a-tag v-else-if="record.deductionStatus === 1" color="orange">部分扣款</a-tag>
              <a-tag v-else-if="record.deductionStatus === 2" color="success">扣款成功</a-tag>
            </template>
            <template v-else-if="column.key === 'orgType'">
              <a-badge v-if="record.orgType === 1" color="pink" text="大区" />
              <a-badge v-else-if="record.orgType === 2" color="blue" text="运营中心" />
              <a-badge v-else-if="record.orgType === 3" color="orange" text="代理商" />
              <a-badge v-else-if="record.orgType === 5" color="orange" text="子级代理商" />
            </template>
            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <TerminalDeductionDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { TerminalDeductionApi } from '@/api/terminal-manage/TerminalDeductionApi';
import TerminalDeductionDetail from './TerminalDeductionDetail.vue';

export default {
  name: 'TerminalDeduction',
  components: {
    TerminalDeductionDetail
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '终端SN',
          dataIndex: 'terminalSn',
          align:'center'
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo',
          align:'center'
        },
        {
          title: '通道商户编号',
          dataIndex: 'chnMerchNo',
          align:'center'
        },
        {
          title: '机构编号',
          dataIndex: 'orgNo',
          align:'center'
        },
        {
          title: '机构类型',
          dataIndex: 'orgType',
          key: 'orgType',
          align:'center'
        },
        {
          title: '机构等级',
          dataIndex: 'orgLevel',
          align:'center'
        },
        {
          title: '总扣款金额(元)',
          dataIndex: 'totalDeductAmt',
          align:'center'
        },
        {
          title: '已扣款金额(元)',
          dataIndex: 'deductedAmt',
          align:'center'
        },
        {
          title: '剩余扣款金额(元)',
          dataIndex: 'residueDeductAmt',
          align:'center'
        },
        {
          title: '扣款类型',
          dataIndex: 'deductionType',
          key: 'deductionType',
          align:'center'
        },
        {
          title: '扣款状态',
          dataIndex: 'deductionStatus',
          key: 'deductionStatus',
          align:'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          align: 'center'
        }
      ],
      // 表格搜索条件
      where: {},
      current: null,
      showDetail: false
    };
  },
  methods: {
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where, orders }) {
      return TerminalDeductionApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
