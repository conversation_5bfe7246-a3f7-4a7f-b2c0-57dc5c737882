<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row>
            <a-form-item label="任务类型:">
              <a-input v-model:value.trim="where.taskType" placeholder="请输入任务类型" allow-clear />
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }" />
      </a-card>
    </div>
  </div>
</template>

<script>
import { TimedTaskApi } from '@/api/taskScheduling/TimedTaskApi';

export default {
  name: 'TaskExecutionRecord',
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '任务类型',
          dataIndex: 'taskType',
          align: 'center'
        },
        {
          title: '执行结果',
          dataIndex: 'executionResult',
          align: 'center'
        },
        {
          title: '执行时间',
          dataIndex: 'executionTime',
          align: 'center'
        },
        {
          title: '计划时间',
          dataIndex: 'planningTime',
          align: 'center'
        },
        {
          title: '下个计划时间',
          dataIndex: 'lastPlanningTime',
          align: 'center'
        },
        {
          title: '备注',
          dataIndex: 'remark'
        }
      ],
      // 表格搜索条件
      where: {},
      // 当前编辑数据
      current: null
    };
  },
  methods: {
    /**
     * 搜索按钮
     *
     * <AUTHOR>
     * @date 2021/4/2 17:03
     */
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    /**
     * 重置搜索
     *
     * <AUTHOR>
     * @date 2021/4/2 17:03
     */
    reset() {
      this.where.taskType = '';
      this.reload();
    },

    /**
     * 获取表格数据
     *
     * <AUTHOR>
     * @date 2022/5/8 15:18
     */
    datasource({ page, limit, where, orders }) {
      return TimedTaskApi.findRecodPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>