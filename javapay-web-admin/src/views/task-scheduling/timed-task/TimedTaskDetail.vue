<template>
  <a-modal
    :width="1000"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2" bordered size="middle">
      <a-descriptions-item label="任务类型">{{ form.taskType }}</a-descriptions-item>
      <a-descriptions-item label="任务名称">{{ form.taskName }}</a-descriptions-item>
      <a-descriptions-item label="任务描述" :span="2">{{ form.description }}</a-descriptions-item>
      <a-descriptions-item label="系统类型">
        <a-tag v-if="form.taskSystemType === 1" color="default">用户任务系统</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="执行方式">
        <a-badge v-if="form.executeWay === 1" color="blue" text="按触发频率执行" />
        <a-badge v-else-if="form.executeWay === 2" color="purple" text="成功后按执行频率执行" />
      </a-descriptions-item>
      <a-descriptions-item label="执行状态" :span="2">
        <a-tag v-if="form.executeStatus === 0" color="default">
          <template #icon> <clock-circle-outlined /> </template> 未执行
        </a-tag>
        <a-tag v-else-if="form.executeStatus === 1" color="processing">
          <template #icon> <sync-outlined :spin="true" /> </template> 执行中
        </a-tag>
        <a-tag v-else-if="form.executeStatus === 2" color="success">
          <template #icon><check-circle-outlined /></template>执行成功
        </a-tag>
        <a-tag v-else-if="form.executeStatus === 3" color="error">
          <template #icon> <close-circle-outlined /> </template> 执行失败
        </a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="失败原因" :span="2" v-if="form.failReason">{{ form.failReason }}</a-descriptions-item>
      <a-descriptions-item label="执行频率">{{ form.executeFrequency }}</a-descriptions-item>
      <a-descriptions-item label="触发频率">{{ form.triggerFrequency }}</a-descriptions-item>
      <a-descriptions-item label="开始执行时间">{{ form.executeStartTime }}</a-descriptions-item>
      <a-descriptions-item label="结束执行时间">{{ form.executeEndTime }}</a-descriptions-item>
      <a-descriptions-item label="上次开始执行时间">{{ form.lastExecuteTime }}</a-descriptions-item>
      <a-descriptions-item label="上次期待成功时间">{{ form.lastExpectSuccessTime }}</a-descriptions-item>
      <a-descriptions-item label="上次失败时间">{{ form.lastFailTime }}</a-descriptions-item>
      <a-descriptions-item label="上次完成时间">{{ form.lastFinishTime }}</a-descriptions-item>
      <a-descriptions-item label="本次执行时间">{{ form.thisExecuteTime }}</a-descriptions-item>
      <a-descriptions-item label="本次完成时间">{{ form.thisFinishTime }}</a-descriptions-item>
      <a-descriptions-item label="下次执行时间" :span="2">{{ form.nextExecuteTime }}</a-descriptions-item>
      <a-descriptions-item label="有效状态">
        <span v-if="form.effectiveStatus === 1" class="ele-text-success">有效</span>
        <span v-else class="ele-text-danger">无效</span>
      </a-descriptions-item>
      <a-descriptions-item label="是否警告">{{ form.isWarning === 1 ? '是' : '否' }}</a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

export default {
  name: 'TimedTaskDetail',
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
