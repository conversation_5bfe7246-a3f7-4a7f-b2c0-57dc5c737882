<template>
  <a-modal
    :width="700"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新增'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules" :labelCol="{ style: { width: '120px' } }">
      <a-form-item label="系统类型" name="taskSystemType">
        <a-select v-model:value="form.taskSystemType" style="width: 100%" placeholder="请选择">
          <a-select-option :value="1">用户任务系统</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="任务类型" name="taskType">
        <a-input v-model:value="form.taskType" placeholder="请输入任务类型" allow-clear />
      </a-form-item>
      <a-form-item label="任务名称" name="taskName">
        <a-input v-model:value="form.taskName" placeholder="请输入任务名称" allow-clear />
      </a-form-item>
      <a-form-item label="任务描述" name="description">
        <a-textarea v-model:value="form.description" :auto-size="{ minRows: 2, maxRows: 4 }" placeholder="请输入任务描述" allow-clear />
      </a-form-item>
      <a-row :gutter="24">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="有效状态" name="effectiveStatus">
            <a-select v-model:value="form.effectiveStatus" style="width: 100%" placeholder="请选择">
              <a-select-option :value="1">有效</a-select-option>
              <a-select-option :value="0">无效</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="是否警告" name="isWarning">
            <a-select v-model:value="form.isWarning" style="width: 100%" placeholder="请选择">
              <a-select-option :value="1">是</a-select-option>
              <a-select-option :value="0">否</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <div style="margin-bottom: 22px">
        <a-divider dashed />
      </div>

      <a-row :gutter="24">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="开始执行时间" name="executeStartTime">
            <a-time-picker v-model:value="form.executeStartTime" value-format="HHmmss" style="width: 100%" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="结束执行时间" name="executeEndTime">
            <a-time-picker v-model:value="form.executeEndTime" value-format="HHmmss" style="width: 100%" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="下次执行时间" name="nextExecuteTime">
            <a-date-picker
              style="width: 100%"
              v-model:value="form.nextExecuteTime"
              format="YYYY-MM-DD HH:mm:ss"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              :disabled-date="disabledDate"
              placeholder="请选择时间"
              :show-time="{ defaultValue: defaultTime }"
            />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="执行状态" name="executeStatus">
            <a-select v-model:value="form.executeStatus" style="width: 100%" placeholder="请选择">
              <a-select-option :value="0">未执行</a-select-option>
              <a-select-option :value="1">执行中</a-select-option>
              <a-select-option :value="2">执行成功</a-select-option>
              <a-select-option :value="3">执行失败</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <div style="margin-bottom: 22px">
        <a-divider dashed />
      </div>

      <a-row :gutter="24">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="执行方式" name="executeWay">
            <a-select v-model:value="form.executeWay" style="width: 100%" placeholder="请选择">
              <a-select-option :value="1">按触发频率执行</a-select-option>
              <a-select-option :value="2">成功后按执行频率执行</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <a-form-item name="triggerFrequency" v-if="form.executeWay">
        <template #label> <span class="form-item__required">触发频率</span> </template>
        <a-row :gutter="[24, 8]">
          <a-col v-for="(f, key) in triggerFrequency" :key="key" :xs="{ span: 8 }" :lg="{ span: 6 }">
            <a-space> <a-input-number v-model:value="f.value" :min="0" class="ele-fluid" /> {{ f.label }} </a-space>
          </a-col>
        </a-row>
      </a-form-item>
      <a-form-item name="executeFrequency" v-if="form.executeWay === 2">
        <template #label> <span class="form-item__required">执行频率</span> </template>
        <a-row :gutter="[24, 8]">
          <a-col v-for="(f, key) in executeFrequency" :key="key" :xs="{ span: 8 }" :lg="{ span: 6 }">
            <a-space> <a-input-number v-model:value="f.value" :min="0" class="ele-fluid" /> {{ f.label }} </a-space>
          </a-col>
        </a-row>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { TimedTaskApi } from '@/api/taskScheduling/TimedTaskApi';
import dayjs from 'dayjs';

export default {
  props: {
    visible: Boolean,
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    const frequencyEnum = () => {
      return [
        { label: '年', value: 0, suffix: 'y' },
        { label: '月', value: 0, suffix: 'M' },
        { label: '周', value: 0, suffix: 'w' },
        { label: '日', value: 0, suffix: 'd' },
        { label: '时', value: 0, suffix: 'H' },
        { label: '分', value: 0, suffix: 'm' },
        { label: '秒', value: 0, suffix: 's' }
      ];
    };

    const validatorFrequency = async keyFrequency => {
      if (!this[keyFrequency].some(f => !!f.value)) {
        return Promise.reject('请至少填写一项频率');
      }
      return Promise.resolve();
    };

    return {
      // 表单数据
      form: {
        taskSystemType: 1,
        effectiveStatus: 1,
        executeStatus: 0,
        isWarning: 0,
        executeStartTime: '000000',
        executeEndTime: '235959'
      },
      // 表单验证规则
      rules: {
        taskType: [{ required: true, message: '请输入任务类型' }],
        taskSystemType: [{ required: true, message: '请选择系统类型' }],
        taskName: [{ required: true, message: '请输入任务名称' }],
        description: [{ required: true, message: '请输入任务描述' }],
        effectiveStatus: [{ required: true, message: '请选择有效状态' }],
        isWarning: [{ required: true, message: '请选择是否警告' }],
        executeStartTime: [{ required: true, message: '请选择开始执行时间' }],
        executeEndTime: [{ required: true, message: '请选择结束执行时间' }],
        nextExecuteTime: [{ required: true, message: '请选择执行时间' }],
        executeWay: [{ required: true, message: '请选择执行方式' }],
        executeStatus: [{ required: true, message: '请选择执行状态' }],
        triggerFrequency: [{ validator: () => validatorFrequency('triggerFrequency'), trigger: 'blur' }],
        executeFrequency: [{ validator: () => validatorFrequency('executeFrequency'), trigger: 'blur' }]
      },
      loading: false,
      isUpdate: false,
      disabledDate: current => current < dayjs().startOf('day'),
      defaultTime: dayjs('00:00:00', 'HH:mm:ss'),
      triggerFrequency: frequencyEnum(),
      executeFrequency: frequencyEnum()
    };
  },
  mounted() {
    const setFrequenc = frequencyKey => {
      const frequencyArr = this.form[frequencyKey]?.split(' ') || [];
      frequencyArr.forEach((f, index) => {
        this[frequencyKey][index].value = f.substring(0, f.length - 1);
      });
    };

    if (this.data) {
      this.isUpdate = true;
      this.form = Object.assign({}, this.data);

      setFrequenc('triggerFrequency');
      setFrequenc('executeFrequency');
    }
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 格式化&设置频率
      const setFormFrequenc = frequencyKey => {
        this.form[frequencyKey] = this[frequencyKey].reduce((pre, cur) => {
          return (pre ? pre + ' ' : pre) + (cur.value || 0) + cur.suffix;
        }, '');
      };
      setFormFrequenc('triggerFrequency');
      setFormFrequenc('executeFrequency');

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      // 执行编辑或修改方法
      if (this.isUpdate) {
        result = TimedTaskApi.edit(this.form);
      } else {
        result = TimedTaskApi.add(this.form);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
<style scoped>
.form-item__required::before {
  display: inline-block;
  margin-right: 4px;
  color: var(--highlight-color);
  font-size: 14px;
  line-height: 1;
  content: '*';
}
</style>
