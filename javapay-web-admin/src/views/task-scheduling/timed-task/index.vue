<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="任务名称">
              <a-input v-model:value.trim="where.taskName" placeholder="请输入任务名称" allow-clear />
            </a-form-item>
            <a-form-item label="任务类型">
              <a-input v-model:value.trim="where.taskType" placeholder="请输入任务类型" allow-clear />
            </a-form-item>
            <a-form-item label="执行状态">
              <a-select v-model:value="where.executeStatus" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">未执行</a-select-option>
                <a-select-option :value="1">执行中</a-select-option>
                <a-select-option :value="2">执行成功</a-select-option>
                <a-select-option :value="3">执行失败</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="执行方式">
              <a-select v-model:value="where.executeWay" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">按触发频率执行</a-select-option>
                <a-select-option :value="2">成功后按执行频率执行</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="系统类型">
              <a-select v-model:value="where.taskSystemType" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">用户任务系统</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="是否警告">
              <a-select v-model:value="where.isWarning" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">是</a-select-option>
                <a-select-option :value="0">否</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="有效状态">
              <a-select v-model:value="where.effectiveStatus" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">有效</a-select-option>
                <a-select-option :value="0">无效</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table
          ref="table"
          row-key="id"
          :datasource="datasource"
          :columns="columns"
          :where="where"
          v-model:current="currentRow"
          :scroll="{ x: 'max-content' }"
        >
          <!-- table上边工具栏 -->
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
              <a-button type="danger" @click="handleForce">
                <template #icon>
                  <exclamation-outlined />
                </template>
                <span>强制执行</span>
              </a-button>
              <a-button @click="handleTaskSwitch" v-if="taskSwitch !== null">
                <template #icon>
                  <pause-outlined v-if="taskSwitch === 1" />
                  <play-circle-outlined v-else />
                </template>
                <span>{{ taskSwitch === 1 ? '暂停任务' : '开启任务' }}</span>
              </a-button>
            </a-space>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'effectiveStatus'">
              <a-tag color="success" v-if="record.effectiveStatus === 1">有效</a-tag>
              <a-tag color="error" v-else>无效</a-tag>
            </template>

            <template v-else-if="column.key === 'executeStatus'">
              <a-tag v-if="record.executeStatus === 0" color="default">
                <template #icon> <clock-circle-outlined /> </template> 未执行
              </a-tag>
              <a-tag v-else-if="record.executeStatus === 1" color="processing">
                <template #icon> <sync-outlined :spin="true" /> </template> 执行中
              </a-tag>
              <a-tag v-else-if="record.executeStatus === 2" color="success">
                <template #icon><check-circle-outlined /></template>执行成功
              </a-tag>
              <a-tag v-else-if="record.executeStatus === 3" color="error">
                <template #icon> <close-circle-outlined /> </template> 执行失败
              </a-tag>
            </template>

            <template v-else-if="column.key === 'taskSystemType'">
              <a-tag v-if="record.taskSystemType === 1" color="default">用户任务系统</a-tag>
            </template>

            <template v-else-if="column.key === 'executeWay'">
              <a-badge v-if="record.executeWay === 1" color="blue" text="按触发频率执行" />
              <a-badge v-else-if="record.executeWay === 2" color="purple" text="成功后按执行频率执行" />
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
                <a-divider type="vertical" />
                <a @click="handleEdit(record)">修改</a>
                <!-- <a-divider type="vertical" />
                <a-popconfirm title="确定要删除吗？" @confirm="remove(record)">
                  <a class="ele-text-danger">删除</a>
                </a-popconfirm> -->
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 编辑 -->
    <TimedTaskEdit v-model:visible="showEdit" v-if="showEdit" :data="current" @done="reload" />

    <!-- 详情 -->
    <TimedTaskDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { message } from 'ant-design-vue';
import { TimedTaskApi } from '@/api/taskScheduling/TimedTaskApi';
import TimedTaskEdit from './TimedTaskEdit.vue';
import TimedTaskDetail from './TimedTaskDetail.vue';

export default {
  name: 'TimedTasks',
  components: { TimedTaskEdit, TimedTaskDetail },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '任务类型',
          dataIndex: 'taskType',
          align: 'center'
        },
        {
          title: '任务名称',
          dataIndex: 'taskName'
        },
        {
          title: '描述',
          dataIndex: 'description',
          width: 200
        },
        {
          title: '系统类型',
          dataIndex: 'taskSystemType',
          key: 'taskSystemType',
          align: 'center'
        },
        {
          title: '执行状态',
          dataIndex: 'executeStatus',
          key: 'executeStatus',
          align: 'center'
        },
        {
          title: '执行方式',
          dataIndex: 'executeWay',
          key: 'executeWay',
          align: 'center'
        },
        {
          title: '有效状态',
          dataIndex: 'effectiveStatus',
          key: 'effectiveStatus',
          width: 120,
          align: 'center'
        },
        {
          title: '执行频率',
          dataIndex: 'executeFrequency',
          align: 'center'
        },
        {
          title: '触发频率',
          dataIndex: 'triggerFrequency',
          align: 'center'
        },
        {
          title: '开始执行时间',
          dataIndex: 'executeStartTime',
          align: 'center'
        },
        {
          title: '结束执行时间',
          dataIndex: 'executeEndTime',
          align: 'center'
        },
        {
          title: '上次完成时间',
          dataIndex: 'lastFinishTime',
          align: 'center'
        },
        {
          title: '本次执行时间',
          dataIndex: 'thisExecuteTime',
          align: 'center'
        },
        {
          title: '本次完成时间',
          dataIndex: 'thisFinishTime',
          align: 'center'
        },
        {
          title: '下次执行时间',
          dataIndex: 'nextExecuteTime',
          align: 'center'
        },
        {
          title: '是否警告',
          dataIndex: 'isWarning',
          align: 'center',
          customRender: function ({ text }) {
            return 1 === text ? '是' : '否';
          }
        },
        {
          title: '操作',
          key: 'action',
          width: 140,
          align: 'center',
          fixed: 'right'
        }
      ],
      // 表格搜索条件
      where: {},
      currentRow: null,
      current: null,
      showDetail: false,
      showEdit: false,
      taskSwitch: null
    };
  },
  mounted() {
    this.getTaskSwitch();
  },
  methods: {
    reload() {
      this.currentRow = null;
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.currentRow = null;
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    handleEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    async handleForce() {
      if (!this.currentRow) return message.warning('请选择一项数据');
      const result = await TimedTaskApi.runTaskForcibly({ id: Number(this.currentRow.id) });
      message.success(result.message);
      this.reload();
    },

    async getTaskSwitch() {
      const data = await TimedTaskApi.queryTaskSwitch();
      this.taskSwitch = Number(data);
    },

    async handleTaskSwitch() {
      const taskMethodName = this.taskSwitch === 1 ? 'pauseTaskSystem' : 'continueTaskSystem';
      const result = await TimedTaskApi[taskMethodName]();
      message.success(result.message);
      this.getTaskSwitch();
    },

    async remove(row) {
      const result = await TimedTaskApi.delete({ id: row.id });
      message.success(result.message);
      this.reload();
    },

    datasource({ page, limit, where, orders }) {
      return TimedTaskApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
