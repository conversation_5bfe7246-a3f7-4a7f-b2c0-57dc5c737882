<!-- 新增和编辑弹窗 -->
<template>
  <a-modal
    :width="900"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新建'"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @cancel="cancel"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 7 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 17 }, sm: { span: 24 } }"
    >
      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="敏感词" name="sensitiveWord">
            <a-input v-model:value="form.sensitiveWord" placeholder="请输入敏感词" style="width: 100%" allow-clear />
          </a-form-item>

          <a-form-item label="支付通道" name="channelCode">
            <a-select v-model:value="form.channelCode" style="width: 100%" placeholder="请选择" allow-clear>
              <a-select-option :value="item.channelCode" v-for="item in channelCodes" :key="item.id">{{
                item.channelName
              }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="敏感词类型" name="sensitiveType">
            <a-select v-model:value="form.sensitiveType" style="width: 100%" placeholder="请选择" allow-clear>
              <a-select-option :value="0">全部</a-select-option>
              <a-select-option :value="1">万商云商户名称</a-select-option>
              <a-select-option :value="2">通道商户名称</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>
    
    <script>
import { SensitiveWordManageApi } from '@/api/base/SensitiveWordManageApi';
import { message } from 'ant-design-vue';

export default {
  name: 'SensitiveWordEdit',
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    data: Object,
    // 支付通道
    channelCodes: Array
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: Object.assign({}, this.data),
      // 表单验证规则
      rules: {
        sensitiveWord: [{ required: true, message: '请输入敏感词' }],
        sensitiveType: [{ required: true, message: '请选择敏感词类型' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false
    };
  },
  watch: {
    data() {
      if (this.data) {
        this.form = Object.assign({}, this.data);
        this.isUpdate = true;
      } else {
        this.form = {};
        this.isUpdate = false;
      }
      if (this.$refs.form) {
        this.$refs.form.clearValidate();
      }
    }
  },
  methods: {
    /**
     * 保存和编辑
     */
    async save() {
      // 校验表单
      await this.$refs.form.validate();
      // 修改加载框为正在加载
      this.loading = true;

      let result = null;
      // 执行编辑或修改
      if (this.isUpdate) {
        result = SensitiveWordManageApi.edit(this.form);
      } else {
        result = SensitiveWordManageApi.add(this.form);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 如果是新增，则form表单置空
          if (!this.isUpdate) {
            this.form = {};
          }

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    cancel() {
      this.form = Object.assign({}, this.data);
      this.$refs.form.clearValidate();
    },

    /**
     * 更新编辑界面的弹框是否显示
     */
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
  
  <style>
.list-margin {
  margin-top: 20px;
}

.list-bottom {
  margin-bottom: 50px;
}
</style>
    