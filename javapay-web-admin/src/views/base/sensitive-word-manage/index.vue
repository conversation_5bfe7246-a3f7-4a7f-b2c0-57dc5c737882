<template>
  <div class="ele-body">
    <!-- 搜索框内容 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="支付通道">
              <a-select v-model:value="where.channelCode" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="item.channelCode" v-for="item in channelCodes" :key="item.id">{{ item.channelName }}</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="敏感词类型">
              <a-select v-model:value="where.sensitiveType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option value="0">全部</a-select-option>
                <a-select-option value="1">万商云名称</a-select-option>
                <a-select-option value="2">通道商户名称</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="敏感词">
              <a-input v-model:value.trim="where.sensitiveWord" placeholder="请输入敏感词" allow-clear />
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格内容 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- 表格上方的操作按钮 -->
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>

          <!-- 表体的操作 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'sensitiveType'">
              <a-tag v-if="record.sensitiveType === 0" color="pink">全部</a-tag>
              <a-tag v-else-if="record.sensitiveType === 1" color="cyan">万商云名称</a-tag>
              <a-tag v-else-if="record.sensitiveType === 2" color="blue">通道商户名称</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleEdit(record)">修改</a>
                <a-divider type="vertical" />
                <a-popconfirm title="确定要删除此行数据吗？" @confirm="remove(record)">
                  <a class="ele-text-danger">删除</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 编辑 -->
    <SensitiveWordEdit v-model:visible="showEdit" :data="current" :channelCodes="channelCodes" @done="reload" />
    <!-- 详情 -->
    <!-- <TripartiteApplicationDetail v-model:visible="showDetail" :detail="current" /> -->
  </div>
</template>
  
  <script>
import { SensitiveWordManageApi } from '@/api/base/SensitiveWordManageApi';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import { message } from 'ant-design-vue';
import SensitiveWordEdit from './sensitive-word-edit.vue';
// import TripartiteApplicationDetail from './tripartite-application-detail.vue';

export default {
  name: 'TripartiteApplicationManage',
  components: {
    SensitiveWordEdit,
    // TripartiteApplicationDetail
  },
  data() {
    return {
      //表格查询条件
      where: {},
      //展示编辑页面传的参数
      current: null,
      //是否展示新增或者是编辑页面
      showEdit: false,
      //是否展示详情页面
      showDetail: false,

      channelCodes: [],
      //表格列配置
      columns: [
        {
          title: '敏感词',
          dataIndex: 'sensitiveWord',
          align: 'center'
        },
        {
          title: '敏感词类型',
          dataIndex: 'sensitiveType',
          key: 'sensitiveType',
          align: 'center'
        },
        {
          title: '支付通道',
          dataIndex: 'channelCode',
          key: 'channelCode',
          customRender: ({ text }) => {
            const item = this.channelCodes.find(c => c.channelCode === text);
            return item?.channelName || '--';
          },
          align: 'center'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          align: 'center'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId',
          align: 'center'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime',
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 120,
          align: 'center'
        }
      ]
    };
  },
  mounted() {
    this.getChannelList()
  },
  methods: {
    //查询方法
    reload() {
      this.$refs.table.reload({ page: 1 });
    },
    //重置
    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    //删除
    async remove(row) {
      const result = await SensitiveWordManageApi.delete({ id: row.id });
      message.success(result.message);
      this.reload();
    },

    //获取通道列表
    async getChannelList() {
      const data = await ChannelManageApi.list({ validStatus: 1 });
      this.channelCodes = data || [];
    },

    //获取数据方法
    datasource({ page, limit, where }) {
      return SensitiveWordManageApi.findPage({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>
  