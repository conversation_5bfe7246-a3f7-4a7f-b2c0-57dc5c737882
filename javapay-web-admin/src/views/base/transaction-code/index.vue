<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="blick-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="交易码">
              <a-input v-model:value.trim="where.transCode" placeholder="请输入交易码" allow-clear />
            </a-form-item>

            <a-form-item label="交易名称">
              <a-input v-model:value.trim="where.transName" placeholder="请输入交易名称" allow-clear />
            </a-form-item>

            <a-form-item label="记账类型">
              <a-select v-model:value="where.acctFlag" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">不入账</a-select-option>
                <a-select-option :value="1">记正</a-select-option>
                <a-select-option :value="2">记负</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="需要限制">
              <a-select v-model:value="where.ctrlFlag" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">不控制允许交易</a-select-option>
                <a-select-option :value="1">需要页面控制</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="记录流水">
              <a-select v-model:value="where.logFlag" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">无流水</a-select-option>
                <a-select-option :value="1">有流水</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="冲正">
              <a-select v-model:value="where.safFlag" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">无冲正</a-select-option>
                <a-select-option :value="1">有冲正</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="是否需要签购单">
              <a-select v-model:value="where.saleFlag" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">否</a-select-option>
                <a-select-option :value="1">是</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="是否参与结算">
              <a-select v-model:value="where.settleFlag" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">无结算</a-select-option>
                <a-select-option :value="1">结算</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="状态">
              <a-select v-model:value="where.status" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">无效</a-select-option>
                <a-select-option :value="1">有效</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table
          ref="table"
          row-key="id"
          :datasource="datasource"
          :columns="columns"
          :where="where"
          v-model:selection="selection"
          :scroll="{ x: 'max-content' }"
        >
          <!-- table上边的工具栏 -->
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="openEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
              <a-button type="danger" @click="removeBatch">
                <template #icon>
                  <delete-outlined />
                </template>
                <span>删除</span>
              </a-button>
            </a-space>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'acctFlag'">
              <a-tag v-if="record.acctFlag === 0" color="gray">不入账</a-tag>
              <a-tag v-if="record.acctFlag === 1" color="green">记正</a-tag>
              <a-tag v-if="record.acctFlag === 2" color="orange">记负</a-tag>
            </template>

            <template v-else-if="column.key === 'ctrlFlag'">
              <a-tag v-if="record.ctrlFlag === 0" color="orange">不控制允许交易</a-tag>
              <a-tag v-if="record.ctrlFlag === 1" color="green">需要页面控制</a-tag>
            </template>

            <template v-else-if="column.key === 'logFlag'">
              <a-tag v-if="record.logFlag === 0" color="orange">无流水</a-tag>
              <a-tag v-if="record.logFlag === 1" color="green">有流水</a-tag>
            </template>

            <template v-else-if="column.key === 'safFlag'">
              <a-tag v-if="record.safFlag === 0" color="orange">无冲正</a-tag>
              <a-tag v-if="record.safFlag === 1" color="green">有冲正</a-tag>
            </template>

            <template v-else-if="column.key === 'saleFlag'">
              <a-tag v-if="record.saleFlag === 0" color="orange">否</a-tag>
              <a-tag v-if="record.saleFlag === 1" color="green">是</a-tag>
            </template>

            <template v-else-if="column.key === 'settleFlag'">
              <a-tag v-if="record.settleFlag === 0" color="orange">无结算</a-tag>
              <a-tag v-if="record.settleFlag === 1" color="green">有结算</a-tag>
            </template>

            <template v-else-if="column.key === 'status'">
              <a-tag v-if="record.status === 0" color="orange">无效</a-tag>
              <a-tag v-if="record.status === 1" color="green">有效</a-tag>
            </template>

            <!-- 最右侧操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="openDetail(record)">详情</a>
                <a-divider type="vertical" />
                <a @click="openEdit(record)">修改</a>
                <a-divider type="vertical" />
                <a-popconfirm title="确定要删除此纪录么?" @confirm="remove(record)">
                  <a class="ele-text-danger">删除</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <TransactionCodeDetail v-model:visible="showDetail" :showId="currentId" />

    <TransActionCodeEdit v-model:visible="showEdit" :showData="current" @done="reload" />
  </div>
</template>

<script>
import { TransactionCodeApi } from '@/api/base/TransactionCodeApi';
import TransActionCodeEdit from './transaction-code-edit.vue'
import TransactionCodeDetail from './transaction-code-detail.vue'
import { message, Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { createVNode } from 'vue';

export default {
  name: 'TransactionCode',
  components: {
    TransactionCodeDetail,
    TransActionCodeEdit,
  },
  data() {
    return {
      // 搜索框搜索条件
      where: {},
      //表格选中的数据
      selection: [],
      //是否展示详情页面
      showDetail: false,
      //是否展示新增或者修改组件页面
      showEdit: false,
      //修改页面需要的id，
      currentId: '',
      //修改页面需要展示的数据
      current: {},


      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '交易码',
          dataIndex: 'transCode',
          align: 'center',
          width: 120
        },
        {
          title: '交易名称',
          dataIndex: 'transName',
          align: 'center',
          width: 120
        },
        {
          title: '记账类型',
          dataIndex: 'acctFlag',
          key: 'acctFlag',
          align: 'center',
          width: 120
        },
        {
          title: '是否需要限制',
          dataIndex: 'ctrlFlag',
          key: 'ctrlFlag',
          align: 'center',
          width: 160
        },
        {
          title: '是否记录流水',
          dataIndex: 'logFlag',
          key: 'logFlag',
          align: 'center',
          width: 120
        },
        {
          title: '是否有冲正',
          dataIndex: 'safFlag',
          key: 'safFlag',
          align: 'center',
          width: 120
        },
        {
          title: '是否需要签购单',
          dataIndex: 'saleFlag',
          key: 'saleFlag',
          align: 'center',
          width: 120
        },
        {
          title: '是否参与结算',
          dataIndex: 'settleFlag',
          key: 'settleFlag',
          align: 'center',
          width: 120
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          align: 'center',
          width: 120
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 180,
          align: 'center'
        }
      ]
    };
  },
  methods: {
    reload() {
      this.selection = [];
      this.$refs.table.reload({ page: 1 });
    },
    reset() {
      this.where = {};
      this.selection = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    //查看详情操作
    openDetail(row) {
        this.showDetail = true;
        this.currentId = row.id;
    },

    //新增或者修改方法
    openEdit(row) {
        this.showEdit = true;
        this.current = row;
    },
    //删除指定的行
    async remove(row) {
        const result = await TransactionCodeApi.deleteTransCode({id: row.id});
        message.success(result.message);
        this.reload();
    },
    //批量删除
    removeBatch() {
        if(!this.selection.length) {
            message.error('请至少选择一条数据');
            return;
        }

        Modal.confirm({
            title: '提示',
            content: '确定要删除选中的交易码数据么?',
            icon: createVNode(ExclamationCircleOutlined),
            maskClosable: true,
            onOk: async() => {
                let params = this.selection.map(d => d.id);
                const result = await TransactionCodeApi.batchDelete({ ids: params });
                message.success(result.message);
                this.reload();
            }
        });
    },

    datasource({ page, limit, where }) {
        const data = TransactionCodeApi.getTransCodePage({ ...where, pageNo: page, pageSize: limit });
        console.log(data);
      return TransactionCodeApi.getTransCodePage({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>

<style>
</style>