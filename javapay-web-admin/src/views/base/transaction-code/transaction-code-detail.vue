<template>
  <a-modal
    :width="500"
    :visible="visible"
    @update:visible="updateVisible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '25px' }"
    :footer="null"
  >
    <a-descriptions :column="1">
      <a-descriptions-item label="交易码">{{ form.transCode }}</a-descriptions-item>
      <a-descriptions-item label="交易名称">{{ form.transName }}</a-descriptions-item>
      <a-descriptions-item label="记账类型">
        <a-tag v-if="form.acctFlag === 0" color="gray">不入账</a-tag>
        <a-tag v-else-if="form.acctFlag === 1" color="green">记正</a-tag>
        <a-tag v-else color="orange">记负</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="是否需要限制">
        <a-tag v-if="form.ctrlFlag === 0" color="orange">不控制允许交易</a-tag>
        <a-tag v-else color="green">需要页面控制</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="是否记录流水">
        <a-tag v-if="form.logFlag === 0" color="orange">无流水</a-tag>
        <a-tag v-else color="green">有流水</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="是否有冲正">
        <a-tag v-if="form.safFlag === 0" color="orange">无冲正</a-tag>
        <a-tag v-else color="green">有冲正</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="是否需要签购单">
        <a-tag v-if="form.saleFlag === 0" color="orange">否</a-tag>
        <a-tag v-else color="green">是</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="是否需要签购单">
        <a-tag v-if="form.saleFlag === 0" color="orange">否</a-tag>
        <a-tag v-else color="green">是</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="是否参与结算">
        <a-tag v-if="form.settleFlag === 0" color="orange">无结算</a-tag>
        <a-tag v-else color="green">有结算</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="状态">
        <a-tag v-if="form.status === 0" color="orange">无效</a-tag>
        <a-tag v-else color="green">有效</a-tag>
      </a-descriptions-item>
    </a-descriptions>
  </a-modal>
</template>

<script>
import { TransactionCodeApi } from '@/api/base/TransactionCodeApi';

export default {
  name: 'TransactionCodeDetail',
  props: {
    visible: Boolean,
    showId: String
  },
  emits: ['update:visible'],
  data() {
    return {
      form: {}
    };
  },

//   computed: {
//      form() {
//           console.log('触发了form数据');
//           const detail = this.getDetailData();
//           console.log(detail)
//           return detail;
//       }
//   },
  watch: {
    showId() {
      this.getDetailData();
    }
  },
  methods: {
    updateVisible(value) {
      this.$emit('update:visible', value);
    },
    async getDetailData() {
      let result = await TransactionCodeApi.getTransCodeDetail({ id: this.showId });
      this.form = result;
    }
  }
};
</script>

<style>
</style>