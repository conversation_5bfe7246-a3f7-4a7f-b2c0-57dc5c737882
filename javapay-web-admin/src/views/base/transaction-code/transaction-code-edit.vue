<template>
  <a-modal
    :width="1000"
    :visible="visible"
    @update:visible="updateVisible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新增'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @cancel="cancel"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 7 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 17 }, sm: { span: 24 } }"
    >
      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="交易码" name="transCode">
            <a-input v-model:value="form.transCode" placeholder="请输入交易码" allow-clear />
          </a-form-item>

          <a-form-item label="记账类型" name="acctFlag">
            <a-select v-model:value="form.acctFlag" placeholder="请选择" allow-clear>
              <a-select-option :value="0">不入账</a-select-option>
              <a-select-option :value="1">记正</a-select-option>
              <a-select-option :value="2">记负</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="是否纪录流水" name="logFlag">
            <a-select v-model:value="form.logFlag" placeholder="请选择" allow-clear>
              <a-select-option :value="0">无流水</a-select-option>
              <a-select-option :value="1">有流水</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="是否需要签购单" name="saleFlag">
            <a-select v-model:value="form.saleFlag" placeholder="请选择" allow-clear>
              <a-select-option :value="0">否</a-select-option>
              <a-select-option :value="1">是</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="状态" name="status">
            <a-select v-model:value="form.status" placeholder="请选择" allow-clear>
              <a-select-option :value="0">无效</a-select-option>
              <a-select-option :value="1">有效</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="交易名称" name="transName">
            <a-input v-model:value="form.transName" placeholder="请输入交易名称" allow-clear />
          </a-form-item>

          <a-form-item label="是否需要限制" name="ctrlFlag">
            <a-select v-model:value="form.ctrlFlag" placeholder="请选择" allow-clear>
              <a-select-option :value="0">不控制允许交易</a-select-option>
              <a-select-option :value="1">需要页面控制</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="是否有冲正" name="safFlag">
            <a-select v-model:value="form.safFlag" placeholder="请选择" allow-clear>
              <a-select-option :value="0">无冲正</a-select-option>
              <a-select-option :value="1">有冲正</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="是否参与结算" name="settleFlag">
            <a-select v-model:value="form.settleFlag" placeholder="请选择" allow-clear>
              <a-select-option :value="0">无结算</a-select-option>
              <a-select-option :value="1">结算</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script>
import { TransactionCodeApi } from '@/api/base/TransactionCodeApi';
import { message } from 'ant-design-vue';

function formDefaults() {
  return {
    ctrlFlag: 1,
    logFlag: 1,
    safFlag: 0,
    settleFlag: 1,
    status: 1
  };
}

export default {
  name: 'TransActionCodeEdit',
  props: {
    visible: Boolean,
    showData: Object
  },
  emits: ['update:visible', 'done'],
  data() {
    return {
      //是否显示加载样式
      loading: false,
      //是更新还是添加
      isUpdate: false,
      //页面数据
      form: {},
      //表单验证规则
      rules: {
        transCode: [{ required: true, message: '请输入交易码' }],
        transName: [{ required: true, message: '请输入交易名称' }],
        acctFlag: [{ required: true, message: '请选择' }],
        ctrlFlag: [{ required: true, message: '请选择' }],
        logFlag: [{ required: true, message: '请选择' }],
        safFlag: [{ required: true, message: '请选择' }],
        saleFlag: [{ required: true, message: '请选择' }],
        settleFlag: [{ required: true, message: '请选择' }],
        status: [{ required: true, message: '请选择' }]
      }
    };
  },
  watch: {
    showData() {
      if (this.showData) {
        this.isUpdate = true;
        this.form = Object.assign({}, this.showData);
      } else {
        this.form = formDefaults();
        this.isUpdate = false;
      }
    }
  },
  methods: {
    /**
     * 点击叉和取消调用，返回给父级是否显示的数值
     */
    updateVisible(value) {
      this.$emit('update:visible', value);
    },
    cancel() {
      this.form = Object.assign({}, this.showData);
      this.$refs.form.clearValidate();
    },
    async save() {
      //校验表单
      await this.$refs.form.validate();

      this.loading = true;

      let result = null;

      if (this.isUpdate) {
        //修改
        const acctFlag = this.form.acctFlag;
        const ctrlFlag = this.form.ctrlFlag;
        const id = this.form.id;
        const logFlag = this.form.logFlag;
        const safFlag = this.form.safFlag;
        const saleFlag = this.form.saleFlag;
        const settleFlag = this.form.settleFlag;
        const status = this.form.status;
        const transCode = this.form.transCode;
        const transName = this.form.transName;
        result = TransactionCodeApi.editTransCode({
          acctFlag,
          ctrlFlag,
          id,
          logFlag,
          safFlag,
          saleFlag,
          settleFlag,
          status,
          transCode,
          transName
        });
      } else {
        //新增
        result = TransactionCodeApi.addTransCode(this.form);
      }
      result
        .then(result => {
          this.loading = false;
          message.success(result.message);

          //如果是新增，把form表单变成空
          if (!this.isUpdate) {
            this.form = formDefaults();
          }

          //关闭弹框，传值给父组件
          this.updateVisible(false);

          //触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    }
  }
};
</script>

<style>
</style>