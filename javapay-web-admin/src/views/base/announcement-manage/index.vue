<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row>
            <a-form-item label="公告标题">
              <a-input v-model:value.trim="where.announceTitle" placeholder="请输入公告标题" allow-clear />
            </a-form-item>
            <!-- <a-form-item label="公告类型">
              <a-select v-model:value="where.announceType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="100">全平台</a-select-option>
                <a-select-option :value="0">平台运营</a-select-option>
                <a-select-option :value="1">大区</a-select-option>
                <a-select-option :value="2">运营中心</a-select-option>
                <a-select-option :value="3">代理商</a-select-option>
                <a-select-option :value="4">商户</a-select-option>
              </a-select>
            </a-form-item> -->
            <a-form-item label="通知用户类型">
              <a-select v-model:value="where.userType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="100">全部用户</a-select-option>
                <a-select-option :value="0">平台运营</a-select-option>
                <a-select-option :value="1">大区</a-select-option>
                <a-select-option :value="2">运营中心</a-select-option>
                <a-select-option :value="3">代理商</a-select-option>
                <a-select-option :value="4">商户</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="公告状态">
              <a-select v-model:value="where.announceStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">启用</a-select-option>
                <a-select-option :value="0">停用</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- table上边工具栏 -->
          <template #toolbar>
            <a-space>
              <a-button type="primary" v-if="hasPurview('0')" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>

          <template #bodyCell="{ column, record }">
            <!-- <template v-if="column.key === 'announceType'">
              <a-badge v-if="record.announceType === 100" color="purple" text="全平台" />
              <a-badge v-else-if="record.announceType === 0" color="geekblue" text="平台运营" />
              <a-badge v-else-if="record.announceType === 1" color="magenta" text="大区" />
              <a-badge v-else-if="record.announceType === 2" color="volcano" text="运营中心" />
              <a-badge v-else-if="record.announceType === 3" color="gold" text="代理商" />
              <a-badge v-else-if="record.announceType === 4" color="lime" text="商户" />
            </template> -->
            <template v-if="column.key === 'userType'">
              <a-badge v-if="record.userType === 100" color="purple" text="全部用户" />
              <a-badge v-else-if="record.userType === 0" color="geekblue" text="平台运营" />
              <a-badge v-else-if="record.userType === 1" color="magenta" text="大区" />
              <a-badge v-else-if="record.userType === 2" color="volcano" text="运营中心" />
              <a-badge v-else-if="record.userType === 3" color="gold" text="代理商" />
              <a-badge v-else-if="record.userType === 4" color="lime" text="商户" />
            </template>
            <template v-if="column.key === 'priority'">
              <a-rate :value="record.priority" disabled :count="3" style="font-size: 16px" />
            </template>
            <template v-else-if="column.key === 'announceContent'">
              <div class="ele-elip" style="width: 200px">{{ record.announceContent }}</div>
            </template>
            <template v-else-if="column.key === 'announceStatus'">
              <a-tag v-if="record.announceStatus === 0">禁用</a-tag>
              <a-tag v-else-if="record.announceStatus === 1" color="success">启用</a-tag>
            </template>
            <!-- table操作栏按钮 -->
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
                <template v-if="hasPurview('0')">
                  <a-divider type="vertical" />
                  <a @click="handleEdit(record)">修改</a>
                  <a-divider type="vertical" />
                  <a-popconfirm title="确定要删除此公告吗？" @confirm="remove(record)">
                    <a class="ele-text-danger">删除</a>
                  </a-popconfirm>
                </template>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 编辑 -->
    <AnnouncementEdit v-if="showEdit" v-model:visible="showEdit" :data="current" @done="reload" />

    <!-- 详情 -->
    <AnnouncementDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { message } from 'ant-design-vue';
import { AnnouncementManageApi } from '@/api/base/AnnouncementManageApi';
import AnnouncementDetail from './AnnouncementDetail.vue';
import AnnouncementEdit from './AnnouncementEdit.vue';
import { hasPurview } from '@/utils/permission';

export default {
  name: 'AnnouncementManage',
  components: {
    AnnouncementDetail,
    AnnouncementEdit
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '公告标题',
          dataIndex: 'announceTitle'
        },
        // {
        //   title: '公告类型',
        //   dataIndex: 'announceType',
        //   key: 'announceType',
        //   width: 150,
        //   align: 'center'
        // },
        {
          title: '通知用户类型',
          dataIndex: 'userType',
          key: 'userType',
          width: 150,
          align: 'center'
        },
        {
          title: '优先级',
          dataIndex: 'priority',
          key: 'priority',
          width: 150,
          align: 'center'
        },
        {
          title: '公告状态',
          dataIndex: 'announceStatus',
          key: 'announceStatus',
          width: 150,
          align: 'center'
        },
        {
          title: '公告内容',
          dataIndex: 'announceContent',
          key: 'announceContent'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          width: 180,
          align: 'center',
          fixed: 'right'
        }
      ],
      // 表格搜索条件
      where: {},
      current: null,
      showDetail: false,
      showEdit: false
    };
  },
  methods: {
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    hasPurview,

    handleEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    async remove(row) {
      const result = await AnnouncementManageApi.delete({ id: row.id });
      message.success(result.message);
      this.reload();
    },

    datasource({ page, limit, where, orders }) {
      return AnnouncementManageApi.list({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
