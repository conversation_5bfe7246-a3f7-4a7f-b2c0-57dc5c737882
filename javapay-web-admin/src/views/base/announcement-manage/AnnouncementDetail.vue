<template>
  <a-modal :width="1000" :visible="visible" title="详情" :mask-closable="false" :body-style="{ paddingBottom: '20px' }" @update:visible="updateVisible">
    <a-descriptions bordered :column="2">
      <a-descriptions-item label="公告标题">{{ form.announceTitle }}</a-descriptions-item>
      <!-- <a-descriptions-item label="公告类型">
        <a-badge v-if="form.announceType === 100" color="purple" text="全平台" />
        <a-badge v-else-if="form.announceType === 0" color="geekblue" text="平台运营" />
        <a-badge v-else-if="form.announceType === 1" color="magenta" text="大区" />
        <a-badge v-else-if="form.announceType === 2" color="volcano" text="运营中心" />
        <a-badge v-else-if="form.announceType === 3" color="gold" text="代理商" />
        <a-badge v-else-if="form.announceType === 4" color="lime" text="商户" />
      </a-descriptions-item> -->
      <a-descriptions-item label="通知用户类型">
        <a-badge v-if="form.userType === 100" color="purple" text="全部用户" />
        <a-badge v-else-if="form.userType === 0" color="geekblue" text="平台运营" />
        <a-badge v-else-if="form.userType === 1" color="magenta" text="大区" />
        <a-badge v-else-if="form.userType === 2" color="volcano" text="运营中心" />
        <a-badge v-else-if="form.userType === 3" color="gold" text="代理商" />
        <a-badge v-else-if="form.userType === 4" color="lime" text="商户" />
      </a-descriptions-item>
      <a-descriptions-item label="优先级">
        <a-rate :value="form.priority" disabled :count="3" style="font-size: 16px" />
      </a-descriptions-item>
      <a-descriptions-item label="公告状态">
        <a-tag v-if="form.announceStatus === 0">禁用</a-tag>
        <a-tag v-else-if="form.announceStatus === 1" color="success">启用</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="创建人">{{ form.createUserId }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改人">{{ form.lastModifyUserId }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>
    <a-divider />
    <a-collapse :active-key="['1']">
      <a-collapse-panel key="1" header="公告内容">
        <p>{{ form.announceContent }}</p>
      </a-collapse-panel>
    </a-collapse>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

export default {
  name: 'AnnouncementDetail',
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
