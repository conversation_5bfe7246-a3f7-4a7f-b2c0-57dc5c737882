<template>
  <a-modal
    :width="600"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新增'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 5 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 19 }, sm: { span: 24 } }"
    >
      <a-form-item label="公告标题" name="announceTitle">
        <a-input v-model:value="form.announceTitle" placeholder="请输入公告标题" />
      </a-form-item>
      <!-- <a-form-item label="公告类型" name="announceType">
        <a-select v-model:value="form.announceType" style="width: 100%" placeholder="请选择">
          <a-select-option :value="100">全平台</a-select-option>
          <a-select-option :value="0">平台运营</a-select-option>
          <a-select-option :value="1">大区</a-select-option>
          <a-select-option :value="2">运营中心</a-select-option>
          <a-select-option :value="3">代理商</a-select-option>
          <a-select-option :value="4">商户</a-select-option>
        </a-select>
      </a-form-item> -->
      <a-form-item label="通知用户类型" name="userType">
        <a-select v-model:value="form.userType" style="width: 100%" placeholder="请选择">
          <a-select-option :value="100">全部用户</a-select-option>
          <a-select-option :value="0">平台运营</a-select-option>
          <a-select-option :value="1">大区</a-select-option>
          <a-select-option :value="2">运营中心</a-select-option>
          <a-select-option :value="3">代理商</a-select-option>
          <a-select-option :value="4">商户</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="公告优先级" name="priority">
        <a-rate v-model:value="form.priority" :count="3" :tooltips="['一般', '中等', '高']" :allowClear="false" />
      </a-form-item>
      <a-form-item label="公告状态" name="announceStatus">
        <a-switch
          v-model:checked="form.announceStatus"
          checked-children="启用"
          un-checked-children="禁用"
          :checkedValue="1"
          :unCheckedValue="0"
        />
      </a-form-item>
      <a-form-item label="公告内容" name="announceContent">
        <a-textarea v-model:value="form.announceContent" placeholder="请输入公告内容" allow-clear :auto-size="{ minRows: 3 }" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { AnnouncementManageApi } from '@/api/base/AnnouncementManageApi';

export default {
  name: 'AnnouncementEdit',
  props: {
    visible: Boolean,
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {
        priority: 1,
        announceStatus: 1
      },
      // 表单验证规则
      rules: {
        announceTitle: [{ required: true, message: '请输入公告标题' }],
        announceContent: [{ required: true, message: '请输入公告内容' }],
        priority: [{ required: true, message: '请选择公告优先级' }],
        announceType: [{ required: true, message: '请选择公告类型' }],
        userType: [{ required: true, message: '请选择通知用户类型' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false
    };
  },
  mounted() {
    if (this.data) {
      this.form = Object.assign({}, this.data);
      this.isUpdate = true;
    } else {
      this.isUpdate = false;
    }
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      // 执行编辑或修改方法
      if (this.isUpdate) {
        result = AnnouncementManageApi.edit(this.form);
      } else {
        result = AnnouncementManageApi.add(this.form);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
