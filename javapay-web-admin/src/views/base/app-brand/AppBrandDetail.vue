<template>
  <a-modal
    :width="1000"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions bordered :column="2">
      <a-descriptions-item label="LOGO" :span="2">
        <a-image :width="58" :height="58" :src="form.appLogoUrl" alt="logo" />
      </a-descriptions-item>
      <a-descriptions-item label="APP名称">{{ form.appName }}</a-descriptions-item>
      <a-descriptions-item label="APP简称">{{ form.appAbbr }}</a-descriptions-item>
      <a-descriptions-item label="品牌类型">{{ form.appBrandType }}</a-descriptions-item>
      <a-descriptions-item label="IOS商城链接">{{ form.iosStoreUrl }}</a-descriptions-item>
      <a-descriptions-item label="创建人">{{ form.createUserId }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改人">{{ form.lastModifyUserId }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>
    <a-collapse :active-key="['1']">
      <a-collapse-panel key="1" header="参数配置">
        <template v-for="(item, idx) in form.paramsList || []" :key="idx">
          <a-descriptions :column="2" size="small">
            <a-descriptions-item label="参数名称">
              {{ item.key }}
            </a-descriptions-item>
            <a-descriptions-item label="参数值">
              {{ item.value }}
            </a-descriptions-item>
          </a-descriptions>
        </template>
      </a-collapse-panel>
    </a-collapse>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);

        let paramsList = [];
        for (let [key, value] of Object.entries(JSON.parse(props.detail.params || '{}'))) {
          paramsList.push({ key, value });
        }
        data.form.paramsList = paramsList;
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
