<template>
  <a-modal
    :width="880"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新增'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <!-- 上传logo图 -->
    <a-upload
      :file-list="form.fileDTO.fileData ? [{ url: form.fileDTO.fileData }] : []"
      :multiple="false"
      list-type="picture-card"
      :before-upload="beforeUpload"
      @remove="handleRemoveImg"
      @preview="handlePreview"
    >
      <div v-if="!form.fileDTO.fileData">
        <plus-outlined />
        <div style="margin-top: 8px">上传LOGO</div>
      </div>
    </a-upload>
    <a-image :style="{ display: 'none' }" :src="previewImage" :preview="{ visible: previewVisible, onVisibleChange: setPreviewVisible }" />

    <!-- 表单 -->
    <a-form ref="form" :model="form" :rules="rules" :validateOnRuleChange="false" :label-col="{ style: { width: '110px' } }">
      <!-- 基本信息 -->
      <a-divider dashed orientation="left" orientationMargin="0">基本信息</a-divider>
      <a-row :gutter="10">
        <a-col :span="11">
          <a-form-item label="APP名称" name="appName">
            <a-input v-model:value="form.appName" placeholder="APP名称" />
          </a-form-item>
          <a-form-item label="品牌类型" name="appBrandType">
            <a-input-number
              v-model:value="form.appBrandType"
              placeholder="APP品牌类型"
              autocomplete="off"
              style="width: 100%"
              :disabled="isUpdate"
            />
          </a-form-item>
        </a-col>
        <a-col :span="11">
          <a-form-item label="APP简称" name="appAbbr">
            <a-input v-model:value="form.appAbbr" placeholder="APP简称" />
          </a-form-item>
          <a-form-item label="IOS商城链接" name="iosStoreUrl">
            <a-textarea v-model:value="form.iosStoreUrl" placeholder="IOS商城链接" auto-size />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 参数配置 -->
      <a-divider dashed style="margin: 20px 0" orientation="left" orientationMargin="0">参数配置</a-divider>
      <a-button type="primary" @click="addParams" style="margin-bottom: 10px">添加参数</a-button>
      <a-row v-for="(item, idx) in form.paramsMap || []" :key="idx" :gutter="10">
        <a-col :span="11">
          <a-form-item label="参数名称" :name="['paramsMap', idx, 'key']" :rules="rules.paramsKey">
            <a-textarea v-model:value.trim="item.key" placeholder="请输入参数名称" auto-size allow-clear />
          </a-form-item>
        </a-col>
        <a-col :span="11">
          <a-form-item label="参数值" :name="['paramsMap', idx, 'value']" :rules="rules.paramsValue">
            <a-textarea v-model:value.trim="item.value" placeholder="请输入参数值" auto-size allow-clear />
          </a-form-item>
        </a-col>
        <a-col :span="2">
          <a-form-item>
            <a-button danger @click="deleteParams(idx)" shape="circle">
              <template #icon><delete-outlined /></template>
            </a-button>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script>
import { Upload, message } from 'ant-design-vue';
import { AppBrandApi } from '@/api/base/AppBrandApi';
import { deepCopy } from '@/utils/util';

export default {
  props: {
    visible: Boolean,
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {
        fileDTO: {
          fileData: '',
          suffixType: 'png'
        },
        paramsMap: []
      },
      // 表单验证规则
      rules: {
        appName: [{ required: true, message: '请输入APP名称' }],
        appAbbr: [{ required: true, message: '请输入APP简称' }],
        appBrandType: [{ required: true, message: '请输入APP品牌类型' }],
        iosStoreUrl: [{ required: true, message: '请输入IOS商城链接' }],
        paramsKey: [{ required: true, message: '请输入KEY值' }],
        paramsValue: [{ required: true, message: '请输入VALUE值' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false,
      previewVisible: false,
      previewImage: ''
    };
  },
  mounted() {
    if (this.data) {
      this.isUpdate = true;

      const detail = deepCopy(this.data);

      let paramsList = [];
      for (let [key, value] of Object.entries(JSON.parse(detail.params || '{}'))) {
        paramsList.push({ key, value });
      }
      detail.paramsMap = paramsList;

      this.form = Object.assign(this.form, detail);
      this.form.fileDTO.fileData = this.form.appLogoUrl || null;
    }
  },
  methods: {
    addParams() {
      this.form.paramsMap.push({
        key: '',
        value: ''
      });
    },
    deleteParams(index) {
      this.form.paramsMap.splice(index, 1);
    },
    handlePreview() {
      this.previewImage = this.form.fileDTO.fileData;
      this.setPreviewVisible(true);
    },
    setPreviewVisible(visible) {
      this.previewVisible = visible;
    },
    beforeUpload(file) {
      fileToData(file).then(base64Img => {
        this.form.fileDTO.fileData = base64Img;
      });
      return Upload.LIST_IGNORE;
    },
    handleRemoveImg() {
      this.form.fileDTO.fileData = '';
    },
    async save() {
      if (!this.form.fileDTO.fileData) {
        message.warn('请上传LOGO');
        return;
      }

      // 校验表单
      await this.$refs.form.validate();

      const params = deepCopy(this.form);

      let paramsObj = {};
      for (const { key, value } of params.paramsMap) {
        paramsObj[key] = value;
      }
      params.paramsMap = paramsObj;

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      // 执行编辑或修改方法
      if (this.isUpdate) {
        if (params.fileDTO.fileData.startsWith('http')) {
          delete params.fileDTO;
        }
        result = AppBrandApi.edit(params);
      } else {
        result = AppBrandApi.add(params);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};

function fileToData(file, type = 'dataURL') {
  return new Promise((resolve, reject) => {
    let reader = new FileReader();
    reader.onload = function (e) {
      resolve(e.target.result);
    };
    reader.onloadend = function () {
      reader = null;
    };
    reader.onabort = function () {
      reject(new Error('Aborted to read the image with FileReader.'));
    };
    reader.onerror = function () {
      reject(new Error('Failed to read the image with FileReader.'));
    };
    if (type === 'arrayBuffer') {
      reader.readAsArrayBuffer(file);
    } else {
      reader.readAsDataURL(file);
    }
  });
}
</script>
