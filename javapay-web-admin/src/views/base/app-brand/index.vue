<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="APP名称">
              <a-input v-model:value.trim="where.appName" placeholder="请输入APP名称" allow-clear />
            </a-form-item>
            <a-form-item label="APP简称">
              <a-input v-model:value.trim="where.appAbbr" placeholder="请输入APP简称" allow-clear />
            </a-form-item>
            <a-form-item label="品牌类型">
              <a-select v-model:value="where.appBrandType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">海捷付</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- table上边工具栏 -->
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'appLogoUrl'">
              <a-image :width="48" :height="48" :src="record.appLogoUrl" alt="logo" />
            </template>

            <!-- table操作栏按钮 -->
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
                <a-divider type="vertical" />
                <a @click="handleEdit(record)">修改</a>
                <a-divider type="vertical" />
                <a-popconfirm title="确定要删除此品牌吗？" @confirm="remove(record)">
                  <a class="ele-text-danger">删除</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 编辑 -->
    <AppBrandEdit v-if="showEdit" v-model:visible="showEdit" :data="current" @done="reload" />

    <!-- 详情 -->
    <AppBrandDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { message } from 'ant-design-vue';
import { AppBrandApi } from '@/api/base/AppBrandApi';
import AppBrandDetail from './AppBrandDetail.vue';
import AppBrandEdit from './AppBrandEdit.vue';

export default {
  name: 'AppBrandManage',
  components: {
    AppBrandDetail,
    AppBrandEdit
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: 'LOGO',
          dataIndex: 'appLogoUrl',
          key: 'appLogoUrl',
          align: 'center'
        },
        {
          title: 'APP名称',
          dataIndex: 'appName',
          align: 'center'
        },
        {
          title: 'APP简称',
          dataIndex: 'appAbbr',
          align: 'center'
        },
        {
          title: '品牌类型',
          dataIndex: 'appBrandType',
          align: 'center'
        },
        {
          title: 'IOS商城链接',
          dataIndex: 'iosStoreUrl',
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          width: 180,
          align: 'center',
          fixed: 'right'
        }
      ],
      // 表格搜索条件
      where: {},
      current: null,
      showDetail: false,
      showEdit: false,
      brands: []
    };
  },
  methods: {
    async getBrands() {
      const data = await AppBrandApi.list();
      this.brands = data || [];
    },

    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    handleEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    async remove(row) {
      const result = await AppBrandApi.delete({ id: row.id });
      message.success(result.message);
      this.reload();
    },

    datasource({ page, limit, where, orders }) {
      return AppBrandApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
