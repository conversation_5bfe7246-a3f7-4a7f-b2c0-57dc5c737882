<template>
  <a-modal
    :width="680"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    :footer="null"
  >
    <a-descriptions :column="1">
      <a-descriptions-item label="地区码">{{ form.areaCode }}</a-descriptions-item>
      <a-descriptions-item label="地区名称">{{ form.areaName }}</a-descriptions-item>
      <a-descriptions-item label="地区等级">
        <a-tag v-if="form.level === 1" color="cyan">全国</a-tag>
        <a-tag v-else-if="form.level === 2" color="blue">省</a-tag>
        <a-tag v-else-if="form.level === 3" color="purple">市</a-tag>
        <a-tag v-else-if="form.level === 4" color="pink">区县</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="国标码">{{ form.gbCode }}</a-descriptions-item>
      <a-descriptions-item label="上级地区码">{{ form.parentCode }}</a-descriptions-item>
      <a-descriptions-item label="上级名称">{{ form.parentName }}</a-descriptions-item>
      <a-descriptions-item label="状态">
        <span v-if="form.status === 1" class="ele-text-success">有效</span>
        <span v-else class="ele-text-danger">无效</span>
      </a-descriptions-item>
    </a-descriptions>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

export default {
  name: 'AreaDetail',
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible,
    };
  }
};
</script>
