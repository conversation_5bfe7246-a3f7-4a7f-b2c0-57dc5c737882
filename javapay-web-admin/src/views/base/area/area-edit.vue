<template>
  <a-modal
    :width="680"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新增'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @cancel="cancel"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 6 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 18 }, sm: { span: 24 } }"
    >
      <a-form-item label="地区名称" name="areaName">
        <a-input v-model:value="form.areaName" placeholder="请输入地区名称" allow-clear />
      </a-form-item>
      <a-form-item label="地区码" name="areaCode">
        <a-input v-model:value="form.areaCode" placeholder="请输入地区码" allow-clear />
      </a-form-item>
      <a-form-item label="地区等级" name="level">
        <a-select v-model:value="form.level" style="width: 205px" placeholder="请选择地区等级" allow-clear>
          <a-select-option :value="1">全国</a-select-option>
          <a-select-option :value="2">省</a-select-option>
          <a-select-option :value="3">市</a-select-option>
          <a-select-option :value="4">区县</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="上级地区码" name="parentCode">
        <a-input v-model:value="form.parentCode" placeholder="请输入上级区码" :disabled="!form.level" @blur="autoSearch" />
      </a-form-item>
      <a-form-item label="上级名称" name="parentName">
        <a-input v-model:value="form.parentName" placeholder="输入上级地区码自动识别" allow-clear disabled />
      </a-form-item>
      <a-form-item label="国标码">
        <a-input v-model:value="form.gbCode" placeholder="请输入六位国标码" allow-clear />
      </a-form-item>
      <a-form-item label="状态" name="status">
        <a-select v-model:value="form.status" style="width: 205px" placeholder="请选择">
          <a-select-option :value="1">有效</a-select-option>
          <a-select-option :value="0">无效</a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { AreaApi } from '@/api/base/AreaApi';

function formDefaults() {
  return {
    status: 1
  };
}
export default {
  name: 'AreaEdit',
  props: {
    visible: Boolean,
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: Object.assign({}, this.data),
      // 表单验证规则
      rules: {
        areaCode: [{ required: true, message: '请输入地区码' }],
        areaName: [{ required: true, message: '请输入地区名称' }],
        level: [{ required: true, message: '请选择地区等级' }],
        parentCode: [{ required: true, message: '请输入上级地区码' }],
        parentName: [{ required: true, message: '不能为空' }],
        status: [{ required: true, message: '请选择状态' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false
    };
  },
  watch: {
    data() {
      if (this.data) {
        this.form = Object.assign({}, this.data);
        this.isUpdate = true;
      } else {
        this.form = formDefaults();
        this.isUpdate = false;
      }
      if (this.$refs.form) {
        this.$refs.form.clearValidate();
      }
    }
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      // 执行编辑或修改方法
      if (this.isUpdate) {
        result = AreaApi.edit(this.form);
      } else {
        result = AreaApi.add(this.form);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 如果是新增，则form表单置空
          if (!this.isUpdate) {
            this.form = formDefaults();
          }

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    async autoSearch() {
      if (!this.form.parentCode) return;
      const { rows = [] } = await AreaApi.findPage({ areaCode: this.form.parentCode,level:this.form.level-1 });
      if (rows?.length) {
        const [{ level, areaName }] = rows;
        if (level > 3) return message.warning('上级地区等级不能为区县!');
        this.form.parentName = areaName;
        // this.form.level = level + 1;
      } else {
        message.warning('无效的上级地区码!');
        this.form.parentName = '';
        // this.form.level = null;
      }
    },

    cancel() {
      this.form = Object.assign(formDefaults(), this.data);
      this.$refs.form.clearValidate();
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
