<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="地区码">
              <a-input v-model:value.trim="where.areaCode" placeholder="请输入地区代码" allow-clear />
            </a-form-item>
            <a-form-item label="地区名称">
              <a-input v-model:value.trim="where.areaName" placeholder="请输入地区名称" allow-clear />
            </a-form-item>
            <a-form-item label="地区等级">
              <a-select v-model:value="where.level" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">全国</a-select-option>
                <a-select-option :value="2">省</a-select-option>
                <a-select-option :value="3">市</a-select-option>
                <a-select-option :value="4">区县</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="上级地区码">
              <a-input v-model:value.trim="where.parentCode" placeholder="请输入上级代码" allow-clear />
            </a-form-item>
            <a-form-item label="上级名称">
              <a-input v-model:value.trim="where.parentName" placeholder="请输入上级名称" allow-clear />
            </a-form-item>
            <a-form-item label="状态">
              <a-select v-model:value="where.status" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">有效</a-select-option>
                <a-select-option :value="0">无效</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table
          ref="table"
          row-key="id"
          :datasource="datasource"
          :columns="columns"
          :where="where"
          v-model:selection="selection"
          :scroll="{ x: 'max-content' }"
        >
          <!-- table上边工具栏 -->
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
              <a-button danger @click="removeBatch">
                <template #icon>
                  <delete-outlined />
                </template>
                <span>删除</span>
              </a-button>
            </a-space>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-switch :checked="record.status === 1" @change="checked => editState(checked, record)" />
            </template>

            <template v-else-if="column.key === 'level'">
              <a-tag v-if="record.level === 1" color="cyan">全国</a-tag>
              <a-tag v-else-if="record.level === 2" color="blue">省</a-tag>
              <a-tag v-else-if="record.level === 3" color="purple">市</a-tag>
              <a-tag v-else-if="record.level === 4" color="pink">区县</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
                <a-divider type="vertical" />
                <a @click="handleEdit(record)">修改</a>
                <a-divider type="vertical" />
                <a-popconfirm title="确定要删除吗？" @confirm="remove(record)">
                  <a class="ele-text-danger">删除</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 编辑 -->
    <AreaEdit v-model:visible="showEdit" :data="current" @done="reload" />

    <!-- 详情 -->
    <AreaDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { message, Modal } from 'ant-design-vue';
import { createVNode } from 'vue';
import { AreaApi } from '@/api/base/AreaApi';
import AreaDetail from './area-detail.vue';
import { PlusOutlined, DeleteOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue';
import AreaEdit from './area-edit.vue';

export default {
  name: 'Area',
  components: {
    AreaDetail,
    AreaEdit,
    PlusOutlined,
    DeleteOutlined
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '地区码',
          dataIndex: 'areaCode',
          align: 'center'
        },
        {
          title: '地区名称',
          dataIndex: 'areaName'
        },
        {
          title: '地区等级',
          dataIndex: 'level',
          key: 'level',
          align: 'center'
        },
        {
          title: '上级代码',
          dataIndex: 'parentCode',
          align: 'center'
        },
        {
          title: '上级名称',
          dataIndex: 'parentName'
        },
        {
          title: '国标码',
          dataIndex: 'gbCode',
          align: 'center'
        },
        {
          title: '有效状态',
          dataIndex: 'status',
          key: 'status',
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center'
        }
      ],
      // 表格搜索条件
      where: {},
      selection: [],
      current: null,
      showDetail: false,
      showEdit: false
    };
  },
  methods: {
    reload() {
      this.selection = [];
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.selection = [];
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    handleEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    async editState(checked, row) {
      const id = row.id;
      const status = checked ? 1 : 0;
      const result = await AreaApi.changeStatus({ id, status });
      message.success(result.message);
      row.status = status;
    },

    async remove(row) {
      const result = await AreaApi.delete({ id: row.id });
      message.success(result.message);
      this.reload();
    },

    removeBatch() {
      if (!this.selection.length) {
        message.error('请至少选择一条数据');
        return;
      }
      Modal.confirm({
        title: '提示',
        content: '确定要删除选中的数据吗?',
        icon: createVNode(ExclamationCircleOutlined),
        maskClosable: true,
        onOk: async () => {
          let params = this.selection.map(d => d.id);
          const result = await AreaApi.batchDelete({ ids: params });
          message.success(result.message);
          this.reload();
        }
      });
    },

    datasource({ page, limit, where, orders }) {
      return AreaApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
