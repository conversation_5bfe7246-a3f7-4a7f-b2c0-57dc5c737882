<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="银行名称">
              <a-input v-model:value.trim="where.bankName" placeholder="请输入银行名称" allow-clear />
            </a-form-item>
            <a-form-item label="银行行别代码">
              <a-input v-model:value.trim="where.typeCode" placeholder="请输入银行行别代码" allow-clear />
            </a-form-item>
            <a-form-item label="银行英文简称（易票联）">
              <a-input v-model:value.trim="where.efpsBankEnCode" placeholder="请输入银行英文简称（易票联）" allow-clear />
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- 表格上方的操作按钮 -->
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>

          <!-- 表体的操作 -->
          <template #bodyCell="{ column, record }">
            <!-- table操作栏按钮 -->
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="handleEdit(record)">修改</a>
                <a-divider type="vertical" />

                <a-popconfirm title="确定要删除此记录吗？" @confirm="remove(record)">
                  <a class="ele-text-danger">删除</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 编辑 -->
    <BankCodeEdit v-model:visible="showEdit" :detailData="current" @done="reload" />


  </div>
</template>

<script>
import { message } from 'ant-design-vue';
import { BankCodeManageApi } from '@/api/base/BankCodeManageApi';
import BankCodeEdit from './bank-code-add.vue';

export default {
  name: 'BankCodeManage',
  components: {
    BankCodeEdit
  },
  data() {
    return {
      // 表格搜索条件
      where: {},
      selection: [],
      current: null,
      showDetail: false,
      showEdit: false,
      //表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center'
        },
        {
          title: '银行名称',
          dataIndex: 'bankName',
          align: 'center'
        },
        {
          title: '银行行别代码',
          dataIndex: 'typeCode',
          align: 'center'
        },
        {
          title: '银行英文简称（易票联）',
          dataIndex: 'efpsBankEnCode',
          align: 'center'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          align: 'center'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId',
          align: 'center'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime',
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 150,
          align: 'center'
        }
      ]
    };
  },
  methods: {
    //查询方法
    reload() {
      this.$refs.table.reload({ page: 1 });
    },
    //重置
    reset() {
      this.where = {}; //清空查询条件
      /**
       * 为啥都清空了还要添加
       */
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    handleDetail(row) {
      console.log('详情' + row);
      this.current = row;
      this.showDetail = true;
    },

    async remove(row) {
      const result = await BankCodeManageApi.delete({ id: row.id });
      message.success(result.message);
      this.reload();
    },

    //获取数据方法
    datasource({ page, limit, where }) {
      return BankCodeManageApi.getBankCodePages({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>

<style>
</style>
