<template>
  <a-modal
    :width="600"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改银行卡编码' : '新建银行卡编码'"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @cancel="cancel"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules" :label-col="{ style: { width: '110px' } }">
      <a-form-item label="银行名称:" name="bankName">
        <a-input v-model:value="form.bankName" placeholder="请输入银行名称" allow-clear />
      </a-form-item>

      <a-form-item label="银行行别代码:" name="typeCode">
        <a-input v-model:value="form.typeCode" placeholder="请输入银行行别代码" allow-clear />
      </a-form-item>

      <a-form-item label="银行英文简称（易票联）:" name="efpsBankEnCode">
        <a-input v-model:value="form.efpsBankEnCode" placeholder="请输入银行英文简称（易票联）" allow-clear />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { BankCodeManageApi } from '@/api/base/BankCodeManageApi';
import { message } from 'ant-design-vue';

export default {
  name: 'BankCodeManage',
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    detailData: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {},
      // 表单验证规则
      rules: {
        bankName: [{ required: true, message: '请输入银行名称' }],
        typeCode: [{ required: true, message: '请输入银行行别代码' }],
        efpsBankEnCode: [{ required: false, message: '请输入银行英文简称（易票联）' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false
    };
  },
  watch: {
    detailData() {
      if (this.detailData) {
        this.form = Object.assign({}, this.detailData);
        this.isUpdate = true;
      } else {
        this.form = {};
        this.isUpdate = false;
      }
      if (this.$refs.form) {
        this.$refs.form.clearValidate();
      }
    }
  },
  methods: {
    /**
     * 保存和编辑
     */
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      // 执行编辑或修改
      if (this.isUpdate) {
        result = BankCodeManageApi.edit(this.form);
      } else {
        result = BankCodeManageApi.add(this.form);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 如果是新增，则form表单置空
          if (!this.isUpdate) {
            this.form = {};
          }

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    cancel() {
      this.form = Object.assign({}, this.data);
      this.$refs.form.clearValidate();
    },

    /**
     * 更新编辑界面的弹框是否显示
     *
     * @param value true-显示，false-隐藏
     */
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>

<style></style>
