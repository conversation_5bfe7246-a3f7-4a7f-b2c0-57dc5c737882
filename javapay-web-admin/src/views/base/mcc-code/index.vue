<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="MCC码">
              <a-input v-model:value.trim="where.mcc" placeholder="请输入MCC码" allow-clear />
            </a-form-item>
            <a-form-item label="MCC名称">
              <a-input v-model:value.trim="where.mccName" placeholder="请输入MCC名称" allow-clear />
            </a-form-item>
            <a-form-item label="父级MCC码">
              <a-input v-model:value.trim="where.parentMcc" placeholder="请输入父级MCC码" allow-clear />
            </a-form-item>
            <a-form-item label="是否日常消费">
              <a-select v-model:value="where.consumeType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">是</a-select-option>
                <a-select-option :value="0">否</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="是否特许行业">
              <a-select v-model:value="where.franchiseType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">是</a-select-option>
                <a-select-option :value="0">否</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="是否允许开通双免">
              <a-select v-model:value="where.freeType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">是</a-select-option>
                <a-select-option :value="0">否</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="是否常用">
              <a-select v-model:value="where.isGeneral" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">是</a-select-option>
                <a-select-option :value="0">否</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="MCC级别">
              <a-select v-model:value="where.mccLevel" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">大类</a-select-option>
                <a-select-option :value="2">小类</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="MCC类别">
              <a-select v-model:value="where.mccType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">标准类</a-select-option>
                <a-select-option :value="1">优惠类</a-select-option>
                <a-select-option :value="2">减免类</a-select-option>
                <a-select-option :value="3">特殊计费类</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="状态">
              <a-select v-model:value="where.status" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">有效</a-select-option>
                <a-select-option :value="0">无效</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table
          ref="table"
          row-key="id"
          :datasource="datasource"
          :columns="columns"
          :where="where"
          v-model:selection="selection"
          :scroll="{ x: 'max-content' }"
        >
          <!-- table上边工具栏 -->
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
              <a-button danger @click="removeBatch">
                <template #icon>
                  <delete-outlined />
                </template>
                <span>删除</span>
              </a-button>
            </a-space>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-switch :checked="record.status === 1" @change="checked => editState(checked, record)" />
            </template>

            <template v-else-if="column.key === 'mccType'">
              <a-tag v-if="record.mccType === 0" color="pink">标准类</a-tag>
              <a-tag v-else-if="record.mccType === 1" color="cyan">优惠类</a-tag>
              <a-tag v-else-if="record.mccType === 2" color="blue">减免类</a-tag>
              <a-tag v-else-if="record.mccType === 3" color="purple">特殊计费类</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
                <a-divider type="vertical" />
                <a @click="handleEdit(record)">修改</a>
                <a-divider type="vertical" />
                <a-popconfirm title="确定要删除此行数据吗？" @confirm="remove(record)">
                  <a class="ele-text-danger">删除</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 编辑 -->
    <MccCodeEdit v-model:visible="showEdit" :data="current" :mccCodeList="mccCodeList" @done="reload" />

    <!-- 详情 -->
    <MccCodeDetail v-model:visible="showDetail" :detail="current" :mccCodeList="mccCodeList" />
  </div>
</template>

<script>
import { message, Modal } from 'ant-design-vue';
import { createVNode } from 'vue';
import { MccCodeApi } from '@/api/base/MccCodeApi';
import MccCodeDetail from './mcc-code-detail.vue';
import MccCodeEdit from './mcc-code-edit.vue';
import { PlusOutlined, DeleteOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue';

export default {
  name: 'MccCode',
  components: {
    MccCodeDetail,
    MccCodeEdit,
    PlusOutlined,
    DeleteOutlined
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: 'MCC码',
          dataIndex: 'mcc',
          align: 'center',
        },
        {
          title: 'MCC名称',
          dataIndex: 'mccName',
          width: 220
        },
        {
          title: 'MCC级别',
          dataIndex: 'mccLevel',
          align: 'center',
          width: 120,
          customRender: function ({ text }) {
            if (1 === text) {
              return '大类';
            } else if (text === 2) {
              return '小类';
            } else {
              return '--';
            }
          }
        },
        {
          title: 'MCC类别',
          dataIndex: 'mccType',
          key: 'mccType',
          align: 'center',
          width: 140
        },
        {
          title: '日常消费',
          dataIndex: 'consumeType',
          align: 'center',
          width: 120,
          customRender: ({ text }) => (text === 1 ? '是' : '否')
        },
        {
          title: '特许行业',
          align: 'center',
          width: 120,
          dataIndex: 'franchiseType',
          customRender: ({ text }) => (text === 1 ? '是' : '否')
        },
        {
          title: '允许开通双免',
          align: 'center',
          width: 120,
          dataIndex: 'freeType',
          customRender: ({ text }) => (text === 1 ? '是' : '否')
        },
        {
          title: '常用查询',
          dataIndex: 'isGeneral',
          width: 120,
          align: 'center',
          customRender: ({ text }) => (text === 1 ? '是' : '否')
        },
        {
          title: '父级MCC码',
          dataIndex: 'parentMcc',
          align: 'center'
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          width: 160,
          align: 'center'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime',
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right',
          width: 180
        }
      ],
      // 表格搜索条件
      where: {},
      selection: [],
      current: null,
      showDetail: false,
      showEdit: false,
      mccCodeList:[]
    };
  },
  async mounted() {
    this.mccCodeList = await MccCodeApi.list({ mccLevel: 1, status: 1 });
  },
  methods: {
    reload() {
      this.selection = [];
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.selection = [];
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    async editState(checked, row) {
      const id = row.id;
      const status = checked ? 1 : 0;
      const result = await MccCodeApi.changeStatus({ id, status });
      message.success(result.message);
      row.status = status;
    },

    handleEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    async remove(row) {
      const result = await MccCodeApi.delete({ id: row.id });
      message.success(result.message);
      this.reload();
    },

    removeBatch() {
      if (!this.selection.length) {
        message.error('请至少选择一条数据');
        return;
      }
      Modal.confirm({
        title: '提示',
        content: '确定要删除选中的数据吗?',
        icon: createVNode(ExclamationCircleOutlined),
        maskClosable: true,
        onOk: async () => {
          let params = this.selection.map(d => d.id);
          const result = await MccCodeApi.batchDelete({ ids: params });
          message.success(result.message);
          this.reload();
        }
      });
    },

    datasource({ page, limit, where, orders }) {
      return MccCodeApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
