<template>
  <a-modal
    :width="800"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
    :footer="null"
  >
    <a-descriptions bordered :column="2">
      <a-descriptions-item label="MCC名称">{{ form.mccName }}</a-descriptions-item>
      <a-descriptions-item label="MCC码">{{ form.mcc }}</a-descriptions-item>
      <a-descriptions-item label="MCC级别">
        {{ form.mccLevel === 1 ? '大类' : form.mccLevel === 2 ? '小类' : '--' }}
      </a-descriptions-item>
      <a-descriptions-item label="MCC类别">
        <a-tag v-if="form.mccType === 0" color="pink">标准类</a-tag>
        <a-tag v-else-if="form.mccType === 1" color="cyan">优惠类</a-tag>
        <a-tag v-else-if="form.mccType === 2" color="blue">减免类</a-tag>
        <a-tag v-else-if="form.mccType === 3" color="purple">特殊计费类</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="状态">
        <span v-if="form.status === 1" class="ele-text-success">有效</span>
        <span v-else class="ele-text-danger">无效</span>
      </a-descriptions-item>
      <a-descriptions-item label="父级MCC">{{ form.parentMccName }}</a-descriptions-item>
      <a-descriptions-item label="日常消费">{{ form.consumeType === 1 ? '是' : '否' }}</a-descriptions-item>
      <a-descriptions-item label="特许行业">{{ form.franchiseType === 1 ? '是' : '否' }}</a-descriptions-item>
      <a-descriptions-item label="允许开通双免">{{ form.freeType === 1 ? '是' : '否' }}</a-descriptions-item>
      <a-descriptions-item label="常用查询">{{ form.isGeneral === 1 ? '是' : '否' }}</a-descriptions-item>
      <a-descriptions-item label="创建人">{{ form.createUserId }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改人">{{ form.lastModifyUserId }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

export default {
  name: 'MccCodeDetail',
  props: {
    visible: Boolean,
    detail: Object,
    mccCodeList: Array
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
        const item = props.mccCodeList.find(m => m.mcc === data.form.parentMcc);
        item && (data.form.parentMccName = item.mccName);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible,
    };
  }
};
</script>
