<template>
  <a-modal
    :width="1000"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新增'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @cancel="cancel"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 7 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 17 }, sm: { span: 24 } }"
    >
      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="MCC名称" name="mccName">
            <a-input v-model:value.trim="form.mccName" placeholder="请输入MCC名称" allow-clear />
          </a-form-item>
          <a-form-item label="MCC级别" name="mccLevel">
            <a-select v-model:value="form.mccLevel" style="width: 100%" placeholder="请选择" allow-clear>
              <a-select-option :value="1">大类</a-select-option>
              <a-select-option :value="2">小类</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item v-if="form.mccLevel === 2" label="父级MCC码" name="parentMcc">
            <a-select v-model:value="form.parentMcc" style="width: 100%" placeholder="请选择" allow-clear>
              <a-select-option v-for="({ mcc, mccName }, key) in mccCodeList" :key="key" :value="mcc">{{ mccName }}</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="特许行业" name="franchiseType">
            <a-select v-model:value="form.franchiseType" style="width: 100%" placeholder="请选择" allow-clear>
              <a-select-option :value="1">是</a-select-option>
              <a-select-option :value="0">否</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="允许开通双免" name="freeType">
            <a-select v-model:value="form.freeType" style="width: 100%" placeholder="请选择" allow-clear>
              <a-select-option :value="1">是</a-select-option>
              <a-select-option :value="0">否</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="MCC码" name="mcc">
            <a-input v-model:value.trim="form.mcc" placeholder="请输入MCC码" allow-clear />
          </a-form-item>
          <a-form-item label="MCC类别" name="mccType">
            <a-select v-model:value="form.mccType" style="width: 100%" placeholder="请选择" allow-clear>
              <a-select-option :value="0">标准类</a-select-option>
              <a-select-option :value="1">优惠类</a-select-option>
              <a-select-option :value="2">减免类</a-select-option>
              <a-select-option :value="3">特殊计费类</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="状态" name="status">
            <a-select v-model:value="form.status" style="width: 100%" placeholder="请选择" allow-clear>
              <a-select-option :value="1">有效</a-select-option>
              <a-select-option :value="0">无效</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="日常消费" name="consumeType">
            <a-select v-model:value="form.consumeType" style="width: 100%" placeholder="请选择" allow-clear>
              <a-select-option :value="1">是</a-select-option>
              <a-select-option :value="0">否</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="常用查询" name="isGeneral">
            <a-select v-model:value="form.isGeneral" style="width: 100%" placeholder="请选择" allow-clear>
              <a-select-option :value="1">是</a-select-option>
              <a-select-option :value="0">否</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { MccCodeApi } from '@/api/base/MccCodeApi';

function formDefaults() {
  return {
    status: 1,
    mccLevel: 2,
    mccType: 0,
    franchiseType: 0,
    freeType: 1,
    consumeType: 1,
    isGeneral: 1
  };
}

export default {
  name: 'MccCodeEdit',
  props: {
    visible: Boolean,
    data: Object,
    mccCodeList: Array
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: Object.assign({}, this.data),
      // 表单验证规则
      rules: {
        mcc: [{ required: true, message: '请输入MCC码' }],
        mccName: [{ required: true, message: '请输入MCC名称' }],
        parentMcc: [{ required: true, message: '请输入父级MCC码' }],
        mccLevel: [{ required: true, message: '请选择' }],
        mccType: [{ required: true, message: '请选择' }],
        status: [{ required: true, message: '请选择' }],
        consumeType: [{ required: true, message: '请选择' }],
        franchiseType: [{ required: true, message: '请选择' }],
        freeType: [{ required: true, message: '请选择' }],
        isGeneral: [{ required: true, message: '请选择' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false
    };
  },
  watch: {
    data() {
      if (this.data) {
        this.form = Object.assign({}, this.data);
        this.isUpdate = true;
      } else {
        this.form = formDefaults();
        this.isUpdate = false;
      }
      if (this.$refs.form) {
        this.$refs.form.clearValidate();
      }
    }
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      // 执行编辑或修改方法
      if (this.isUpdate) {
        result = MccCodeApi.edit(this.form);
      } else {
        result = MccCodeApi.add(this.form);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 如果是新增，则form表单置空
          if (!this.isUpdate) {
            this.form = formDefaults();
          }

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    cancel() {
      this.form = Object.assign(formDefaults(), this.data);
      this.$refs.form.clearValidate();
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
