<template>
  <a-modal
    :width="1000"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions bordered :column="2">
      <a-descriptions-item label="APP品牌类型" :span="2">{{ form.appBrandName }}</a-descriptions-item>
      <a-descriptions-item label="版本号" :span="2">{{ form.versionCode }}</a-descriptions-item>
      <a-descriptions-item label="版本描述" :span="2">{{ form.versionDesc }}</a-descriptions-item>
      <a-descriptions-item label="设备类型">
        <a-badge v-if="form.deviceType === 1" color="blue" text="安卓" />
        <a-badge v-else-if="form.deviceType === 2" color="green" text="苹果" />
      </a-descriptions-item>
      <a-descriptions-item label="是否强制升级">
        <a-tag v-if="form.isForcedUpgrade === 0">否</a-tag>
        <a-tag v-else-if="form.isForcedUpgrade === 1" color="red">是</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="创建人">{{ form.createUserId }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改人">{{ form.lastModifyUserId }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>
    <a-divider />
    <a-collapse :active-key="['1']">
      <a-collapse-panel key="1" header="版本参数">
        <template v-for="(item, idx) in form.versionParamsDTOList || []" :key="idx">
          <a-divider v-if="item.firmName" orientation="left" orientationMargin="0" dashed>{{ item.firmName }}</a-divider>
          <a-descriptions bordered :column="1" size="small">
            <a-descriptions-item label="是否审核通过">
              <a-tag v-if="item.isCheckPass === 0">否</a-tag>
              <a-tag v-else-if="item.isCheckPass === 1" color="green">是</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="版本下载地址" v-if="item.downloadUrl">
              {{ item.downloadUrl }}
            </a-descriptions-item>
          </a-descriptions>
        </template>
      </a-collapse-panel>
    </a-collapse>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

export default {
  name: 'AppVersionDetail',
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {},
      // 厂商枚举
      FirmEnum: [
        { label: '蒲公英', value: 'public' },
        { label: '华为', value: 'huawei' },
        { label: '小米', value: 'xiaomi' },
        { label: 'oppo', value: 'oppo' },
        { label: 'vivo', value: 'vivo' },
        { label: '荣耀', value: 'honor' },
        { label: 'AppStore', value: 'ios' }
      ]
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);

        if (data.form.versionParams) {
          const json2arr = JSON.parse(data.form.versionParams);
          json2arr?.forEach(item => {
            const firmItem = data.FirmEnum.find(e => e.value === item.pubChn);
            item.firmName = firmItem?.label;
          });

          data.form.versionParamsDTOList = json2arr;
        }
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
