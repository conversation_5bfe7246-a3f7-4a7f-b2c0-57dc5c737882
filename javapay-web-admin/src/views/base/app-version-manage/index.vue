<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row>
            <a-form-item label="版本号">
              <a-input v-model:value.trim="where.versionCode" placeholder="请输入版本号" allow-clear />
            </a-form-item>
            <a-form-item label="是否强制升级">
              <a-select v-model:value="where.isForcedUpgrade" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">否</a-select-option>
                <a-select-option :value="1">是</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="设备类型">
              <a-select v-model:value="where.deviceType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">安卓</a-select-option>
                <a-select-option :value="2">苹果</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="品牌类型">
              <a-select v-model:value="where.appBrandType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option v-for="(item, key) in brands" :key="key" :value="item.appBrandType">{{ item.appName }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- table上边工具栏 -->
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'deviceType'">
              <a-badge v-if="record.deviceType === 1" color="blue" text="安卓" />
              <a-badge v-else-if="record.deviceType === 2" color="green" text="苹果" />
            </template>
            <template v-else-if="column.key === 'isForcedUpgrade'">
              <a-tag v-if="record.isForcedUpgrade === 0">否</a-tag>
              <a-tag v-else-if="record.isForcedUpgrade === 1" color="red">是</a-tag>
            </template>
            <!-- table操作栏按钮 -->
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
                <a-divider type="vertical" />
                <a @click="handleEdit(record)">修改</a>
                <a-divider type="vertical" />
                <a-popconfirm title="确定要删除此APP版本吗？" @confirm="remove(record)">
                  <a class="ele-text-danger">删除</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 编辑 -->
    <AppVersionEdit v-if="showEdit" v-model:visible="showEdit" :data="current" :brands="brands" @done="reload" />

    <!-- 详情 -->
    <AppVersionDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { message } from 'ant-design-vue';
import { AppVersionManageApi } from '@/api/base/AppVersionManageApi';
import AppVersionDetail from './AppVersionDetail.vue';
import AppVersionEdit from './AppVersionEdit.vue';
import { AppBrandApi } from '@/api/base/AppBrandApi';

export default {
  name: 'AppVersionManage',
  components: {
    AppVersionDetail,
    AppVersionEdit
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '品牌类型',
          dataIndex: 'appBrandType',
          key: 'appBrandType',
          align: 'center',
          customRender: ({ text, record }) => {
            const item = this.brands.find(c => c.appBrandType === text);
            record.appBrandName = item?.appName || '--';
            return item?.appName || '--';
          }
        },
        {
          title: '版本号',
          dataIndex: 'versionCode'
        },
        {
          title: '设备类型',
          dataIndex: 'deviceType',
          key: 'deviceType',
          width: 150,
          align: 'center'
        },
        {
          title: '是否强制升级',
          dataIndex: 'isForcedUpgrade',
          key: 'isForcedUpgrade',
          width: 150,
          align: 'center'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          width: 180,
          align: 'center',
          fixed: 'right'
        }
      ],
      // 表格搜索条件
      where: {},
      current: null,
      showDetail: false,
      showEdit: false,
      brands: []
    };
  },
  activated() {
    this.getBrands();
  },
  methods: {
    async getBrands() {
      const data = await AppBrandApi.list();
      this.brands = data || [];
    },

    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    handleEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    async remove(row) {
      const result = await AppVersionManageApi.delete({ id: row.id });
      message.success(result.message);
      this.reload();
    },

    datasource({ page, limit, where, orders }) {
      return AppVersionManageApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
