<template>
  <a-modal
    :width="700"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新增'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 6 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 18 }, sm: { span: 24 } }"
    >
      <div class="label-custom">
        <a-form-item label="APP品牌类型" name="appBrandType">
          <a-select v-model:value="form.appBrandType" style="width: 100%" placeholder="请选择" :disabled="isUpdate">
            <a-select-option v-for="(item, key) in brands" :key="key" :value="item.appBrandType">{{ item.appName }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="设备类型" name="deviceType">
          <a-radio-group v-model:value="form.deviceType" button-style="solid" @change="changeDeviceType">
            <a-radio-button :value="1" :disabled="isUpdate && form.deviceType === 2">安卓</a-radio-button>
            <a-radio-button :value="2" :disabled="isUpdate && form.deviceType === 1">苹果</a-radio-button>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="版本号" name="versionCode">
          <a-input v-model:value="form.versionCode" placeholder="版本号(eg: 1.0.0)" :disabled="isUpdate" />
        </a-form-item>
        <a-form-item label="版本描述" name="versionDesc">
          <a-textarea auto-size v-model:value="form.versionDesc" placeholder="版本描述" />
        </a-form-item>
        <a-form-item label="下载地址" name="downloadUrl">
          <a-input v-model:value="form.downloadUrl" placeholder="版本下载地址" />
        </a-form-item>
        <a-form-item label="是否强制升级" name="isForcedUpgrade">
          <a-radio-group v-model:value="form.isForcedUpgrade">
            <a-radio :value="0">否</a-radio>
            <a-radio :value="1">是</a-radio>
          </a-radio-group>
        </a-form-item>

        <template v-if="form.deviceType === 1">
          <a-form-item label="应用市场" name="firms">
            <a-checkbox-group :value="checkedFirms" name="checkboxgroup" :options="FirmEnum" @change="changeCheckedFirm" />
          </a-form-item>
        </template>
      </div>

      <template v-for="(item, idx) in form.versionParamsDTOList || []" :key="idx">
        <a-divider orientation="left" :orientationMargin="10" dashed>
          {{ item.firmName || (item.pubChn === 'ios' ? 'AppStroe' : '--') }}
        </a-divider>
        <a-row :gutter="16">
          <a-col :md="6" :sm="24" :xs="24">
            <a-form-item
              label="是否审核通过"
              :label-col="{ md: { span: 18 }, sm: { span: 24 } }"
              :wrapper-col="{ md: { span: 6 }, sm: { span: 24 } }"
            >
              <a-switch
                v-model:checked="item.isCheckPass"
                checked-children="是"
                un-checked-children="否"
                :checked-value="1"
                :un-checked-value="0"
              />
            </a-form-item>
          </a-col>
          <a-col :md="18" :sm="24" :xs="24">
            <a-form-item v-if="item.pubChn === 'public'" label="下载地址">
              <a-textarea auto-size v-model:value="item.downloadUrl" placeholder="请输入版本下载地址" />
            </a-form-item>
          </a-col>
        </a-row>
      </template>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { AppVersionManageApi } from '@/api/base/AppVersionManageApi';

export default {
  name: 'AppVersionEdit',
  props: {
    visible: Boolean,
    data: Object,
    brands: Array
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {
        deviceType: 1,
        versionCode: '',
        versionDesc: '',
        isForcedUpgrade: 0,
        versionParamsDTOList: [],
        appBrandType: null
      },
      // 选中的厂商
      checkedFirms: [],
      // 表单验证规则
      rules: {
        versionCode: [
          { required: true, message: '请输入版本号' },
          { pattern: /^([1-9])(\.([0-9])){2}$/, message: '版本号不符合规则 例:1.0.0', trigger: 'blur' }
        ],
        versionDesc: [{ required: true, message: '请输入版本描述' }],
        appBrandType: [{ required: true, message: '请选择' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false,
      // 安卓厂商枚举
      FirmEnum: [
        { label: '蒲公英', value: 'public' },
        { label: '华为', value: 'huawei' },
        { label: '小米', value: 'xiaomi' },
        { label: 'oppo', value: 'oppo' },
        { label: 'vivo', value: 'vivo' },
        { label: '荣耀', value: 'honor' }
      ]
    };
  },
  mounted() {
    if (this.data) {
      this.form = Object.assign({}, this.data);
      if (this.form.versionParams) {
        const json2arr = JSON.parse(this.form.versionParams);
        json2arr?.forEach(item => {
          const firmItem = this.FirmEnum.find(e => e.value === item.pubChn);
          const firmIndex = this.FirmEnum.findIndex(e => e.value === item.pubChn);
          item.firmName = firmItem?.label;

          if (firmItem) {
            this.FirmEnum[firmIndex].disabled = true;
          }
        });

        this.form.versionParamsDTOList = json2arr;

        this.checkedFirms = json2arr.map(f => f.pubChn);
      }
      this.isUpdate = true;
    } else {
      this.changeDeviceType();
      this.isUpdate = false;
    }
  },
  methods: {
    changeCheckedFirm(value) {
      // 取差集 (A-B)∪(B-A), 即变化的项
      const a = value;
      const b = this.checkedFirms;
      const diffValue = a.reduce((u, va) => (u.every(vu => vu != va) ? u.concat(va) : u.filter(vu => vu != va)), b);
      const diffItem = this.FirmEnum.find(c => c.value === diffValue[0]);
      const diffIndex = this.FirmEnum.findIndex(c => c.value === diffValue[0]);

      const isAdd = value.length > this.checkedFirms.length;
      if (isAdd) {
        this.form.versionParamsDTOList.splice(diffIndex, 0, {
          firmName: diffItem.label,
          isCheckPass: diffItem.value === 'public' ? 1 : 0,
          pubChn: diffItem.value,
          downloadUrl: ''
        });
      } else {
        this.form.versionParamsDTOList = this.form.versionParamsDTOList.filter(i => i.pubChn !== diffItem.value);
      }

      this.checkedFirms = value;
    },
    changeDeviceType() {
      if (this.form.deviceType === 1) {
        let firmList = [];
        this.FirmEnum.forEach(t => {
          firmList.push({
            firmName: t.label,
            isCheckPass: t.value === 'public' ? 1 : 0,
            pubChn: t.value,
            downloadUrl: ''
          });
        });
        this.form.versionParamsDTOList = firmList;

        this.checkedFirms = this.FirmEnum.map(f => f.value);
      } else {
        this.form.versionParamsDTOList = [
          {
            isCheckPass: 0,
            pubChn: 'ios'
          }
        ];
      }
    },
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      // 执行编辑或修改方法
      if (this.isUpdate) {
        result = AppVersionManageApi.edit(this.form);
      } else {
        result = AppVersionManageApi.add(this.form);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>

<style lang="less" scoped>
:deep(.ant-divider-horizontal.ant-divider-with-text) {
  margin: 0 0 16px;
}

:deep(.label-custom .ant-form-item-label) {
  background-color: #f3f5f7;
  text-align: center;
}
</style>
