<template>
  <a-modal
    :width="750"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    :footer="null"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="总行编码">{{ form.typeCode }}</a-descriptions-item>
      <a-descriptions-item label="总行名称">{{ form.typeName }}</a-descriptions-item>
      <a-descriptions-item label="英文编码">{{ form.typeEnCode }}</a-descriptions-item>
      <a-descriptions-item label="银行性质">{{ natureEnpm[Number(form.nature) - 1] }}</a-descriptions-item>
      <a-descriptions-item label="状态" :span="2">
        <a-tag v-if="form.status === 1" color="success">有效</a-tag>
        <a-tag v-else color="error">无效</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="创建人">{{ form.createUserId }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改人">{{ form.lastModifyUserId }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

export default {
  name: 'BnkTypeDetail',
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible,
      natureEnpm
    };
  }
};
const natureEnpm = ['中央银行', '国有独资商业银行', '政策性银行', '其他商业银行', '非银行金融机构', '外资在华银行或代表处', '特区参与者'];
</script>
