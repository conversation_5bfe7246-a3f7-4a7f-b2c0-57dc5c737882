<template>
  <a-modal
    :width="600"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新增'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @cancel="cancel"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 6 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 18 }, sm: { span: 24 } }"
    >
      <a-form-item label="总行编码" name="typeCode">
        <a-input v-model:value="form.typeCode" placeholder="请输入总行编码" allow-clear />
      </a-form-item>
      <a-form-item label="总行名称" name="typeName">
        <a-input v-model:value="form.typeName" placeholder="请输入总行名称" allow-clear />
      </a-form-item>
      <a-form-item label="英文编码">
        <a-input v-model:value="form.typeEnCode" placeholder="请输入英文编码" allow-clear />
      </a-form-item>
      <a-form-item label="银行性质" name="nature">
        <a-select v-model:value="form.nature" style="width: 100%" placeholder="请选择">
          <a-select-option v-for="(text, key) in natureEnpm" :key="key" :value="key + 1">{{ text }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="状态" name="status">
        <a-radio-group v-model:value="form.status" name="status">
          <a-radio :value="1">有效</a-radio>
          <a-radio :value="0">无效</a-radio>
        </a-radio-group>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { BankTypeApi } from '@/api/base/BankTypeApi';

const natureEnpm = [
  '中央银行',
  '国有独资商业银行',
  '政策性银行',
  '其他商业银行',
  '非银行金融机构',
  '外资在华银行或代表处',
  '特区参与者'
];
function formDefaults() {
  return {
    status: 1
  };
}

export default {
  name: 'BankTypeEdit',
  props: {
    visible: Boolean,
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: Object.assign({}, this.data),
      // 表单验证规则
      rules: {
        typeCode: [{ required: true, message: '请输入总行编码' }],
        typeName: [{ required: true, message: '请输入总行名称' }],
        nature: [{ required: true, message: '请选择银行性质' }],
        status: [{ required: true, message: '请选择有效状态' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false,
      natureEnpm
    };
  },
  watch: {
    data() {
      if (this.data) {
        this.form = Object.assign({}, this.data);
        this.isUpdate = true;
      } else {
        this.form = formDefaults();
        this.isUpdate = false;
      }
      if (this.$refs.form) {
        this.$refs.form.clearValidate();
      }
    }
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      // 执行编辑或修改方法
      if (this.isUpdate) {
        result = BankTypeApi.edit(this.form);
      } else {
        result = BankTypeApi.add(this.form);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 如果是新增，则form表单置空
          if (!this.isUpdate) {
            this.form = formDefaults();
          }

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    cancel() {
      this.form = Object.assign(formDefaults(), this.data);
      this.$refs.form.clearValidate();
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
