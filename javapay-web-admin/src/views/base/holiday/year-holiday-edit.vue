<template>
  <a-modal
    :width="500"
    :visible="visible"
    :confirm-loading="loading"
    title="节假日数据编辑"
    :mask-closable="false"
    @update:visible="updateVisible"
    :bodyStyle="{ paddingBottom: '40px' }"
    :footer="null"
  >
    <h2>
      即将更改
      <span style="color: red; font-weight: 700">{{ date }}</span>
      这天设置
    </h2>
    <br />
    <a-row class="button-container" v-show="isHolidays === false">
      <a-button class="holidays-button" @click="changeForHolidays">更改为节假日</a-button>
    </a-row>

    <a-row class="button-container" v-show="isStatutoryHolidays === false">
      <a-button class="statutoryHolidays-button" @click="changeForStatutoryHolidays">更改为法定节假日</a-button>
    </a-row>

    <a-row class="button-container" v-show="isWorkingDay === false">
      <a-button class="workingDay-button" @click="changeForWorkingDay">更改为工作日</a-button>
    </a-row>
  </a-modal>
</template>

<script>
import { HolidayApi } from '@/api/base/HolidayApi';
import { message } from 'ant-design-vue';

export default {
  name: 'YearHolidayEdit',
  props: {
    visible: Boolean,
    showDataValue: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      loading: false,
      //选中的日期YYYY-MM-DD
      date: '',
      //传进来的数据进行复制的数据
      dataObj: {},
      isHolidays: false,
      isStatutoryHolidays: false,
      isWorkingDay: false,
      otherMonthDay: false
    };
  },
  mounted() {
    //进入页面开始处理
    this.dataObj = Object.assign({}, this.showDataValue);
    //小于10的前面进行补0
    this.dataObj.month = this.dataObj.month + 1 < 10 ? '0' + (this.dataObj.month + 1) : (this.dataObj.month + 1) + '';
    //小于10的前面进行补0
    this.dataObj.day = this.dataObj.day < 10 ? '0' + this.dataObj.day : this.dataObj.day + '';
    //获得数据YYYY-MM-DD
    this.date = this.dataObj.year + '-' + this.dataObj.month + '-' + this.dataObj.day;

    //对选择日的状态进行处理和数据赋值
    if (this.dataObj.holidayStatus === 'Holidays') {
      this.isHolidays = true;
      this.isStatutoryHolidays = false;
      this.isWorkingDay = false;
      this.otherMonthDay = false;
    } else if (this.dataObj.holidayStatus === 'isStatutoryHolidays') {
      this.isHolidays = false;
      this.isStatutoryHolidays = true;
      this.isWorkingDay = false;
      this.otherMonthDay = false;
    } else if (this.dataObj.holidayStatus === 'isWorkingDay') {
      this.isHolidays = false;
      this.isStatutoryHolidays = false;
      this.isWorkingDay = true;
      this.otherMonthDay = false;
    } else {
      //其它日期的处理
      this.isHolidays = false;
      this.isStatutoryHolidays = false;
      this.isWorkingDay = false;
      this.otherMonthDay = true;
    }
  },
  methods: {
    updateVisible(value) {
      this.$emit('update:visible', value);
    },

    //改成节假日的状态
    async changeForHolidays() {
      // 修改加载框为正在加载
      this.loading = true;
      await HolidayApi.editHoliday({ id: this.dataObj.id, days: this.date, isWorkingDay: 0, isHolidays: 1, isStatutoryHolidays: 0 })
        .then(res => {
          this.loading = false;
          message.success(res.message);
          this.updateVisible(false);
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },
    //改成法定节假日的状态
    async changeForStatutoryHolidays() {
      // 修改加载框为正在加载
      this.loading = true;
      await HolidayApi.editHoliday({ id: this.dataObj.id, days: this.date, isWorkingDay: 0, isHolidays: 0, isStatutoryHolidays: 1 })
        .then(res => {
          this.loading = false;
          message.success(res.message);
          this.updateVisible(false);
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },
    //改成工作日的状态
    async changeForWorkingDay() {
      // 修改加载框为正在加载
      this.loading = true;
      await HolidayApi.editHoliday({ id: this.dataObj.id, days: this.date, isWorkingDay: 1, isHolidays: 0, isStatutoryHolidays: 0 })
        .then(res => {
          this.loading = false;
          message.success(res.message);
          this.updateVisible(false);
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    }
  }
};
</script>

<style scoped>
.button-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-bottom: 20px;
}

.holidays-button {
  width: 300px;
  height: 40px;
  color: white;
  background-color: rgb(248, 172, 89);
}

.statutoryHolidays-button {
  width: 300px;
  height: 40px;
  color: white;
  background-color: rgb(237, 85, 101);
}

.workingDay-button {
  width: 300px;
  height: 40px;
  color: white;
  background-color: rgb(35, 198, 200);
}

h2 {
  text-align: center;
}
</style>
