<template>
  <div>
    <div class="top-con">
      <div class="top" v-for="item in top" :key="item">星期{{ item }}</div>
    </div>
    <!-- 日历号 -->
    <div class="date-con">
      <div class="date" :class="[item.thisMonth, item.afterToday, item.holidayStatus, item.isToday]" v-for="(item, index) in visibleCalendar" :key="index" @click="dateClick(item)">
        <div class="date-day-show">{{ item.day }}</div>
        <div style="color: white;">{{ item.holidayShowText }}</div>
      </div>
    </div>
  </div>
</template>
  
<script>
export default {
  props: {
    time: {
      type: Object,
      default: () => {
        return {};
      }
    },
    //从主页面传进来的，页面月份，每一天的请求数据
    showMonthData: Object
  },
  emits: ['dateClickMethod'],
  data() {
    return {
      top: ['一', '二', '三', '四', '五', '六', '日']
    };
  },
  computed: {
    // 获取当前月份显示日历,共42天
    visibleCalendar: function () {
      // 获取今天的年月日
      const today = new Date();
      today.setHours(0);
      today.setMinutes(0);
      today.setSeconds(0);
      today.setMilliseconds(0);

      const calendarArr = [];
      // 获取当前月份第一天
      const currentFirstDay = new Date(this.time.year, this.time.month, 1);
      // 获取第一天是周几
      const weekDay = currentFirstDay.getDay();

      // 用当前月份第一天减去周几前面几天，就是看见的日历的第一天
      let startDay = null;
      if (weekDay === 0) {
        startDay = currentFirstDay - (6) * 24 * 3600 * 1000;
      } else {
        startDay = currentFirstDay - (weekDay - 1) * 24 * 3600 * 1000;
      }
      
      // 我们统一用42天来显示当前显示日历
      for (let i = 0; i < 42; i++) {
        //开始日期，当前选中月份的开始日期，也就是日历的最开始的一天，可能不是这个月，有可能上上个月尾随的几天
        const date = new Date(startDay + i * 24 * 3600 * 1000);
        //当前日的id
        let hereId = "";
        //当前日的状态
        let hereState = '';
        //当前日显示文字
        let hereShowText = '';
        //当前日所在月
        let hereMonth = date.getMonth() + 1;
        //当前日
        let hereDate = date.getDate();
        //小于10的进行补0
        if (hereMonth < 10) {
          hereMonth = '0' + hereMonth;
        }
        if (hereDate < 10) {
          hereDate = '0' + hereDate;
        }
        //找到对应的这一天日期 YYYY-MM-DD
        const hereAllDate = date.getFullYear() + '-' + hereMonth + '-' + hereDate;
        
        
        //找到对应这一天的数据
        if (this.showMonthData !== null) {
          //通过日期判断是否有这天的数据
          const result = this.showMonthData.find(d => d.days === hereAllDate);
          //有数据的去判断假期状态
          if (typeof result !== 'undefined') {
            hereId = result.id;
            if (result.isHolidays === 1) {
              hereState = 'Holidays';
              hereShowText = '节假日';
            }
            if (result.isStatutoryHolidays === 1) {
              hereState = 'isStatutoryHolidays';
              hereShowText = '法定节假日';
            }
            if (result.isWorkingDay === 1) {
              hereState = 'isWorkingDay';
              hereShowText = '工作日';
            }
          } else {//没有数据说明不是这个月的
            hereState = 'otherMonthDay';
            hereShowText = '';
          }
        }
        //整理获得的数据，然后返回
        calendarArr.push({
          id: hereId,
          date: new Date(startDay + i * 24 * 3600 * 1000),
          year: date.getFullYear(),
          month: date.getMonth(),
          day: date.getDate(),
          // 是否在当月
          thisMonth: date.getFullYear() === today.getFullYear() && date.getMonth() === today.getMonth() ? 'thisMonth' : '',
          // 是否是今天
          isToday:
            date.getFullYear() === today.getFullYear() && date.getMonth() === today.getMonth() && date.getDate() === today.getDate()
              ? 'isToday'
              : '',
          // 是否在今天之后
          afterToday: date.getTime() >= today.getTime() ? 'afterToday' : '',
          //假期状态
          holidayStatus: hereState,
          holidayShowText: hereShowText,
        });
      }
      return calendarArr;
    }
  },

  methods: {
    dateClick(value) {
      this.$emit('dateClickMethod',value);
    }
  }
};
</script>
<style lang="less" scoped>
.top-con {
  display: flex;
  align-items: center;
  .top {
    width: 14.285%;
    background-color: rgb(242, 242, 242);
    padding: 10px 0;
    margin: 5px;
    text-align: center;
  }
}

.date-con {
  display: flex;
  flex-wrap: wrap;
  .date {
    width: 14.285%;
    height: 100px;
    text-align: center;
    padding: 10px 0;
    border: 5px solid white;
    
    // opacity: 0;
  }
  .thisMonth {
    background-color: blue;
  }
  .Holidays {
    background-color: rgb(248, 172, 89);

    .date-day-show {
      font-size: 30px;
      padding-top: 0px;
      color: white;
    }
  }
  .isStatutoryHolidays {
    background-color: rgb(237, 85, 101);

    .date-day-show {
      font-size: 30px;
      padding-top: 0px;
      color: white;
    }
  }
  .isWorkingDay {
    background-color: rgb(35, 198, 200);

    .date-day-show {
      font-size: 30px;
      padding-top: 0px;
      color: white;
    }
  }
  .otherMonthDay {
    background: #f2f2f2;

    .date-day-show {
      font-size: 20px;
      padding-top: 10px;
      color: #d2d2d2;
    }
  }

  .isToday {
    background-color: red;
    font-weight: 700;
  }
}
.tip-con {
  margin-top: 51px;
  .tip {
    margin-top: 21px;
    width: 100%;
  }
}
</style>