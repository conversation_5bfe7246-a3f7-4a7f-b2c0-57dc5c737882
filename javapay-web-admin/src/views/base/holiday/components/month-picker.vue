<template>
  <!-- 月份选择器 -->
  <div class="container">
    <!-- 初始化数据 -->
    <a-button class="left-button" @click="initYearDateClick()">
      <template #icon>
        <plus-outlined />
      </template>
      <span>初始化日期数据</span>
    </a-button>
    <!-- 中间的时间和左右箭头 -->
    <div class="month-con center">
      <a-space>
        <a-button @click="reduceMonth()">
          <template #icon>
            <left-outlined />
          </template>
        </a-button>
        <span class="month">{{ time.year }}年{{ time.month + 1 > 9 ? time.month + 1 : '0' + (time.month + 1) }}月</span>
        <a-button @click="addMonth()">
          <template #icon>
            <right-outlined />
          </template>
        </a-button>
      </a-space>
    </div>
    <!-- 选择年份 -->
    <a-select ref="select" v-model:value="time.year" style="width: 100px" @change="handleChange">
      <a-select-option v-for="item in yearsArr" :key="item.id" :value="item.year">{{ item.year }}</a-select-option>
    </a-select>
  </div>
</template>
  
<script>
import { HolidayApi } from '@/api/base/HolidayApi';

export default {
  name: 'MonthPicker',
  emits: ['changeMonth', 'editInitHoliday'],
  data() {
    return {
      //初始化的时间
      time: {
        year: new Date().getFullYear(),
        month: new Date().getMonth()
      },
      //可选择年数组，目前为2023开始的五年
      yearsArr: []
    };
  },
  mounted() {
    //创建5年
    for (let i = 0; i < 5; i++) {
      const yearDic = {
        id: i,
        year: 2023 + i + ''
      };
      this.yearsArr.push(yearDic);
    }
  },
  methods: {
    async reduceMonth() {
      if (this.time.month > 0) {
        this.time.month = this.time.month - 1;
      } else {
        this.time.month = 11;
        this.time.year = this.time.year - 1;
      }
      //月份变化之后，先请求数据，然后再传送给主页面，进行时间数据的刷新
      const monthData = await HolidayApi.getHolidayRecord({ year: this.time.year, month: this.time.month + 1, status: 1 });
      this.$emit('changeMonth', { showTime: this.time, showMonthData: monthData });
    },
    async addMonth() {
      if (this.time.month < 11) {
        this.time.month = this.time.month + 1;
      } else {
        this.time.month = 0;
        this.time.year = this.time.year + 1;
      }
      //月份变化之后，先请求数据，然后再传送给主页面，进行时间数据的刷新
      const monthData = await HolidayApi.getHolidayRecord({ year: this.time.year, month: this.time.month + 1, status: 1 });
      this.$emit('changeMonth', { showTime: this.time, showMonthData: monthData });
    },
    //点击初始化数据
    initYearDateClick() {
      this.$emit('editInitHoliday');
    },
    //年份改变了触发的事件
    async handleChange() {
      //年份变化之后，先请求数据，然后再传送给主页面，进行时间数据的刷新
      const monthData = await HolidayApi.getHolidayRecord({ year: this.time.year, month: this.time.month + 1, status: 1 });
      this.$emit('changeMonth', { showTime: this.time, showMonthData: monthData });
    }
  }
};
</script>
<style lang="less" scoped>
.container {
  display: flex;
  justify-content: space-between;
}
.month-con {
  font-weight: 700;
  font-size: 18px;
  margin: auto;
  .month {
    margin: 0 10px;
  }
}

.left-button {
  float: left;
  width: 200px;
  font-weight: 700;
}

.center {
  position: absolute;
  left: 50%; /* 元素距离父元素左边的距离为 50% */
  transform: translateX(-50%); /* 将div向左平移自身宽度的1/2(即对应的50%) */
}
</style>
  