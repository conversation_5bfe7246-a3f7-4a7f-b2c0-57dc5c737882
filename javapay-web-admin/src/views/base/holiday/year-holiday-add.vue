<template>
  <a-modal
    :width="500"
    :visible="visible"
    title="节假日数据新增"
    placeholder="请选择"
    :mask-closable="false"
    :confirm-loading="loading"
    :bodyStyle="{ paddingBottom: '40px' }"
    @update:visible="updateVisible"
    @ok="submit"
  >
    <div class="center">
      <a-select ref="select" v-model:value="selectYear" style="width: 200px">
        <a-select-option v-for="item in yearArr" :key="item.id" :value="item.year">{{ item.year }}</a-select-option>
      </a-select>
    </div>
  </a-modal>
</template>

<script>
import { HolidayApi } from '@/api/base/HolidayApi';
import { message } from 'ant-design-vue';

export default {
  name: 'YearHolidayAdd',
  props: {
    visible: Boolean
  },
  emits: ['update:visible', 'done'],
  data() {
    return {
        //选择的年
        selectYear: new Date().getFullYear(),
        //年数组，从2023开始，有五年
        yearArr: [],
        loading: false,
    };
  },
  mounted() {
    //创建五年
    for (let i = 0; i < 5; i++){
        let yearDic = {
            id: i,
            year: (2023 + i) + '',
        }
        this.yearArr.push(yearDic);
    }
},
  methods: {
    async submit() {
        // 修改加载框为正在加载
        this.loading = true;
        //初始化年份
        HolidayApi.createHoliday({ year: this.selectYear })
        .then(res => {
            this.loading = false;
            //提示添加成功
            message.success(res.message);
            this.updateVisible(false);
            //完成之后进行回调，让父级页面刷新数据
            this.$emit('done');
        })
        .catch(() => {
            this.loading = false;
        })

    },
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>

<style scoped>
.center {
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>