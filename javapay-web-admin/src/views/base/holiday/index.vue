<template>
  <div class="ele-body">
    <a-card>
      <!-- 操作栏 -->
      <a-row type="flex" justify="space-around">
        <a-col :span="24">
          <!-- 月份选择器 -->
          <Monthpicker @changeMonth="changeMonth" @editInitHoliday="editInitHoliday" />
        </a-col>
      </a-row>
      <!-- 日历栏 -->
      <a-row class="calendar-con">
        <Calendar :time="time" :showMonthData="showMonthData" @dateClickMethod="dateDayClick" />
      </a-row>
    </a-card>

    <!-- 初始化年 -->
    <YearHolidayAdd v-model:visible="showInit" @done="reload" v-if="showInit" />
    <!-- 编辑操作 -->
    <YearHolidayEdit v-model:visible="showEdit" :showDataValue="dataValue" @done="reload" v-if="showEdit" />
  </div>
</template>
  
  <script>
// 月份选择器
import Monthpicker from './components/month-picker.vue';
// 日历组件
import Calendar from './components/calendar.vue';
import { HolidayApi } from '@/api/base/HolidayApi';
import YearHolidayAdd from './year-holiday-add.vue';
import YearHolidayEdit from './year-holiday-edit.vue';

export default {
  name: 'Holiday',
  components: {
    Monthpicker,
    Calendar,
    YearHolidayAdd,
    YearHolidayEdit
  },
  data() {
    return {
      time: {
        year: new Date().getFullYear(),
        month: new Date().getMonth()
      },
      showMonthData: null,
      //是否展示初始化日期数据页面
      showInit: false,
      showEdit: false,
      //传递给编辑页面的数据
      dataValue: {},
    };
  },
  async created() {
    //进来加载当月相关日期的数据,显示当前页面的
    const monthData = await HolidayApi.getHolidayRecord({ year: this.time.year, month: this.time.month + 1, status: 1 });
    this.showMonthData = monthData;
  },
  methods: {
    // 修改月份
    changeMonth(param) {
      this.time = param.showTime;
      this.showMonthData = param.showMonthData;
    },
    // 对年份进行初始化
    editInitHoliday() {
      this.showInit = true;
    },

    //显示日期编辑
    dateDayClick(value) {
      //只有点击本页面的月份才能更改
      if (this.time.month === value.month) {
        this.dataValue = value;
        this.showEdit = true;
      }
    },

    //重新加载日历
    async reload() {
      //进来加载当月相关日期的数据
      const monthData = await HolidayApi.getHolidayRecord({ year: this.time.year, month: this.time.month + 1, status: 1 });
      this.showMonthData = monthData;
    }
  }
};
</script>
<style lang="less" scoped>
.month-con {
  font-weight: 700;
  font-size: 18px;
  .month {
    margin: 0 10px;
  }
}
.calendar-con {
  margin-top: 20px;
}
</style>