<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="联行号">
              <a-input v-model:value.trim="where.bankChannelNo" placeholder="请输入联行号" allow-clear />
            </a-form-item>
            <a-form-item label="银行名称">
              <a-input v-model:value.trim="where.bankName" placeholder="请输入银行名称" allow-clear />
            </a-form-item>
            <a-form-item label="总行编码">
              <a-input v-model:value.trim="where.typeCode" placeholder="请输入总行编码" allow-clear />
            </a-form-item>
            <a-form-item label="清算行行号">
              <a-input v-model:value.trim="where.clearChannelNo" placeholder="请输入清算行行号" allow-clear />
            </a-form-item>
            <a-form-item label="有效状态">
              <a-select v-model:value="where.status" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">有效</a-select-option>
                <a-select-option :value="0">无效</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table
          ref="table"
          row-key="id"
          :datasource="datasource"
          :columns="columns"
          :where="where"
          v-model:selection="selection"
          :scroll="{ x: 'max-content' }"
        >
          <!-- table上边工具栏 -->
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
              <a-button danger @click="removeBatch">
                <template #icon>
                  <delete-outlined />
                </template>
                <span>删除</span>
              </a-button>
            </a-space>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-switch :checked="record.status === 1" @change="checked => editState(checked, record)" />
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
                <a-divider type="vertical" />
                <a @click="handleEdit(record)">修改</a>
                <a-divider type="vertical" />
                <a-popconfirm title="确定要删除吗？" @confirm="remove(record)">
                  <a class="ele-text-danger">删除</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 编辑 -->
    <BankInfoEdit v-model:visible="showEdit" :data="current" @done="reload" />

    <!-- 详情 -->
    <BankInfoDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { message, Modal } from 'ant-design-vue';
import { createVNode } from 'vue';
import { BankInfoApi } from '@/api/base/BankInfoApi';
import BankInfoDetail from './bank-info-detail.vue';
import { PlusOutlined, DeleteOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue';
import BankInfoEdit from './bank-info-edit.vue';

export default {
  name: 'BankInfo',
  components: {
    BankInfoDetail,
    BankInfoEdit,
    PlusOutlined,
    DeleteOutlined
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '联行号',
          dataIndex: 'bankChannelNo',
          align: 'center'
        },
        {
          title: '银行名称',
          dataIndex: 'bankName'
        },
        {
          title: '清算行行号',
          dataIndex: 'clearChannelNo',
          align: 'center'
        },
        {
          title: '总行编码',
          dataIndex: 'typeCode',
          align: 'center'
        },
        {
          title: '省份',
          dataIndex: 'provinceName',
          width: 120,
          align: 'center'
        },
        {
          title: '省份编码',
          dataIndex: 'provinceCode',
          width: 120,
          align: 'center'
        },
        {
          title: '城市',
          dataIndex: 'cityName',
          width: 120,
          align: 'center'
        },
        {
          title: '城市编码',
          dataIndex: 'cityCode',
          width: 120,
          align: 'center'
        },
        {
          title: '区县',
          dataIndex: 'areaName',
          width: 120,
          align: 'center'
        },
        {
          title: '区县编码',
          dataIndex: 'areaCode',
          width: 120,
          align: 'center'
        },
        {
          title: '有效状态',
          dataIndex: 'status',
          key: 'status',
          width: 120,
          align: 'center'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
        },
        {
          title: '修改人',
          dataIndex: 'lastModifyUserId'
        },
        {
          title: '修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          width: 180,
          align: 'center',
          fixed: 'right'
        }
      ],
      // 表格搜索条件
      where: {},
      selection: [],
      current: null,
      showDetail: false,
      showEdit: false
    };
  },
  methods: {
    reload() {
      this.selection = [];
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.selection = [];
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    handleEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    async editState(checked, row) {
      const id = row.id;
      const status = checked ? 1 : 0;
      const result = await BankInfoApi.changeStatus({ id, status });
      message.success(result.message);
      row.status = status;
    },

    async remove(row) {
      const result = await BankInfoApi.delete({ id: row.id });
      message.success(result.message);
      this.reload();
    },

    removeBatch() {
      if (!this.selection.length) {
        message.error('请至少选择一条数据');
        return;
      }
      Modal.confirm({
        title: '提示',
        content: '确定要删除选中的数据吗?',
        icon: createVNode(ExclamationCircleOutlined),
        maskClosable: true,
        onOk: async () => {
          let params = this.selection.map(d => d.id);
          const result = await BankInfoApi.batchDelete({ ids: params });
          message.success(result.message);
          this.reload();
        }
      });
    },

    datasource({ page, limit, where, orders }) {
      return BankInfoApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
