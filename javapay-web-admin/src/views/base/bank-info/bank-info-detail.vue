<template>
  <a-modal
    :width="750"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    :footer="null"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="联行号">{{ form.bankChannelNo }}</a-descriptions-item>
      <a-descriptions-item label="银行名称">{{ form.bankName }}</a-descriptions-item>
      <a-descriptions-item label="清算行行号">{{ form.clearChannelNo }}</a-descriptions-item>
      <a-descriptions-item label="总行编码">{{ form.typeCode }}</a-descriptions-item>
      <a-descriptions-item label="省份">{{ form.provinceName }}</a-descriptions-item>
      <a-descriptions-item label="省份编码">{{ form.provinceCode }}</a-descriptions-item>
      <a-descriptions-item label="城市">{{ form.cityName }}</a-descriptions-item>
      <a-descriptions-item label="城市编码">{{ form.cityCode }}</a-descriptions-item>
      <a-descriptions-item label="区县">{{ form.areaName }}</a-descriptions-item>
      <a-descriptions-item label="区县编码">{{ form.areaCode }}</a-descriptions-item>
      <a-descriptions-item label="状态">
        <a-tag v-if="form.status === 1" color="success">有效</a-tag>
        <a-tag v-else color="error">无效</a-tag>
      </a-descriptions-item>
    </a-descriptions>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

export default {
  name: 'BankInfoDetail',
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
