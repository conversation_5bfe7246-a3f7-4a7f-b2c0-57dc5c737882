<template>
  <a-modal
    :width="680"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新增'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    :after-close="() => cascader_key++"
    @update:visible="updateVisible"
    @cancel="cancel"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 5 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 19 }, sm: { span: 24 } }"
    >
      <a-form-item label="联行号" name="bankChannelNo">
        <a-input v-model:value="form.bankChannelNo" placeholder="3位银行代码+4位城市代码+4位银行编号+1位校验位" allow-clear />
      </a-form-item>
      <a-form-item label="银行名称" name="bankName">
        <a-input v-model:value="form.bankName" placeholder="请输入银行名称" allow-clear />
      </a-form-item>
      <a-form-item label="清算行号" name="clearChannelNo">
        <a-input v-model:value="form.clearChannelNo" placeholder="请输入清算行行号" allow-clear />
      </a-form-item>
      <a-form-item label="总行编码" name="typeCode">
        <a-input v-model:value="form.typeCode" placeholder="请输入总行编码" allow-clear />
      </a-form-item>
      <a-form-item label="地区" name="areaCode">
        <a-cascader
          :class="{ 'edit-cascader': isUpdate }"
          :key="cascader_key"
          :options="regionsData"
          :load-data="loadAreaData"
          :placeholder="cascaderPlaceholder"
          :allow-clear="false"
          @change="selectedAreaValue"
        />
      </a-form-item>
      <a-form-item label="状态" name="status">
        <a-radio-group v-model:value="form.status" name="status">
          <a-radio :value="1">有效</a-radio>
          <a-radio :value="0">无效</a-radio>
        </a-radio-group>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { BankInfoApi } from '@/api/base/BankInfoApi';
import { AreaApi } from '@/api/base/AreaApi';

function formDefaults() {
  return {
    status: 1
  };
}

export default {
  name: 'BankInfoEdit',
  props: {
    visible: Boolean,
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: Object.assign({}, this.data),
      // 表单验证规则
      rules: {
        bankChannelNo: [{ required: true, message: '请输入联行号' }],
        bankName: [{ required: true, message: '请输入银行名称' }],
        clearChannelNo: [{ required: true, message: '请输入清算行行号' }],
        typeCode: [{ required: true, message: '请输入总行编码' }],
        areaCode: [{ required: true, message: '请选择地区' }],
        status: [{ required: true, message: '请选择状态' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false,
      regionsData: [],
      cascader_key: 1,
      cascaderPlaceholder: '请选择地区'
    };
  },
  watch: {
    data() {
      if (this.data) {
        this.form = Object.assign({}, this.data);
        this.isUpdate = true;
        this.cascaderPlaceholder = [this.form.provinceName, this.form.cityName, this.form.areaName].join('/');
      } else {
        this.form = formDefaults();
        this.cascaderPlaceholder = '请选择地区';
        this.isUpdate = false;
      }
      if (this.$refs.form) {
        this.$refs.form.clearValidate();
      }

      this.loadAreaData();
    }
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      // 执行编辑或修改方法
      if (this.isUpdate) {
        result = BankInfoApi.edit(this.form);
      } else {
        result = BankInfoApi.add(this.form);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 如果是新增，则form表单置空
          if (!this.isUpdate) {
            this.form = formDefaults();
          }

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    cancel() {
      this.form = Object.assign(formDefaults(), this.data);
      this.$refs.form.clearValidate();
    },

    async loadAreaData(selectedOptions) {
      const targetOption = selectedOptions ? selectedOptions[selectedOptions.length - 1] : { level: 1, value: '' };
      const { level, value } = targetOption;

      const data = await AreaApi.list({ level: level + 1, status: 1, parentCode: value });
      const filterData = data.map(d => {
        return { label: d.areaName, value: d.areaCode, isLeaf: level > 2, level: d.level };
      });

      if (level === 1) {
        this.regionsData = filterData;
      } else {
        targetOption.children = filterData;
      }
    },

    selectedAreaValue(value) {
      [this.form.provinceCode, this.form.cityCode, this.form.areaCode] = value || [];
    },
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
<style scoped>
::v-deep(.edit-cascader) .ant-select-selection-placeholder {
  color: var(--text-color);
}
</style>
