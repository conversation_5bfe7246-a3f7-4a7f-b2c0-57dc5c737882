<template>
  <a-modal
    :width="600"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新建'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @cancel="cancel"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules">
      <a-form-item label="职业级别" name="professionLevel">
        <a-select v-model:value="form.professionLevel" placeholder="请选择职业级别" style="width: 100%">
          <a-select-option :value="1">大类</a-select-option>
          <a-select-option :value="2">小类</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item v-if="form.professionLevel == 2" label="父类职业编码" name="parentCode">
        <a-select v-model:value="form.parentCode" placeholder="请选择父类职业编码" style="width: 100%" allow-clear>
          <a-select-option v-for="({ professionCode, professionName }, key) in parentProfessionDataList" :key="key" :value="professionCode">{{
            professionName
          }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="职业名称" name="professionName">
        <a-input v-model:value.trim="form.professionName" placeholder="请输入职业编码" allow-clear />
      </a-form-item>
      <a-form-item label="职业编码" name="professionCode">
        <a-input v-model:value.trim="form.professionCode" placeholder="请输入职业编码" allow-clear />
      </a-form-item>
      <a-form-item label="有效状态" name="status">
        <a-select v-model:value="form.status" placeholder="请选择有效状态" style="width: 100%">
          <a-select-option :value="0">无效</a-select-option>
          <a-select-option :value="1">有效</a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { CareerManageApi } from '@/api/base/CareerManageApi';
import { message } from 'ant-design-vue';

function formDefaults() {
  return {
    status: 1,
  };
}

export default {
  name: 'CareerManageEdit',
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    showData: Object,
    //父类职业编码选择
    parentProfessionDataList: Array
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      //表单数据
      form: Object.assign({}, this.showData),
      // 表单验证规则
      rules: {
        professionLevel: [{ required: true, message: '请选择职业级别' }],
        parentData: [{ required: true, message: '请选择父类职业编码' }],
        professionName: [{ required: true, message: '请输入职业名称' }],
        professionCode: [{ required: true, message: '请输入职业编码' }],
        status: [{ required: true, message: '请选择有效状态' }]
      },
      //提交状态
      loading: false,
      //是否是修改
      isUpdate: false
    };
  },
  watch: {
    showData() {
      if (this.showData) {
        //有数据，那么就是修改
        this.form = Object.assign({}, this.showData);
        this.isUpdate = true;
      } else {
        //没有，是新增
        this.form = formDefaults();
        this.isUpdate = false;
      }
      if (this.$refs.form) {
        this.$refs.form.clearValidate();
      }
    }
  },
  methods: {
    //保存和编辑
    async save() {
      //校验表单
      await this.$refs.form.validate();

      //修改加载框为正在加载
      this.loading = true;
      let result = null;
      if (this.form.professionLevel === 2) {
            const parentProfessionData = this.parentProfessionDataList.find(item => {
                return item.professionCode === this.form.parentCode;
            });
            this.form.parentName = parentProfessionData.professionName;
      }
      if (this.isUpdate) {
        //编辑
        result = CareerManageApi.edit(this.form);
      } else {
        //新增
        result = CareerManageApi.add(this.form);
      }
      result
        .then(res => {
          //移除加载框
          this.loading = false;
          //添加成功提示
          message.success(res.message);
          if (!this.isUpdate) {
            //新增的话就把表清空
            this.form = formDefaults();
          }
          //关闭弹框
          this.updateVisible(false);
          //触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    //取消
    cancel() {
      this.form = Object.assign(formDefaults(), this.showData);
      this.$refs.form.clearValidate();
    },

    //更新状态
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>

<style>
</style>