<template>
  <div class="ele-body">
    <!-- 搜索框内容 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="职业编码">
              <a-input v-model:value.trim="where.professionCode" placeholder="请输入职业编码" allow-clear />
            </a-form-item>

            <a-form-item label="职业名称">
              <a-input v-model:value.trim="where.professionName" placeholder="请输入职业名称" allow-clear />
            </a-form-item>

            <a-form-item label="父类职业编码">
              <a-input v-model:value.trim="where.parentCode" placeholder="请输入父类职业编码" allow-clear />
            </a-form-item>

            <a-form-item label="父类职业名称">
              <a-input v-model:value.trim="where.parentName" placeholder="请输入父类职业编码" allow-clear />
            </a-form-item>

            <a-form-item label="职业级别">
              <a-select v-model:value="where.professionLevel" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">大类</a-select-option>
                <a-select-option :value="2">小类</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="有效状态">
              <a-select v-model:value="where.status" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">无效</a-select-option>
                <a-select-option :value="1">有效</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格内容 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- table上边工具栏 -->
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="openEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'professionLevel'">
              <a-tag v-if="record.professionLevel === 1" color="pink">大类</a-tag>
              <a-tag v-else-if="record.professionLevel === 2" color="blue">小类</a-tag>
            </template>

            <template v-else-if="column.key === 'status'">
              <a-switch :checked="record.status === 1" @change="checked => editStatus(checked, record)" />
            </template>

            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="openEdit(record)">修改</a>
                <a-divider type="vertical" />
                <a @click="handleShowDetail(record)">详情</a>
                <a-divider type="vertical" />
                <a-popconfirm title="确定要删除此记录吗?" @confirm="remove(record)">
                  <a class="ele-text-danger">删除</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 新增或者修改页面 -->
    <CareerManageEdit v-model:visible="showEdit" :showData="current" :parentProfessionDataList="parentProfessionDataList" @done="reload" />
    <!-- 详情页面 -->
    <CareerManageDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { CareerManageApi } from '@/api/base/CareerManageApi';
import { message } from 'ant-design-vue';
import CareerManageEdit from './career-manage-edit.vue';
import CareerManageDetail from './career-manage-detail.vue';

export default {
  name: 'CareerManage',
  components: {
    CareerManageEdit,
    CareerManageDetail,
  },
  data() {
    return {
      //表格查询条件
      where: {},
      //展示编辑页面传的参数
      current: null,
      //是否展示新增或者是编辑页面
      showEdit: false,
      //是否展示详情页面
      showDetail: false,
      //父级职业名称列表，用于添加修改时父级内容选择使用
      parentProfessionDataList:[],
      //表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center'
        },
        {
          title: '职业名称',
          dataIndex: 'professionName',
          align: 'center'
        },
        {
          title: '职业编码',
          dataIndex: 'professionCode',
          align: 'center'
        },
        {
          title: '父类职业名称',
          dataIndex: 'parentName',
          align: 'center'
        },
        {
          title: '父类职业编码',
          dataIndex: 'parentCode',
          align: 'center'
        },
        {
          title: '职业级别',
          dataIndex: 'professionLevel',
          key: 'professionLevel',
          align: 'center'
        },
        {
          title: '有效状态',
          dataIndex: 'status',
          key: 'status',
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 150,
          align: 'center'
        }
      ]
    };
  },
  async mounted() {
    const result = await CareerManageApi.getProfessionList({ professionLevel: 1, status: 1 });
    this.parentProfessionDataList = result.data;
  },
  methods: {
    //查询方法
    async reload() {
      console.log('请求啦');
      this.$refs.table.reload({ page: 1 });

      const result = await CareerManageApi.getProfessionList({ professionLevel: 1, status: 1 });
      this.parentProfessionDataList = result.data;
    },

    //重置
    reset() {
      this.where = {}; //清空查询条件
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    //新增或者修改
    openEdit(row) {
        this.current = row;
        this.showEdit = true;
    },

    //展示详情
    handleShowDetail(row) {
        this.current = row;
        this.showDetail = true;
    },

    //删除
    async remove(row) {
        const result = await CareerManageApi.delete({ id: row.id });
        message.success(result.message);
        this.reload();
    },

    //修改状态
    async editStatus(checked, row) {
      const id = row.id;
      const status = checked ? 1 : 0;
      const result = await CareerManageApi.changeStatus({ id, status });
      message.success(result.message);
      row.status = status;
      this.reload();
    },

    //获取数据方法
    datasource({ page, limit, where }) {
      return CareerManageApi.getCareerManagePages({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>

<style>
</style>