<template>
  <a-modal
    :width="600"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '30px' }"
    @update:visible="updateVisible"
    :footer="null"
  >
    <a-descriptions :column="1">
      <a-descriptions-item label="职业名称">{{ form.professionName }}</a-descriptions-item>
      <a-descriptions-item label="职业编码">{{ form.professionCode }}</a-descriptions-item>
      <a-descriptions-item v-if="form.professionLevel === 2" label="父类职业名称">{{ form.parentName }}</a-descriptions-item>
      <a-descriptions-item v-if="form.professionLevel === 2" label="父类职业编码">{{ form.parentCode }}</a-descriptions-item>
      <a-descriptions-item label="职业级别">
        <a-tag v-if="form.professionLevel === 1" color="pink">大类</a-tag>
        <a-tag v-else-if="form.professionLevel === 2" color="blue">小类</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="有效状态">
        <span v-if="form.status === 0" class="ele-text-danger">无效</span>
        <span v-else-if="form.status === 1" class="ele-text-success">有效</span>
      </a-descriptions-item>
    </a-descriptions>
  </a-modal>
</template>

<script>
export default {
  name: 'CareerManageDetail',
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  data() {
    return {
      form: {}
    };
  },
  watch: {
    //父组件值改变则重新赋值
    detail() {
      this.form = Object.assign({}, this.detail);
    }
  },
  methods: {
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>

<style>
</style>