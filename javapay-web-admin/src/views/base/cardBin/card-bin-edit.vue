<!-- 新增和编辑弹窗 -->
<template>
  <a-modal
    :width="1000"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改卡bin' : '新建卡bin'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @cancel="cancel"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 7 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 17 }, sm: { span: 24 } }"
    >
      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="卡bin标识:" name="cardBinValue">
            <a-input v-model:value="form.cardBinValue" placeholder="请输入卡bin标识" allow-clear />
          </a-form-item>
          <a-form-item label="卡类型" name="cardType">
            <a-select v-model:value="form.cardType" placeholder="请选择卡类型" style="width: 100%">
              <a-select-option :value="1">借记卡</a-select-option>
              <a-select-option :value="2">贷记卡</a-select-option>
              <a-select-option :value="3">准贷记卡</a-select-option>
              <a-select-option :value="4">预付费卡</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="卡bin长度:" name="cardBinLength">
            <a-input-number v-model:value="form.cardBinLength" placeholder="请输入卡bin长度" allow-clear style="width: 100%" />
          </a-form-item>
          <a-form-item label="发卡行代码:" name="bankCode">
            <a-input v-model:value="form.bankCode" placeholder="请输入发卡行代码" allow-clear />
          </a-form-item>
          <a-form-item label="银行行别代码:" name="typeCode">
            <a-input v-model:value="form.typeCode" placeholder="请输入银行行别代码" allow-clear />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="卡名称:" name="cardName">
            <a-input v-model:value="form.cardName" placeholder="请输入卡名称" allow-clear />
          </a-form-item>
          <a-form-item label="卡号前几位:" name="cardNoBefore">
            <a-input v-model:value="form.cardNoBefore" placeholder="请输入卡号前几位" allow-clear />
          </a-form-item>
          <a-form-item label="卡号长度:" name="cardNoLength">
            <a-input-number v-model:value="form.cardNoLength" placeholder="请输入卡号长度" allow-clear style="width: 100%" />
          </a-form-item>
          <a-form-item label="发卡行名称:" name="bankName">
            <a-input v-model:value="form.bankName" placeholder="请输入发卡行名称" allow-clear />
          </a-form-item>
          <a-form-item label="状态" name="status">
            <a-select v-model:value="form.status" placeholder="请选择有效状态" style="width: 100%">
              <a-select-option :value="1">有效</a-select-option>
              <a-select-option :value="0">无效</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script>
import { CardBinApi } from '@/api/base/CardBinApi';
import { message } from 'ant-design-vue';

function formDefaults() {
  return {
    status: 1
  };
}
export default {
  name: 'CardBinEdit',
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: Object.assign({}, this.data),
      // 表单验证规则
      rules: {
        typeCode: [{ required: true, message: '请输入银行行别代码' }],
        bankCode: [{ required: true, message: '请输入发卡行代码' }],
        bankName: [{ required: true, message: '请输入发卡行名称' }],
        cardName: [{ required: true, message: '请输入卡名称' }],
        cardType: [{ required: true, message: '请选择卡类型', type: 'number' }],
        cardNoLength: [{ required: true, message: '请输入卡号长度', type: 'number' }],
        cardNoBefore: [{ required: true, message: '请输入卡号前几位' }],
        cardBinValue: [{ required: true, message: '请输入卡bin标识' }],
        cardBinLength: [{ required: true, message: '请输入卡bin长度', type: 'number' }],
        status: [{ required: true, message: '请选择状态', type: 'number' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false
    };
  },
  watch: {
    data() {
      if (this.data) {
        this.form = Object.assign({}, this.data);
        this.isUpdate = true;
      } else {
        this.form = formDefaults();
        this.isUpdate = false;
      }
      if (this.$refs.form) {
        this.$refs.form.clearValidate();
      }
    }
  },
  methods: {
    /**
     * 保存和编辑
     *
     * <AUTHOR>
     * @date 2022/11/03 17:53
     */
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      // 执行编辑或修改
      if (this.isUpdate) {
        result = CardBinApi.edit(this.form);
      } else {
        result = CardBinApi.add(this.form);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 如果是新增，则form表单置空
          if (!this.isUpdate) {
            this.form = formDefaults();
          }

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    cancel() {
      this.form = Object.assign(formDefaults(), this.data);
      this.$refs.form.clearValidate();
    },

    /**
     * 更新编辑界面的弹框是否显示
     *
     * @param value true-显示，false-隐藏
     * <AUTHOR>
     * @date 2022/11/03 17:53
     */
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
