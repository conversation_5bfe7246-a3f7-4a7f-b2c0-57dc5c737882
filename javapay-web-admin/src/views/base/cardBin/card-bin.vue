<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="发卡行名称">
              <a-input v-model:value.trim="where.bankName" placeholder="请输入发卡行名称" allow-clear />
            </a-form-item>
            <a-form-item label="卡bin标识">
              <a-input v-model:value.trim="where.cardBinValue" placeholder="请输入卡bin标识" allow-clear />
            </a-form-item>
            <a-form-item label="卡号前几位">
              <a-input v-model:value.trim="where.cardNoBefore" placeholder="请输入卡号前几位" allow-clear />
            </a-form-item>
            <a-form-item label="卡号长度">
              <a-input v-model:value.trim="where.cardNoLength" placeholder="请输入卡号长度" allow-clear />
            </a-form-item>
            <a-form-item label="卡类型">
              <a-select v-model:value="where.cardType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">借记卡</a-select-option>
                <a-select-option :value="2">贷记卡</a-select-option>
                <a-select-option :value="3">准贷记卡</a-select-option>
                <a-select-option :value="4">预付费卡</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="状态">
              <a-select v-model:value="where.status" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">有效</a-select-option>
                <a-select-option :value="0">无效</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table
          ref="table"
          row-key="id"
          :datasource="datasource"
          :columns="columns"
          :where="where"
          v-model:selection="selection"
          :scroll="{ x: 'max-content' }"
        >
          <!-- table上边工具栏 -->
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="openEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
              <a-button danger @click="removeBatch">
                <template #icon>
                  <delete-outlined />
                </template>
                <span>删除</span>
              </a-button>
            </a-space>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'cardType'">
              <a-tag v-if="record.cardType === 1" color="pink">借记卡</a-tag>
              <a-tag v-if="record.cardType === 2" color="blue">贷记卡</a-tag>
              <a-tag v-if="record.cardType === 3" color="cyan">准贷记卡</a-tag>
              <a-tag v-if="record.cardType === 4" color="purple">预付费卡</a-tag>
            </template>

            <template v-else-if="column.key === 'status'">
              <a-switch :checked="record.status === 1" @change="checked => editState(checked, record)" />
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="openEdit(record)">修改</a>
                <a-divider type="vertical" />
                <a-popconfirm title="确定要删除此记录吗？" @confirm="remove(record)">
                  <a class="ele-text-danger">删除</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 编辑弹窗 -->
    <CardBinEdit v-model:visible="showEdit" :data="current" @done="reload" />
  </div>
</template>

<script>
import { createVNode } from 'vue';
import { PlusOutlined, DeleteOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { message, Modal } from 'ant-design-vue';
import { CardBinApi } from '@/api/base/CardBinApi';
import CardBinEdit from './card-bin-edit.vue';

export default {
  name: 'CardBin',
  components: {
    PlusOutlined,
    DeleteOutlined,
    CardBinEdit
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '卡bin标识',
          dataIndex: 'cardBinValue',
          align: 'center'
        },
        {
          title: '卡bin长度',
          dataIndex: 'cardBinLength',
          align: 'center'
        },
        {
          title: '卡名称',
          dataIndex: 'cardName'
        },
        {
          title: '卡类型',
          key: 'cardType',
          dataIndex: 'cardType',
          width: 160,
          align: 'center'
        },
        {
          title: '卡号长度',
          dataIndex: 'cardNoLength',
          align: 'center'
        },
        {
          title: '卡号前几位',
          dataIndex: 'cardNoBefore',
          align: 'center'
        },
        {
          title: '发卡行代码',
          dataIndex: 'bankCode',
          align: 'center'
        },
        {
          title: '发卡行名称',
          dataIndex: 'bankName'
        },
        {
          title: '银行行别代码',
          dataIndex: 'typeCode',
          align: 'center'
        },
        {
          title: '状态',
          key: 'status',
          dataIndex: 'status',
          width: 120,
          align: 'center'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime',
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 120,
          align: 'center'
        }
      ],
      // 表格搜索条件
      where: {},
      // 表格选中数据
      selection: [],
      // 当前编辑数据
      current: null,
      // 是否显示编辑弹窗
      showEdit: false
    };
  },
  methods: {
    /**
     * 批量删除
     */
    removeBatch() {
      if (!this.selection.length) {
        message.error('请至少选择一条数据');
        return;
      }
      Modal.confirm({
        title: '提示',
        content: '确定要删除选中的卡BIN吗?',
        icon: createVNode(ExclamationCircleOutlined),
        maskClosable: true,
        onOk: async () => {
          let params = this.selection.map(d => d.id);
          const result = await CardBinApi.batchDelete({ ids: params });
          message.success(result.message);
          this.reload();
        }
      });
    },
    /**
     * 搜索按钮
     *
     * <AUTHOR>
     * @date 2022/11/03 17:53
     */
    reload() {
      this.selection = [];
      this.$refs.table.reload({ page: 1 });
    },

    /**
     * 重置搜索
     *
     * <AUTHOR>
     * @date 2022/11/03 17:53
     */
    reset() {
      this.where = {};
      this.selection = [];
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    /**
     * 删除
     *
     * <AUTHOR>
     * @date 2022/11/03 17:53
     */
    async remove(row) {
      const result = await CardBinApi.delete({ id: row.id });
      message.success(result.message);
      this.reload();
    },

    async editState(checked, row) {
      const id = row.id;
      const status = checked ? 1 : 0;
      const result = await CardBinApi.changeStatus({ id, status });
      message.success(result.message);
      row.status = status;
    },

    /**
     * 打开新增或编辑弹窗
     *
     * <AUTHOR>
     * @date 2022/11/03 17:53
     */
    openEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    /**
     * 获取表格数据
     *
     * <AUTHOR>
     * @date 2022/5/8 15:18
     */
    datasource({ page, limit, where, orders }) {
      return CardBinApi.getCardBinPages({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
