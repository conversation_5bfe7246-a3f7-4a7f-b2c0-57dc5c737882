<template>
  <a-modal
    :width="650"
    :visible="visible"
    :confirm-loading="loading"
    :title="`上传二维码图片 (${data.channelName})`"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" layout="vertical">
      <a-radio-group v-model:value="payMethodRadio" @change="changePayMethod">
        <a-radio-button v-for="(item, key) in payMethodOptions" :value="item.value" :key="key">{{ item.label }}</a-radio-button>
      </a-radio-group>

      <div style="margin: 16px 0">
        <a-button block type="dashed" @click="onEdit('', 'add')">
          <template #icon>
            <cloud-upload-outlined />
          </template>
          添加二维码图片
        </a-button>
      </div>

      <a-tabs v-model:activeKey="activeKey" type="editable-card" hide-add @edit="onEdit">
        <a-tab-pane
          v-for="(item, key) in form.authQrcodeDTOS.filter(a => a.payMethod === payMethodRadio)"
          :tab="`${payMethodRadio == 2 ? '微信' : '支付宝'}-${key + 1}`"
          :key="String(item.key)"
        >
          <a-form-item label="上传图片" :rules="[{ required: true }]">
            <a-upload
              v-model:file-list="item.fileDTO"
              list-type="picture-card"
              accept=".png, .jpg, .jpeg"
              :maxCount="1"
              :before-upload="file => beforeUpload(file, item)"
              @preview="() => handlePreview(item)"
            >
              <div v-if="item.fileDTO.length < 1">
                <plus-outlined />
                <div style="margin-top: 8px">选择图片</div>
              </div>
            </a-upload>

            <!-- 预览图片 -->
            <a-image
              :style="{ display: 'none' }"
              :src="previewImage"
              :preview="{ visible: previewVisible, onVisibleChange: setPreviewVisible }"
            />
          </a-form-item>

          <a-form-item label="规则定义" name="rules">
            <a-textarea v-model:value="item.rules" placeholder="定义一些规则..." auto-size />
          </a-form-item>
        </a-tab-pane>
      </a-tabs>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';

import { deepCopy } from '@/utils/util';
export default {
  props: {
    visible: Boolean,
    data: Object
  },
  emits: ['update:visible'],
  data() {
    return {
      payMethodOptions: [
        { label: '微信', value: 2 },
        { label: '支付宝', value: 3 }
      ],
      // 表单数据
      form: {
        id: this.data.id,
        authQrcodeDTOS: []
      },
      payMethodRadio: 2,
      checkedPayMethodList: [],
      activeKey: '',
      newTabIndex: 0,
      loading: false,
      previewImage: '',
      previewVisible: false
    };
  },
  created() {
    this.getDetail();
  },
  methods: {
    async getDetail() {
      const { chnAuthQrcodeDTOList } = await ChannelManageApi.queryAuthQrcodeInfo({ id: this.data.id });

      const list = chnAuthQrcodeDTOList || [];

      list.forEach((i, key) => {
        i.key = String(key);
        i.fileDTO = [
          {
            url: i.url,
            imageBase64: i.imageBase64
          }
        ];
      });

      this.form.authQrcodeDTOS = list;

      this.changePayMethod();
    },
    beforeUpload(file, item) {
      getBase64(file).then(data => {
        item.fileDTO[0].imgBase64 = data;
      });
      return false;
    },
    handlePreview({ fileDTO }) {
      const file = fileDTO[0];
      this.previewImage = file.imgBase64 || file.url;
      this.setPreviewVisible(true);
    },
    setPreviewVisible(visible) {
      this.previewVisible = visible;
    },
    async save() {
      if (!this.form.authQrcodeDTOS.length) {
        message.warn('请添加图片');
        return;
      }

      this.form.authQrcodeDTOS.forEach(a => {
        if (!a.fileDTO[0]) {
          const errorMsg = `请检查${a.payMethod == 2 ? '微信' : '支付宝'}图片是否全部上传`;
          message.error(errorMsg);
          throw new Error('图片均为必填');
        }
      });

      // 修改加载框为正在加载
      this.loading = true;

      const params = deepCopy(this.form);

      params.authQrcodeDTOS.forEach(a => {
        const file = a.fileDTO[0];
        a.fileDTO = {
          fileData: file.imgBase64 || 'data:image/png;base64,' + file.imageBase64,
          suffixType: file.type?.split('/')[1] || 'png'
        };
      });

      ChannelManageApi.uploadAuthQrcodeInfo(params)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示修改成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);
        })
        .catch(() => {
          this.loading = false;
        });
    },

    changePayMethod() {
      const item = this.form.authQrcodeDTOS.filter(a => a.payMethod === this.payMethodRadio)[0];
      if (item) {
        this.activeKey = item.key;
      }
    },

    changeCheckedChannel(value) {
      // 取差集 (A-B)∪(B-A), 即变化的项
      const a = value;
      const b = this.checkedPayMethodList;
      const diffValue = a.reduce((u, va) => (u.every(vu => vu != va) ? u.concat(va) : u.filter(vu => vu != va)), b);

      const isAdd = value.length > this.checkedPayMethodList.length;
      if (isAdd) {
        this.activeKey = diffValue + '';
      }

      this.checkedPayMethodList = value;
    },

    onEdit(targetKey, action) {
      if (action === 'add') {
        if (this.form.authQrcodeDTOS.filter(i => i.payMethod === this.payMethodRadio).length > 4) {
          message.warn('每个平台最多添加5张图片');
          return;
        }
        const key = --this.newTabIndex;
        this.activeKey = key + '';
        this.form.authQrcodeDTOS.push({
          rules: '',
          fileDTO: [],
          payMethod: this.payMethodRadio,
          key: this.activeKey
        });
      } else {
        this.form.authQrcodeDTOS = this.form.authQrcodeDTOS.filter(pane => pane.key !== targetKey);
      }
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
// 图片文件转base64
function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });
}
</script>
<style scoped></style>
