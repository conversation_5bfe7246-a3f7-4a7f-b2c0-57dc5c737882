<template>
  <a-modal
    :width="1200" :visible="visible" :confirm-loading="loading" :title="title"
    :body-style="{ paddingBottom: '8px' }" :mask-closable="false" @update:visible="updateVisible" @ok="save">
    <a-form
      ref="form" :model="form" :rules="rules" :label-col="{ md: { span: 10 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 14 }, sm: { span: 24 } }">
      <a-row>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="支付宝单笔支付限额" name="alipayOnceLimit">
            <a-input v-model:value="form.alipayOnceLimit" placeholder="请输入支付宝单笔支付限额" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="支付宝单日支付额度" name="alipayDayLimit">
            <a-input v-model:value="form.alipayDayLimit" placeholder="请输入支付宝单日支付额度" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="EPOS单笔支付限额" name="eposOnceLimit">
            <a-input v-model:value="form.eposOnceLimit" placeholder="请输入EPOS单笔支付限额" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="EPOS单日支付额度" name="eposDayLimit">
            <a-input v-model:value="form.eposDayLimit" placeholder="请输入EPOS单日支付额度" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="银联单笔支付限额" name="unionpayOnceLimit">
            <a-input v-model:value="form.unionpayOnceLimit" placeholder="请输入银联单笔支付限额" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="银联单日支付额度" name="unionpayDayLimit">
            <a-input v-model:value="form.unionpayDayLimit" placeholder="请输入银联单日支付额度" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="微信单笔支付限额" name="wechatOnceLimit">
            <a-input v-model:value="form.wechatOnceLimit" placeholder="请输入微信单笔支付限额" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="微信单日支付额度" name="wechatDayLimit">
            <a-input v-model:value="form.wechatDayLimit" placeholder="请输入微信单日支付额度" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="POS手机Pay贷记卡单笔支付限额" name="posNfcCreditOnceLimit">
            <a-input v-model:value="form.posNfcCreditOnceLimit" placeholder="请输入POS手机Pay贷记卡单笔支付限额" />
          </a-form-item>
          <a-form-item label="POS手机Pay借记卡单笔支付限额" name="posNfcDebitOnceLimit">
            <a-input v-model:value="form.posNfcDebitOnceLimit" placeholder="请输入POS手机Pay借记卡单笔支付限额" />
          </a-form-item>
          <a-form-item label="POS芯片贷记卡单笔支付限额" name="posIcCreditOnceLimit">
            <a-input v-model:value="form.posIcCreditOnceLimit" placeholder="请输入POS芯片贷记卡单笔支付限额" />
          </a-form-item>
          <a-form-item label="POS芯片借记卡单笔支付限额" name="posIcDebitOnceLimit">
            <a-input v-model:value="form.posIcDebitOnceLimit" placeholder="请输入POS芯片借记卡单笔支付限额" />
          </a-form-item>
          <a-form-item label="POS磁条贷记卡单笔支付限额" name="posCiCreditOnceLimit">
            <a-input v-model:value="form.posCiCreditOnceLimit" placeholder="请输入POS磁条贷记卡单笔支付限额" />
          </a-form-item>
          <a-form-item label="POS磁条借记卡单笔支付限额" name="posCiDebitOnceLimit">
            <a-input v-model:value="form.posCiDebitOnceLimit" placeholder="请输入POS磁条借记卡单笔支付限额" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="POS手机Pay贷记卡单日支付限额" name="posNfcCreditDayLimit">
            <a-input v-model:value="form.posNfcCreditDayLimit" placeholder="请输入POS手机Pay贷记卡单日支付限额" />
          </a-form-item>
          <a-form-item label="POS手机Pay借记卡单日支付限额" name="posNfcDebitDayLimit">
            <a-input v-model:value="form.posNfcDebitDayLimit" placeholder="请输入POS手机Pay借记卡单日支付限额" />
          </a-form-item>
          <a-form-item label="POS芯片贷记卡单日支付限额" name="posIcCreditDayLimit">
            <a-input v-model:value="form.posIcCreditDayLimit" placeholder="请输入POS芯片贷记卡单日支付限额" />
          </a-form-item>
          <a-form-item label="POS芯片借记卡单日支付限额" name="posIcDebitDayLimit">
            <a-input v-model:value="form.posIcDebitDayLimit" placeholder="请输入POS芯片借记卡单日支付限额" />
          </a-form-item>
          <a-form-item label="POS磁条贷记卡单日支付限额" name="posCiCreditDayLimit">
            <a-input v-model:value="form.posCiCreditDayLimit" placeholder="请输入POS磁条贷记卡单日支付限额" />
          </a-form-item>
          <a-form-item label="POS磁条借记卡单日支付限额" name="posCiDebitDayLimit">
            <a-input v-model:value="form.posCiDebitDayLimit" placeholder="请输入POS磁条借记卡单日支付限额" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      title: '',
      form: {},
      // 表单验证规则
      rules: {
        alipayDayLimit: [{ required: true, message: '请输入支付宝单日支付额度' }],
        alipayOnceLimit: [{ required: true, message: '请输入支付宝单笔支付限额' }],
        eposDayLimit: [{ required: true, message: '请输入EPOS单日支付额度' }],
        eposOnceLimit: [{ required: true, message: '请输入EPOS单笔支付限额' }],
        unionpayDayLimit: [{ required: true, message: '请输入银联单日支付额度' }],
        unionpayOnceLimit: [{ required: true, message: '请输入银联单笔支付限额' }],
        wechatDayLimit: [{ required: true, message: '请输入微信单日支付额度' }],
        wechatOnceLimit: [{ required: true, message: '请输入微信单笔支付限额' }],

        posNfcCreditOnceLimit: [{ required: true, message: '请输入单笔支付限额' }],
        posNfcDebitOnceLimit: [{ required: true, message: '请输入单笔支付限额' }],
        posIcCreditOnceLimit: [{ required: true, message: '请输入单笔支付限额' }],
        posIcDebitOnceLimit: [{ required: true, message: '请输入单笔支付限额' }],
        posCiCreditOnceLimit: [{ required: true, message: '请输入单笔支付限额' }],
        posCiDebitOnceLimit: [{ required: true, message: '请输入单笔支付限额' }],
        posNfcCreditDayLimit: [{ required: true, message: '请输入单日支付限额' }],
        posNfcDebitDayLimit: [{ required: true, message: '请输入单日支付限额' }],
        posIcCreditDayLimit: [{ required: true, message: '请输入单日支付限额' }],
        posIcDebitDayLimit: [{ required: true, message: '请输入单日支付限额' }],
        posCiCreditDayLimit: [{ required: true, message: '请输入单日支付限额' }],
        posCiDebitDayLimit: [{ required: true, message: '请输入单日支付限额' }],
      },
      // 提交状态
      loading: false
    };
  },
  created() {
    this.title = '交易限制编辑';
    this.getDetail();
    this.title += ` (${this.detail.channelName})`;
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let submitMethod = null;

      submitMethod = ChannelManageApi.editChannelTransLimit;

      this.form.channelCode = this.detail.channelCode;
      submitMethod(this.form)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    async getDetail() {
      const data = await ChannelManageApi.detailChannelTransLimit({ channelCode: this.detail.channelCode });
      this.form = data || {}
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
