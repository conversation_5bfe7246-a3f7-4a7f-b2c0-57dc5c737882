<template>
  <a-modal
    :width="900"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新增'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @cancel="cancel"
    @ok="save"
  >
    <!-- 上传logo图 -->
    <a-upload
      :file-list="form.chnIconBase64Str ? [{ url: form.chnIconBase64Str }] : []"
      :multiple="false"
      list-type="picture-card"
      :before-upload="beforeUpload"
      @remove="handleRemoveImg"
      @preview="handlePreview"
    >
      <div v-if="!form.chnIconBase64Str">
        <plus-outlined />
        <div style="margin-top: 8px">上传LOGO</div>
      </div>
    </a-upload>
    <a-image :style="{ display: 'none' }" :src="previewImage" :preview="{ visible: previewVisible, onVisibleChange: setPreviewVisible }" />

    <div style="height: 12px;"></div>

    <a-form ref="form" :model="form" :rules="rules" :label-col="{ style: { width: '100px' } }">
      <a-form-item label="通道编号" name="channelCode">
        <a-input
          style="width: 280px"
          v-model:value="form.channelCode"
          placeholder="请输入通道编号"
          :disabled="isChannelCodeInputDisabled"
          allow-clear
        />
      </a-form-item>

      <a-form-item label="通道名称" name="channelName">
        <a-input style="width: 280px" v-model:value="form.channelName" placeholder="请输入通道名称" allow-clear />
      </a-form-item>

      <a-form-item label="通道优先级" name="chnPriority">
        <a-input-number style="width: 280px" v-model:value="form.chnPriority" :min="0" :max="100" />
      </a-form-item>

      <a-form-item label="通道描述" name="chnDescription">
        <a-textarea
          style="width: 280px"
          v-model:value="form.chnDescription"
          :auto-size="{ minRows: 2, maxRows: 5 }"
          placeholder="请输入通道描述"
          allow-clear
        />
      </a-form-item>

      <a-form-item label="有效状态" name="validStatus">
        <a-radio-group v-model:value="form.validStatus">
          <a-radio :value="1">有效</a-radio>
          <a-radio :value="0">无效 </a-radio>
        </a-radio-group>
      </a-form-item>
    </a-form>

    <br />

    <div class="block-interval channel-bottom">
      <a-button type="primary" @click="addRow">添加</a-button>

      <a-form v-for="(item, index) in paramsArr" :key="index" :rules="paramsRules" :model="item" layout="inline" class="channel-margin">
        <a-row>
          <a-form-item label="参数名称" name="parKey">
            <a-input style="width: 280px" v-model:value.trim="item.parKey" placeholder="请输入参数名称" allow-clear />
          </a-form-item>
          <a-form-item label="参数值" name="parValue">
            <a-input style="width: 280px" v-model:value.trim="item.parValue" placeholder="请输入参数值" allow-clear />
          </a-form-item>
          <a-form-item class="ele-text-center">
            <a-button type="danger" @click="deleteRow(index)">删除</a-button>
          </a-form-item>
        </a-row>
      </a-form>
    </div>
  </a-modal>
</template>

<script>
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import { fileToData } from '@/utils/image-compressor-util';
import { message, Upload } from 'ant-design-vue';

function formDefaults() {
  return {
    chnPriority: 0,
    validStatus: 1
  };
}

export default {
  name: 'ChannelEdit',
  props: {
    visible: Boolean,
    showData: Object
  },
  emits: ['update:visible', 'done'],

  data() {
    return {
      //表单数据
      form: {},
      //是否是修改
      isUpdate: false,
      //提交状态
      loading: false,
      //通道编号是否可以编辑
      isChannelCodeInputDisabled: true,
      //接收键值对数组
      paramsArr: [],
      //表单规则
      rules: {
        channelCode: [{ required: true, message: '请输入通道编号' }],
        channelName: [{ required: true, message: '请输入通道名称' }],
        chnPriority: [{ required: true, message: '请输入通道优先级' }],
        chnDescription: [{ required: true, message: '请输入通道描述' }],
        validStatus: [{ required: true, message: '请选择有效状态' }]
      },

      //键值对表格规则
      paramsRules: {
        parKey: [{ required: true, message: '请输入参数名称' }],
        parValue: [{ required: true, message: '请输入参数值' }]
      },

      previewVisible: false,
      previewImage: ''
    };
  },

  watch: {
    showData() {
      //有就是编辑
      if (this.showData) {
        this.isUpdate = true;
        this.form = Object.assign({}, this.showData);
        //编辑不能修改
        this.isChannelCodeInputDisabled = true;
        //首先将数组初始化
        this.paramsArr = [];
        //将json串转换为obj对象
        const obj = JSON.parse(this.showData.channelParams);
        //将obj数据转换成[{parkey:'',parValue:''}]这种形式
        Object.keys(obj).forEach(item => {
          this.paramsArr.push({
            parKey: item,
            parValue: obj[item]
          });
        });

        this.form.chnIconBase64Str = this.showData.chnIcon;
      } else {
        //没有就是新增
        this.isUpdate = false;
        this.form = formDefaults();
        //新增可以修改
        this.isChannelCodeInputDisabled = false;
        //没有就将数组清空
        this.paramsArr = [];
      }
      if (this.$refs.form) {
        this.$refs.form.clearValidate();
      }
    }
  },

  methods: {
    handlePreview() {
      this.previewImage = this.form.chnIconBase64Str;
      this.setPreviewVisible(true);
    },
    setPreviewVisible(visible) {
      this.previewVisible = visible;
    },
    beforeUpload(file) {
      fileToData(file).then(base64Img => {
        this.form.chnIconBase64Str = base64Img;
      });
      return Upload.LIST_IGNORE;
    },
    handleRemoveImg() {
      this.form.chnIconBase64Str = '';
    },

    //传值更新父组件的值
    updateVisible(value) {
      this.$emit('update:visible', value);
    },
    addRow() {
      this.paramsArr.push({
        parKey: '',
        parValue: ''
      });
    },
    //删除键值对
    deleteRow(index) {
      this.paramsArr.splice(index, 1);
    },
    //取消事件
    cancel() {
      this.form = Object.assign({}, this.showData);
      this.$refs.form.clearValidate();
      this.paramsArr = [];
      //将json串转换为obj对象
      const obj = JSON.parse(this.showData.channelParams);
      //将obj数据转换成[{parkey:'',parValue:''}]这种形式
      Object.keys(obj).forEach(item => {
        this.paramsArr.push({
          parKey: item,
          parValue: obj[item]
        });
      });
      //关闭需要传值返回
      this.$emit('update:visible', false);
    },
    //新增或者保存
    async save() {
      if (!this.form.chnIconBase64Str) {
        message.warn('请上传LOGO');
        return;
      }

      await this.$refs.form.validate();
      this.loading = true;
      //创建新字典型数据用于接收 param数组值
      const paramdict = {};
      //将数组提取然后转换成json串
      this.paramsArr.forEach(item => {
        if (item.parKey && item.parKey !== 0 && item.parValue && item.parValue !== 0) {
          paramdict[item.parKey] = item.parValue;
        }
      });
      this.form.channelParams = paramdict;

      const id = this.form.id;
      const channelCode = this.form.channelCode;
      const channelParamsObj = this.form.channelParams;
      const channelName = this.form.channelName;
      const validStatus = this.form.validStatus;
      const chnPriority = this.form.chnPriority;
      const chnDescription = this.form.chnDescription;
      const chnIconBase64Str = this.form.chnIconBase64Str.startsWith('http') ? null : this.form.chnIconBase64Str;

      let result = null;
      if (this.isUpdate) {
        //更新事件
        result = ChannelManageApi.editChannel({
          id,
          channelCode,
          channelParamsObj,
          channelName,
          validStatus,
          chnPriority,
          chnDescription,
          chnIconBase64Str
        });
      } else {
        //新增事件
        result = ChannelManageApi.addChannel({
          channelCode,
          channelParamsObj,
          channelName,
          validStatus,
          chnPriority,
          chnDescription,
          chnIconBase64Str
        });
      }

      result
        .then(res => {
          this.loading = false;
          message.success(res.message);

          if (!this.isUpdate) {
            this.form = formDefaults();
          }
          //请求成功，删除键值对的数据和页面
          this.paramsArr = [];
          //关闭弹框，传值给父组件
          this.updateVisible(false);
          //操作完成，提示父组件刷新页面
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    }
  }
};
</script>

<style>
.channel-margin {
  margin-top: 20px;
}

.channel-bottom {
  margin-bottom: 50px;
}
</style>
