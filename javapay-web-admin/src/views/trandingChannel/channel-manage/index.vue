<template>
  <div class="ele-body">
    <!-- 搜索框内容 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="通道编码">
              <a-input v-model:value.trim="where.channelCode" placeholder="请输入通道编码" allow-clear />
            </a-form-item>

            <a-form-item label="通道名称">
              <a-input v-model:value.trim="where.channelName" placeholder="请输入通道名称" allow-clear />
            </a-form-item>

            <a-form-item label="有效状态">
              <a-select v-model:value="where.validStatus" style="width: 120px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">有效</a-select-option>
                <a-select-option :value="0">无效</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格内容 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- 表格上方的操作按钮 -->
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>

          <!-- 表体的操作 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'chnIcon'">
              <a-image :width="48" :src="record.chnIcon" alt="logo" />
            </template>

            <template v-if="column.key === 'validStatus'">
              <a-switch :checked="record.validStatus === 1" @change="checked => editValidStatus(checked, record)" />
            </template>
            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleEdit(record)">修改</a>
                <a-divider type="vertical" />
                <a @click="handleShowDetail(record)">详情</a>
                <a-divider type="vertical" />
                <a @click="handleUploadImage(record)">上传二维码图片</a>
                <a-divider type="vertical" />
                <a @click="handleEditTransLimit(record)">交易限制编辑</a>
                <a-divider type="vertical" />
                <a @click="handleTransParam(record)">交易参数</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 新增编辑 -->
    <ChannelEdit v-model:visible="showEdit" :showData="current" @done="reload" />

    <!-- 详情页面 -->
    <ChannelDetail v-model:visible="showDetail" :detail="current" />

    <!-- 上传图片 -->
    <UploadImage v-if="showUploadImage" v-model:visible="showUploadImage" :data="data" />

    <!-- 交易限制编辑 -->
    <EditTransLimit v-if="showTransLimit" v-model:visible="showTransLimit" :detail="current" />

    <!-- 交易参数信息 -->
    <TransParamsInfo v-if="showTransParamsInfo" v-model:visible="showTransParamsInfo" :detail="detail" />
  </div>
</template>

<script>
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import EditTransLimit from './EditTransLimit.vue';
import { message } from 'ant-design-vue';
import ChannelEdit from './channel-edit.vue';
import ChannelDetail from './channel-detail.vue';
import UploadImage from './upload-image.vue';
import TransParamsInfo from './TransParamsInfo.vue';

export default {
  name: 'ChannelManage',
  components: {
    ChannelEdit,
    ChannelDetail,
    UploadImage,
    EditTransLimit,
    TransParamsInfo
  },
  data() {
    return {
      //表格查询条件
      where: {},
      //展示编辑页面传的参数
      current: null,
      detail: null,
      //是否展示新增或者是编辑页面
      showEdit: false,
      //是否展示详情页面
      showDetail: false,
      showTransLimit: false,
      showUploadImage: false,
      showTransParamsInfo: false,
      data: null,
      //表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center'
        },
         {
          title: 'LOGO',
          dataIndex: 'chnIcon',
          key: 'chnIcon',
          align: 'center'
        },
        {
          title: '通道编码',
          dataIndex: 'channelCode',
          align: 'center'
        },
        {
          title: '通道名称',
          dataIndex: 'channelName',
          align: 'center'
        },
        {
          title: '通道优先级',
          dataIndex: 'chnPriority',
          align: 'center'
        },
        {
          title: '通道描述',
          dataIndex: 'chnDescription',
          width: 200
        },
        {
          title: '有效状态',
          dataIndex: 'validStatus',
          align: 'center',
          key: 'validStatus'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center'
        }
      ]
    };
  },
  methods: {
    async handleTransParam(row) {
      const data = await ChannelManageApi.channekTransInfoDetail({ channelCode: row.channelCode });
      this.detail = data;
      this.showTransParamsInfo = true;
    },
    handleEditTransLimit(row) {
      this.current = row;
      this.showTransLimit = true;
    },

    //查询方法
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    //重置
    reset() {
      this.where = {}; //清空查询条件
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    //新建或者编辑
    handleEdit(row) {
      this.showEdit = true;
      this.current = row;
    },

    //展示详情
    handleShowDetail(row) {
      this.showDetail = true;
      this.current = row;
    },

    handleUploadImage(row) {
      this.data = {
        id: row.id,
        channelName: row.channelName
      };
      this.showUploadImage = true;
    },

    //获取数据方法
    datasource({ page, limit, where }) {
      return ChannelManageApi.findChannelList({ ...where, pageNo: page, pageSize: limit });
    },

    //改变有效状态
    async editValidStatus(checked, row) {
      const channelName = row.channelName;
      const channelParamsObj = row.channelParamsRequest;
      const id = row.id;
      const validStatus = checked ? 1 : 0;
      const result = await ChannelManageApi.editChannel({
        channelName,
        validStatus,
        id,
        channelParamsObj
      });
      message.success(result.message);
      row.validStatus = validStatus;
    }
  }
};
</script>

<style></style>
