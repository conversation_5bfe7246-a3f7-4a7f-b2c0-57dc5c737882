<template>
  <a-modal
    :width="600"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '30px' }"
    @update:visible="updateVisivle"
    :footer="null"
  >
    <a-descriptions :column="1">
      <a-descriptions-item label="LOGO" :span="2">
        <a-image :width="58" :src="form.chnIcon" alt="logo" />
      </a-descriptions-item>
      <a-descriptions-item label="通道编码">{{ form.channelCode }}</a-descriptions-item>
      <a-descriptions-item label="通道名称">{{ form.channelName }}</a-descriptions-item>
      <a-descriptions-item label="通道优先级">{{ form.chnPriority }}</a-descriptions-item>
      <a-descriptions-item label="通道描述">{{ form.chnDescription }}</a-descriptions-item>
      <a-descriptions-item label="有效状态">
        <a-tag v-if="form.validStatus === 1" color="success">有效</a-tag>
        <a-tag v-else color="error">无效</a-tag>
      </a-descriptions-item>
    </a-descriptions>

    <div v-for="(value, key, index) in JSON.parse(form.channelParams)" :key="index">
      <a-descriptions :column="2">
        <a-descriptions-item label="  参数名称">{{ key }}</a-descriptions-item>
        <a-descriptions-item label="参数值">{{ value }}</a-descriptions-item>
      </a-descriptions>
    </div>
  </a-modal>
</template>

<script>
export default {
  name: 'ChannelDetail',
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  data() {
    return {
      form: {}
    };
  },
  watch: {
    //父组件值改变则重新赋值
    detail() {
      this.form = Object.assign({}, this.detail);
    }
  },
  methods: {
    updateVisivle(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>

<style></style>
