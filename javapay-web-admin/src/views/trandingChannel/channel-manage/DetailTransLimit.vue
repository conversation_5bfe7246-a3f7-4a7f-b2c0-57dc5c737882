<template>
  <a-modal
    :width="1000"
    :visible="visible"
    :maskClosable="false"
    :title="`详情 (${detail.channelName})`"
    :mask-closable="false"
    :body-style="{ paddingBottom: '30px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="支付宝单日支付额度">{{ form.alipayDayLimit }}</a-descriptions-item>
      <a-descriptions-item label="支付宝单笔支付限额">{{ form.alipayOnceLimit }}</a-descriptions-item>
      <a-descriptions-item label="EPOS单日支付额度">{{ form.eposDayLimit }}</a-descriptions-item>
      <a-descriptions-item label="EPOS单笔支付限额">{{ form.eposOnceLimit }}</a-descriptions-item>
      <a-descriptions-item label="银联单日支付额度">{{ form.unionpayDayLimit }}</a-descriptions-item>
      <a-descriptions-item label="银联单笔支付限额">{{ form.unionpayOnceLimit }}</a-descriptions-item>
      <a-descriptions-item label="微信单日支付额度">{{ form.wechatDayLimit }}</a-descriptions-item>
      <a-descriptions-item label="微信单笔支付限额">{{ form.wechatOnceLimit }}</a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  data() {
    return {
      form: {}
    };
  },
  created() {
    this.getDetail();
  },
  methods: {
    async getDetail() {
      const data = await ChannelManageApi.detailChannelTransLimit({ channelCode: this.detail.channelCode });
      this.form = data || {}
    },
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
