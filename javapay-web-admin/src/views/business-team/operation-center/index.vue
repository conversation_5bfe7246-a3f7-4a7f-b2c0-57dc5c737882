<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item v-if="!hasPurview('1')" label="账户标识">
              <a-input v-model:value.trim="where.accountUuid" placeholder="请输入账户标识" allow-clear />
            </a-form-item>
            <a-form-item label="运营中心名称">
              <a-input v-model:value.trim="where.branchName" placeholder="请输入运营中心名称" allow-clear />
            </a-form-item>
            <a-form-item label="运营中心编号">
              <a-input v-model:value.trim="where.branchNo" placeholder="请输入运营中心编号" allow-clear />
            </a-form-item>
            <a-form-item v-if="!hasPurview('1')" label="归属大区编号">
              <a-input v-model:value.trim="where.regionNo" placeholder="请输入归属大区编号" allow-clear />
            </a-form-item>
            <a-form-item label="费率政策">
              <a-select v-model:value="where.policyNo" placeholder="请选择" style="width: 200px" allow-clear>
                <a-select-option v-for="(item, key) in ratePolicyList" :value="item.policyNo" :key="key">
                  {{ item.policyDesc }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="联系人姓名">
              <a-input v-model:value.trim="where.contactsName" placeholder="请输入联系人姓名" allow-clear />
            </a-form-item>
            <a-form-item label="联系人号码">
              <a-input v-model:value.trim="where.contactsTel" placeholder="请输入联系人手机号码" allow-clear />
            </a-form-item>
            <a-form-item label="清算类型">
              <a-select v-model:value="where.remitType" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">平台清算</a-select-option>
                <a-select-option :value="2">自行提现</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="结算方式">
              <a-select v-model:value="where.settleMode" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">日结</a-select-option>
                <a-select-option :value="2">月结</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="分润出款方式">
              <a-select
                v-model:value="where.settleChannelWay"
                style="width: 200px"
                placeholder="请选择"
                allow-clear
                @change="where.settleChannelCode = null"
              >
                <!-- <a-select-option :value="3">展业平台</a-select-option> -->
                <a-select-option :value="1">平台出款</a-select-option>
                <!-- <a-select-option :value="0">交易通道</a-select-option> -->
              </a-select>
            </a-form-item>
            <a-form-item label="分润出款通道" v-if="[1, 2].includes(where.settleChannelWay)">
              <a-select
                v-model:value="where.settleChannelCode"
                style="width: 200px"
                placeholder="请选择"
                :options="subChannelNos"
                :fieldNames="{ label: 'channelName', value: 'channelNo' }"
                allow-clear
              />
            </a-form-item>
            <a-form-item label="分润出款通道" v-if="[0].includes(where.settleChannelWay)">
              <a-select v-model:value="where.settleChannelCode" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
                  {{ channelName }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="签约日期">
              <a-date-picker v-model:value="where.signDate" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table
          ref="table"
          row-key="id"
          :datasource="datasource"
          :columns="columns"
          :where="where"
          :scroll="{ x: 'max-content' }"
          v-model:selection="selection"
        >
          <!-- table上边的工具栏 -->
          <template #toolbar>
            <a-space wrap>
              <a-button v-purview="'1'" type="primary" @click="handleAdd">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>

              <a-button v-if="hasPurview(['1'])" @click="handleEditActityPolicy">
                <template #icon>
                  <edit-outlined />
                </template>
                <span>修改活动政策</span>
              </a-button>

              <a-button v-if="hasPurview('0')" @click="handleEditTaxPoint">
                <template #icon>
                  <edit-outlined />
                </template>
                <span>修改税点</span>
              </a-button>

              <a-button v-if="hasPurview('0')" @click="handleEditStatus">
                <template #icon>
                  <edit-outlined />
                </template>
                <span>修改状态</span>
              </a-button>

              <a-dropdown>
                <template #overlay>
                  <a-menu @click="handleEditSettleRemit">
                    <a-menu-item key="1">单个修改</a-menu-item>
                    <a-menu-item key="2">批量修改</a-menu-item>
                  </a-menu>
                </template>
                <a-button v-purview="'0'">
                  <template #icon>
                    <edit-outlined />
                  </template>
                  <span>修改结算周期&方式</span>
                  <DownOutlined />
                </a-button>
              </a-dropdown>

              <a-button v-if="hasPurview('0')" @click="handleEditAppBrandType">
                <template #icon>
                  <edit-outlined />
                </template>
                <span>修改APP品牌类型</span>
              </a-button>
            </a-space>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'remitType'">
              <a-tag v-if="record.remitType === 1" color="cyan">平台清算</a-tag>
              <a-tag v-else-if="record.remitType === 2" color="pink">自行提现</a-tag>
              <span v-else>--</span>
            </template>

            <template v-else-if="column.key === 'settleMode'">
              <a-tag v-if="record.settleMode === 1" color="cyan">日结</a-tag>
              <a-tag v-else-if="record.settleMode === 2" color="blue">月结</a-tag>
              <span v-else>--</span>
            </template>

            <template v-else-if="column.key === 'settleChannelWay'">
              <a-tag v-if="record.settleChannelWay === 3" color="cyan">展业平台</a-tag>
              <a-tag v-else-if="record.settleChannelWay === 1" color="blue">平台出款</a-tag>
              <a-tag v-else-if="record.settleChannelWay === 0" color="orange">交易通道</a-tag>
            </template>

            <!-- table右边操作按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record.id, record)">详情</a>
                <a-divider type="vertical" />
                <a @click="handleEdit(record.id)">修改</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <OperationCenterEdit
      v-if="showEdit"
      v-model:visible="showEdit"
      :data="current"
      :opt-type="operationType"
      :channelCodes="channelCodes"
      :ratePolicyList="ratePolicyList"
      :bankList="bankList"
      :serviceFeeList="serviceFeeList"
      :simFeeList="simFeeList"
      :activityCashbackTemplateList="activityCashbackTemplateList"
      @done="reload"
    />

    <SettleRemitEdit v-model:visible="showEditSettleRemit" :data="current" @done="reload" />

    <ActivityCashBackPolicyEdit
      v-if="showEditActivitPolicy"
      v-model:visible="showEditActivitPolicy"
      :channelCodes="channelCodes"
      :data="current"
      @done="reload"
    />

    <EditTaxPoint v-if="showEditTaxPoint" v-model:visible="showEditTaxPoint" :data="taxPointInfo" @done="reload" />

    <EditAppBrandType
      v-if="showEditAppBrandType"
      v-model:visible="showEditAppBrandType"
      :data="appBrandInfo"
      @done="reload"
      :app-brands="appBrands"
    />

    <OperationStatusEdit v-if="showEditStatus" v-model:visible="showEditStatus" :data="statusRow" @done="reload" />
  </div>
</template>

<script>
import { OperationCenterApi } from '@/api/businessTeam/operation-center/OperationCenterApi';
import OperationCenterEdit from './OperationCenterEdit.vue';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import { RatePolicyApi } from '@/api/businessTeam/rate-policy/RatePolicyApi';
import { BankCodeManageApi } from '@/api/base/BankCodeManageApi';
import { hasPurview } from '@/utils/permission';
import { message } from 'ant-design-vue';
import SettleRemitEdit from './SettleRemitEdit.vue';
import OperationStatusEdit from './OperationStatusEdit.vue';
import EditTaxPoint from '../_components/EditTaxPoint.vue';
import ActivityCashBackPolicyEdit from './ActivityCashBackPolicyEdit.vue';
import { ServiceFeeManageApi } from '@/api/businessTeam/activity-Config/ServiceFeeManageApi';
import { ActivityCashbackTemplateManageApi } from '@/api/businessTeam/activity-Config/ActivityCashbackTemplateManageApi';
import { SimFeeManageApi } from '@/api/businessTeam/activity-Config/SimFeeManageApi';
import EditAppBrandType from '../_components/EditAppBrandType.vue';
import { AppBrandApi } from '@/api/base/AppBrandApi';
import { RemitChannelApi } from '@/api/account/remit-channel/RemitChannelApi';

export default {
  name: 'OperationCenter',
  components: {
    OperationCenterEdit,
    SettleRemitEdit,
    ActivityCashBackPolicyEdit,
    EditTaxPoint,
    EditAppBrandType,
    OperationStatusEdit
  },
  data() {
    const isHideCol = hasPurview('1');
    return {
      where: {},
      showEdit: false,
      showEditSettleRemit: false,
      showEditActivitPolicy: false,
      operationType: null,
      current: null,
      channelCodes: [],
      ratePolicyList: [],
      bankList: [],
      selection: [],
      serviceFeeList: [],
      activityCashbackTemplateList: [],
      simFeeList: [],
      showEditTaxPoint: false,
      taxPointInfo: null,
      appBrandInfo: null,
      appBrands: [],
      subChannelNos: [],
      showEditAppBrandType: false,
      showEditStatus: false,
      statusRow: null,
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left',
          hideCol: isHideCol
        },
        {
          title: '运营中心编号',
          dataIndex: 'branchNo',
          align: 'center'
        },
        {
          title: '运营中心名称',
          dataIndex: 'branchName'
        },
        {
          title: '运营中心简称',
          dataIndex: 'branchSname'
        },
        {
          title: '归属大区编号',
          dataIndex: 'regionNo',
          align: 'center',
          hideCol: isHideCol
        },
        {
          title: '费率政策',
          dataIndex: 'policyDesc'
        },
        {
          title: '法人姓名',
          dataIndex: 'legalName',
          align: 'center'
        },
        {
          title: '法人手机号码',
          dataIndex: 'legalTelMask',
          align: 'center'
        },
        {
          title: '联系人姓名',
          dataIndex: 'contactsName',
          align: 'center'
        },
        {
          title: '联系人手机号码',
          dataIndex: 'contactsTelMask',
          align: 'center'
        },
        {
          title: '清算类型',
          dataIndex: 'remitType',
          key: 'remitType',
          align: 'center',
          width: 100
        },
        {
          title: '结算方式',
          dataIndex: 'settleMode',
          key: 'settleMode',
          align: 'center',
          width: 100
        },
        {
          title: '分润出款方式',
          dataIndex: 'settleChannelWay',
          key: 'settleChannelWay',
          width: 180,
          align: 'center'
        },
        {
          title: '分润出款通道',
          dataIndex: 'settleChannelCode',
          width: 160,
          align: 'center',
          customRender: ({ text, record }) => {
            if ([0].includes(record.settleChannelWay)) {
              const item = this.channelCodes.find(item => item.channelCode === text);
              return item?.channelName;
            }

            if ([1, 2].includes(record.settleChannelWay)) {
              const item = this.subChannelNos.find(item => item.channelNo === text);
              return item?.channelName;
            }

            return '--';
          }
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 120,
          align: 'center'
        }
      ].filter(i => !i.hideCol)
    };
  },
  mounted() {
    this.getChannelCodes();
    this.getRatePolicyList();
    this.getBankList();
    // this.getServiceFeeList();
    this.getActivityCashbackTemplateList();
    // this.getSimFeeList();
    if (hasPurview('0')) {
      this.geApptBrands();
    }
    this.getSubChannelNos();
  },
  methods: {
    async getSubChannelNos() {
      const data = await RemitChannelApi.findAll({ validStatus: 1, remitChannelClassify: 2 });
      this.subChannelNos = data || [];
    },
    async geApptBrands() {
      const data = await AppBrandApi.list();
      this.appBrands = data || [];
    },
    async handleEditAppBrandType() {
      if (this.selection.length !== 1) return message.warn('请选择一条数据');
      const [{ id, appBrandType, branchNo }] = this.selection;
      this.appBrandInfo = {
        id,
        userType: '2',
        userNo: branchNo,
        appBrandType
      };
      this.showEditAppBrandType = true;
    },

    async getServiceFeeList() {
      const data = await ServiceFeeManageApi.selfOpenList();
      this.serviceFeeList = data || [];
    },

    async getActivityCashbackTemplateList() {
      let data = await ActivityCashbackTemplateManageApi.querySelfList();
      this.activityCashbackTemplateList = data || [];
    },

    async getSimFeeList() {
      const data = await SimFeeManageApi.selfOpenList();
      this.simFeeList = data || [];
    },

    async handleAdd() {
      this.operationType = 0;
      this.showEdit = true;
    },

    async handleEditStatus() {
      if (this.selection.length !== 1) return message.warn('请选择一条数据');

      const [{ id }] = this.selection;
      const data = await OperationCenterApi.statusDetail({ id });
      this.statusRow = Object.assign({}, data);
      this.showEditStatus = true;
    },

    async handleEditTaxPoint() {
      if (this.selection.length !== 1) return message.warn('请选择一条数据');
      const [{ id, taxPoint, branchNo }] = this.selection;
      this.taxPointInfo = {
        id,
        userType: '2',
        userNo: branchNo,
        taxPoint
      };
      this.showEditTaxPoint = true;
    },

    async handleEditActityPolicy() {
      if (this.selection.length !== 1) return message.warn('请选择一条数据');
      const [{ branchNo }] = this.selection;
      const params = {
        orgNo: branchNo
      };
      const data = await ActivityCashbackTemplateManageApi.detailCashbackPolicy(params);
      this.current = Object.assign(params, data);
      this.showEditActivitPolicy = true;
    },

    async handleEdit(id) {
      const data = await OperationCenterApi.detailBranch({ id, isEditOpt: 1 });
      this.current = data;
      this.operationType = 1;
      this.showEdit = true;
    },

    async getBankList() {
      const data = await BankCodeManageApi.list();
      this.bankList = data || [];
    },

    async handleDetail(id, row) {
      let data = await OperationCenterApi.detailBranch({ id, isEditOpt: 0 });
      data.subChannelName = row.subChannelName;
      this.current = data;
      this.operationType = 2;
      this.showEdit = true;
    },

    handleEditSettleRemit({ key }) {
      if (key === '1') {
        if (this.selection.length !== 1) return message.warn('请选择一条数据');
        this.current = this.selection[0];
      } else {
        this.current = null;
      }
      this.showEditSettleRemit = true;
    },

    async getChannelCodes() {
      const data = await ChannelManageApi.list({ validStatus: 1 });
      this.channelCodes = data;
    },

    async getRatePolicyList() {
      const ratePolicyList = await RatePolicyApi.list({ userType: 1, policyType: 1 });
      this.ratePolicyList = ratePolicyList || [];
    },

    reload() {
      this.selection = [];
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.selection = [];
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    datasource({ page, limit, where }) {
      return OperationCenterApi.findBranchPage({ ...where, pageNo: page, pageSize: limit });
    },
    hasPurview
  }
};
</script>
