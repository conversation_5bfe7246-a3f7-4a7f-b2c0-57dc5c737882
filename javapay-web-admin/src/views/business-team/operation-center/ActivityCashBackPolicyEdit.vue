<template>
  <a-modal
    :width="800"
    title="修改活动返现政策"
    :visible="visible"
    :mask-closable="false"
    @update:visible="updateVisible"
    :confirm-loading="loading"
    :body-style="{ paddingBottom: '8px' }"
    @ok="save"
  >
    <a-form ref="policyForm" :model="policyForm" :label-col="{ style: { width: '130px' } }">
      <a-checkbox-group v-model:value="checkedServiceFeePolicyKey" class="ele-fluid" name="serviceFee">
        <a-tabs v-model:activeKey="activeChannelTabKey" type="card" @change="onChangeChannelTab">
          <a-tab-pane
            v-for="(channel, cIndex) in policyForm.policyConfigByChannelGroup || []"
            :tab="channel.channelName"
            :key="String(cIndex)"
            force-render
            class="tabpane__background"
          >
            <a-card :bordered="false">
              <a-tabs v-model:activeKey="activeTerminalSourceTabKey">
                <a-tab-pane v-for="(tab, tabIndex) in channel.channelData || []" :tab="tab.label" :key="String(tab.key)" force-render>
                  <a-divider orientation="left" dashed orientationMargin="0">终端服务费返现政策</a-divider>
                  <template v-if="tab.serviceFeeData?.length">
                    <template v-for="(item, key) in tab.serviceFeeData" :key="key">
                      <a-row v-if="item.show">
                        <a-col>
                          <a-space>
                            <a-checkbox style="margin-top: 4px" :value="item.configId" :disabled="item.openStatus" />
                            <a-divider type="vertical" />
                          </a-space>
                        </a-col>
                        <a-col :span="22">
                          <a-row :gutter="48">
                            <a-col :span="12">
                              <a-form-item label="服务费金额(元)">
                                <a-input v-model:value="item.policyName" placeholder="服务费金额" disabled />
                              </a-form-item>
                            </a-col>
                            <a-col :span="12">
                              <a-form-item
                                label="返现金额(元)"
                                :name="[
                                  'policyConfigByChannelGroup',
                                  cIndex,
                                  'channelData',
                                  tabIndex,
                                  'serviceFeeData',
                                  key,
                                  'cashbackAmt'
                                ]"
                                :rules="serviceCashbackAmtRules(item.configId, item.parentCashbackAmt)"
                                v-if="checkedServiceFeePolicyKey.includes(item.configId)"
                              >
                                <a-input v-model:value="item.cashbackAmt" placeholder="请输入返现金额" allow-clear />
                              </a-form-item>
                            </a-col>
                          </a-row>
                        </a-col>
                      </a-row>
                    </template>
                  </template>
                  <a-alert v-else message="没有政策配置哦~" banner />

                  <a-divider orientation="left" dashed orientationMargin="0">流量费返现政策</a-divider>
                  <template v-if="tab.simFeeData?.some(s => s.data.length)">
                    <div v-for="(period, idx) in tab.simFeeData" :key="idx">
                      <template v-if="period.show">
                        <div style="margin-bottom: 10px">
                          <a-typography-text strong>{{ period.name }}</a-typography-text>
                        </div>
                        <a-checkbox-group v-model:value="period.checkedList" class="ele-fluid" name="simFee">
                          <template v-for="(item, key) in period.data || []" :key="key">
                            <a-row v-if="item.show">
                              <a-col>
                                <a-space>
                                  <a-checkbox style="margin-top: 4px" :value="item.configId" :disabled="item.openStatus" />
                                  <a-divider type="vertical" />
                                </a-space>
                              </a-col>
                              <a-col :span="22">
                                <a-row :gutter="48">
                                  <a-col :span="12">
                                    <a-form-item label="流量费金额(元)">
                                      <a-input v-model:value="item.policyName" placeholder="流量费金额" disabled />
                                    </a-form-item>
                                  </a-col>
                                  <a-col :span="12" v-if="period.checkedList.includes(item.configId)">
                                    <a-form-item
                                      label="返现金额(元)"
                                      :name="[
                                        'policyConfigByChannelGroup',
                                        cIndex,
                                        'channelData',
                                        tabIndex,
                                        'simFeeData',
                                        idx,
                                        'data',
                                        key,
                                        'cashbackAmt'
                                      ]"
                                      :rules="simFeeCashbackAmtRules(item.configId, item.parentCashbackAmt, period)"
                                    >
                                      <a-input v-model:value="item.cashbackAmt" placeholder="请输入返现金额" allow-clear />
                                    </a-form-item>
                                  </a-col>
                                </a-row>
                              </a-col>
                            </a-row>
                          </template>
                        </a-checkbox-group>
                      </template>
                    </div>
                  </template>
                  <a-alert v-else message="没有政策配置哦~" banner />
                </a-tab-pane>
              </a-tabs>
            </a-card>
          </a-tab-pane>
        </a-tabs>
      </a-checkbox-group>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { ActivityCashbackTemplateManageApi } from '@/api/businessTeam/activity-Config/ActivityCashbackTemplateManageApi';
import { deepCopy } from '@/utils/util';

const simFeePolicyPeriodGroupDef = [
  {
    name: '第一期',
    field: 'firstPeriodList',
    data: [],
    checkedList: []
  },
  {
    name: '第二期',
    field: 'secondPeriodList',
    data: [],
    checkedList: []
  },
  {
    name: '第三期',
    field: 'thirdPeriodList',
    data: [],
    checkedList: []
  },
  {
    name: '标准期 (第四期以及后续阶段)',
    field: 'fourthPeriodList',
    data: [],
    checkedList: []
  }
];

const policyConfigByTerminalSourceDef = [
  {
    label: '全款机配置',
    key: 1,
    serviceFeeData: [],
    simFeeData: []
  },
  {
    label: '分期机配置',
    key: 2,
    serviceFeeData: [],
    simFeeData: []
  }
];

export default {
  props: {
    visible: Boolean,
    data: Object,
    channelCodes: Array
  },
  emits: ['update:visible', 'done'],
  data() {
    return {
      loading: false,
      checkedServiceFeePolicyKey: [],
      checkedSimFeePolicyKey: [],
      policyForm: {
        serviceFeePolicyDTOList: [],
        simFeePolicyPeriodGroup: []
      },
      activeTerminalSourceTabKey: '1',
      activeChannelTabKey: '0'
    };
  },
  async mounted() {
    const formatDataInfo = data => {
      this.policyForm = Object.assign({}, this.data);

      this.policyForm.serviceFeePolicyDTOList = data.serviceFeePolicyDTOList || [];

      this.policyForm.serviceFeePolicyDTOList.forEach(i => {
        i.show = Number(i.parentCashbackAmt) > 0 || !(Number(i.serviceFeeAmt) > 0);

        if (i.openStatus) {
          this.checkedServiceFeePolicyKey.push(i.configId);
        } else if (!(i.show)) {
          this.checkedServiceFeePolicyKey.push(i.configId);
          i.cashbackAmt = 0;
        } else {
          i.cashbackAmt = '';
        }
      });

      const simFeePolicyMap = data.simFeeNewPolicyDTO || {};
      const simFeePolicyPeriodGroupMap = deepCopy(simFeePolicyPeriodGroupDef);
      simFeePolicyPeriodGroupMap.forEach(period => {
        Object.keys(simFeePolicyMap).forEach(key => {
          if (period.field === key) {
            period.data = simFeePolicyMap[key];
            period.checkedList = [];
            period.data.forEach(item => {
              item.show = Number(item.parentCashbackAmt) > 0 || !(Number(item.simFeeAmt) > 0);
              if (item.openStatus) {
                period.checkedList.push(item.configId);
              } else if (!(item.show)) {
                period.checkedList.push(item.configId);
                item.cashbackAmt = 0;
              } else {
                item.cashbackAmt = '';
              }
            });
          }
        });
      });

      let allChannelCodes = [];
      try {
        const channelCodes = new Set(
          [...(this.policyForm.serviceFeePolicyDTOList || []), ...Object.values(simFeePolicyMap || {})]
            .flat(Infinity)
            .flatMap(item => (Array.isArray(item) ? item.map(subItem => subItem?.channelCode) : item?.channelCode))
            .filter(code => code?.trim())
            .map(code => code.trim())
        );
        allChannelCodes = [...channelCodes];
      } catch (error) {
        console.log(error);
      }

      let policyConfigByChannelGroup = [];
      allChannelCodes.forEach(item => {
        const channelItem = this.channelCodes.find(channel => channel.channelCode === item);
        policyConfigByChannelGroup.push({
          channelCode: item,
          channelName: channelItem?.channelName,
          channelData: deepCopy(policyConfigByTerminalSourceDef)
        });
      });

      policyConfigByChannelGroup.forEach(channel => {
        channel.channelData.forEach(tab => {
          tab.serviceFeeData = this.policyForm.serviceFeePolicyDTOList.filter(
            item => item.terminalSource === tab.key && item.channelCode === channel.channelCode
          );

          const simFeeData = simFeePolicyPeriodGroupMap.map(period => {
            const periodData = period.data.filter(item => item.terminalSource === tab.key && item.channelCode === channel.channelCode);
            return {
              ...period,
              data: periodData,
              show: periodData.some(item => item.show)
            };
          });

          tab.simFeeData = simFeeData;
        });
      });

      this.policyForm.policyConfigByChannelGroup = policyConfigByChannelGroup;
    };

    formatDataInfo(deepCopy(this.data || {}));
  },

  methods: {
    onChangeChannelTab() {
      this.activeTerminalSourceTabKey = '1';
    },

    simFeeCashbackAmtRules(key, maxsCashbackAmt, period) {
      if (period.checkedList.includes(key)) {
        return [
          { required: true, message: '请输入' },
          {
            validator: async (rule, value) => {
              if (Number(value) > Number(maxsCashbackAmt)) {
                return Promise.reject(`注：返现必须低于或等于${maxsCashbackAmt}`);
              }
              return Promise.resolve();
            },
            trigger: 'blur'
          }
        ];
      }
      return [];
    },

    serviceCashbackAmtRules(key, maxsCashbackAmt) {
      if (this.checkedServiceFeePolicyKey.includes(key)) {
        return [
          { required: true, message: '请输入' },
          {
            validator: async (rule, value) => {
              if (Number(value) > Number(maxsCashbackAmt)) {
                return Promise.reject(`注：返现必须低于或等于${maxsCashbackAmt}`);
              }
              return Promise.resolve();
            },
            trigger: 'blur'
          }
        ];
      }
      return [];
    },

    async save() {
      //校验表单
      await this.$refs.policyForm.validate();

      // if (!this.checkedServiceFeePolicyKey.length) {
      //   return message.warn('请至少勾选一条服务费返现政策');
      // }

      // const isPassSimPolicyCheck = this.policyForm.simFeePolicyPeriodGroup.every(period => {
      //   return period.checkedList?.length;
      // });
      // if (!isPassSimPolicyCheck) {
      //   return message.warn('流量费返现政策每期请至少勾选一条');
      // }

      let serviceFeePolicyDTOList = [];
      const simPolicySubmitData = {};

      this.policyForm.policyConfigByChannelGroup.forEach(channel => {
        channel.channelData.forEach(item => {
          // 格式化服务费
          const checkedServiceFeeData = item.serviceFeeData.filter(i => this.checkedServiceFeePolicyKey.includes(i.configId));
          serviceFeePolicyDTOList = [...serviceFeePolicyDTOList, ...checkedServiceFeeData];

          // 格式化流量费
          item.simFeeData.forEach(period => {
            const checkedSimFeeData = period.data.filter(i => period.checkedList.includes(i.configId));
            simPolicySubmitData[period.field] = [...(simPolicySubmitData[period.field] || []), ...checkedSimFeeData];
          });
        });
      });

      const params = {
        orgNo: this.policyForm.orgNo,
        serviceFeePolicyDTOList,
        simFeeNewPolicyDTO: simPolicySubmitData
      };

      //修改加载框为正在加载
      this.loading = true;

      ActivityCashbackTemplateManageApi.editCashbackPolicy(params)
        .then(result => {
          // 移除加载框
          this.loading = false;
          // 提示添加成功
          message.success(result.message);
          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);
          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
<style scoped>
.tabpane__background {
  background-color: #ececec;
  padding: 15px;
}
</style>
