<template>
  <a-modal
    :width="800"
    :title="modalTitle"
    :visible="visible"
    :confirm-loading="loading"
    :body-style="{ paddingBottom: '8px' }"
    :mask-closable="false"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-tabs v-model:activeKey="activeTabKey" type="card">
      <a-tab-pane key="0" tab="政策信息" v-if="[0, 2].includes(optType)">
        <a-form ref="policyForm" :model="policyForm" :rules="policyFormRules" :label-col="{ style: { width: '130px' } }">
          <a-row :gutter="24">
            <a-col :md="20" :sm="24" :xs="24">
              <a-form-item label="费率结算政策" name="ratePolicyId">
                <a-select
                  v-model:value="policyForm.ratePolicyId"
                  placeholder="请选择"
                  class="ele-fluid"
                  @change="selectRatePolicy"
                  :disabled="[1, 2].includes(optType)"
                >
                  <a-select-option v-for="(item, key) in ratePolicyList" :value="item.id" :key="key">{{ item.policyDesc }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="4" :sm="24" :xs="24" v-if="!isDisabled">
              <a-button style="margin-top: 5px" type="primary" size="small" @click="handleToAddPolicy">新增政策</a-button>
            </a-col>
          </a-row>
          <a-form
            :label-col="{ md: { span: 17 }, sm: { span: 24 } }"
            :wrapper-col="{ md: { span: 7 }, sm: { span: 24 } }"
            hideRequiredMark
            v-if="[0, 2].includes(optType)"
          >
            <a-tabs v-model:activeKey="activeRateKey">
              <a-tab-pane v-for="(item, key) in policyForm.rateDTOList" :tab="item.channelName" :key="String(key)">
                <!-- 费率相关 -->
                <RateModule
                  v-for="(rateItem, key2) in item.rateInfoDTO || []"
                  :key="key2"
                  :rate-item="rateItem"
                  :name-prefix="['rateDTOList', key, 'rateInfoDTO', key2]"
                  :bankList="bankList"
                  disabled
                />
              </a-tab-pane>
            </a-tabs>
          </a-form>

          <a-divider dashed style="margin-bottom: 40px" />

          <a-row :gutter="24" v-if="optType === 0">
            <a-col :md="20" :sm="24" :xs="24">
              <a-form-item label="活动返现政策" required>
                <a-select placeholder="请选择" class="ele-fluid" @change="selectActivityCashbackTemplatet">
                  <a-select-option v-for="(item, key) in activityCashbackTemplateList" :value="item.templateNo" :key="key">
                    {{ item.templateName }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <template v-if="optType !== 1">
            <a-checkbox-group v-model:value="checkedServiceFeePolicyKey" class="ele-fluid" name="serveceFee">
              <a-tabs v-model:activeKey="activeChannelTabKey" type="card" @change="onChangeChannelTab">
                <a-tab-pane
                  v-for="(channel, cIndex) in policyForm.policyConfigByChannelGroup || []"
                  :tab="channel.channelName"
                  :key="String(cIndex)"
                  force-render
                  class="tabpane__background"
                >
                  <a-card :bordered="false">
                    <a-tabs v-model:activeKey="activeTerminalSourceTabKey">
                      <a-tab-pane v-for="(tab, tabIndex) in channel.channelData || []" :tab="tab.label" :key="String(tab.key)" force-render>
                        <a-divider orientation="left" dashed orientationMargin="0">终端服务费返现政策</a-divider>
                        <template v-if="tab.serviceFeeData?.length">
                          <template v-for="(item, key) in tab.serviceFeeData" :key="key">
                            <a-row v-if="item.show">
                              <a-col>
                                <a-space>
                                  <a-checkbox style="margin-top: 4px" :value="item.configId" :disabled="isDisabled" />
                                  <a-divider type="vertical" />
                                </a-space>
                              </a-col>
                              <a-col :span="22">
                                <a-row :gutter="48">
                                  <a-col :span="12">
                                    <a-form-item label="服务费金额(元)">
                                      <a-input v-model:value="item.policyName" placeholder="服务费金额" disabled />
                                    </a-form-item>
                                  </a-col>
                                  <a-col :span="12">
                                    <a-form-item
                                      label="返现金额(元)"
                                      :name="[
                                        'policyConfigByChannelGroup',
                                        cIndex,
                                        'channelData',
                                        tabIndex,
                                        'serviceFeeData',
                                        key,
                                        'cashbackAmt'
                                      ]"
                                      :rules="serviceCashbackAmtRules(item.configId, item.parentCashbackAmt)"
                                      v-if="checkedServiceFeePolicyKey.includes(item.configId)"
                                    >
                                      <a-input
                                        v-model:value="item.cashbackAmt"
                                        placeholder="请输入返现金额"
                                        allow-clear
                                        :disabled="isDisabled"
                                      />
                                    </a-form-item>
                                  </a-col>
                                </a-row>
                              </a-col>
                            </a-row>
                          </template>
                        </template>
                        <a-alert v-else message="没有政策配置哦~" banner />

                        <a-divider orientation="left" dashed orientationMargin="0">流量费返现政策</a-divider>
                        <template v-if="tab.simFeeData?.some(s => s.data.length)">
                          <div v-for="(period, idx) in tab.simFeeData" :key="idx">
                            <template v-if="period.show">
                              <div style="margin-bottom: 10px">
                                <a-typography-text strong>{{ period.name }}</a-typography-text>
                              </div>
                              <a-checkbox-group v-model:value="period.checkedList" class="ele-fluid" name="simFee">
                                <template v-for="(item, key) in period.data || []" :key="key">
                                  <a-row v-if="item.show">
                                    <a-col>
                                      <a-space>
                                        <a-checkbox style="margin-top: 4px" :value="item.configId" :disabled="isDisabled" />
                                        <a-divider type="vertical" />
                                      </a-space>
                                    </a-col>
                                    <a-col :span="22">
                                      <a-row :gutter="48">
                                        <a-col :span="12">
                                          <a-form-item label="流量费金额(元)">
                                            <a-input v-model:value="item.policyName" placeholder="流量费金额" disabled />
                                          </a-form-item>
                                        </a-col>
                                        <a-col :span="12" v-if="period.checkedList.includes(item.configId)">
                                          <a-form-item
                                            label="返现金额(元)"
                                            :name="[
                                              'policyConfigByChannelGroup',
                                              cIndex,
                                              'channelData',
                                              tabIndex,
                                              'simFeeData',
                                              idx,
                                              'data',
                                              key,
                                              'cashbackAmt'
                                            ]"
                                            :rules="simFeeCashbackAmtRules(item.configId, item.parentCashbackAmt, period)"
                                          >
                                            <a-input
                                              v-model:value="item.cashbackAmt"
                                              placeholder="请输入返现金额"
                                              allow-clear
                                              :disabled="isDisabled"
                                            />
                                          </a-form-item>
                                        </a-col>
                                      </a-row>
                                    </a-col>
                                  </a-row>
                                </template>
                              </a-checkbox-group>
                            </template>
                          </div>
                        </template>
                        <a-alert v-else message="没有政策配置哦~" banner />
                      </a-tab-pane>
                    </a-tabs>
                  </a-card>
                </a-tab-pane>
              </a-tabs>
            </a-checkbox-group>
          </template>
        </a-form>
      </a-tab-pane>

      <a-tab-pane key="1" tab="基本信息">
        <a-form ref="form" :model="form" :rules="rules" :layout="activeTabKey === '2' ? 'horizontal' : 'vertical'">
          <!-- 基础信息 -->
          <div class="card-title card-title-background">基础信息</div>
          <a-row :gutter="100" justify="space-around">
            <a-form-item v-for="(item, key) in [fileMap[7]]" :key="key" :label="item.label" :required="item.required">
              <a-upload
                v-model:file-list="item.fileData"
                accept=".png,.jpg,.jpeg"
                :multiple="false"
                list-type="picture-card"
                :disabled="isDisabled"
                :before-upload="file => beforeUpload(file, item)"
                @remove="() => handleRemove(item)"
                @preview="() => handlePreview(item)"
              >
                <div v-if="!item.fileData?.length">
                  <plus-outlined />
                  <div style="margin-top: 8px">上传</div>
                </div>
              </a-upload>
            </a-form-item>
            <span></span>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="企业名称" name="branchName">
                <a-input v-model:value="form.branchName" placeholder="请输入企业名称" allow-clear :disabled="isDisabled" />
              </a-form-item>
              <a-form-item label="统一社会信用代码" name="licenseNo">
                <a-input v-model:value="form.licenseNo" placeholder="请输入统一社会信用代码" allow-clear :disabled="isDisabled" />
              </a-form-item>
              <a-form-item label="经营省市区" name="cityCode">
                <a-cascader
                  v-model:value="officeAreaValue"
                  :options="officeRegionsData"
                  :load-data="options => loadAreaData(options, 3)"
                  :allow-clear="false"
                  placeholder="请选择"
                  :disabled="isDisabled"
                  @change="selectedOfficeAreaValue"
                />
              </a-form-item>
              <a-form-item label="对公开票税点(%)" name="taxPoint">
                <a-input-number
                  v-model:value="form.taxPoint"
                  placeholder="请输入对公开票税点"
                  class="ele-fluid"
                  :disabled="optType !== 0"
                />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="企业简称" name="branchSname">
                <a-input v-model:value="form.branchSname" placeholder="请输入企业简称" allow-clear :disabled="isDisabled" />
              </a-form-item>
              <a-form-item label="企业地址(营业执照注册地址)" name="licenseAddr">
                <a-textarea
                  v-model:value="form.licenseAddr"
                  placeholder="请输入企业地址"
                  :auto-size="{ minRows: 1, maxRows: 6 }"
                  :disabled="isDisabled"
                />
              </a-form-item>
              <a-form-item label="企业办公地址" name="officeAddr">
                <a-textarea
                  v-model:value="form.officeAddr"
                  placeholder="请输入企业办公地址(详细地址)"
                  :auto-size="{ minRows: 1, maxRows: 6 }"
                  :disabled="isDisabled"
                />
              </a-form-item>
              <a-form-item label="营业执照有效期">
                <a-space>
                  <a-input v-model:value="form.licenseStartDate" placeholder="格式: 2020-05-20" allow-clear :disabled="isDisabled" />
                  -
                  <a-input v-model:value="form.licenseEndDate" placeholder="格式: 2020-05-20" allow-clear :disabled="isDisabled" />
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="24">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="分润出款方式" name="settleChannelWay">
                <a-select
                  v-model:value="form.settleChannelWay"
                  placeholder="请选择"
                  class="ele-fluid"
                  :disabled="optType !== 0"
                  @change="form.settleChannelCode = null"
                >
                  <!-- <a-select-option :value="3">展业平台</a-select-option> -->
                  <a-select-option :value="1">平台出款</a-select-option>
                  <!-- <a-select-option :value="0">交易通道</a-select-option> -->
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="分润出款通道" name="settleChannelCode" v-if="[1, 2].includes(form.settleChannelWay)">
                <a-select
                  v-model:value="form.settleChannelCode"
                  style="width: 100%"
                  placeholder="请选择"
                  :options="subChannelNos"
                  :fieldNames="{ label: 'channelName', value: 'channelNo' }"
                  :disabled="optType !== 0"
                />
              </a-form-item>
              <a-form-item label="分润出款通道" name="settleChannelCode" v-if="[0].includes(form.settleChannelWay)">
                <a-select v-model:value="form.settleChannelCode" style="width: 100%" placeholder="请选择" :disabled="optType !== 0">
                  <a-select-option value="1010">杉德</a-select-option>
                  <!-- <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
                    {{ channelName }}
                  </a-select-option> -->
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 法人信息 -->
          <div class="card-title card-title-background">法人信息</div>
          <div class="require-mark">图片资质信息</div>
          <a-row :gutter="24" justify="space-around">
            <a-form-item v-for="(item, key) in [fileMap[1], fileMap[2]]" :key="key" :label="item.label" :required="item.required">
              <a-upload
                v-model:file-list="item.fileData"
                accept=".png,.jpg,.jpeg"
                :multiple="false"
                list-type="picture-card"
                :before-upload="file => beforeUpload(file, item)"
                :disabled="isDisabled"
                @remove="() => handleRemove(item)"
                @preview="() => handlePreview(item)"
              >
                <div v-if="!item.fileData?.length">
                  <plus-outlined />
                  <div style="margin-top: 8px">上传</div>
                </div>
              </a-upload>
            </a-form-item>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="法人姓名" name="legalName">
                <a-input v-model:value="form.legalName" placeholder="请输入法人姓名" allow-clear :disabled="isDisabled" />
              </a-form-item>
              <a-form-item label="法人证件类型" name="legalCertType">
                <a-select v-model:value="form.legalCertType" class="ele-fluid" placeholder="请选择" :disabled="isDisabled">
                  <a-select-option v-for="({ label, value }, key) in certTypeEnum" :key="key" :value="value">{{ label }}</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="证件有效期">
                <a-space>
                  <a-input v-model:value="form.legalCertStartDate" placeholder="格式: 2020-05-20" allow-clear :disabled="isDisabled" />
                  -
                  <a-input v-model:value="form.legalCertEndDate" placeholder="格式: 2020-05-20" allow-clear :disabled="isDisabled" />
                </a-space>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="法人手机号" name="legalTel">
                <a-input v-model:value="form.legalTel" placeholder="请输入法人手机号码" allow-clear :disabled="isDisabled" />
              </a-form-item>
              <a-form-item label="法人证件号码" name="legalCertNo">
                <a-input v-model:value="form.legalCertNo" placeholder="请输入法人证件号码" allow-clear :disabled="isDisabled" />
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 联系人信息 -->
          <div class="card-title card-title-background">联系人信息</div>
          <div style="margin-bottom: 20px" v-if="optType === 0">
            <span class="ele-text-primary" style="margin-right: 20px"><info-circle-outlined /> 联系人信息是否同法人</span>
            <a-radio-group v-model:value="isSameLegalAndContact" name="radioGroup" @change="onSetContactByLegal">
              <a-radio :value="1">是</a-radio>
              <a-radio :value="0">否</a-radio>
            </a-radio-group>
          </div>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="联系人姓名" name="contactsName">
                <a-input v-model:value="form.contactsName" placeholder="请输入联系人姓名" allow-clear :disabled="isDisabled" />
              </a-form-item>
              <a-form-item label="联系人证件类型" name="contactsCertType">
                <a-select v-model:value="form.contactsCertType" placeholder="请选择" class="ele-fluid" :disabled="isDisabled">
                  <a-select-option v-for="({ label, value }, key) in certTypeEnum" :key="key" :value="value">{{ label }}</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="证件有效期">
                <a-space>
                  <a-input v-model:value="form.contactsCertStartDate" placeholder="格式: 2020-05-20" allow-clear :disabled="isDisabled" />
                  -
                  <a-input v-model:value="form.contactsCertEndDate" placeholder="格式: 2020-05-20" allow-clear :disabled="isDisabled" />
                </a-space>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item :label="`联系人手机号${[0, 2].includes(optType) ? '(登录手机号)' : ''}`" name="contactsTel">
                <a-input v-model:value="form.contactsTel" placeholder="请输入联系人手机号码" allow-clear :disabled="optType === 2" />
              </a-form-item>
              <a-form-item label="联系人证件号码" name="contactsCertNo">
                <a-input v-model:value="form.contactsCertNo" placeholder="请输入联系人证件号码" allow-clear :disabled="isDisabled" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="签约日期" name="signDate">
                <a-date-picker
                  v-model:value="form.signDate"
                  valueFormat="YYYY-MM-DD"
                  placeholder="请选择"
                  class="ele-fluid"
                  :disabled="isDisabled"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-tab-pane>

      <!-- 费率信息 -->
      <a-tab-pane key="2" tab="费率信息" v-if="optType === 2">
        <a-form
          ref="rateForm"
          :model="form"
          :label-col="{ md: { span: 17 }, sm: { span: 24 } }"
          :wrapper-col="{ md: { span: 7 }, sm: { span: 24 } }"
        >
          <a-tabs v-model:activeKey="activeRateKey">
            <a-tab-pane v-for="(item, key) in form.rateDTOList || []" :tab="`${item.channelName}-${item.channelCode}`" :key="String(key)">
              <!-- 费率相关 -->
              <RateModule
                v-for="(rateItem, key2) in item.rateInfoDTO || []"
                :key="key2"
                :rate-item="rateItem"
                :name-prefix="['rateDTOList', key, 'rateInfoDTO', key2]"
                :bankList="bankList"
                :disabled="isDisabled"
              />
            </a-tab-pane>
          </a-tabs>
        </a-form>
      </a-tab-pane>

      <a-tab-pane key="3" tab="结算信息" v-if="optType !== 1">
        <a-form ref="settleForm" :model="settleForm" :rules="settleFormRules" layout="vertical">
          <div style="margin-bottom: 20px">
            <span class="require-mark" style="margin-right: 20px">结算账户类型</span>
            <a-radio-group v-model:value="settleForm.accountType" disabled>
              <a-radio value="G">对公</a-radio>
              <a-radio value="S">对私</a-radio>
            </a-radio-group>
          </div>
          <div class="require-mark">结算卡图片信息</div>
          <a-row :gutter="100" justify="space-around">
            <a-form-item v-for="(item, key) in [fileMap[16]]" :key="key" :label="item.label" :required="item.required">
              <a-upload
                v-model:file-list="item.fileData"
                accept=".png,.jpg,.jpeg"
                :multiple="false"
                list-type="picture-card"
                :disabled="isDisabled"
                :before-upload="file => beforeUpload(file, item)"
                @remove="() => handleRemove(item)"
                @preview="() => handlePreview(item)"
              >
                <div v-if="!item.fileData?.length">
                  <plus-outlined />
                  <div style="margin-top: 8px">上传</div>
                </div>
              </a-upload>
            </a-form-item>
            <span></span>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="银行卡账户姓名" name="bankAccountName">
                <a-input v-model:value="settleForm.bankAccountName" placeholder="请输入银行卡账户姓名" disabled />
              </a-form-item>
              <a-form-item label="开户行总行" name="typeCode">
                <a-select
                  v-model:value="settleForm.typeCode"
                  style="width: 100%"
                  placeholder="请选择"
                  :disabled="isDisabled"
                  showSearch
                  :options="bankHeadOffice"
                  optionFilterProp="typeName"
                  :fieldNames="{ label: 'typeName', value: 'typeCode' }"
                  @change="(value, option) => selectBankHeadOffice(option)"
                />
              </a-form-item>
              <a-form-item label="开户行支行" name="bankChannelNo">
                <a-select
                  v-model:value="settleForm.bankChannelNo"
                  style="width: 100%"
                  placeholder="请选择"
                  showSearch
                  :options="bankSubBranch"
                  optionFilterProp="bankName"
                  :fieldNames="{ label: 'bankName', value: 'bankChannelNo' }"
                  :disabled="isDisabled || !(settleForm.typeCode && settleForm.bankCity)"
                  @change="(value, option) => selectBankSubBranch(option)"
                  @dropdownVisibleChange="open => open && getBankSubBranch()"
                />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="银行卡卡号" name="bankAccountNo">
                <a-input v-model:value="settleForm.bankAccountNo" placeholder="请输入银行卡卡号" allow-clear :disabled="isDisabled" />
              </a-form-item>
              <a-form-item label="开户行所在地区" name="bankCity">
                <a-cascader
                  v-model:value="bankAreaValue"
                  :options="bankRegionsData"
                  :load-data="options => loadAreaData(options, 2)"
                  :allow-clear="false"
                  :disabled="isDisabled"
                  placeholder="请选择"
                  @change="selectedBankAreaValue"
                />
              </a-form-item>
              <a-form-item label="银行卡预留手机号" name="mobile">
                <a-input v-model:value="settleForm.mobile" placeholder="请输入银行卡预留手机号" allow-clear :disabled="isDisabled" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-tab-pane>

      <!-- 图片信息 -->
      <a-tab-pane key="4" tab="图片信息">
        <a-row :gutter="24">
          <a-col :span="6" v-for="(item, key) in Object.values(fileMap)" :key="key">
            <a-form-item :label="item.label" :required="item.required">
              <a-upload
                v-model:file-list="item.fileData"
                accept=".png,.jpg,.jpeg,.gif"
                :multiple="false"
                list-type="picture-card"
                :before-upload="file => beforeUpload(file, item)"
                @remove="() => handleRemove(item)"
                @preview="() => handlePreview(item)"
                :disabled="isDisabled"
              >
                <div v-if="!item.fileData?.length">
                  <plus-outlined />
                  <div style="margin-top: 8px">Upload</div>
                </div>
              </a-upload>
            </a-form-item>
          </a-col>
        </a-row>
      </a-tab-pane>
    </a-tabs>

    <!-- 预览图片 -->
    <a-image :style="{ display: 'none' }" :src="previewImage" :preview="{ visible: previewVisible, onVisibleChange: setPreviewVisible }" />

    <template #footer v-if="isDisabled">
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { OperationCenterApi } from '@/api/businessTeam/operation-center/OperationCenterApi';
import { certTypeEnum } from '@/config/enumerate';
import { message, Upload } from 'ant-design-vue';
import { phoneReg } from 'ele-admin-pro';
import RateModule from '../_components/RateModule.vue';
import { compressorImage } from '@/utils/image-compressor-util';
import { OcrApi } from '@/api/base/OcrApi';
import dayjs from 'dayjs';
import { BankTypeApi } from '@/api/base/BankTypeApi';
import { BankInfoApi } from '@/api/base/BankInfoApi';
import { AreaApi } from '@/api/base/AreaApi';
import { deepCopy } from '@/utils/util';
import { BankCodeManageApi } from '@/api/base/BankCodeManageApi';
import { ActivityCashbackTemplateManageApi } from '@/api/businessTeam/activity-Config/ActivityCashbackTemplateManageApi';
import { RatePolicyApi } from '@/api/businessTeam/rate-policy/RatePolicyApi';
import { RemitChannelApi } from '@/api/account/remit-channel/RemitChannelApi';
import { hasPurview } from '@/utils/permission';

const simFeePolicyPeriodGroupDef = [
  {
    name: '第一期',
    field: 'firstPeriodList',
    data: [],
    checkedList: []
  },
  {
    name: '第二期',
    field: 'secondPeriodList',
    data: [],
    checkedList: []
  },
  {
    name: '第三期',
    field: 'thirdPeriodList',
    data: [],
    checkedList: []
  },
  {
    name: '标准期 (第四期以及后续阶段)',
    field: 'fourthPeriodList',
    data: [],
    checkedList: []
  }
];

const policyConfigByTerminalSourceDef = [
  {
    label: '全款机配置',
    key: 1,
    serviceFeeData: [],
    simFeeData: []
  },
  {
    label: '分期机配置',
    key: 2,
    serviceFeeData: [],
    simFeeData: []
  }
];
export default {
  components: { RateModule },
  props: {
    visible: Boolean,
    data: Object,
    channelCodes: Array,
    /**
     * @prop {Number} 操作类型 0:添加  1:修改  2:详情
     */
    optType: Number
  },
  emits: ['update:visible', 'done'],
  data() {
    return {
      subChannelNos: [],
      bankList: [],
      activityCashbackTemplateList: [],
      ratePolicyList: [],
      modalTitle: titleArr[this.optType],
      certTypeEnum,
      loading: false,
      //当前选中的tab项
      activeTabKey: '0',
      activeRateKey: '0',
      activeTerminalSourceTabKey: '1',
      activeChannelTabKey: '0',
      //图片预览的数据
      previewVisible: false,
      previewImage: '',
      isSameLegalAndContact: 0,
      isDisabled: this.optType === 2,
      bankHeadOffice: [],
      bankSubBranch: [],
      bankAreaValue: [],
      bankRegionsData: [],
      officeAreaValue: [],
      officeRegionsData: [],
      showPolicyAddFront: false,
      activePolicyKey: '0',
      //表单数据
      form: {
        // 税点默认值
        taxPoint: 6,
        // 分润出款方式默认值
        settleChannelWay: 1,
        // 分润出款通道默认值
        settleChannelCode: '1002',
        // 证件类型默认身份证
        contactsCertType: '01',
        legalCertType: '01'
      },
      settleForm: { accountType: 'G' },
      policyForm: {
        serviceFeePolicyDTOList: [],
        simFeePolicyPeriodGroup: []
      },
      checkedServiceFeePolicyKey: [],
      checkedSimFeePolicyKey: [],
      displayTemplateData: [],
      //图片数组
      fileMap: {
        1: {
          label: '身份证头像面',
          fileType: 1,
          required: true
        },
        2: {
          label: '身份证国徽面',
          fileType: 2,
          required: true
        },
        16: {
          label: '对公开户许可证',
          fileType: 16,
          required: true
        },
        7: {
          label: '营业执照照片',
          fileType: 7,
          required: false
        }
      },
      //验证规则
      rules: {
        cityCode: [{ required: true, message: '请选择省市区' }],
        branchName: [{ required: true, message: '请输入企业名称' }],
        branchSname: [{ required: true, message: '请输入企业简称' }],
        taxPoint: [{ required: true, message: '请输入对公开票税点' }],
        contactsName: [{ required: true, message: '请输入联系人姓名' }],
        contactsTel: [
          { required: true, message: '请输入联系人手机号码' },
          { pattern: phoneReg, message: '手机号码格式错误', trigger: 'blur' }
        ],
        contactsCertType: [{ required: true, message: '请输入联系人件类型' }],
        contactsCertNo: [{ required: true, message: '请输入联系人证件号码' }],
        officeAddr: [{ required: true, message: '请输入办公地址' }],
        legalName: [{ required: true, message: '请输入法人姓名' }],
        legalTel: [
          { required: true, message: '请输入法人手机号码' },
          { pattern: phoneReg, message: '手机号码格式错误', trigger: 'blur' }
        ],
        legalCertType: [{ required: true, message: '请输入法人证件类型' }],
        legalCertNo: [{ required: true, message: '请输入法人证件号码' }],
        licenseNo: [{ required: true, message: '请输入统一社会信用代码' }],
        licenseAddr: [{ required: true, message: '请输入营业执照注册地址' }],
        ratePolicyId: [{ required: true, message: '请选择费率政策' }],
        signDate: [{ required: true, message: '请选择签约日期' }],
        settleChannelWay: [{ required: true, message: '请选择分润出款方式' }],
        settleChannelCode: [{ required: true, message: '请选择' }]
      },
      settleFormRules: {
        accountType: [{ required: true, message: '请选择结算账户类型' }],
        bankAccountName: [{ required: true, message: '请输入银行卡账户姓名' }],
        typeCode: [{ required: true, message: '请选择开户行总行' }],
        bankChannelNo: [{ required: true, message: '请选择开户行支行' }],
        bankAccountNo: [{ required: true, message: '请输入银行卡卡号' }],
        bankCity: [{ required: true, message: '请选择开户行所在地区' }],
        mobile: [
          { required: true, message: '请输入银行卡预留手机号' },
          { pattern: phoneReg, message: '手机号码格式错误', trigger: 'blur' }
        ]
      },
      policyFormRules: {
        ratePolicyId: [{ required: true, message: '请选择费率结算政策' }]
      }
    };
  },
  watch: {
    'form.branchName'(val) {
      this.settleForm.bankAccountName = val;
    }
  },
  async mounted() {
    this.getRatePolicyList();
    this.getBankList();
    this.getSubChannelNos();

    await this.getActivityCashbackTemplateList();

    if (!hasPurview('0') && [2].includes(this.optType)) {
      await this.getDisplayDetail();
    }
    const formatBaseInfo = data => {
      // 格式化基本信息
      this.form = Object.assign({}, data);

      this.policyForm.ratePolicyId = this.form.ratePolicyId = data.policyId;

      let serviceFeePolicyDTOMap = {};
      let simFeePolicyDTOMap = {};
      this.displayTemplateData.forEach(item => {
        // 自身全部服务费
        item.serviceFeePolicyDTOList.forEach(s => {
          serviceFeePolicyDTOMap[s.configId] = s;
        });

        // 自身全部流量费
        Object.keys(item.simFeeNewPolicyDTO || {}).forEach(key => {
          simFeePolicyDTOMap[key] = Object.assign({}, simFeePolicyDTOMap[key] || {});
          item.simFeeNewPolicyDTO[key].forEach(s => {
            simFeePolicyDTOMap[key][s.configId] = s;
          });
        });
      });

      this.policyForm.serviceFeePolicyDTOList = data.serviceFeePolicyDTOList || [];
      this.policyForm.serviceFeePolicyDTOList.forEach(i => {
        this.checkedServiceFeePolicyKey.push(i.configId);

        if (!i.parentCashbackAmt) {
          const configItem = serviceFeePolicyDTOMap[i.configId] || {};
          i.parentCashbackAmt = configItem.parentCashbackAmt;
        }

        i.show = Number(i.parentCashbackAmt) > 0 || !(Number(i.serviceFeeAmt) > 0);
      });

      const simFeePolicyMap = data.simFeeNewPolicyDTO || {};
      const simFeePolicyPeriodGroupMap = deepCopy(simFeePolicyPeriodGroupDef);
      simFeePolicyPeriodGroupMap.forEach(period => {
        Object.keys(simFeePolicyMap).forEach(key => {
          if (period.field === key) {
            period.data = simFeePolicyMap[key] || [];
            period.checkedList = [];
            period.data.forEach(item => {
              period.checkedList.push(item.configId);

              if (!item.parentCashbackAmt) {
                const configItem = (simFeePolicyDTOMap[key] && simFeePolicyDTOMap[key][item.configId]) || {};
                item.parentCashbackAmt = configItem.parentCashbackAmt;
              }

              item.show = Number(item.parentCashbackAmt) > 0 || !(Number(item.simFeeAmt) > 0);
            });
          }
        });
      });

      let allChannelCodes = [];
      try {
        const channelCodes = new Set(
          [...(this.policyForm.serviceFeePolicyDTOList || []), ...Object.values(simFeePolicyMap || {})]
            .flat(Infinity)
            .flatMap(item => (Array.isArray(item) ? item.map(subItem => subItem?.channelCode) : item?.channelCode))
            .filter(code => code?.trim())
            .map(code => code.trim())
        );
        allChannelCodes = [...channelCodes];
      } catch (error) {
        console.log(error);
      }

      let policyConfigByChannelGroup = [];
      allChannelCodes.forEach(item => {
        const channelItem = this.channelCodes.find(channel => channel.channelCode === item);
        policyConfigByChannelGroup.push({
          channelCode: item,
          channelName: channelItem?.channelName,
          channelData: deepCopy(policyConfigByTerminalSourceDef)
        });
      });

      policyConfigByChannelGroup.forEach(channel => {
        channel.channelData.forEach(tab => {
          tab.serviceFeeData = this.policyForm.serviceFeePolicyDTOList.filter(
            item => item.terminalSource === tab.key && item.channelCode === channel.channelCode
          );

          const simFeeData = simFeePolicyPeriodGroupMap.map(period => {
            const periodData = period.data.filter(item => item.terminalSource === tab.key && item.channelCode === channel.channelCode);
            return {
              ...period,
              data: periodData,
              show: periodData.some(item => item.show)
            };
          });

          tab.simFeeData = simFeeData;
        });
      });

      this.policyForm.policyConfigByChannelGroup = policyConfigByChannelGroup;

      const fieldMark = this.optType === 1 ? 'Cipher' : 'Mask';
      const foundKeys = Object.keys(this.form).filter(k => k.endsWith(fieldMark));
      foundKeys.forEach(v => {
        this.form[v.replace(fieldMark, '')] = this.form[v];
      });

      this.settleForm = Object.assign(this.settleForm, data?.bankCard);

      const foundKeys2 = Object.keys(this.settleForm).filter(k => k.endsWith(fieldMark));
      foundKeys2.forEach(v => {
        this.settleForm[v.replace(fieldMark, '')] = this.settleForm[v];
      });

      this.officeAreaValue = [this.form.provinceCode, this.form.cityCode, this.form.districtCode];

      if (this.settleForm.bankCity) {
        this.bankAreaValue = [this.settleForm.bankProvince, this.settleForm.bankCity];
        if (this.settleForm.typeCode) {
          this.getBankSubBranch();
        }
      }

      // 反显图片
      Object.values(this.fileMap).forEach(f => {
        if (data.fileDTOList) {
          const item = data.fileDTOList.find(i => i.imageType === f.fileType);
          if (item) {
            f.fileData = item.imagePath ? [{ url: item.imagePath }] : [];
            f.fileName = item.imageName;
          }
        }
      });
    };

    // 格式化费率信息
    const formatRateInfo = (allData, data) => {
      this.form.rateDTOList = [];
      this.policyForm.rateDTOList = [];

      Object.keys(data).forEach(channelCode => {
        const item = this.channelCodes.find(i => i.channelCode === channelCode);
        const formatData = this.formatRateInfo(data[channelCode]);

        this.form.rateDTOList.push({
          channelCode,
          channelName: item?.channelName,
          rateInfoDTO: formatData
        });
        this.policyForm.rateDTOList.push({
          channelCode,
          channelName: item?.channelName,
          rateInfoDTO: formatData
        });
      });

      this.activeRateKey = '0';
    };

    if (this.optType === 1) {
      formatBaseInfo(this.data);
      this.activeTabKey = '1';
    } else if (this.optType === 2) {
      // 详情
      formatBaseInfo(this.data);
      formatRateInfo(null, this.data.rateMap || {});
    }

    this.loadAreaData();
    this.getBankHeadOffice();
  },

  methods: {
    onChangeChannelTab() {
      this.activeTerminalSourceTabKey = '1';
    },

    async getSubChannelNos() {
      const data = await RemitChannelApi.findAll({ validStatus: 1, remitChannelClassify: 2 });
      this.subChannelNos = data || [];
    },

    async getBankList() {
      const data = await BankCodeManageApi.list();
      this.bankList = data || [];
    },

    async getActivityCashbackTemplateList() {
      let data = await ActivityCashbackTemplateManageApi.querySelfList();
      data = data || [];
      this.activityCashbackTemplateList = data;
      this.displayTemplateData = data;
    },

    async getDisplayDetail() {
      let data = await ActivityCashbackTemplateManageApi.displayDetail();
      data = data || {};
      this.displayTemplateData = [{ ...data }];
    },
    async getRatePolicyList() {
      const ratePolicyList = await RatePolicyApi.list({ userType: 1, policyType: 1 });
      this.ratePolicyList = ratePolicyList || [];
    },

    simFeeCashbackAmtRules(key, maxsCashbackAmt, period) {
      if (period.checkedList.includes(key)) {
        return [
          { required: true, message: '请输入' },
          {
            validator: async (rule, value) => {
              if (Number(value) > Number(maxsCashbackAmt)) {
                return Promise.reject(`注：您的返现金额为${maxsCashbackAmt}元，下级必须低于或等于${maxsCashbackAmt}`);
              }
              return Promise.resolve();
            },
            trigger: 'blur'
          }
        ];
      }
      return [];
    },
    serviceCashbackAmtRules(key, maxsCashbackAmt) {
      if (this.checkedServiceFeePolicyKey.includes(key)) {
        return [
          { required: true, message: '请输入' },
          {
            validator: async (rule, value) => {
              if (Number(value) > Number(maxsCashbackAmt)) {
                return Promise.reject(`注：您的返现金额为${maxsCashbackAmt}元，下级必须低于或等于${maxsCashbackAmt}`);
              }
              return Promise.resolve();
            },
            trigger: 'blur'
          }
        ];
      }
      return [];
    },
    selectActivityCashbackTemplatet(val) {
      const item = this.activityCashbackTemplateList.find(({ templateNo }) => templateNo === val);
      if (item) {
        this.policyForm.serviceFeePolicyDTOList = item.serviceFeePolicyDTOList || [];
        this.checkedServiceFeePolicyKey = [];
        this.policyForm.serviceFeePolicyDTOList.forEach(i => {
          this.checkedServiceFeePolicyKey.push(i.configId);

          i.show = Number(i.parentCashbackAmt) > 0 || !(Number(i.serviceFeeAmt) > 0);
          if (!i.show) {
            i.cashbackAmt = 0;
          }
        });

        const simFeePolicyPeriodGroup = deepCopy(simFeePolicyPeriodGroupDef);
        simFeePolicyPeriodGroup.forEach(period => {
          Object.keys(item.simFeeNewPolicyDTO || {}).forEach(key => {
            if (period.field === key) {
              period.data = deepCopy(item.simFeeNewPolicyDTO[key]);

              period.checkedList = [];
              period.data.forEach(item => {
                period.checkedList.push(item.configId);

                item.show = Number(item.parentCashbackAmt) > 0 || !(Number(item.simFeeAmt) > 0);

                if (!item.show) {
                  item.cashbackAmt = 0;
                }
              });
            }
          });
        });

        let allChannelCodes = [];
        try {
          const channelCodes = new Set(
            [...(this.policyForm.serviceFeePolicyDTOList || []), ...Object.values(item.simFeeNewPolicyDTO || {})]
              .flat(Infinity)
              .flatMap(item2 => (Array.isArray(item2) ? item2.map(subItem => subItem?.channelCode) : item2?.channelCode))
              .filter(code => code?.trim())
              .map(code => code.trim())
          );
          allChannelCodes = [...channelCodes];
        } catch (error) {
          console.log(error);
        }

        let policyConfigByChannelGroup = [];
        allChannelCodes.forEach(item => {
          const channelItem = this.channelCodes.find(channel => channel.channelCode === item);
          policyConfigByChannelGroup.push({
            channelCode: item,
            channelName: channelItem?.channelName,
            channelData: deepCopy(policyConfigByTerminalSourceDef)
          });
        });

        policyConfigByChannelGroup.forEach(channel => {
          channel.channelData.forEach(tab => {
            tab.serviceFeeData = this.policyForm.serviceFeePolicyDTOList.filter(
              item => item.terminalSource === tab.key && item.channelCode === channel.channelCode
            );

            const simFeeData = simFeePolicyPeriodGroup.map(period => {
              const periodData = period.data.filter(item => item.terminalSource === tab.key && item.channelCode === channel.channelCode);
              return {
                ...period,
                data: periodData,
                show: periodData.some(item => item.show)
              };
            });

            tab.simFeeData = simFeeData;
          });
        });

        this.policyForm.policyConfigByChannelGroup = policyConfigByChannelGroup;
      }
    },
    selectRatePolicy(val) {
      const item = this.ratePolicyList.find(({ id }) => id === val);
      if (item) {
        this.filterRateInfo(item?.rateDTOList || []);
        this.activeRateKey = '0';
      }
    },
    async save() {
      //校验表单
      if (this.optType === 0) {
        await this.$refs.policyForm.validate();

        // if (!this.policyForm.serviceFeePolicyDTOList.length) {
        //   return message.warn('请至少勾选一条服务费返现政策');
        // }

        // const isPassSimPolicyCheck = this.policyForm.simFeePolicyPeriodGroup.every(period => {
        //   return period.checkedList?.length;
        // });
        // if (!isPassSimPolicyCheck) {
        //   return message.warn('流量费返现政策每期请至少勾选一条');
        // }

        let serviceFeePolicyDTOList = [];
        const simPolicySubmitData = {};

        this.policyForm.policyConfigByChannelGroup.forEach(channel => {
          channel.channelData.forEach(item => {
            // 格式化服务费
            const checkedServiceFeeData = item.serviceFeeData.filter(i => this.checkedServiceFeePolicyKey.includes(i.configId));
            serviceFeePolicyDTOList = [...serviceFeePolicyDTOList, ...checkedServiceFeeData];

            // 格式化流量费
            item.simFeeData.forEach(period => {
              const checkedSimFeeData = period.data.filter(i => period.checkedList.includes(i.configId));
              simPolicySubmitData[period.field] = [...(simPolicySubmitData[period.field] || []), ...checkedSimFeeData];
            });
          });
        });

        this.form.serviceFeePolicyDTOList = serviceFeePolicyDTOList;
        this.form.simFeeNewPolicyDTO = simPolicySubmitData;
      }

      await this.$refs.form.validate();
      this.$refs.settleForm && (await this.$refs.settleForm.validate());

      //校验图片是否上传完整
      await this.validateFileList();
      this.form.fileDTOList = Object.values(this.fileMap)
        .map(item => {
          if (item.fileData?.[0]) {
            return {
              fileName: item.fileName,
              fileData: item.fileData[0].url,
              fileType: item.fileType,
              suffixType: item.suffixType || 'png'
            };
          }
          return null;
        })
        .filter(f => !!f);

      //修改加载框为正在加载
      this.loading = true;

      let result = null;
      //执行编辑或修改的方法
      switch (this.optType) {
        case 0: //新增
          this.form.orgBankCardRequest = this.settleForm;
          this.form.ratePolicyId = this.policyForm.ratePolicyId;
          result = OperationCenterApi.addBranch(this.form);
          break;

        case 1: //编辑
          result = OperationCenterApi.editBranch(this.form);
          break;
      }

      result
        .then(result => {
          // 移除加载框
          this.loading = false;
          // 提示添加成功
          message.success(result.message);
          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);
          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    filterRateInfo(data) {
      this.policyForm.rateDTOList = [];

      const channelCodes = data.map(i => i.channelCode) || [];
      const allChannel = Array.from(new Set(channelCodes));
      var rateDTOList = [];

      allChannel.forEach(channelCode => {
        const channelItem = this.channelCodes.find(c => channelCode === c.channelCode);
        const channelName = channelItem?.channelName;

        const dataByCode = data.filter(c => c.channelCode === channelCode);

        dataByCode.forEach(r => {
          if (r.rateInfo) {
            r.rateInfoDTO = JSON.parse(r.rateInfo);
          } else {
            r.rateInfoDTO = r.rateInfoDTO || {};
          }

          r.rateRatio = 100;
          const rateFields = ['withdrawRate', 'withdrawSingleFee'];
          rateFields.forEach(f => (r.rateInfoDTO[f] = 0));
        });

        dataByCode.sort(function (a, b) {
          return a.rateType - b.rateType;
        });

        rateDTOList.push({
          channelCode,
          channelName,
          rateInfoDTO: dataByCode
        });
      });

      this.policyForm.rateDTOList = rateDTOList;
    },

    /**
     * 格式化费率信息
     * @param {Array} data 费率信息
     */
    formatRateInfo(data) {
      const formatData = data ? JSON.parse(JSON.stringify(data).replaceAll(/\brateInfo\b/g, 'rateInfoDTO')) : [];

      formatData.forEach(r => {
        r.rateInfoDTO = typeof r.rateInfoDTO === 'string' ? JSON.parse(r.rateInfoDTO) : r.rateInfoDTO;

        r.rateRatio = 100;
        const rateFields = ['withdrawRate', 'withdrawSingleFee'];
        rateFields.forEach(f => (r.rateInfoDTO[f] = 0));

        // if (!(r.rateType || r.rateInfoDTO.rateType)) {
        //   if (['1000', '1002', '1001'].includes(r.channelCode)) {
        //     r.rateInfoDTO.rateType = 1;
        //   } else if (r.channelCode === '1003') {
        //     r.rateInfoDTO.rateType = 3;
        //   }
        // }
      });

      formatData.sort(function (a, b) {
        return a.rateInfoDTO.rateType - b.rateInfoDTO.rateType;
      });

      return formatData;
    },

    onSetContactByLegal(e) {
      const isSame = e.target.value;
      const legalFields = Object.keys(this.form).filter(k => k.startsWith('legal'));
      legalFields.forEach(l => {
        this.form[l.replace('legal', 'contacts')] = isSame ? this.form[l] : '';
      });

      if (!isSame) this.form.contactsCertType = '01';
    },

    handleToAddPolicy() {
      this.updateVisible(false);
      this.$router.push('/rate-policy-manage');
    },
    async ocrIdcard(imgFile, side) {
      const data = await OcrApi.ocrIdcard({
        imgFile,
        side
      });
      const { success, name, num, startDate, endDate } = data;
      if (success) {
        switch (side) {
          case 'face':
            this.form.legalName = name;
            this.form.legalCertNo = num;
            this.form.legalCertType = '01';
            break;
          case 'back':
            this.form.legalCertStartDate = startDate ? dayjs(startDate).format('YYYY-MM-DD') : '';
            this.form.legalCertEndDate = endDate === '长期' ? '2999-12-31' : endDate ? dayjs(endDate).format('YYYY-MM-DD') : '';
            break;
        }
      }
    },
    async ocrBankcard(imgFile) {
      const data = await OcrApi.ocrBankcard({
        imgFile
      });
      const { success, cardNum } = data;
      if (success) {
        this.settleForm.bankAccountNo = cardNum;
      }
    },
    async ocarBusinessLicense(imgFile) {
      const data = await OcrApi.ocarBusinessLicense({
        imgFile
      });
      const { success, regNum, name, establishDate, validPeriod, address } = data;
      if (success) {
        this.form.branchName = name == 'FailInRecognition' ? '' : name;
        this.form.licenseNo = regNum == 'FailInRecognition' ? '' : regNum;
        this.form.licenseAddr = address == 'FailInRecognition' ? '' : address;
        this.form.licenseStartDate = establishDate ? dayjs(establishDate).format('YYYY-MM-DD') : '';
        this.form.licenseEndDate = validPeriod ? dayjs(validPeriod).format('YYYY-MM-DD') : '';
      }
    },
    beforeUpload(file, item) {
      compressorImage(file).then(({ url, mime }) => {
        item.fileData = [{ url }];
        item.fileName = file.uid + file.name;
        item.suffixType = mime.split('/')[1];

        switch (item.fileType) {
          case 1:
          case 2:
            this.ocrIdcard(url, item.fileType === 1 ? 'face' : 'back');
            break;
          case 3:
            if (this.settleForm.accountType === 'S') {
              this.ocrBankcard(url);
            }
            break;
          case 7:
            this.ocarBusinessLicense(url);
            break;
          default:
            break;
        }
      });

      return Upload.LIST_IGNORE;
    },

    handleRemove(item) {
      item.fileData = [];
      item.suffixType = '';
    },

    handlePreview({ fileData }) {
      this.previewImage = fileData[0].url;
      this.setPreviewVisible(true);
    },

    setPreviewVisible(visible) {
      this.previewVisible = visible;
    },

    validateFileList() {
      return new Promise((resolve, reject) => {
        if (Object.values(this.fileMap).every(item => !item.required || item.fileData?.length)) {
          resolve();
        } else {
          message.warn('请上传完整图片信息');
          reject();
        }
      });
    },

    async loadAreaData(selectedOptions, totalLevel = 2) {
      const targetOption = selectedOptions ? selectedOptions[selectedOptions.length - 1] : { level: 1, code: '' };
      const { level, code } = targetOption;
      const data = await AreaApi.list({ level: level + 1, status: 1, parentCode: code });
      const filterData = data.map(d => {
        return { label: d.areaName, value: d.areaCode, code: d.areaCode, isLeaf: level > totalLevel - 1, level: d.level };
      });
      if (level === 1) {
        this.officeRegionsData = deepCopy(filterData);
        this.bankRegionsData = deepCopy(filterData);
      } else {
        targetOption.children = filterData;
      }

      if (this.optType && !selectedOptions) {
        const officeItem = this.officeRegionsData.find(r => r.value === this.form.provinceCode);
        if (officeItem) {
          await this.loadAreaData([officeItem]);
          const citem = officeItem.children.find(i => i.value === this.form.cityCode);
          citem && this.loadAreaData([citem]);
        }

        const bankItem = this.bankRegionsData.find(r => r.value === this.settleForm.bankProvince);
        bankItem && this.loadAreaData([bankItem]);
      }
    },

    selectedBankAreaValue(value) {
      [this.settleForm.bankProvince, this.settleForm.bankCity] = value || [];
      this.settleForm.bankChannelNo = null;
    },

    async getBankSubBranch() {
      this.bankSubBranch = [];
      const data = await BankInfoApi.list({
        status: 1,
        typeCode: this.settleForm.typeCode,
        provinceCode: this.settleForm.bankProvince,
        cityCode: this.settleForm.bankCity
      });
      this.bankSubBranch = data || [];
    },

    selectedOfficeAreaValue(value, arr) {
      [this.form.provinceCode, this.form.cityCode, this.form.districtCode] = value || [];
      const labels = arr.map(i => i.label);
      [this.form.provinceName, this.form.cityName, this.form.districtName] = labels || [];
    },

    selectBankSubBranch({ bankName }) {
      this.settleForm.bankSubName = bankName;
    },

    selectBankHeadOffice({ typeName }) {
      this.settleForm.typeName = typeName;
      this.settleForm.bankChannelNo = null;
    },

    async getBankHeadOffice() {
      const data = await BankTypeApi.list({ status: 1 });
      this.bankHeadOffice = data || [];
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};

// 弹框标题
const titleArr = ['添加运营', '修改信息', '运营详情'];
</script>

<style scoped>
.card-title {
  border-left: 5px solid;
  border-color: var(--primary-color);
  padding-left: 10px;
}

.card-title-background {
  background-color: #f5f5f5;
  height: 2em;
  line-height: 2em;
  margin-bottom: 20px;
}

.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}
</style>
