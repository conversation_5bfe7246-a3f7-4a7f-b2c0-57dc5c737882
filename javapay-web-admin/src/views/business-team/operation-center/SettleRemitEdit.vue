<template>
  <a-modal
    :width="520"
    :visible="visible"
    :confirm-loading="loading"
    :title="`修改结算周期&结算方式 (${isBatchEdit ? '批量' : '单个'}修改)`"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @cancel="cancel"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 5 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 19 }, sm: { span: 24 } }"
    >
      <a-form-item label="结算方式" name="remitType">
        <a-select v-model:value="form.remitType" style="width: 100%" placeholder="请选择">
          <a-select-option :value="1">平台清算</a-select-option>
          <a-select-option :value="2">自行提现</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="结算周期" name="settleMode">
        <a-select v-model:value="form.settleMode" style="width: 100%" placeholder="请选择">
          <a-select-option :value="1">日结</a-select-option>
          <a-select-option :value="2">月结</a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { OperationCenterApi } from '@/api/businessTeam/operation-center/OperationCenterApi';

export default {
  props: {
    visible: Boolean,
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: Object.assign({}, this.data),
      // 表单验证规则
      rules: {
        remitType: [{ required: true, message: '请选择结算方式' }],
        settleMode: [{ required: true, message: '请选择结算周期' }]
      },
      // 提交状态
      loading: false,
      isBatchEdit: true
    };
  },
  watch: {
    data() {
      if (this.data) {
        this.form = Object.assign({}, this.data);
        this.isBatchEdit = false;
      } else {
        this.form = {};
        this.isBatchEdit = true;
      }
      if (this.$refs.form) {
        this.$refs.form.clearValidate();
      }
    }
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      const params = {
        remitType: this.form.remitType,
        settleMode: this.form.settleMode
      };

      let result = null;

      // 执行编辑或修改方法
      if (this.isBatchEdit) {
        result = OperationCenterApi.batchEditSettleRemit(params);
      } else {
        params.id = this.form.id;
        result = OperationCenterApi.editSettleRemit(params);
      }

      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          if (this.isBatchEdit) {
            this.form = {};
          }

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    cancel() {
      this.form = Object.assign({}, this.data);
      this.$refs.form.clearValidate();
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
