<template>
  <a-modal
    :width="760"
    :visible="visible"
    :confirm-loading="loading"
    :title="modalTitle"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :class="{ 'ele-form-detail': optType === 2 }"
      :label-col="{ style: { width: '100px' } }"
    >
      <a-row :gutter="24">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="支付通道" name="channelCode">
            <a-select v-model:value="form.channelCode" class="ele-fluid" placeholder="请选择" allow-clear :disabled="optType !== 0">
              <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
                {{ channelName }}
              </a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="费率类型" name="rateType">
            <a-select v-model:value="form.rateType" style="width: 100%" placeholder="请选择" allow-clear :disabled="optType !== 0">
              <a-select-option :value="1">扫码费率</a-select-option>
              <a-select-option :value="2">刷卡费率</a-select-option>
              <a-select-option :value="3">EPOS费率</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="有效状态" name="validStatus" required>
            <a-switch :checkedValue="1" :un-checked-value="0" v-model:checked="form.validStatus" :disabled="isDisabled">
              <template #checkedChildren><check-outlined /></template>
              <template #unCheckedChildren><close-outlined /></template>
            </a-switch>
          </a-form-item>
        </a-col>
      </a-row>

      <div style="margin-bottom: 22px">
        <a-divider dashed />
      </div>

      <a-form
        ref="rateForm"
        :model="form"
        class="rate-form"
        :label-col="{ md: { span: 17 }, sm: { span: 24 } }"
        :wrapper-col="{ md: { span: 7 }, sm: { span: 24 } }"
      >
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item label="分润对公提现费率(%)" :name="['rateInfoDTO', 'pfWalletPubWdRate']" :rules="rules.rateValidate">
              <a-input v-model:value="form.rateInfoDTO.pfWalletPubWdRate" :placeholder="placeholder" :disabled="isDisabled" />
            </a-form-item>
            <a-form-item label="分润对私提现费率(%)" :name="['rateInfoDTO', 'pfWalletPriWdRate']" :rules="rules.rateValidate">
              <a-input v-model:value="form.rateInfoDTO.pfWalletPriWdRate" :placeholder="placeholder" :disabled="isDisabled" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item label="分润对公提现单笔费用(元)" :name="['rateInfoDTO', 'pfWalletPubWdSingleFee']" :rules="rules.amountValidate">
              <a-input v-model:value="form.rateInfoDTO.pfWalletPubWdSingleFee" :placeholder="placeholder" :disabled="isDisabled" />
            </a-form-item>
            <a-form-item label="分润对私提现单笔费用(元)" :name="['rateInfoDTO', 'pfWalletPriWdSingleFee']" :rules="rules.amountValidate">
              <a-input v-model:value="form.rateInfoDTO.pfWalletPriWdSingleFee" :placeholder="placeholder" :disabled="isDisabled" />
            </a-form-item>
          </a-col>
        </a-row>

        <div style="margin-bottom: 22px">
          <a-divider dashed />
        </div>

        <a-row :gutter="24">
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item label="代付对公提现费率(%)" :name="['rateInfoDTO', 'payWalletPubWdRate']" :rules="rules.rateValidate">
              <a-input v-model:value="form.rateInfoDTO.payWalletPubWdRate" :placeholder="placeholder" :disabled="isDisabled" />
            </a-form-item>
            <a-form-item label="代付对私提现费率(%)" :name="['rateInfoDTO', 'payWalletPriWdRate']" :rules="rules.rateValidate">
              <a-input v-model:value="form.rateInfoDTO.payWalletPriWdRate" :placeholder="placeholder" :disabled="isDisabled" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item label="代付对公提现单笔费用(元)" :name="['rateInfoDTO', 'payWalletPubWdSingleFee']" :rules="rules.amountValidate">
              <a-input v-model:value="form.rateInfoDTO.payWalletPubWdSingleFee" :placeholder="placeholder" :disabled="isDisabled" />
            </a-form-item>
            <a-form-item label="代付对私提现单笔费用(元)" :name="['rateInfoDTO', 'payWalletPriWdSingleFee']" :rules="rules.amountValidate">
              <a-input v-model:value="form.rateInfoDTO.payWalletPriWdSingleFee" :placeholder="placeholder" :disabled="isDisabled" />
            </a-form-item>
          </a-col>
        </a-row>

        <div style="margin-bottom: 22px">
          <a-divider dashed />
        </div>

        <a-row :gutter="24">
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item label="营销活动对公提现费率(%)" :name="['rateInfoDTO', 'rewardWalletPubWdRate']" :rules="rules.rateValidate">
              <a-input v-model:value="form.rateInfoDTO.rewardWalletPubWdRate" :placeholder="placeholder" :disabled="isDisabled" />
            </a-form-item>
            <a-form-item label="营销活动对私提现费率(%)" :name="['rateInfoDTO', 'rewardWalletPriWdRate']" :rules="rules.rateValidate">
              <a-input v-model:value="form.rateInfoDTO.rewardWalletPriWdRate" :placeholder="placeholder" :disabled="isDisabled" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item
              label="营销活动对公提现单笔费用(元)"
              :name="['rateInfoDTO', 'rewardWalletPubWdSingleFee']"
              :rules="rules.amountValidate"
            >
              <a-input v-model:value="form.rateInfoDTO.rewardWalletPubWdSingleFee" :placeholder="placeholder" :disabled="isDisabled" />
            </a-form-item>
            <a-form-item
              label="营销活动对私提现单笔费用(元)"
              :name="['rateInfoDTO', 'rewardWalletPriWdSingleFee']"
              :rules="rules.amountValidate"
            >
              <a-input v-model:value="form.rateInfoDTO.rewardWalletPriWdSingleFee" :placeholder="placeholder" :disabled="isDisabled" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-form>

    <template #footer v-if="isDisabled">
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { RateTemplateApi } from '@/api/businessTeam/rate-template/RateTemplateApi';
import { deepCopy } from '@/utils/util';

const rateReg = /^\d+(\.\d{1,4})?$/;
const amountReg = /^\d+(\.\d{1,2})?$/;

export default {
  props: {
    visible: Boolean,
    data: Object,
    channelCodes: Array,
    optType: Number // 0 添加; 1 编辑; 2 详情
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      modalTitle: this.optType === 2 ? '提现费率' + titleArr[this.optType] : titleArr[this.optType] + '提现费率',
      isDisabled: this.optType === 2,
      // 表单数据
      form: {
        validStatus: 1,
        templateType: 4,
        rateInfoDTO: {}
      },
      // 提交状态
      loading: false,
      placeholder: this.optType !== 2 ? '请填写' : '--',
      rules: {
        channelCode: [{ required: true, message: '请选择支付通道' }],
        limitType: [{ required: true, message: '请选择费率限制类型' }],
        rateType: [{ required: true, message: '请选择费率类型' }],
        rateValidate: [
          { required: true, message: '必填项' },
          { pattern: rateReg, message: '正数且小数点后最多四位', trigger: 'blur' }
        ],
        amountValidate: [
          { required: true, message: '必填项' },
          { pattern: amountReg, message: '正数且小数点后最多两位', trigger: 'blur' }
        ]
      }
    };
  },
  created() {
    if (this.data) {
      this.form = Object.assign({}, this.data);

      const { rateInfo } = this.data;
      if (rateInfo) {
        const formatRateInfo = JSON.parse(rateInfo);
        this.form.rateInfoDTO = formatRateInfo || {};
      }
      delete this.form['rateInfo'];
    }

    this.form.rateRatio = 100;
    const rateFields = ['withdrawRate', 'withdrawSingleFee'];
    rateFields.forEach(f => (this.form.rateInfoDTO[f] = 0));
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();
      await this.$refs.rateForm.validate();

      const submitParams = deepCopy(this.form);

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      if (this.optType) {
        result = RateTemplateApi.edit(submitParams);
      } else {
        result = RateTemplateApi.add(submitParams);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示修改成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    onChangeRateType() {
      if (this.optType === 0) {
        this.form.rateInfoDTO = {};
      }
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};

// 弹框标题
const titleArr = ['添加', '修改', '详情'];
</script>
<style scoped>
::v-deep(.rate-form .ant-form-item-label) {
  text-align: center;
  background-color: #f1f3f4;
}
</style>
