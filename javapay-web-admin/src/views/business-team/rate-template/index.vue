<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="支付通道">
              <a-select v-model:value="where.channelCode" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">{{
                  channelName
                }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="模板类型">
              <a-select v-model:value="where.templateType" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">大区费率模板</a-select-option>
                <a-select-option :value="2">商户费率模板</a-select-option>
                <a-select-option :value="3">商户费率限制</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="费率类型">
              <a-select v-model:value="where.rateType" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">扫码费率</a-select-option>
                <a-select-option :value="2">刷卡费率</a-select-option>
                <a-select-option :value="3">EPOS费率</a-select-option>
                <a-select-option :value="4">机构出款费率</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="费率限制类型">
              <a-select v-model:value="where.limitType" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">费率下限</a-select-option>
                <a-select-option :value="2">费率上限</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="状态">
              <a-select v-model:value="where.validStatus" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">有效</a-select-option>
                <a-select-option :value="0">无效</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- table上边工具栏 -->
          <template #toolbar>
            <a-space>
              <a-dropdown>
                <template #overlay>
                  <a-menu @click="handleAdd">
                    <a-menu-item key="1">大区费率模板</a-menu-item>
                    <a-menu-item key="2">商户费率模板</a-menu-item>
                    <a-menu-item key="3">商户费率限制</a-menu-item>
                  </a-menu>
                </template>
                <a-button type="primary">
                  <template #icon>
                    <plus-outlined />
                  </template>
                  <span>新建</span>
                  <DownOutlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'validStatus'">
              <a-tag color="success" v-if="record.validStatus === 1">有效</a-tag>
              <a-tag color="error" v-else>无效</a-tag>
            </template>

            <template v-if="column.key === 'templateType'">
              <a-tag v-if="record.templateType === 1" color="cyan">大区费率模板</a-tag>
              <a-tag v-else-if="record.templateType === 2" color="pink">商户费率模板</a-tag>
              <a-tag v-else-if="record.templateType === 3" color="blue">商户费率限制</a-tag>
            </template>

            <template v-if="column.key === 'rateType'">
              <a-tag v-if="record.rateType === 1">扫码费率</a-tag>
              <a-tag v-else-if="record.rateType === 2">刷卡费率</a-tag>
              <a-tag v-else-if="record.rateType === 3">EPOS费率</a-tag>
              <a-tag v-else-if="record.rateType === 4">机构出款费率</a-tag>
            </template>

            <template v-if="column.key === 'limitType'">
              <a-tag v-if="record.limitType === 1">下限</a-tag>
              <a-tag v-else-if="record.limitType === 2">上限</a-tag>
              <span v-else>--</span>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleEdit(record.id, 2, record.templateType)">详情</a>
                <a-divider type="vertical" />
                <a @click="handleEdit(record.id, 1, record.templateType)">修改</a>

                <template v-if="record.templateType === 1 && record.rateType === 3 && record.validStatus === 1">
                  <a-divider type="vertical" />
                  <a @click="bankCodesSyncHandle(record)">银行编码同步</a>
                </template>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 修改费率模板信息 -->
    <RateTemplateHandle
      v-if="showEditRateTemplate"
      v-model:visible="showEditRateTemplate"
      :data="current"
      :template-type="templateType"
      :opt-type="optType"
      :channelCodes="channelCodes"
      :bankList="bankList"
      @done="reload"
    />

    <WithdrawRateHandle
      v-if="showWithdrawRate"
      v-model:visible="showWithdrawRate"
      :data="current"
      :opt-type="optType"
      :channelCodes="channelCodes"
      @done="reload"
    />

    <!-- 银行编码同步 -->
    <BankCodeSync v-model:visible="showBankCodeSync" :data="current" @done="reload" />
  </div>
</template>

<script>
import RateTemplateHandle from './RateTemplateHandle.vue';
import WithdrawRateHandle from './WithdrawRateHandle.vue';
import { RateTemplateApi } from '@/api/businessTeam/rate-template/RateTemplateApi';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import { BankCodeManageApi } from '@/api/base/BankCodeManageApi';
import BankCodeSync from './BankCodeSync.vue';

export default {
  name: 'RateTemplate',
  components: {
    RateTemplateHandle,
    WithdrawRateHandle,
    BankCodeSync
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '费率编号',
          dataIndex: 'templateNo',
          align: 'center'
        },
        {
          title: '支付通道',
          dataIndex: 'channelCode',
          align: 'center',
          customRender: ({ text }) => {
            const item = this.channelCodes.find(c => c.channelCode === text);
            return item?.channelName || '--';
          }
        },
        {
          title: '模板类型',
          dataIndex: 'templateType',
          key: 'templateType',
          align: 'center'
        },
        {
          title: '费率限制类型',
          dataIndex: 'limitType',
          key: 'limitType',
          align: 'center'
        },
        {
          title: '费率类型',
          dataIndex: 'rateType',
          key: 'rateType',
          align: 'center'
        },
        {
          title: '有效状态',
          dataIndex: 'validStatus',
          key: 'validStatus',
          width: 100,
          align: 'center'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '修改人',
          dataIndex: 'lastModifyUserId'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right'
        }
      ],
      // 表格搜索条件
      where: {},
      current: null,
      showEditRateTemplate: false,
      showWithdrawRate: false,
      showBankCodeSync: false,
      channelCodes: [],
      bankList: [],
      optType: null,
      templateType: 1 //1:大区费率模版 2:商户费率模版 3:商户限制模版
    };
  },
  mounted() {
    this.getChannelList();
    this.getBankList();
  },
  methods: {
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    // 新增
    handleAdd({ key }) {
      this.optType = 0;
      this.current = null;

      this.templateType = Number(key);
      switch (this.templateType) {
        case 4:
          this.showWithdrawRate = true;
          break;
        default:
          this.showEditRateTemplate = true;
          break;
      }
    },

    // 修改，详情
    async handleEdit(id, optType, templateType = 0) {
      const data = await RateTemplateApi.detail({ id });
      this.current = data || {};
      this.optType = optType;

      this.templateType = templateType;
      switch (this.templateType) {
        case 4:
          this.showWithdrawRate = true;
          break;
        default:
          this.showEditRateTemplate = true;
          break;
      }
    },

    //银行编码同步
    bankCodesSyncHandle(row) {
      this.current = row;
      this.showBankCodeSync = true;
    },

    async datasource({ page, limit, where }) {
      const tableData = await RateTemplateApi.findPage({ ...where, pageNo: page, pageSize: limit });
      return tableData || [];
    },

    async getChannelList() {
      const data = await ChannelManageApi.list({ validStatus: 1 });
      this.channelCodes = data || [];
    },

    async getBankList() {
      const data = await BankCodeManageApi.list();
      this.bankList = data || [];
    }
  }
};
</script>
