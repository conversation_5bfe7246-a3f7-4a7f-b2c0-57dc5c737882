<template>
  <a-modal
    :width="760"
    :visible="visible"
    :confirm-loading="loading"
    :title="modalTitle"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :class="{ 'ele-form-detail': optType === 2 }"
      :label-col="{ style: { width: '110px' } }"
    >
      <a-row :gutter="24">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="支付通道" name="channelCode">
            <a-select v-model:value="form.channelCode" class="ele-fluid" placeholder="请选择" allow-clear :disabled="optType !== 0">
              <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
                {{ channelName }}
              </a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="费率类型" name="rateType">
            <a-select
              v-model:value="form.rateType"
              style="width: 100%"
              placeholder="请选择"
              allow-clear
              :disabled="optType !== 0"
              @change="onChangeRateType"
            >
              <a-select-option :value="1">扫码费率</a-select-option>
              <a-select-option :value="2">刷卡费率</a-select-option>
              <a-select-option :value="3">EPOS费率</a-select-option>
              <a-select-option :value="4" v-if="[1, 3].includes(templateType)">机构出款费率</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="有效状态" name="validStatus" required>
            <a-switch :checkedValue="1" :un-checked-value="0" v-model:checked="form.validStatus" :disabled="isDisabled">
              <template #checkedChildren><check-outlined /></template>
              <template #unCheckedChildren><close-outlined /></template>
            </a-switch>
          </a-form-item>

          <a-form-item label="费率限制类型" name="limitType" v-if="templateType === 3">
            <a-select v-model:value="form.limitType" style="width: 100%" placeholder="请选择" allow-clear :disabled="optType !== 0">
              <a-select-option :value="1">费率下限</a-select-option>
              <a-select-option :value="2">费率上限</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <div v-if="!isDisabled && form.rateType === 1">
        <a-divider orientation="left" orientation-margin="0px"> 请选择扫码费率字段 </a-divider>
        <a-transfer
          class="rate-transfer"
          v-model:target-keys="targetRateKeys"
          :data-source="rateTransferSource"
          :one-way="true"
          :titles="['未选中', '选中']"
          :render="item => item.title"
          :disabled="false"
          @change="handleChange"
        />
      </div>

      <a-form-item label="支持的银行" name="bankCodes" v-if="templateType === 1 && form.rateType === 3">
        <a-select
          v-model:value="form.bankCodes"
          mode="multiple"
          :filter-option="filterBank"
          showSearch
          style="width: 100%"
          placeholder="请选择"
          :disabled="isDisabled"
        >
          <a-select-option v-for="({ bankName, typeCode }, key) in bankList" :key="key" :value="typeCode">{{ bankName }} </a-select-option>
        </a-select>
      </a-form-item>

      <a-form
        ref="rateForm"
        :model="form"
        class="rate-form"
        :label-col="{ md: { span: 17 }, sm: { span: 24 } }"
        :wrapper-col="{ md: { span: 7 }, sm: { span: 24 } }"
      >
        <template v-if="form.rateType !== 4">
          <!--* 扫码交易(微信/支付宝/银联二维码) -->
          <template v-if="form.rateType === 1">
            <template v-if="targetRateKeys.length > 0">
              <a-divider orientation="left" :orientationMargin="0" dashed>线下收单</a-divider>
              <a-row
                v-for="(groupList, groupIndex) in Object.values(groupBy(rateTransferSource, 'group_id'))"
                :key="groupIndex"
                :gutter="24"
              >
                <template v-for="(groupItem, groupItemIndex) in groupList" :key="groupItemIndex">
                  <template v-if="targetRateKeys.includes(groupItem.key)">
                    <a-col :md="12" :sm="24" :xs="24">
                      <a-form-item :label="groupItem.title" :name="['rateInfoDTO', groupItem.key]" :rules="setItemRules(groupItem.key)">
                        <a-input v-model:value="form.rateInfoDTO[groupItem.key]" :placeholder="placeholder" :disabled="isDisabled" />
                      </a-form-item>
                    </a-col>
                    <a-col :md="12" :sm="24" :xs="24" v-if="groupItem.single === 1" />
                  </template>
                </template>
              </a-row>
            </template>
          </template>

          <!--* POS刷卡交易 -->
          <template v-if="form.rateType === 2">
            <a-divider orientation="left" :orientationMargin="0" dashed>POS收单交易</a-divider>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" :xs="24">
                <a-form-item
                  label="银联标准贷记卡费率(%)"
                  :name="['rateInfoDTO', 'creditRate']"
                  :rules="templateType === 1 ? rules.regionRateValidate : rules.rateValidate"
                >
                  <a-input v-model:value="form.rateInfoDTO.creditRate" :placeholder="placeholder" :disabled="isDisabled" />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24" :xs="24">
                <a-form-item
                  label="银联标准借记卡费率(%)"
                  :name="['rateInfoDTO', 'debitRate']"
                  :rules="templateType === 1 ? rules.regionRateValidate : rules.rateValidate"
                >
                  <a-input v-model:value="form.rateInfoDTO.debitRate" :placeholder="placeholder" :disabled="isDisabled" />
                </a-form-item>
              </a-col>

              <a-col :md="12" :sm="24" :xs="24">
                <a-form-item
                  label="银联标准借记卡手续费封顶(元)"
                  :name="['rateInfoDTO', 'debitFeeMax']"
                  :rules="templateType === 1 ? rules.regionFeeMax : rules.feeMax"
                >
                  <a-input v-model:value="form.rateInfoDTO.debitFeeMax" :placeholder="placeholder" :disabled="isDisabled" />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24" :xs="24">
                <a-form-item
                  label="银联云闪付贷记卡费率(%)"
                  :name="['rateInfoDTO', 'nfcCreditRate']"
                  :rules="templateType === 1 ? rules.regionRateValidate : rules.rateValidate"
                >
                  <a-input v-model:value="form.rateInfoDTO.nfcCreditRate" :placeholder="placeholder" :disabled="isDisabled" />
                </a-form-item>
              </a-col>

              <a-col :md="12" :sm="24" :xs="24">
                <a-form-item
                  label="银联云闪付借记卡费率(%)"
                  :name="['rateInfoDTO', 'nfcDebitRate']"
                  :rules="templateType === 1 ? rules.regionRateValidate : rules.rateValidate"
                >
                  <a-input v-model:value="form.rateInfoDTO.nfcDebitRate" :placeholder="placeholder" :disabled="isDisabled" />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24" :xs="24">
                <a-form-item
                  label="刷卡贷记卡D0附加费率(%)"
                  :name="['rateInfoDTO', 'creditD0Rate']"
                  :rules="templateType === 1 ? rules.regionRateValidate : rules.rateValidate"
                >
                  <a-input v-model:value="form.rateInfoDTO.creditD0Rate" :placeholder="placeholder" :disabled="isDisabled" />
                </a-form-item>
              </a-col>

              <a-col :md="12" :sm="24" :xs="24">
                <a-form-item
                  label="刷卡贷记卡D0单笔附加费用(元)"
                  :name="['rateInfoDTO', 'creditD0SingleFee']"
                  :rules="templateType === 1 ? rules.regionAmountValidate : rules.amountValidate"
                >
                  <a-input v-model:value="form.rateInfoDTO.creditD0SingleFee" :placeholder="placeholder" :disabled="isDisabled" />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24" :xs="24">
                <a-form-item
                  label="刷卡借记卡D0附加费率(%)"
                  :name="['rateInfoDTO', 'debitD0Rate']"
                  :rules="templateType === 1 ? rules.regionRateValidate : rules.rateValidate"
                >
                  <a-input v-model:value="form.rateInfoDTO.debitD0Rate" :placeholder="placeholder" :disabled="isDisabled" />
                </a-form-item>
              </a-col>

              <a-col :md="12" :sm="24" :xs="24">
                <a-form-item
                  label="刷卡借记卡D0附加单笔费用(元)"
                  :name="['rateInfoDTO', 'debitD0SingleFee']"
                  :rules="templateType === 1 ? rules.regionAmountValidate : rules.amountValidate"
                >
                  <a-input v-model:value="form.rateInfoDTO.debitD0SingleFee" :placeholder="placeholder" :disabled="isDisabled" />
                </a-form-item>
              </a-col>
            </a-row>
          </template>

          <!--* 无卡支付 -->
          <template v-if="form.rateType === 3">
            <a-divider orientation="left" :orientationMargin="0" dashed>无卡交易</a-divider>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" :xs="24">
                <a-form-item
                  label="无卡贷记卡费率(%)"
                  :name="['rateInfoDTO', 'creditEposRate']"
                  :rules="templateType === 1 ? rules.regionRateValidate : rules.rateValidate"
                >
                  <a-input v-model:value="form.rateInfoDTO.creditEposRate" :placeholder="placeholder" :disabled="isDisabled" />
                </a-form-item>
              </a-col>

              <a-col :md="12" :sm="24" :xs="24">
                <a-form-item
                  label="无卡借记卡费率(%)"
                  :name="['rateInfoDTO', 'debitEposRate']"
                  :rules="templateType === 1 ? rules.regionRateValidate : rules.rateValidate"
                >
                  <a-input v-model:value="form.rateInfoDTO.debitEposRate" :placeholder="placeholder" :disabled="isDisabled" />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24" :xs="24">
                <a-form-item
                  label="无卡贷记卡D0附加费率(%)"
                  :name="['rateInfoDTO', 'creditEposD0Rate']"
                  :rules="templateType === 1 ? rules.regionRateValidate : rules.rateValidate"
                >
                  <a-input v-model:value="form.rateInfoDTO.creditEposD0Rate" :placeholder="placeholder" :disabled="isDisabled" />
                </a-form-item>
              </a-col>

              <a-col :md="12" :sm="24" :xs="24">
                <a-form-item
                  label="无卡贷记卡D0附加单笔费用(元)"
                  :name="['rateInfoDTO', 'creditEposD0SingleFee']"
                  :rules="templateType === 1 ? rules.regionAmountValidate : rules.amountValidate"
                >
                  <a-input v-model:value="form.rateInfoDTO.creditEposD0SingleFee" :placeholder="placeholder" :disabled="isDisabled" />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :md="12" :sm="24" :xs="24">
                <a-form-item
                  label="无卡借记卡D0附加费率(%)"
                  :name="['rateInfoDTO', 'debitEposD0Rate']"
                  :rules="templateType === 1 ? rules.regionRateValidate : rules.rateValidate"
                >
                  <a-input v-model:value="form.rateInfoDTO.debitEposD0Rate" :placeholder="placeholder" :disabled="isDisabled" />
                </a-form-item>
              </a-col>

              <a-col :md="12" :sm="24" :xs="24">
                <a-form-item
                  label="无卡借记卡D0附加单笔费用(元)"
                  :name="['rateInfoDTO', 'debitEposD0SingleFee']"
                  :rules="templateType === 1 ? rules.regionAmountValidate : rules.amountValidate"
                >
                  <a-input v-model:value="form.rateInfoDTO.debitEposD0SingleFee" :placeholder="placeholder" :disabled="isDisabled" />
                </a-form-item>
              </a-col>
            </a-row>
          </template>
        </template>

        <template v-if="form.rateType === 4">
          <a-divider orientation="left" :orientationMargin="0" dashed>机构出款费率</a-divider>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="分润对公提现税率(%)" :name="['rateInfoDTO', 'pfWalletPubWdRate']" :rules="rules.regionRateValidate">
                <a-input v-model:value="form.rateInfoDTO.pfWalletPubWdRate" :placeholder="placeholder" :disabled="isDisabled" />
              </a-form-item>
              <a-form-item label="分润对私提现税率(%)" :name="['rateInfoDTO', 'pfWalletPriWdRate']" :rules="rules.regionRateValidate">
                <a-input v-model:value="form.rateInfoDTO.pfWalletPriWdRate" :placeholder="placeholder" :disabled="isDisabled" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item
                label="分润对公提现单笔费用(元)"
                :name="['rateInfoDTO', 'pfWalletPubWdSingleFee']"
                :rules="rules.regionAmountValidate"
              >
                <a-input v-model:value="form.rateInfoDTO.pfWalletPubWdSingleFee" :placeholder="placeholder" :disabled="isDisabled" />
              </a-form-item>
              <a-form-item
                label="分润对私提现单笔费用(元)"
                :name="['rateInfoDTO', 'pfWalletPriWdSingleFee']"
                :rules="rules.regionAmountValidate"
              >
                <a-input v-model:value="form.rateInfoDTO.pfWalletPriWdSingleFee" :placeholder="placeholder" :disabled="isDisabled" />
              </a-form-item>
            </a-col>
          </a-row>

          <div style="margin-bottom: 22px">
            <a-divider dashed />
          </div>

          <a-row :gutter="24">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="代付对公提现税率(%)" :name="['rateInfoDTO', 'payWalletPubWdRate']" :rules="rules.regionRateValidate">
                <a-input v-model:value="form.rateInfoDTO.payWalletPubWdRate" :placeholder="placeholder" :disabled="isDisabled" />
              </a-form-item>
              <a-form-item label="代付对私提现税率(%)" :name="['rateInfoDTO', 'payWalletPriWdRate']" :rules="rules.regionRateValidate">
                <a-input v-model:value="form.rateInfoDTO.payWalletPriWdRate" :placeholder="placeholder" :disabled="isDisabled" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item
                label="代付对公提现单笔费用(元)"
                :name="['rateInfoDTO', 'payWalletPubWdSingleFee']"
                :rules="rules.regionAmountValidate"
              >
                <a-input v-model:value="form.rateInfoDTO.payWalletPubWdSingleFee" :placeholder="placeholder" :disabled="isDisabled" />
              </a-form-item>
              <a-form-item
                label="代付对私提现单笔费用(元)"
                :name="['rateInfoDTO', 'payWalletPriWdSingleFee']"
                :rules="rules.regionAmountValidate"
              >
                <a-input v-model:value="form.rateInfoDTO.payWalletPriWdSingleFee" :placeholder="placeholder" :disabled="isDisabled" />
              </a-form-item>
            </a-col>
          </a-row>

          <div style="margin-bottom: 22px">
            <a-divider dashed />
          </div>

          <a-row :gutter="24">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item
                label="营销活动对公提现税率(%)"
                :name="['rateInfoDTO', 'rewardWalletPubWdRate']"
                :rules="rules.regionRateValidate"
              >
                <a-input v-model:value="form.rateInfoDTO.rewardWalletPubWdRate" :placeholder="placeholder" :disabled="isDisabled" />
              </a-form-item>
              <a-form-item
                label="营销活动对私提现税率(%)"
                :name="['rateInfoDTO', 'rewardWalletPriWdRate']"
                :rules="rules.regionRateValidate"
              >
                <a-input v-model:value="form.rateInfoDTO.rewardWalletPriWdRate" :placeholder="placeholder" :disabled="isDisabled" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item
                label="营销活动对公提现单笔费用(元)"
                :name="['rateInfoDTO', 'rewardWalletPubWdSingleFee']"
                :rules="rules.regionAmountValidate"
              >
                <a-input v-model:value="form.rateInfoDTO.rewardWalletPubWdSingleFee" :placeholder="placeholder" :disabled="isDisabled" />
              </a-form-item>
              <a-form-item
                label="营销活动对私提现单笔费用(元)"
                :name="['rateInfoDTO', 'rewardWalletPriWdSingleFee']"
                :rules="rules.regionAmountValidate"
              >
                <a-input v-model:value="form.rateInfoDTO.rewardWalletPriWdSingleFee" :placeholder="placeholder" :disabled="isDisabled" />
              </a-form-item>
            </a-col>
          </a-row>
        </template>
      </a-form>
    </a-form>

    <template #footer v-if="isDisabled">
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>

  <!-- 商户选择弹窗 -->
  <BankCodeSelect v-if="showBankCodeList" v-model:visible="showBankCodeList" @done="setBankCodeInfo" />
</template>

<script>
import { message } from 'ant-design-vue';
import { RateTemplateApi } from '@/api/businessTeam/rate-template/RateTemplateApi';
import { deepCopy } from '@/utils/util';
import BankCodeSelect from '../../aisleManage/aisle-rate/BankCodeSelect.vue';
import { groupBy } from 'lodash-es';

const rateReg = /^\d+(\.\d{1,3})?$/;
const amountReg = /^\d+(\.\d{1})?$/;
const feeMaxReg = /^(0|[1-9][0-9]*)$/;
//大区的规则
const regionRateReg = /^\d+(\.\d{1,4})?$/;
const regionAmountReg = /^\d+(\.\d{1,2})?$/;
const regionFeeMaxReg = /^\d+(\.\d{1,2})?$/;

// 扫码相关的字段
const allScanRateField = [
  { field: 'creditRate', name: '银联标准贷记卡费率(%)', group_id: 1, single: 1 },
  { field: 'debitRate', name: '银联标准借记卡费率(%)', group_id: 1 },
  { field: 'debitFeeMax', name: '银联标准借记卡手续费封顶(元)', group_id: 1 },
  { field: 'nfcCreditRate', name: '银联云闪付贷记卡费率(%)', group_id: 2 },
  { field: 'nfcDebitRate', name: '银联云闪付借记卡费率(%)', group_id: 2 },
  { field: 'aliPayRate', name: '支付宝扫码费率(%)', group_id: 3 },
  { field: 'aliPayLargeRate', name: '支付宝大额费率(%)', group_id: 3 },
  { field: 'wechatRate', name: '微信扫码费率(%)', group_id: 3 },
  { field: 'creditQrD0Rate', name: '贷记卡D0附加费率(%)', group_id: 4 },
  { field: 'creditQrD0SingleFee', name: '贷记卡D0附加单笔(元)', group_id: 4 },
  { field: 'debitQrD0Rate', name: '借记卡D0附加费率(%)', group_id: 4 },
  { field: 'debitQrD0SingleFee', name: '借记卡D0附加单笔(元)', group_id: 4 }
];

const rateTransferSourceMap = [];
allScanRateField.forEach(item => {
  rateTransferSourceMap.push({
    key: item.field,
    title: item.name,
    disabled: false,
    valueType: item.field.endsWith('Rate') ? 'rate' : 'amount',
    group_id: item.group_id,
    single: item.single
  });
});

export default {
  name: 'RateTemplateHandle',
  components: {
    BankCodeSelect
  },
  props: {
    visible: Boolean,
    data: Object,
    bankList: Array,
    channelCodes: Array,
    optType: Number, // 0 添加; 1 编辑; 2 详情
    templateType: Number //1:大区费率模版 2:商户费率模版 3:商户限制模版
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      rateTransferSource: rateTransferSourceMap,
      targetRateKeys: [],
      modalTitle:
        this.optType === 2
          ? templateTypeArr[this.templateType - 1] + titleArr[this.optType]
          : titleArr[this.optType] + templateTypeArr[this.templateType - 1],
      isDisabled: this.optType === 2,
      // 表单数据
      form: {
        validStatus: 1,
        templateType: 0,
        rateInfoDTO: {},
        bankCodes: []
      },
      // 提交状态
      loading: false,
      //是否展示银行编码选择
      showBankCodeList: false,
      placeholder: this.optType !== 2 ? '请填写' : '--',
      rules: {
        channelCode: [{ required: true, message: '请选择支付通道' }],
        limitType: [{ required: true, message: '请选择费率限制类型' }],
        rateType: [{ required: true, message: '请选择费率类型' }],
        rateValidate: [
          { required: true, message: '必填项' },
          { pattern: rateReg, message: '正数且小数点后最多三位', trigger: 'blur' },
          {
            validator: async (rule, value) => {
              if (value) {
                const dotAfterValue = value.toString().split('.')[1];
                if (dotAfterValue?.length === 3) {
                  if (['0', '5'].includes([...dotAfterValue].pop())) {
                    return Promise.resolve();
                  }
                  return Promise.reject('小数点后第三位必须为0或5');
                } else {
                  return Promise.resolve();
                }
              } else {
                return Promise.resolve();
              }
            },
            trigger: 'blur'
          }
        ],
        feeMax: [
          { required: true, message: '必填项' },
          { pattern: feeMaxReg, message: '整数', trigger: 'blur' }
        ],
        amountValidate: [
          { required: true, message: '必填项' },
          { pattern: amountReg, message: '正数且小数点后最多一位', trigger: 'blur' }
        ],
        //大区相关规则
        regionRateValidate: [
          { required: true, message: '必填项' },
          { pattern: regionRateReg, message: '正数且小数点后最多四位', trigger: 'blur' }
        ],
        regionFeeMax: [
          { required: true, message: '必填项' },
          { pattern: regionFeeMaxReg, message: '正数且小数点后最多两位', trigger: 'blur' }
        ],
        regionAmountValidate: [
          { required: true, message: '必填项' },
          { pattern: regionAmountReg, message: '正数且小数点后最多两位', trigger: 'blur' }
        ]
      }
    };
  },
  created() {
    if (this.data) {
      this.form = Object.assign({}, this.data);

      const { rateInfo } = this.data;
      if (rateInfo) {
        const formatRateInfo = JSON.parse(rateInfo);
        this.form.rateInfoDTO = formatRateInfo || {};
      }
      delete this.form['rateInfo'];

      //有数据的话先将字符串转换成数组类型显示
      this.form.bankCodes = this.form.bankCodes ? this.form.bankCodes.split(',') : [];

      if (this.form.rateType === 1) {
        // this.setRateDefValue();

        const rateKeys = Object.keys(this.form.rateInfoDTO);
        this.targetRateKeys = this.rateTransferSource.filter(item => rateKeys.includes(item.key)).map(item => item.key);
      }
    }

    this.form.rateRatio = 100;
    this.form.templateType = this.templateType;
    const rateFields = ['withdrawRate', 'withdrawSingleFee'];
    rateFields.forEach(f => (this.form.rateInfoDTO[f] = 0));
  },
  methods: {
    groupBy,

    setItemRules(field) {
      if (field.endsWith('Rate')) {
        return this.templateType === 1 ? this.rules.regionRateValidate : this.rules.rateValidate;
      } else if (field.endsWith('FeeMax')) {
        return this.templateType === 1 ? this.rules.regionFeeMax : this.rules.feeMax;
      } else {
        return this.templateType === 1 ? this.rules.regionAmountValidate : this.rules.amountValidate;
      }
    },

    handleChange(nextTargetKeys, direction, moveKeys) {
      console.log('targetKeys: ', nextTargetKeys);
      console.log('direction: ', direction);
      console.log('moveKeys: ', moveKeys);
    },

    onInputChangeRate(e) {
      const value = e.target.value;

      const rateKeys = ['nfcCreditRate', 'nfcDebitRate', 'wechatRate', 'aliPayRate'];
      rateKeys.forEach(key => {
        this.form.rateInfoDTO[key] = value;
      });
    },

    setRateDefValue() {
      this.onInputChangeRate({ target: { value: this.form.rateInfoDTO.wechatRate } });

      const rateKeys = ['creditQrD0Rate', 'debitQrD0Rate', 'creditQrD0SingleFee', 'debitQrD0SingleFee'];
      rateKeys.forEach(key => {
        this.form.rateInfoDTO[key] = 0;
      });
    },

    //对银行选择 名称的模糊查询
    filterBank(input, option) {
      const bank = this.bankList.find(bank => bank.typeCode === option.value);
      if (bank) {
        return bank.bankName.toLowerCase().includes(input.toLowerCase());
      }
      return false;
    },

    async save() {
      // 校验表单
      await this.$refs.form.validate();
      await this.$refs.rateForm.validate();

      //深拷贝之后，下面的提交数据都用 submitParams 来处理
      const submitParams = deepCopy(this.form);

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      submitParams.bankCodes = submitParams.bankCodes.join(',');

      submitParams.rateInfoDTO.rateType = submitParams.rateType;
      if (submitParams.rateType === 3) {
        submitParams.rateInfoDTO.bankCodes = submitParams.bankCodes;
      }
      if (submitParams.rateType === 1) {
        const excludeFields = ['withdrawRate', 'withdrawSingleFee', 'rateType'];
        const allFileds = [...excludeFields, ...this.targetRateKeys];
        const rateInfoDTO = {};
        allFileds.forEach(f => {
          rateInfoDTO[f] = submitParams.rateInfoDTO[f];
        });
        submitParams.rateInfoDTO = rateInfoDTO;
      }

      if (this.optType) {
        result = RateTemplateApi.edit(submitParams);
      } else {
        result = RateTemplateApi.add(submitParams);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示修改成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    onChangeRateType() {
      if (this.optType === 0) {
        this.form.rateInfoDTO = {};
        //这两个是默认值，清空的时候要添加上
        const rateFields = ['withdrawRate', 'withdrawSingleFee'];
        rateFields.forEach(f => (this.form.rateInfoDTO[f] = 0));

        if (this.form.rateType === 1) {
          // this.setRateDefValue();
          this.targetRateKeys = [];
        }
      }
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};

const templateTypeArr = ['大区费率模版', '商户费率模版', '商户费率限制'];
// 弹框标题
const titleArr = ['添加', '修改', '详情'];
</script>
<style scoped>
::v-deep(.rate-form .ant-form-item-label) {
  text-align: center;
  background-color: #f1f3f4;
}

::v-deep(.rate-transfer .ant-transfer-list) {
  flex: 1;
  height: 250px;
}

::v-deep(.rate-transfer .ant-transfer-operation) {
  flex-shrink: 0;
}
</style>
