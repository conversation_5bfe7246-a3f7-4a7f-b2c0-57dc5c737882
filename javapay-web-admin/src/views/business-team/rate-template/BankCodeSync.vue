<template>
  <a-modal
    :width="600"
    :visible="visible"
    :confirm-loading="loading"
    title="银行编码同步"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @cancel="cancel"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 7 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 17 }, sm: { span: 24 } }"
    >
      <a-form-item label="同步对象" name="bankCodeSyncType">
        <a-radio-group v-model:value="form.bankCodeSyncType" name="bankCodeSyncType">
          <a-radio :value="1">费率政策</a-radio>
          <a-radio :value="2">用户费率</a-radio>
        </a-radio-group>
      </a-form-item>
    </a-form>
  </a-modal>
</template>
    
    <script>
import { message } from 'ant-design-vue';
import { RateTemplateApi } from '@/api/businessTeam/rate-template/RateTemplateApi';

export default {
  name: 'BankCodeSync',
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {},
      // 提交状态
      loading: false,
      // 表单验证规则
      rules: {
        bankCodeSyncType: [{ required: true, message: '请选择是银行编码同步类型' }]
      }
    };
  },
  watch: {
    data() {
      if (this.data) {
        this.form = Object.assign({}, this.data);
      }
      if (this.$refs.form) {
        this.$refs.form.clearValidate();
      }
    }
  },
  methods: {
    /**
     * 保存和编辑
     */
    async save() {
      // 校验表单
      await this.$refs.form.validate();
      // 修改加载框为正在加载
      this.loading = true;
      let result = null;
      // 执行编辑
      if (this.form.bankCodeSyncType === 1) {
        //同步到费率政策
        result = RateTemplateApi.bankCodesSyncToPolicy({
          id: this.form.id
        });
      } else {
        //同步到用户费率
        result = RateTemplateApi.bankCodesSyncToRate({
          id: this.form.id
        });
      }

      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    cancel() {
      this.$refs.form.clearValidate();
      this.form = Object.assign({}, this.data);
    },

    /**
     * 更新编辑界面的弹框是否显示
     *
     * @param value true-显示，false-隐藏
     */
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
    
    <style>
.coupon-margin {
  margin-top: 20px;
}

.coupon-bottom {
  margin-bottom: 50px;
}

.coupon-div-left {
  margin-left: 48px;
}
</style>
    