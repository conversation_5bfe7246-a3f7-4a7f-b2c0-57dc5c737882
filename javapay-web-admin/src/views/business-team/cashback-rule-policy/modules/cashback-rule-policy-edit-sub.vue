<template>
  <a-modal
    :width="800"
    :visible="visible"
    :confirm-loading="loading"
    title="编辑直属下级政策"
    :mask-closable="false"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules" layout="vertical">
      <a-row :gutter="24">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="返现政策类型" name="cashbackType">
            <a-select v-model:value="form.cashbackType" style="width: 100%" placeholder="请选择" @change="onChangeCashbackType">
              <a-select-option :value="0">激活返现政策</a-select-option>
              <a-select-option :value="1">达标返现政策</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="选择下级机构" name="orgNoList">
            <a-select
              v-model:value="form.orgNoList"
              mode="tags"
              style="width: 100%"
              placeholder="选择下级机构"
              :open="false"
              :disabled="!isCashbackTypeHasVal"
              @focus="showAgentSelect = true"
            />
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="选择返现政策" name="policyNo">
            <a-select
              v-model:value="form.policyNo"
              style="width: 100%"
              placeholder="请选择"
              :disabled="!isCashbackTypeHasVal"
              @change="onChangePolicy"
            >
              <a-select-option v-for="({ policyName, policyNo }, key) in policyOptions" :key="key" :value="policyNo">
                {{ `${policyName}(${policyNo})` }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <template v-if="form.policyNo">
        <div style="margin: 0 0 20px">
          <a-divider dashed />
        </div>

        <div>
          <a-typography-text keyboard>
            统计周期:从
            <a-typography-text underline>终端绑定后</a-typography-text>
            算起
          </a-typography-text>
        </div>

        <a-table :columns="columns" :data-source="form.policyDataSource" bordered :pagination="false" size="middle">
          <template #bodyCell="{ column, text, record, index }">
            <template v-if="tableEditKeys.includes(column.dataIndex)">
              <div>
                <a-form-item
                  :name="['policyDataSource', index, column.dataIndex]"
                  :rules="rules[column.dataIndex] || []"
                  class="table-edit-form__item"
                >
                  <a-input-number
                    v-if="editableData[record.key]"
                    v-model:value="editableData[record.key][column.dataIndex]"
                    placeholder="必填"
                    :min="0"
                    style="margin: -5px 0; width: 100%"
                  />
                  <template v-else>
                    {{ text }}
                  </template>
                </a-form-item>
              </div>
            </template>
            <!-- 操作栏 -->
            <template v-else-if="column.dataIndex === 'operation'">
              <div class="editable-row-operations">
                <span v-if="editableData[record.key]">
                  <a @click="updateRow(record.key, index)" class="ele-text-danger">保存</a>
                </span>
                <span v-else>
                  <a @click="editRow(record.key)">编辑</a>
                </span>
              </div>
            </template>
          </template>
        </a-table>
      </template>
    </a-form>

    <AgentSelect v-if="showAgentSelect" v-model:visible="showAgentSelect" :where="agentWhere" @done="onConfirmSelectAgent" />
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { useUserStore } from '@/store/modules/user';
import { CashbackRulePolicyApi } from '@/api/businessTeam/cashback-rule-policy/CashbackRulePolicyApi';
import AgentSelect from './agent-select.vue';
import { cloneDeep, isEmpty } from 'lodash-es';

export default {
  components: { AgentSelect },
  props: {
    visible: Boolean,
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {},
      // 表单验证规则
      rules: {
        orgNoList: [{ required: true, message: '请选择' }],
        cashbackAmount: [{ required: true, message: '请填写', trigger: 'submit' }],
        cashbackType: [{ required: true, message: '请选择' }],
        policyNo: [{ required: true, message: '请选择' }]
      },
      loading: false,
      isUpdate: false,
      showAgentSelect: false,

      tableEditKeys: ['cashbackAmount'],
      columns: [
        {
          title: '统计周期-开始天数',
          dataIndex: 'timeCycleStart',
          width: '20%',
          align: 'center'
        },
        {
          title: '统计周期-结束天数',
          dataIndex: 'timeCycleEnd',
          width: '20%',
          align: 'center'
        },
        {
          title: '交易标准金额(元)',
          dataIndex: 'tradeVolume',
          width: '20%',
          align: 'center'
        },
        {
          title: '返现金额(元)',
          dataIndex: 'cashbackAmount',
          width: '20%',
          align: 'center',
          defaultFilteredValue: [0],
          onFilter: (value, record) => {
            return Number(record.cashbackAmount) > value;
          }
        },
        {
          title: '操作',
          dataIndex: 'operation',
          align: 'center'
        }
      ],
      editableData: {},
      policyKey: 0,
      policyOptions: []
    };
  },
  computed: {
    agentWhere() {
      return {
        fetchType: 1,
        cashbackType: this.form.cashbackType,
        openStatus: true
      };
    },
    // 当前登录用户信息
    loginUser() {
      const userStore = useUserStore();
      return userStore.$state.info;
    },
    isCashbackTypeHasVal() {
      return [0, 1].includes(this.form.cashbackType);
    }
  },
  methods: {
    /** 变更政策类型 */
    onChangeCashbackType() {
      this.form.policyNo = null;
      this.form.policyDataSource = [];
      this.form.orgNoList = [];
      this.getPolicyOptions();
    },

    /** 变更政策 */
    onChangePolicy() {
      this.getPolicyDataSource();
    },

    onConfirmSelectAgent(rows) {
      this.form.orgNoList = rows.map(i => i.agentNo);
    },

    async getPolicyOptions() {
      this.policyOptions = [];
      const data = await CashbackRulePolicyApi.policyNameList({ cashbackType: this.form.cashbackType, orgNo: this.loginUser.orgCode });
      this.policyOptions = data || [];
    },

    /** 获取返现规则列表 */
    async getPolicyDataSource() {
      this.form.policyDataSource = [];

      let data = await CashbackRulePolicyApi.list({
        cashbackType: this.form.cashbackType,
        orgNo: this.loginUser.orgCode,
        policyNo: this.form.policyNo
      });
      data = data || [];
      data.forEach((item, index) => {
        item.key = -(index + 1);
      });

      this.form.policyDataSource = data;
      this.form.cashCalculateType = data[0]?.cashCalculateType;
    },

    editRow(key) {
      this.editableData[key] = cloneDeep(this.form.policyDataSource.filter(item => key === item.key)[0]);
    },

    async updateRow(key, index) {
      Object.assign(this.form.policyDataSource.filter(item => key === item.key)[0], this.editableData[key]);

      const validateFields = this.tableEditKeys.map(field => {
        return ['policyDataSource', index, field];
      });
      await this.$refs.form.validateFields(validateFields).catch(() => {
        return Promise.reject('返现规则校验失败');
      });

      delete this.editableData[key];
    },

    async save() {
      // 校验表单
      await this.$refs.form.validate();

      if (!isEmpty(this.editableData)) {
        return message.warn(`存在未保存的返现规则`);
      }

      // 修改加载框为正在加载
      this.loading = true;

      const params = cloneDeep(this.form);
      params.rulePolicyList = params.policyDataSource;
      delete params['policyDataSource'];

      CashbackRulePolicyApi.subAgentEdit(params)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
<style lang="less" scoped>
.editable-row-operations a {
  margin-right: 8px;
}

:deep(.table-edit-form__item) {
  margin-bottom: 0;
  .ant-form-item-explain {
    display: none;
  }
}
</style>
