<template>
  <a-modal :width="800" :visible="visible" title="详情" :body-style="{ paddingBottom: '8px' }" @update:visible="updateVisible">
    <a-descriptions :column="2" bordered>
      <a-descriptions-item label="机构编号">{{ form.orgNo }}</a-descriptions-item>
      <a-descriptions-item label="上级机构编号">{{ form.parentOrgNo }}</a-descriptions-item>
      <a-descriptions-item label="政策名称">{{ form.policyName }}</a-descriptions-item>
      <a-descriptions-item label="政策编号">{{ form.policyNo }}</a-descriptions-item>
      <a-descriptions-item label="返现政策类型">
        <a-tag v-if="form.cashbackType === 0" color="purple">激活返现政策</a-tag>
        <a-tag v-else-if="form.cashbackType === 1" color="blue">达标返现政策</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="达标计算方式">
        <a-tag v-if="form.cashCalculateType === 0" color="purple">交易量优先</a-tag>
        <a-tag v-else-if="form.cashCalculateType === 1" color="blue">时间优先</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>
    <!-- 规则 -->
    <a-table
      :columns="columns"
      :data-source="form.policyDataSource"
      bordered
      :pagination="false"
      size="middle"
      :scroll="{ x: 'max-content' }"
    />
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { onMounted, reactive, toRefs } from 'vue';
import { hasPurview } from '@/utils/permission';
import { CashbackRulePolicyApi } from '@/api/businessTeam/cashback-rule-policy/CashbackRulePolicyApi';

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {
        policyDataSource: []
      },
      columns: [
        {
          title: '统计周期-开始天数',
          dataIndex: 'timeCycleStart',
          align: 'center'
        },
        {
          title: '统计周期-结束天数',
          dataIndex: 'timeCycleEnd',
          align: 'center'
        },
        {
          title: '交易标准金额(元)',
          dataIndex: 'tradeVolume',
          align: 'center'
        },
        {
          title: '返现金额(元)',
          dataIndex: 'cashbackAmount',
          align: 'center',
          defaultFilteredValue: [0],
          onFilter: (value, record) => {
            return Number(record.cashbackAmount) > value;
          }
        },
        {
          title: '版本号',
          dataIndex: 'version',
          align: 'center'
        }
      ]
    });

    onMounted(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);

        getPolicyDataSource();
      }
    });

    /** 获取返现规则列表 */
    async function getPolicyDataSource() {
      let res = await CashbackRulePolicyApi.list({
        cashbackType: data.form.cashbackType,
        orgNo: data.form.orgNo,
        policyNo: data.form.policyNo
      });
      res = res || [];

      data.form.policyDataSource = res;
    }

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      updateVisible,
      hasPurview
    };
  }
};
</script>
