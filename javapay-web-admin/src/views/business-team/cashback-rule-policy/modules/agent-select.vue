<template>
  <a-modal :width="800" :visible="visible" title="选择下级" :mask-closable="false" @update:visible="updateVisible" @ok="save">
    <a-badge status="processing" :text="`${CashbackTypeMap.get(where.cashbackType)}开通状态`" />

    <div style="padding: 24px 24px 0" v-if="showTips && where.openStatus">
      <a-alert :message="`暂无${where.openStatus ? '已' : '未'}开通下级`" type="warning" show-icon />
    </div>

    <!-- 表格内容 -->
    <div>
      <a-card :bordered="false">
        <ele-pro-table
          ref="table"
          row-key="agentNo"
          :pagination="false"
          :need-page="false"
          :datasource="datasource"
          :columns="columns"
          v-model:selection="selectedRows"
          :toolbar="false"
        >
          <!-- 表体的操作 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'openSt'">
              <a-tag v-if="record.openSt" color="green">已开通</a-tag>
              <a-tag v-else>未开通</a-tag>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>
  </a-modal>
</template>

<script>
import { CashbackRulePolicyApi } from '@/api/businessTeam/cashback-rule-policy/CashbackRulePolicyApi';

const CashbackTypeMap = new Map([
  [0, '激活返现政策'],
  [1, '达标返现政策']
]);

export default {
  props: {
    visible: Boolean,
    where: {
      type: Object,
      default() {
        return {
          // 返现政策类型 (0-激活返现政策 1-达标返现政策)
          cashbackType: null,
          // 此返现政策类型 代理商开通状态 (true-已开通 false-未开通)
          openStatus: null
        };
      }
    }
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      showTips: false,
      CashbackTypeMap,
      //选择数据
      selectedRows: [],
      // 表格列配置
      columns: [
        {
          title: '机构名称',
          dataIndex: 'agentName'
        },
        {
          title: '机构编号',
          dataIndex: 'agentNo'
        },
        {
          title: '状态',
          dataIndex: 'openSt',
          key: 'openSt',
          align: 'center'
        }
      ]
    };
  },
  methods: {
    save() {
      this.$emit('done', this.selectedRows);
      this.updateVisible(false);
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    },

    async datasource() {
      let tableData = await CashbackRulePolicyApi.getDirectAgentOpenSt({
        cashbackType: this.where.cashbackType,
        fetchType: this.where.fetchType,
        policyNo: this.where.policyNo || ''
      });
      tableData = tableData || [];
      const filterTableData = tableData.filter(i => i.openSt === this.where.openStatus);
      this.showTips = !filterTableData.length;
      return filterTableData;
    }
  }
};
</script>
