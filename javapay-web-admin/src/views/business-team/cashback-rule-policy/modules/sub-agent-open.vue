<template>
  <a-modal
    :width="800"
    :visible="visible"
    :confirm-loading="loading"
    title="开通直属下级政策"
    :mask-closable="false"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules" layout="vertical">
      <!-- 基本信息录入 -->
      <a-row :gutter="24">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="返现政策类型" name="cashbackType">
            <a-select v-model:value="form.cashbackType" style="width: 100%" placeholder="请选择" @change="onChangeCashbackType">
              <a-select-option :value="0">激活返现政策</a-select-option>
              <a-select-option :value="1">达标返现政策</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="选择下级机构" name="orgNoList">
            <a-select
              v-model:value="form.orgNoList"
              mode="tags"
              style="width: 100%"
              placeholder="选择下级机构"
              :open="false"
              :disabled="!form.policyNo"
              @focus="showAgentSelect = true"
            />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="选择开通政策" name="policyNo">
            <a-select
              v-model:value="form.policyNo"
              style="width: 100%"
              placeholder="请选择"
              :disabled="!isCashbackTypeHasVal"
              @change="onChangePolicy"
            >
              <a-select-option v-for="({ policyName, policyNo }, key) in policyOptions" :key="key" :value="policyNo">
                {{ `${policyName}(${policyNo})` }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <div style="margin: 0 0 20px">
        <a-divider dashed />
      </div>

      <div v-show="form.policyNo">
        <a-typography-text keyboard>
          统计周期:从
          <a-typography-text underline>终端绑定后</a-typography-text>
          算起
        </a-typography-text>
      </div>

      <!-- 返现规则表格 -->
      <a-table :columns="columns" v-show="form.policyNo" :data-source="form.policyDataSource" bordered :pagination="false" size="middle">
        <template #bodyCell="{ column, text, record }">
          <!-- 可编辑列 -->
          <template v-if="tableEditKeys.includes(column.dataIndex)">
            <div>
              <a-form-item
                :name="['policyDataSource', record.key, column.dataIndex]"
                :rules="
                  column.dataIndex === 'cashbackAmount' ? cashbackAmtRules(record.parentCashbackAmount) : rules[column.dataIndex] || []
                "
                class="table-edit-form__item"
              >
                <a-input-number
                  v-if="editableData[record.key]"
                  v-model:value="editableData[record.key][column.dataIndex]"
                  placeholder="必填"
                  :min="0"
                  style="margin: -5px 0; width: 100%"
                />
                <template v-else>
                  {{ text }}
                </template>
                <template #help v-if="editableData[record.key]">
                  <a-typography-text type="warning" style="font-size: 12px"
                  >注: 下级不能大于{{ record.parentCashbackAmount }}</a-typography-text
                  >
                </template>
              </a-form-item>
            </div>
          </template>
          <!-- 操作栏 -->
          <template v-else-if="column.dataIndex === 'operation'">
            <div class="editable-row-operations">
              <span v-if="editableData[record.key]">
                <a @click="updateRow(record.key)" class="ele-text-danger">保存</a>
              </span>
              <span v-else>
                <a @click="editRow(record.key)">编辑</a>
              </span>
            </div>
          </template>
        </template>
      </a-table>
    </a-form>

    <!-- 选择代理商 -->
    <AgentSelect v-if="showAgentSelect" v-model:visible="showAgentSelect" :where="agentWhere" @done="onConfirmSelectAgent" />
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { CashbackRulePolicyApi } from '@/api/businessTeam/cashback-rule-policy/CashbackRulePolicyApi';
import AgentSelect from './agent-select.vue';
import { cloneDeep, isEmpty } from 'lodash-es';
import { useUserStore } from '@/store/modules/user';

export default {
  components: { AgentSelect },
  props: {
    visible: Boolean
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {
        // 返现规则列表
        policyDataSource: []
      },
      // 表单验证规则
      rules: {
        cashbackAmount: [{ required: true, message: '请填写', trigger: 'submit' }],
        orgNoList: [{ required: true, message: '请选择' }],
        cashbackType: [{ required: true, message: '请选择' }],
        policyNo: [{ required: true, message: '请选择' }]
      },
      loading: false,
      showAgentSelect: false,

      // 表格可编辑键
      tableEditKeys: ['cashbackAmount'],
      // 表格列
      columns: [
        {
          title: '统计周期-开始天数',
          dataIndex: 'timeCycleStart',
          width: '20%',
          align: 'center'
        },
        {
          title: '统计周期-结束天数',
          dataIndex: 'timeCycleEnd',
          width: '20%',
          align: 'center'
        },
        {
          title: '交易标准金额(元)',
          dataIndex: 'tradeVolume',
          width: '20%',
          align: 'center'
        },
        {
          title: '下级返现金额(元)',
          dataIndex: 'cashbackAmount',
          width: '20%',
          align: 'center',
          defaultFilteredValue: [0],
          onFilter: (value, record) => {
            return Number(record.parentCashbackAmount) > value;
          }
        },
        {
          title: '操作',
          dataIndex: 'operation',
          align: 'center'
        }
      ],
      // 编辑中的行
      editableData: {},
      policyKey: 0,
      policyOptions: []
    };
  },
  computed: {
    // 选择代理商 查询条件
    agentWhere() {
      return {
        fetchType: 0,
        cashbackType: this.form.cashbackType,
        openStatus: false,
        policyNo: this.form.policyNo
      };
    },
    // 当前登录用户信息
    loginUser() {
      const userStore = useUserStore();
      return userStore.$state.info;
    },
    isCashbackTypeHasVal() {
      return [0, 1].includes(this.form.cashbackType);
    }
  },
  methods: {
    /** 变更政策类型 */
    onChangeCashbackType() {
      this.form.policyNo = null;
      this.form.policyDataSource = [];
      this.form.orgNoList = [];
      this.getPolicyOptions();
    },

    /** 变更政策 */
    onChangePolicy() {
      this.getPolicyDataSource();
    },

    onConfirmSelectAgent(rows) {
      this.form.orgNoList = rows.map(i => i.agentNo);
    },

    async getPolicyOptions() {
      this.policyOptions = [];
      const data = await CashbackRulePolicyApi.policyNameList({ cashbackType: this.form.cashbackType, orgNo: this.loginUser.orgCode });
      this.policyOptions = data || [];
    },

    cashbackAmtRules(maxsCashbackAmt) {
      if (!maxsCashbackAmt && maxsCashbackAmt !== 0) {
        return [{ required: true, message: '请填写', trigger: 'submit' }];
      }
      return [
        { required: true, message: '请填写', trigger: 'submit' },
        {
          validator: async (rule, value) => {
            if (Number(value) > Number(maxsCashbackAmt)) {
              return Promise.reject(`注：下级不能大于${maxsCashbackAmt}`);
            }
            return Promise.resolve();
          },
          trigger: 'submit'
        }
      ];
    },

    /** 获取返现规则列表 */
    async getPolicyDataSource() {
      this.form.policyDataSource = [];

      let data = await CashbackRulePolicyApi.list({
        cashbackType: this.form.cashbackType,
        orgNo: this.loginUser.orgCode,
        policyNo: this.form.policyNo
      });
      data = data || [];
      data.forEach((item, index) => {
        item.key = index;
        item.parentCashbackAmount = item.cashbackAmount;
      });

      this.form.policyDataSource = data;
      this.form.cashCalculateType = data[0]?.cashCalculateType;
    },

    /** 选中表格行 */
    editRow(key) {
      this.editableData[key] = cloneDeep(this.form.policyDataSource.filter(item => key === item.key)[0]);
    },

    /** 更新表格行 */
    async updateRow(key) {
      // 先赋值
      Object.assign(this.form.policyDataSource.filter(item => key === item.key)[0], this.editableData[key]);
      // 后校验
      const validateFields = this.tableEditKeys.map(field => {
        return ['policyDataSource', key, field];
      });
      await this.$refs.form.validateFields(validateFields).catch(() => {
        return Promise.reject('返现规则校验失败');
      });

      delete this.editableData[key];
    },

    async save() {
      // 校验表单
      await this.$refs.form.validate();

      if (!isEmpty(this.editableData)) {
        return message.warn(`存在未保存的返现规则`);
      }

      // 修改加载框为正在加载
      this.loading = true;

      const params = cloneDeep(this.form);
      params.rulePolicyList = params.policyDataSource;
      delete params['policyDataSource'];

      CashbackRulePolicyApi.subAgentOpen(params)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
<style lang="less" scoped>
.editable-row-operations a {
  margin-right: 8px;
}
:deep(.table-edit-form__item) {
  margin-bottom: 0;
}
</style>
