<template>
  <a-modal
    :width="550"
    :visible="visible"
    :confirm-loading="loading"
    title="关闭直属下级政策"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules" layout="vertical">
      <a-form-item label="返现政策类型" name="cashbackType">
        <a-select v-model:value="form.cashbackType" style="width: 100%" placeholder="请选择" @change="onChangeCashbackType">
          <a-select-option :value="0">激活返现政策</a-select-option>
          <a-select-option :value="1">达标返现政策</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="选择要关闭的政策" name="policyNoList">
        <a-select
          v-model:value="form.policyNoList"
          mode="multiple"
          style="width: 100%"
          placeholder="可多选, 不选则关闭下级所有政策"
          :disabled="!isCashbackTypeHasVal"
        >
          <a-select-option v-for="({ policyName, policyNo }, key) in policyOptions" :key="key" :value="policyNo">
            {{ `${policyName}(${policyNo})` }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="选择下级机构" name="orgNoList">
        <a-select
          v-model:value="form.orgNoList"
          mode="tags"
          style="width: 100%"
          placeholder="选择下级机构"
          :open="false"
          :disabled="!isCashbackTypeHasVal"
          @focus="showAgentSelect = true"
        />
      </a-form-item>
    </a-form>

    <AgentSelect v-if="showAgentSelect" v-model:visible="showAgentSelect" :where="agentWhere" @done="onConfirmSelectAgent" />
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { useUserStore } from '@/store/modules/user';
import { CashbackRulePolicyApi } from '@/api/businessTeam/cashback-rule-policy/CashbackRulePolicyApi';
import AgentSelect from './agent-select.vue';

export default {
  components: { AgentSelect },
  props: {
    visible: Boolean
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {},
      // 表单验证规则
      rules: {
        orgNoList: [{ required: true, message: '请选择' }],
        cashbackType: [{ required: true, message: '请选择' }]
      },
      loading: false,
      showAgentSelect: false,
      policyOptions: []
    };
  },
  computed: {
    // 当前登录用户信息
    loginUser() {
      const userStore = useUserStore();
      return userStore.$state.info;
    },
    agentWhere() {
      return {
        fetchType: 1,
        cashbackType: this.form.cashbackType,
        openStatus: true
      };
    },
    isCashbackTypeHasVal() {
      return [0, 1].includes(this.form.cashbackType);
    }
  },
  methods: {
    onConfirmSelectAgent(rows) {
      this.form.orgNoList = rows.map(i => i.agentNo);
    },

    onChangeCashbackType() {
      this.form.orgNoList = [];
      this.form.policyNoList = [];
      this.getPolicyOptions();
    },

    async getPolicyOptions() {
      this.policyOptions = [];
      const data = await CashbackRulePolicyApi.policyNameList({ cashbackType: this.form.cashbackType, orgNo: this.loginUser.orgCode });
      this.policyOptions = data || [];
    },

    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      CashbackRulePolicyApi.subAgentClose(this.form)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
