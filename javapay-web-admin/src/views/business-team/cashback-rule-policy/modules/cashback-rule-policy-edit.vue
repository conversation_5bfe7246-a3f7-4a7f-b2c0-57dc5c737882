<template>
  <a-modal
    :width="800"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新增'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules" layout="vertical">
      <a-row :gutter="24">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="政策名称" name="policyName">
            <a-input v-model:value="form.policyName" placeholder="请输入政策名称" allow-clear />
          </a-form-item>

          <a-form-item label="达标计算方式" name="cashCalculateType" v-if="form.cashbackType === 1">
            <a-select
              v-model:value="form.cashCalculateType"
              style="width: 100%"
              placeholder="请选择"
              :disabled="isUpdate"
              @change="onChangeCashCalculateType"
            >
              <a-select-option :value="0">交易量优先</a-select-option>
              <a-select-option :value="1">时间优先</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="返现政策类型" name="cashbackType">
            <a-select
              v-model:value="form.cashbackType"
              style="width: 100%"
              placeholder="请选择"
              :disabled="isUpdate"
              @change="onChangeCashbackType"
            >
              <a-select-option :value="0">激活返现政策</a-select-option>
              <a-select-option :value="1">达标返现政策</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <div style="margin: 0 0 20px" v-show="form.cashbackType === 0 || [0, 1].includes(form.cashCalculateType)">
        <a-divider dashed />
      </div>

      <a-button
        v-if="!isUpdate && form.cashbackType === 1 && [0, 1].includes(form.cashCalculateType)"
        danger
        style="margin-bottom: 8px"
        @click="addRow"
      >
        添加返现规则
      </a-button>

      <div v-show="form.cashbackType === 0 || [0, 1].includes(form.cashCalculateType)">
        <a-typography-text keyboard>
          统计周期:从
          <a-typography-text underline>终端绑定后</a-typography-text>
          算起
        </a-typography-text>
      </div>

      <a-table
        v-show="form.cashbackType === 0 || [0, 1].includes(form.cashCalculateType)"
        :columns="columns"
        :data-source="form.policyDataSource"
        bordered
        :pagination="false"
        size="middle"
      >
        <template #bodyCell="{ column, text, record, index }">
          <template v-if="tableEditKeys.includes(column.dataIndex)">
            <div>
              <a-form-item
                :name="['policyDataSource', index, column.dataIndex]"
                :rules="rules[column.dataIndex] || []"
                class="table-edit-form__item"
              >
                <a-input-number
                  v-if="editableData[record.key]"
                  v-model:value="editableData[record.key][column.dataIndex]"
                  placeholder="必填"
                  :min="0"
                  style="margin: -5px 0; width: 100%"
                />
                <template v-else>
                  {{ text }}
                </template>
              </a-form-item>
            </div>
          </template>
          <template v-else-if="column.dataIndex === 'ruleNo'">
            <span> {{ isUpdate ? record.ruleNo : index + 1 }}</span>
          </template>
          <!-- 操作栏 -->
          <template v-else-if="column.dataIndex === 'operation'">
            <div class="editable-row-operations">
              <span v-if="editableData[record.key]">
                <a @click="updateRow(record.key, index)" class="ele-text-danger">保存</a>
              </span>
              <span v-else>
                <a @click="editRow(record.key)">编辑</a>
              </span>
              <a-popconfirm title="确定要删除吗?" @confirm="deleteRow(record.key)">
                <a v-if="!isUpdate && form.policyDataSource.length !== 1" class="ele-text-warning">删除</a>
              </a-popconfirm>
            </div>
          </template>
        </template>
      </a-table>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { useUserStore } from '@/store/modules/user';
import { CashbackRulePolicyApi } from '@/api/businessTeam/cashback-rule-policy/CashbackRulePolicyApi';
import { cloneDeep, isEmpty } from 'lodash-es';

export default {
  props: {
    visible: Boolean,
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {
        // 规则列表
        policyDataSource: []
      },
      // 表单验证规则
      rules: {
        policyName: [{ required: true, message: '请输入政策名称' }],
        timeCycleStart: [{ required: true, message: '请填写', trigger: 'submit' }],
        timeCycleEnd: [{ required: true, message: '请填写', trigger: 'submit' }],
        cashbackAmount: [{ required: true, message: '请填写', trigger: 'submit' }],
        tradeVolume: [{ required: true, message: '请填写', trigger: 'submit' }],
        cashCalculateType: [{ required: true, message: '请选择' }],
        cashbackType: [{ required: true, message: '请选择' }]
      },
      loading: false,
      isUpdate: false,

      columns: [
        {
          title: '统计周期-开始天数',
          dataIndex: 'timeCycleStart',
          width: '20%',
          align: 'center'
        },
        {
          title: '统计周期-结束天数',
          dataIndex: 'timeCycleEnd',
          width: '20%',
          align: 'center'
        },
        {
          title: '交易标准金额(元)',
          dataIndex: 'tradeVolume',
          width: '20%',
          align: 'center'
        },
        {
          title: '返现金额(元)',
          dataIndex: 'cashbackAmount',
          width: '20%',
          align: 'center'
        },
        {
          title: '操作',
          dataIndex: 'operation',
          align: 'center'
        }
      ],
      editableData: {},
      policyKey: 0
    };
  },
  computed: {
    // 当前登录用户信息
    loginUser() {
      const userStore = useUserStore();
      return userStore.$state.info;
    },
    tableEditKeys() {
      const PublicKeys = ['cashbackAmount', 'tradeVolume', 'timeCycleEnd'];
      let keys = cloneDeep(PublicKeys);
      if (this.form.cashbackType === 1 && this.form.cashCalculateType === 1) {
        keys.push('timeCycleStart');
      }
      return keys;
    }
  },
  mounted() {
    if (this.data) {
      this.isUpdate = true;
      this.form = Object.assign({}, this.data);

      this.getPolicyDataSource();
    }
  },
  methods: {
    /** 获取返现规则列表 */
    async getPolicyDataSource() {
      this.form.policyDataSource = [];

      let data = await CashbackRulePolicyApi.list({
        cashbackType: this.form.cashbackType,
        orgNo: this.form.orgNo,
        policyNo: this.form.policyNo
      });
      data = data || [];
      data.forEach((item, index) => {
        item.key = -(index + 1);
      });

      this.form.policyDataSource = data;
    },

    onChangeCashbackType() {
      this.form.policyDataSource = [];
      this.editableData = {};
      this.addRow();
    },

    onChangeCashCalculateType() {
      this.onChangeCashbackType();
    },

    addRow() {
      if (!isEmpty(this.editableData)) {
        return message.warn(`存在未填写完成的的返现规则`);
      }

      const policyKey = this.policyKey++;

      const timeCycleStart = this.form.cashbackType === 0 || this.form.cashCalculateType === 0 ? 0 : '';

      this.form.policyDataSource.push({
        key: policyKey,
        ruleNo: '',
        cashbackAmount: '',
        timeCycleStart: timeCycleStart,
        timeCycleEnd: '',
        tradeVolume: ''
      });

      this.editRow(policyKey);
    },

    editRow(key) {
      this.editableData[key] = cloneDeep(this.form.policyDataSource.filter(item => key === item.key)[0]);
    },

    async updateRow(key, index) {
      Object.assign(this.form.policyDataSource.filter(item => key === item.key)[0], this.editableData[key]);

      const validateFields = this.tableEditKeys.map(field => {
        return ['policyDataSource', index, field];
      });
      await this.$refs.form.validateFields(validateFields).catch(() => {
        return Promise.reject('返现规则校验失败');
      });

      delete this.editableData[key];
    },

    deleteRow(key) {
      this.form.policyDataSource = this.form.policyDataSource.filter(item => item.key !== key);
      delete this.editableData[key];
    },

    async save() {
      // 校验表单
      await this.$refs.form.validate();

      if (!isEmpty(this.editableData)) {
        return message.warn(`存在未保存的返现规则`);
      }

      const params = cloneDeep(this.form);
      if (!this.isUpdate) {
        params.policyDataSource?.forEach((item, index) => {
          item.ruleNo = index + 1;
        });
      }
      params.rulePolicyList = params.policyDataSource;
      delete params['policyDataSource'];

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      // 执行编辑或修改方法
      if (this.isUpdate) {
        result = CashbackRulePolicyApi.selfEdit(params);
      } else {
        result = CashbackRulePolicyApi.add(params);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
<style lang="less" scoped>
.editable-row-operations a {
  margin-right: 8px;
}

:deep(.table-edit-form__item) {
  margin-bottom: 0;
  .ant-form-item-explain {
    display: none;
  }
}
</style>
