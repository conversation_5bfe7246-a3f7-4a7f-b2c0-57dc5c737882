<template>
  <div class="ele-body">
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="机构编号">
              <a-input v-model:value.trim="where.orgNo" placeholder="机构编号" allow-clear />
            </a-form-item>
            <a-form-item label="返现政策类型">
              <a-select v-model:value="where.cashbackType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">激活返现政策</a-select-option>
                <a-select-option :value="1">达标返现政策</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- table上边工具栏 -->
          <template #toolbar>
            <a-space>
              <a-button v-if="hasPurview(['1'])" type="primary" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>添加</span>
              </a-button>
              <a-button v-if="!hasPurview(['0']) && directAgents.length" @click="showOpenSubPolicy = true">
                <template #icon>
                  <SendOutlined />
                </template>
                <span>开通下级政策</span>
              </a-button>
              <a-button v-if="!hasPurview(['0']) && directAgents.length" @click="showEditSub = true">
                <template #icon>
                  <EditOutlined />
                </template>
                <span>编辑下级政策</span>
              </a-button>
              <a-button v-if="hasPurview(['1', '3']) && directAgents.length" @click="showCloseSubPolicy = true">
                <template #icon>
                  <PoweroffOutlined />
                </template>
                <span>关闭下级政策</span>
              </a-button>
            </a-space>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'cashbackType'">
              <a-tag v-if="record.cashbackType === 0" color="purple">激活返现政策</a-tag>
              <a-tag v-else-if="record.cashbackType === 1" color="blue">达标返现政策</a-tag>
            </template>
            <template v-else-if="column.key === 'cashCalculateType'">
              <a-tag v-if="record.cashCalculateType === 0" color="purple">交易量优先</a-tag>
              <a-tag v-else-if="record.cashCalculateType === 1" color="blue">时间优先</a-tag>
            </template>
            <template v-else-if="column.key === 'activeOrgType'">
              <a-badge v-if="record.activeOrgType === 1" color="pink" text="大区" />
              <a-badge v-else-if="record.activeOrgType === 2" color="blue" text="运营中心" />
              <a-badge v-else-if="record.activeOrgType === 3" color="orange" text="代理商" />
              <a-badge v-else-if="record.activeOrgType === 5" color="orange" text="子级代理商" />
            </template>

            <!-- table操作栏按钮 -->
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
                <template v-if="record.orgNo === record.activeOrgNo">
                  <template v-if="hasPurview(['1', '3'])">
                    <a @click="handleEdit(record)">修改</a>
                  </template>
                </template>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <CashbackRulePolicyDetail v-if="showDetail" v-model:visible="showDetail" :detail="current" />

    <!-- 编辑 -->
    <CashbackRulePolicyEdit v-if="showEdit" v-model:visible="showEdit" :data="current" @done="reload" />

    <!-- 子代编辑 -->
    <CashbackRulePolicyEditSub v-if="showEditSub" v-model:visible="showEditSub" @done="reload" />

    <!-- 开通直属下级 -->
    <SubAgentOpen v-if="showOpenSubPolicy" v-model:visible="showOpenSubPolicy" @done="reload" />

    <!-- 关闭直属下级 -->
    <SubAgentClose v-if="showCloseSubPolicy" v-model:visible="showCloseSubPolicy" @done="reload" />
  </div>
</template>

<script>
import { hasPurview } from '@/utils/permission';
import { CashbackRulePolicyApi } from '@/api/businessTeam/cashback-rule-policy/CashbackRulePolicyApi';
import CashbackRulePolicyDetail from './modules/cashback-rule-policy-detail.vue';
import CashbackRulePolicyEdit from './modules/cashback-rule-policy-edit.vue';
import CashbackRulePolicyEditSub from './modules/cashback-rule-policy-edit-sub.vue';
import SubAgentOpen from './modules/sub-agent-open.vue';
import SubAgentClose from './modules/sub-agent-close.vue';
import { useUserStore } from '@/store/modules/user';

export default {
  name: 'CashbackRulePolicy',
  components: {
    CashbackRulePolicyDetail,
    CashbackRulePolicyEdit,
    SubAgentOpen,
    SubAgentClose,
    CashbackRulePolicyEditSub
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: '机构编号',
          dataIndex: 'orgNo',
          align: 'center'
        },
        {
          title: '上级机构编号',
          dataIndex: 'parentOrgNo',
          align: 'center'
        },
        {
          title: '活动发起机构编号',
          dataIndex: 'activeOrgNo',
          align: 'center',
          hideCol: !hasPurview('0')
        },
        {
          title: '活动发起机构类型',
          dataIndex: 'activeOrgType',
          key: 'activeOrgType',
          align: 'center',
          hideCol: !hasPurview('0')
        },
        {
          title: '政策名称',
          dataIndex: 'policyName',
          align: 'center'
        },
        {
          title: '政策编号',
          dataIndex: 'policyNo',
          align: 'center'
        },
        {
          title: '返现政策类型',
          dataIndex: 'cashbackType',
          key: 'cashbackType',
          align: 'center'
        },
        {
          title: '达标计算方式',
          dataIndex: 'cashCalculateType',
          key: 'cashCalculateType',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 160,
          align: 'center'
        }
      ].filter(i => !i.hideCol),
      // 表格搜索条件
      where: {},
      // 当前编辑数据
      current: null,
      // 是否显示编辑弹窗
      showDetail: false,
      showEdit: false,
      showEditSub: false,
      showOpenSubPolicy: false,
      showCloseSubPolicy: false,
      directAgents: []
    };
  },
  computed: {
    // 当前登录用户信息
    loginUser() {
      const userStore = useUserStore();
      return userStore.$state.info;
    }
  },
  mounted() {
    if (!hasPurview(['0'])) {
      this.getDirectAgentOpenSt();
    }
  },
  methods: {
    /** 判断有无直属下级 */
    async getDirectAgentOpenSt() {
      let tableData = await CashbackRulePolicyApi.getDirectAgentOpenSt({ fetchType: 1 });
      this.directAgents = tableData || [];
    },

    hasPurview,

    reload() {
      this.$refs.table.reload({ page: 1 });
      if (!hasPurview(['0']) && !this.directAgents.length) {
        this.getDirectAgentOpenSt();
      }
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    handleEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    handleEditSub(row) {
      this.current = row;
      this.showEditSub = true;
    },

    async datasource({ page, limit, where, orders }) {
      return CashbackRulePolicyApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
