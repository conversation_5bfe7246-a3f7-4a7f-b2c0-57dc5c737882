<template>
  <div class="ele-body">
    <a-spin :spinning="spinning" tip="下载中, 请稍候...">
      <!-- 搜索表单 -->
      <div class="block-interval">
        <a-card :bordered="false">
          <a-form layout="inline" :model="where">
            <a-row :gutter="[0, 16]">
              <a-form-item label="出款批次">
                <a-input v-model:value.trim="where.withdrawBatchNo" placeholder="请输入出款批次号" allow-clear />
              </a-form-item>
              <a-form-item label="清算通道">
                <a-select v-model:value="where.remitChannelCode" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option v-for="({ channelName, channelNo }, key) in channelCodes" :key="key" :value="channelNo">
                    {{ channelName }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="批次状态">
                <a-select v-model:value="where.batchStatus" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option :value="0">已创建</a-select-option>
                  <a-select-option :value="1">已生成</a-select-option>
                  <a-select-option :value="2">已提交</a-select-option>
                  <a-select-option :value="3">已回盘</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item class="ele-text-center">
                <a-space>
                  <a-button type="primary" @click="reload">查询</a-button>
                  <a-button @click="reset">重置</a-button>
                </a-space>
              </a-form-item>
            </a-row>
          </a-form>
        </a-card>
      </div>

      <!-- 表格 -->
      <div>
        <a-card :bordered="false" class="table-height">
          <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'batchStatus'">
                <a-tag v-if="record.batchStatus === 0">已创建</a-tag>
                <a-tag v-else-if="record.batchStatus === 1" color="blue">已生成</a-tag>
                <a-tag v-else-if="record.batchStatus === 2" color="green">已提交</a-tag>
                <a-tag v-else-if="record.batchStatus === 3" color="orange">已回盘</a-tag>
              </template>
              <!-- table操作栏按钮 -->
              <template v-else-if="column.key === 'action'">
                <a-space>
                  <a @click="handleDetail(record)">详情</a>
                  <a-divider type="vertical" />
                  <a-popconfirm :title="`确定要下载出款批次号${record.withdrawBatchNo}的文件？`" @confirm="handleDownload(record)">
                    <a>出款文件下载</a>
                  </a-popconfirm>
                  <a-divider type="vertical" />
                  <a-upload
                    name="file"
                    accept=".csv"
                    :maxCount="1"
                    :action="`${FileUploadUrl}?withdrawBatchNo=${record.withdrawBatchNo}&remitChannelCode=${record.remitChannelCode}`"
                    :headers="headers"
                    :before-upload="beforeUpload"
                    @change="afterUpload"
                    :showUploadList="false"
                  >
                    <a>上传结果文件(.csv)</a>
                  </a-upload>
                </a-space>
              </template>
            </template>
          </ele-pro-table>
        </a-card>
      </div>

      <!-- 详情 -->
      <WithdrawBatchDetail v-model:visible="showDetail" :detail="current" />
    </a-spin>
  </div>
</template>

<script>
import { WithdrawBatchManageApi } from '@/api/businessTeam/withdraw-batch-manage/WithdrawBatchManageApi';
import WithdrawBatchDetail from './WithdrawBatchDetail.vue';
import { RemitChannelApi } from '@/api/account/remit-channel/RemitChannelApi';
import { message } from 'ant-design-vue';
import { getToken } from '@/utils/token-util';

export default {
  name: 'OrgWithdrawBatchManage',
  components: {
    WithdrawBatchDetail
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '出款批次号',
          dataIndex: 'withdrawBatchNo'
        },
        {
          title: '批次状态',
          dataIndex: 'batchStatus',
          key: 'batchStatus',
          align: 'center'
        },
        {
          title: '清算通道名称',
          dataIndex: 'remitChannelName'
        },
        {
          title: '手续费总金额(元)',
          dataIndex: 'feeSumAmount'
        },
        {
          title: '到账总金额(元)',
          dataIndex: 'receivedSumAmount'
        },
        {
          title: '提现总金额(元)',
          dataIndex: 'withdrawSumAmount'
        },
        {
          title: '提现总笔数',
          dataIndex: 'withdrawSumCount'
        },
        {
          title: '批次下载时间',
          dataIndex: 'batchDownloadTime'
        },
        {
          title: '批次回盘时间',
          dataIndex: 'batchResultTime'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 290,
          align: 'center'
        }
      ],
      // 表格搜索条件
      where: {},
      current: null,
      showDetail: false,
      channelCodes: [],
      spinning: false,
      headers: {
        Authorization: getToken()
      },
      FileUploadUrl: '/api/profitRemitBatch/upload'
    };
  },
  async mounted() {
    const data = await RemitChannelApi.findAll({ validStatus: 1 });
    this.channelCodes = data || [];
  },
  methods: {
    beforeUpload(file) {
      const isLt10M = file.size / 1024 / 1024 <= 10;
      if (!isLt10M) {
        message.error('上传文件不能超过10MB');
      }
      return isLt10M;
    },
    afterUpload({ file }) {
      if (!file.response) return;

      if (file.response.code === '00000') {
        message.success('上传成功');
        this.reload();
      } else {
        message.error(file.response.message || '上传失败');
      }
    },
    async handleDownload(row) {
      this.spinning = true;
      const data = await WithdrawBatchManageApi.download({ withdrawBatchNo: row.withdrawBatchNo }).catch(() => {
        this.spinning = false;
      });

      this.spinning = false;

      const fileReader = new FileReader();
      fileReader.onload = () => {
        try {
          // 说明是普通对象数据，后台转换失败
          const jsonData = JSON.parse(fileReader.result);
          message.error(jsonData.message);
        } catch (err) {
          const contentDisposition = data.headers['content-disposition'];
          let fileName = decodeURIComponent(contentDisposition.substring(contentDisposition.indexOf('=') + 1));
          fileName = fileName ? fileName.replace("utf-8''", '') + '.xlsx' : '出款文件记录-下载.xlsx';

          // 解析对象失败，说明是正常的文件流
          let blob = new Blob([data.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.style.display = 'none';
          a.href = url;
          a.download = fileName;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }
      };
      fileReader.readAsText(data.data);
    },

    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where, orders }) {
      return WithdrawBatchManageApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
