<template>
  <a-modal
    :width="600"
    :visible="visible"
    :confirm-loading="loading"
    title="出款文件记录下载"
    :body-style="{ paddingBottom: '8px' }"
    :mask-closable="false"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 5 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 19 }, sm: { span: 24 } }"
    >
      <a-form-item label="出款批次" name="withdrawBatchNo">
        <a-input v-model:value="form.withdrawBatchNo" placeholder="批次号" allow-clear />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { WithdrawBatchManageApi } from '@/api/businessTeam/withdraw-batch-manage/WithdrawBatchManageApi';

export default {
  props: {
    visible: Boole<PERSON>
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {},
      // 表单验证规则
      rules: {
        withdrawBatchNo: [{ required: true, message: '请输入批次号' }]
      },
      // 提交状态
      loading: false
    };
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      const data = await WithdrawBatchManageApi.download(this.form).catch(() => {
        this.loading = false;
      });

      this.loading = false;

      const fileReader = new FileReader();
      fileReader.onload = () => {
        try {
          // 说明是普通对象数据，后台转换失败
          const jsonData = JSON.parse(fileReader.result);
          message.error(jsonData.message);
        } catch (err) {
          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 解析对象失败，说明是正常的文件流
          let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.style.display = 'none';
          a.href = url;
          a.download = `出款文件记录${this.form.withdrawBatchNo}.xlsx`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }
      };
      fileReader.readAsText(data);
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
