<template>
  <a-modal
    :width="1000"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="出款批次号">{{ form.withdrawBatchNo }}</a-descriptions-item>
      <a-descriptions-item label="批次状态">
        <a-tag v-if="form.batchStatus === 0">已创建</a-tag>
        <a-tag v-else-if="form.batchStatus === 1" color="blue">已生成</a-tag>
        <a-tag v-else-if="form.batchStatus === 2" color="green">已提交</a-tag>
        <a-tag v-else-if="form.batchStatus === 3" color="orange">已回盘</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="清算通道名称">{{ form.remitChannelName }}</a-descriptions-item>
      <a-descriptions-item label="手续费总金额(元)">{{ form.feeSumAmount }}</a-descriptions-item>
      <a-descriptions-item label="到账总金额(元)">{{ form.receivedSumAmount }}</a-descriptions-item>
      <a-descriptions-item label="提现总金额(元)">{{ form.withdrawSumAmount }}</a-descriptions-item>
      <a-descriptions-item label="提现总笔数">{{ form.withdrawSumCount }}</a-descriptions-item>
      <a-descriptions-item label="批次下载时间">{{ form.batchDownloadTime }}</a-descriptions-item>
      <a-descriptions-item label="批次回盘时间">{{ form.batchResultTime }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
