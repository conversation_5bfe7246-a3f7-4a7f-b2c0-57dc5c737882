<template>
  <a-modal
    title="基本信息变更审核"
    :width="1000"
    :visible="visible"
    :body-style="{ paddingBottom: '8px' }"
    :mask-closable="false"
    @update:visible="updateVisible"
  >
    <a-alert message="左侧为新内容, 右侧为旧内容" show-icon type="info" style="margin-bottom: 24px" />
    <a-form :label-col="{ md: { span: 6 }, sm: { span: 24 } }" :wrapper-col="{ md: { span: 18 }, sm: { span: 24 } }" :colon="false">
      <a-tabs v-model:activeKey="activeKey" type="card">
        <!-- 基本信息 -->
        <a-tab-pane key="1" tab="基本信息">
          <template v-for="({ label, infoKey, formItemType, isHide }, key) in baseInfoMap" :key="key">
            <a-form-item
              v-if="!isHide"
              :class="{ 'info-changed': isInfoChanged(form['checkBaseInfo'][infoKey], form['oldBaseInfo'][infoKey]) }"
              :label="label"
            >
              <template v-for="i in ['checkBaseInfo', 'oldBaseInfo']" :key="i">
                <a-input v-if="!formItemType" :value="form[i][infoKey]" readonly />
                <a-textarea
                  v-else-if="formItemType === 'textarea'"
                  :value="form[i][infoKey]"
                  :auto-size="{ minRows: 2, maxRows: 5 }"
                  readonly
                />
                <a-select v-else-if="formItemType === 'select'" :value="form[i][infoKey]" class="ele-fluid" disabled>
                  <template v-if="infoKey === 'policyId'">
                    <!-- 费率政策项 -->
                    <a-select-option v-for="(item, key) in ratePolicyList" :value="Number(item.id)" :key="key">
                      {{ item.policyDesc }}
                    </a-select-option>
                  </template>
                  <template v-else-if="['contactsCertType', 'legalCertType'].includes(infoKey)">
                    <!-- 证件类型项 -->
                    <a-select-option v-for="({ label, value }, key) in certTypeEnum" :key="key" :value="value">{{ label }}</a-select-option>
                  </template>
                  <!-- 子代分润出款方式 -->
                  <template v-else-if="['subAgentSettleChannelWay', 'settleChannelWay'].includes(infoKey)">
                    <a-select-option :value="3">展业平台</a-select-option>
                    <a-select-option :value="1">平台出款</a-select-option>
                    <a-select-option :value="0">交易通道</a-select-option>
                  </template>
                  <!-- 子代分润出款通道 -->
                  <template v-else-if="['subAgentSettleChannelCode', 'settleChannelCode'].includes(infoKey)">
                    <a-select-option v-for="(item, key) in subChannelNos" :key="key" :value="item.channelNo">
                      {{ item.channelName }}
                    </a-select-option>
                  </template>
                </a-select>
              </template>
            </a-form-item>
          </template>
        </a-tab-pane>
        <!-- 图片信息 -->
        <a-tab-pane key="3" tab="图片信息">
          <template v-for="(f, keyF) in fileList" :key="keyF">
            <a-form-item v-if="!f.isHide" :label="f.label" :class="{ 'info-changed': isImgChanged(f.imageType) }">
              <template v-for="k in ['checkImageInfo', 'oldImageInfo']" :key="k">
                <template v-for="(c, keyC) in form[k] || []" :key="keyC">
                  <a-upload
                    v-if="f.imageType === c.imageType"
                    :file-list="[{ url: c.imagePath }]"
                    list-type="picture-card"
                    @preview="() => handlePreview(c.imagePath)"
                    disabled
                  />
                </template>
              </template>
            </a-form-item>
          </template>
          <!-- 预览图片 -->
          <a-image
            :style="{ display: 'none' }"
            :src="previewImage"
            :preview="{ visible: previewVisible, onVisibleChange: setPreviewVisible }"
          />
        </a-tab-pane>
      </a-tabs>

      <a-divider style="margin-bottom: 22px" dashed />

      <a-form-item label="审核意见" required>
        <a-textarea v-model:value="form.checkDesc" :auto-size="{ minRows: 3, maxRows: 5 }" />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-button type="primary" :loading="loading" @click="handleAudit(1)"><check-outlined />通过</a-button>
      <a-button type="danger" :loading="loading" @click="handleAudit(2)"><close-outlined />驳回</a-button>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { reactive, ref, toRefs, watchEffect, computed } from 'vue';
import { certTypeEnum } from '@/config/enumerate';
import { AgencyAuditApi } from '@/api/businessTeam/agency-audit/AgencyAuditApi';
import dayjs from 'dayjs';
import { RemitChannelApi } from '@/api/account/remit-channel/RemitChannelApi';

export default {
  props: {
    visible: Boolean,
    detail: Object,
    ratePolicyList: Array
  },
  emits: ['update:visible'],
  setup(props, context) {
    const isHidePolicyItem = computed(() => {
      return !data.userType || ![2, 3].includes(data.userType);
    });

    const isHideSettleChannel = computed(() => {
      return !data.userType || data.userType !== 3;
    });

    const isHideSettleChannelCode = computed(() => {
      return !(!isHideSettleChannel.value && [1, 2].includes(data.form.checkBaseInfo.subAgentSettleChannelWay));
    });

    const isHideSelfSettleChannelCode = computed(() => {
      return ![1, 2].includes(data.form.checkBaseInfo.settleChannelWay);
    });

    const fieldPrefixs = ['', 'region', 'branch', 'agent'];
    const companyNameKey = computed(() => {
      return fieldPrefixs[data.userType] + 'Name';
    });
    const companySnameKey = computed(() => {
      return fieldPrefixs[data.userType] + 'Sname';
    });
    // 基本信息
    const baseInfoMap = ref([
      {
        label: '费率政策',
        infoKey: 'policyId',
        formItemType: 'select',
        isHide: isHidePolicyItem
      },
      { label: '企业名称', infoKey: companyNameKey },
      { label: '企业简称', infoKey: companySnameKey },
      { label: '对公开票税点(%)', infoKey: 'taxPoint' },
      { label: '组织机构统一代码', infoKey: 'licenseNo' },
      { label: '企业注册地址', infoKey: 'licenseAddr', formItemType: 'textarea' },
      { label: '企业办公地址', infoKey: 'officeAddr', formItemType: 'textarea' },

      { label: '法人姓名', infoKey: 'legalName' },
      { label: '法人手机号', infoKey: 'legalTelMask' },
      { label: '法人证件类型', infoKey: 'legalCertType', formItemType: 'select' },
      { label: '法人证件号码', infoKey: 'legalCertNoMask' },
      { label: '法人证件有效期开始', infoKey: 'legalCertStartDate' },
      { label: '法人证件有效期截止', infoKey: 'legalCertEndDate' },

      { label: '联系人姓名', infoKey: 'contactsName' },
      { label: '联系人手机号', infoKey: 'contactsTelMask' },
      { label: '联系人证件类型', infoKey: 'contactsCertType', formItemType: 'select' },
      { label: '联系人证件号码', infoKey: 'contactsCertNoMask' },
      { label: '联系人证件有效期开始', infoKey: 'contactsCertStartDate' },
      { label: '联系人证件有效期截止', infoKey: 'contactsCertEndDate' },

      { label: '签约日期', infoKey: 'signDate' },
      { label: '分润出款方式', infoKey: 'settleChannelWay', formItemType: 'select' },
      { label: '分润出款通道', infoKey: 'settleChannelCode', formItemType: 'select', isHide: isHideSelfSettleChannelCode },
      {
        label: `子代分润出款方式`,
        infoKey: 'subAgentSettleChannelWay',
        formItemType: 'select',
        isHide: isHideSettleChannel
      },
      {
        label: `子代分润出款通道`,
        infoKey: 'subAgentSettleChannelCode',
        formItemType: 'select',
        isHide: isHideSettleChannelCode
      }
    ]);

    const isHideImageType8 = computed(() => {
      return data.userType ? [2, 3].includes(data.userType) : true;
    });
    // 图片列表
    const fileList = ref([
      { label: '身份证头像面', imageType: 1 },
      { label: '身份证国徽面', imageType: 2 },
      { label: '营业执照照片', imageType: 7 },
      { label: '税务登记证照片', imageType: 8, isHide: isHideImageType8 }
    ]);

    // 设置表单默认值
    function formDefaults() {
      return {
        checkBaseInfo: {},
        oldBaseInfo: {},
        checkImageInfo: [],
        oldImageInfo: []
      };
    }
    const data = reactive({
      form: formDefaults(),
      activeKey: '1',
      previewVisible: false,
      previewTitle: '',
      previewImage: '',
      loading: false,
      certTypeEnum,
      /**
       * 用户类型
       * @prop {number} 1 大区; 2 运营; 3 代理商
       */
      userType: null,
      subChannelNos: []
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = formDefaults();
        data.userType = props.detail.userType;

        data.activeKey = '1';

        Object.keys(data.form).forEach(k => {
          data.form[k] = props.detail[k]
            ? typeof props.detail[k] === 'string'
              ? JSON.parse(props.detail[k])
              : props.detail[k]
            : data.form[k];
        });

        data.form.id = props.detail.id;
        data.form.checkDesc = '';

        ['checkBaseInfo', 'oldBaseInfo'].forEach(i => {
          const signDate = data.form[i].signDate;
          data.form[i].signDate = signDate ? dayjs(signDate).format('YYYY-MM-DD') : '';
        });

        if (data.userType === 3) {
          getSubChannelNos();
        }
      }
    });

    const handlePreview = imagePath => {
      data.previewImage = imagePath;
      setPreviewVisible(true);
    };

    const setPreviewVisible = visible => {
      data.previewVisible = visible;
    };

    const isInfoChanged = (current, old) => {
      if ([current, old].every(i => i !== 0 && !i)) return false;
      return current !== old;
    };

    const isImgChanged = imageType => {
      const [currentItem, oldItem] = ['checkImageInfo', 'oldImageInfo'].map(i => {
        const item = data.form[i].find(c => c.imageType === imageType);
        return item || {};
      });

      return isInfoChanged(currentItem.imagePath, oldItem.imagePath);
    };

    /**
     * 发起审核操作
     * @param {*} checkStatus 审核状态 1 通过 ; 2 驳回
     */
    const handleAudit = async checkStatus => {
      const { id, checkDesc } = data.form;

      if (!checkDesc) return message.warning('请填写审核意见!');

      // 修改加载框为正在加载
      data.loading = true;

      AgencyAuditApi.audit({ checkStatus, id, checkDesc })
        .then(result => {
          // 移除加载框
          data.loading = false;

          // 提示修改成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          updateVisible(false);

          // 触发父组件done事件
          context.emit('done');
        })
        .catch(() => {
          data.loading = false;
        });
    };

    const getSubChannelNos = async () => {
      const list = await RemitChannelApi.findAll({ validStatus: 1, remitChannelClassify: 2 });
      data.subChannelNos = list || [];
    };

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible,
      handlePreview,
      setPreviewVisible,
      isImgChanged,
      isInfoChanged,
      baseInfoMap,
      fileList,
      handleAudit
    };
  }
};
</script>

<style lang="less" scoped>
:deep(.ant-form-item-label) {
  text-align: center;
  background-color: #f1f3f4;
}
.info-changed {
  :deep(.ant-form-item-label) {
    background-color: #fa541cc9;
  }
}
.hide-label {
  :deep(.ant-form-item-label) {
    display: none;
  }
  .ant-divider-horizontal.ant-divider-with-text {
    margin: 0;
  }
}
:deep(.ant-row) {
  align-items: flex-start;
  .ant-form-item-control-input-content {
    display: flex;
    .ant-input,
    .ant-select-selector {
      border-radius: 0;
    }
  }
}
:deep(.ant-select-disabled.ant-select:not(.ant-select-customize-input)) .ant-select-selector {
  background-color: transparent;
}
</style>
