<template>
  <a-modal
    title="信息变更详情"
    :width="1000"
    :visible="visible"
    :body-style="{ paddingBottom: '8px' }"
    :mask-closable="false"
    @update:visible="updateVisible"
  >
    <a-alert message="左侧为新内容, 右侧为旧内容" show-icon type="info" style="margin-bottom: 24px" />
    <a-form :label-col="{ md: { span: 6 }, sm: { span: 24 } }" :wrapper-col="{ md: { span: 18 }, sm: { span: 24 } }" :colon="false">
      <a-tabs v-model:activeKey="activeKey" type="card">
        <!-- 基本信息 -->
        <a-tab-pane key="1" tab="基本信息" v-if="[1, 4].includes(optType)">
          <template v-for="({ label, infoKey, formItemType, isHide }, key) in baseInfoMap" :key="key">
            <a-form-item
              v-if="!isHide"
              :class="{ 'info-changed': isInfoChanged(form['checkBaseInfo'][infoKey], form['oldBaseInfo'][infoKey]) }"
              :label="label"
            >
              <template v-for="i in ['checkBaseInfo', 'oldBaseInfo']" :key="i">
                <a-input v-if="!formItemType" :value="form[i][infoKey]" readonly />
                <a-textarea
                  v-else-if="formItemType === 'textarea'"
                  :value="form[i][infoKey]"
                  :auto-size="{ minRows: 2, maxRows: 5 }"
                  readonly
                />
                <a-select v-else-if="formItemType === 'select'" :value="form[i][infoKey]" class="ele-fluid" disabled>
                  <template v-if="infoKey === 'policyId'">
                    <!-- 费率政策项 -->
                    <a-select-option v-for="(item, key) in ratePolicyList" :value="Number(item.id)" :key="key">
                      {{ item.policyDesc }}
                    </a-select-option>
                  </template>
                  <template v-else-if="['contactsCertType', 'legalCertType'].includes(infoKey)">
                    <!-- 证件类型项 -->
                    <a-select-option v-for="({ label, value }, key) in certTypeEnum" :key="key" :value="value">{{ label }}</a-select-option>
                  </template>
                  <!-- 子代分润出款方式 -->
                  <template v-else-if="['subAgentSettleChannelWay', 'settleChannelWay'].includes(infoKey)">
                    <a-select-option :value="3">展业平台</a-select-option>
                    <a-select-option :value="1">平台出款</a-select-option>
                    <a-select-option :value="0">交易通道</a-select-option>
                  </template>
                  <!-- 子代分润出款通道 -->
                  <template v-else-if="['subAgentSettleChannelCode', 'settleChannelCode'].includes(infoKey)">
                    <a-select-option v-for="(item, key) in subChannelNos" :key="key" :value="item.channelNo">
                      {{ item.channelName }}
                    </a-select-option>
                  </template>
                </a-select>
              </template>
            </a-form-item>
          </template>
        </a-tab-pane>
        <!-- 费率信息 -->
        <template v-if="optType === 2" #renderTabBar></template>
        <a-tab-pane key="2" tab="费率信息" v-if="[1, 2].includes(optType)">
          <a-tabs v-model:activeKey="activeRateKey">
            <a-tab-pane
              v-for="([checkRateInfo, oldRateInfo], key) in rateInfoComparisonArr"
              :tab="`${checkRateInfo.channelName}-${checkRateInfo.channelCode}`"
              :key="String(key)"
            >
              <template v-for="(rateItem, keyl) in checkRateInfo.rateDTOList" :key="keyl">
                <template v-for="({ label, infoKey, isHide }, keyi) in rateInfoMap[rateItem.rateType] || []" :key="keyi">
                  <a-form-item
                    v-if="!isHide && (rateItem.rateType !== 1 || checkRateInfo.targetScanRateKeys.includes(infoKey))"
                    :class="{
                      'info-changed': isInfoChanged(rateItem[infoKey], oldRateInfo.rateDTOList[keyl]?.[infoKey]),
                      'hide-label': infoKey === 'divider'
                    }"
                    :label="label"
                  >
                    <template v-if="infoKey !== 'divider'">
                      <a-input
                        v-for="(item, keyc) in [rateItem, oldRateInfo.rateDTOList[keyl] || {}]"
                        :key="keyc"
                        :value="item[infoKey]"
                        readonly
                      />
                    </template>
                    <a-divider v-else style="height: 100%" orientation="left" orientationMargin="0" dashed>{{
                      label + rateItem.templateNo
                    }}</a-divider>
                  </a-form-item>
                </template>
              </template>
            </a-tab-pane>
          </a-tabs>
        </a-tab-pane>
        <!-- 图片信息 -->
        <a-tab-pane key="3" tab="图片信息" v-if="[1, 4].includes(optType)">
          <template v-for="(f, keyF) in fileList" :key="keyF">
            <a-form-item v-if="!f.isHide" :label="f.label" :class="{ 'info-changed': isImgChanged(f.imageType) }">
              <template v-for="k in ['checkImageInfo', 'oldImageInfo']" :key="k">
                <template v-for="(c, keyC) in form[k] || []" :key="keyC">
                  <a-upload
                    v-if="f.imageType === c.imageType"
                    :file-list="[{ url: c.imagePath }]"
                    list-type="picture-card"
                    @preview="() => handlePreview(c.imagePath)"
                    disabled
                  />
                </template>
              </template>
            </a-form-item>
          </template>
          <!-- 预览图片 -->
          <a-image
            :style="{ display: 'none' }"
            :src="previewImage"
            :preview="{ visible: previewVisible, onVisibleChange: setPreviewVisible }"
          />
        </a-tab-pane>
        <a-tab-pane key="4" tab="结算信息" v-if="[1].includes(optType) && form.checkSettleAccount.userType !== 5">
          <template v-for="({ label, infoKey, formItemType, isHide }, key) in settleInfoMap" :key="key">
            <a-form-item
              v-if="!isHide"
              :class="{ 'info-changed': isInfoChanged(form['checkSettleAccount'][infoKey], form['oldSettleAccount'][infoKey]) }"
              :label="label"
            >
              <template v-for="i in ['checkSettleAccount', 'oldSettleAccount']" :key="i">
                <a-input v-if="!formItemType" :value="form[i][infoKey]" readonly />
                <a-textarea
                  v-else-if="formItemType === 'textarea'"
                  :value="form[i][infoKey]"
                  :auto-size="{ minRows: 2, maxRows: 5 }"
                  readonly
                />
                <a-select v-else-if="formItemType === 'select'" :value="form[i][infoKey]" class="ele-fluid" disabled>
                  <template v-if="infoKey === 'accountType'">
                    <a-select-option value="G"> 对公 </a-select-option>
                    <a-select-option value="S"> 对私 </a-select-option>
                  </template>
                </a-select>
              </template>
            </a-form-item>
          </template>
        </a-tab-pane>
      </a-tabs>
    </a-form>

    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, ref, toRefs, watchEffect, computed } from 'vue';
import { certTypeEnum } from '@/config/enumerate';
import dayjs from 'dayjs';
import { RemitChannelApi } from '@/api/account/remit-channel/RemitChannelApi';

export default {
  props: {
    visible: Boolean,
    detail: Object,
    channelCodes: Array,
    ratePolicyList: Array
  },
  emits: ['update:visible'],
  setup(props, context) {
    const isHidePolicyItem = computed(() => {
      return !data.userType || ![2, 3].includes(data.userType);
    });

    const isHideSettleChannel = computed(() => {
      return !data.userType || data.userType !== 3;
    });

    const isHideSettleChannelCode = computed(() => {
      return !(!isHideSettleChannel.value && [1, 2].includes(data.form.checkBaseInfo.subAgentSettleChannelWay));
    });

    const isHideSelfSettleChannelCode = computed(() => {
      return ![1, 2].includes(data.form.checkBaseInfo.settleChannelWay);
    });

    const fieldPrefixs = ['', 'region', 'branch', 'agent', '', 'agent'];
    const companyNameKey = computed(() => {
      return fieldPrefixs[data.userType] + 'Name';
    });
    const companySnameKey = computed(() => {
      return fieldPrefixs[data.userType] + 'Sname';
    });
    // 基本信息
    const baseInfoMap = ref([
      {
        label: '费率政策',
        infoKey: 'policyId',
        formItemType: 'select',
        isHide: isHidePolicyItem
      },
      { label: '企业名称', infoKey: companyNameKey },
      { label: '企业简称', infoKey: companySnameKey },
      { label: '对公开票税点(%)', infoKey: 'taxPoint' },
      { label: '组织机构统一代码', infoKey: 'licenseNo' },
      { label: '企业注册地址', infoKey: 'licenseAddr', formItemType: 'textarea' },
      { label: '企业办公地址', infoKey: 'officeAddr', formItemType: 'textarea' },

      { label: '法人姓名', infoKey: 'legalName' },
      { label: '法人手机号', infoKey: 'legalTelMask' },
      { label: '法人证件类型', infoKey: 'legalCertType', formItemType: 'select' },
      { label: '法人证件号码', infoKey: 'legalCertNoMask' },
      { label: '法人证件有效期开始', infoKey: 'legalCertStartDate' },
      { label: '法人证件有效期截止', infoKey: 'legalCertEndDate' },

      { label: '联系人姓名', infoKey: 'contactsName' },
      { label: '联系人手机号', infoKey: 'contactsTelMask' },
      { label: '联系人证件类型', infoKey: 'contactsCertType', formItemType: 'select' },
      { label: '联系人证件号码', infoKey: 'contactsCertNoMask' },
      { label: '联系人证件有效期开始', infoKey: 'contactsCertStartDate' },
      { label: '联系人证件有效期截止', infoKey: 'contactsCertEndDate' },

      { label: '签约日期', infoKey: 'signDate' },
      { label: '分润出款方式', infoKey: 'settleChannelWay', formItemType: 'select' },
      { label: '分润出款通道', infoKey: 'settleChannelCode', formItemType: 'select', isHide: isHideSelfSettleChannelCode },
      { label: '子代分润出款方式', infoKey: 'subAgentSettleChannelWay', formItemType: 'select', isHide: isHideSettleChannel },
      { label: '子代分润出款通道', infoKey: 'subAgentSettleChannelCode', formItemType: 'select', isHide: isHideSettleChannelCode }
    ]);

    const isHidePayWallet = computed(() => {
      return [5].includes(data.userType);
    });
    // 费率信息
    const rateInfoMap = ref({
      1: [
        { label: '线下收单', infoKey: 'divider' },
        { label: '银联标准贷记卡费率(%)', infoKey: 'creditRate' },
        { label: '银联标准借记卡费率(%)', infoKey: 'debitRate' },
        { label: '银联标准借记卡手续费封顶(元)', infoKey: 'debitFeeMax' },
        { label: '银联云闪付贷记卡费率(%)', infoKey: 'nfcCreditRate' },
        { label: '银联云闪付借记卡费率(%)', infoKey: 'nfcDebitRate' },
        { label: '支付宝扫码费率(%)', infoKey: 'aliPayRate' },
        { label: '支付宝大额费率(%)', infoKey: 'aliPayLargeRate' },
        { label: '微信扫码费率(%)', infoKey: 'wechatRate' },
        { label: '贷记卡D0附加费率(%)', infoKey: 'creditQrD0Rate' },
        { label: '贷记卡D0附加单笔(元)', infoKey: 'creditQrD0SingleFee' },
        { label: '借记卡D0附加费率(%)', infoKey: 'debitQrD0Rate' },
        { label: '借记卡D0附加单笔(元)', infoKey: 'debitQrD0SingleFee' }
      ],
      2: [
        { label: 'POS收单交易', infoKey: 'divider' },
        { label: '银联标准贷记卡费率(%)', infoKey: 'creditRate' },
        { label: '银联标准借记卡费率(%)', infoKey: 'debitRate' },
        { label: '银联标准借记卡手续费封顶值(元)', infoKey: 'debitFeeMax' },
        { label: '银联云闪付贷记卡费率(%)', infoKey: 'nfcCreditRate' },
        { label: '银联云闪付借记卡费率(%)', infoKey: 'nfcDebitRate' },
        { label: '刷卡贷记卡D0附加费率(%)', infoKey: 'creditD0Rate' },
        { label: '刷卡借记卡D0附加费率(%)', infoKey: 'debitD0Rate' },
        { label: '刷卡贷记卡D0附加单笔费用(元)', infoKey: 'creditD0SingleFee' },
        { label: '刷卡借记卡D0附加单笔费用(元)', infoKey: 'debitD0SingleFee' }
      ],
      3: [
        { label: '无卡交易', infoKey: 'divider' },
        { label: '无卡贷记卡费率(%)', infoKey: 'creditEposRate' },
        { label: '无卡贷记卡D0费率(%)', infoKey: 'creditEposD0Rate' },
        { label: '无卡借记卡D0费率(%)', infoKey: 'debitEposD0Rate' },
        { label: '无卡借记卡费率(%)', infoKey: 'debitEposRate' },
        { label: '无卡贷记卡D0附加单笔费用(元)', infoKey: 'creditEposD0SingleFee' },
        { label: '无卡借记卡D0附加单笔费用(元)', infoKey: 'debitEposD0SingleFee' }
      ],
      4: [
        { label: '机构出款费率', infoKey: 'divider' },
        { label: '分润对公提现税率(%)', infoKey: 'pfWalletPubWdRate' },
        { label: '分润对私提现税率(%)', infoKey: 'pfWalletPriWdRate' },
        { label: '分润对公提现单笔费用(元)', infoKey: 'pfWalletPubWdSingleFee' },
        { label: '分润对私提现单笔费用(元)', infoKey: 'pfWalletPriWdSingleFee' },
        { label: '代付对公提现税率(%)', infoKey: 'payWalletPubWdRate', isHide: isHidePayWallet },
        { label: '代付对私提现税率(%)', infoKey: 'payWalletPriWdRate', isHide: isHidePayWallet },
        { label: '代付对公提现单笔费用(元)', infoKey: 'payWalletPubWdSingleFee', isHide: isHidePayWallet },
        { label: '代付对私提现单笔费用(元)', infoKey: 'payWalletPriWdSingleFee', isHide: isHidePayWallet },
        { label: '营销活动对公提现税率(%)', infoKey: 'rewardWalletPubWdRate' },
        { label: '营销活动对私提现税率(%)', infoKey: 'rewardWalletPriWdRate' },
        { label: '营销活动对公提现单笔费用(元)', infoKey: 'rewardWalletPubWdSingleFee' },
        { label: '营销活动对私提现单笔费用(元)', infoKey: 'rewardWalletPriWdSingleFee' }
      ]
    });

    const isHideImageType8 = computed(() => {
      return data.userType ? [2, 3, 5].includes(data.userType) : true;
    });
    // 图片列表
    const fileList = ref([
      { label: '身份证头像面', imageType: 1 },
      { label: '身份证国徽面', imageType: 2 },
      { label: '营业执照照片', imageType: 7 },
      { label: '税务登记证照片', imageType: 8, isHide: isHideImageType8 }
    ]);

    // 结算信息
    const settleInfoMap = ref([
      { label: '结算账户类型', infoKey: 'accountType', formItemType: 'select' },
      { label: '银行卡账户姓名', infoKey: 'bankAccountName' },
      { label: '开户行总行', infoKey: 'bankName' },
      { label: '开户行支行', infoKey: 'bankSubName' },
      { label: '银行卡卡号', infoKey: 'bankAccountNoMask' },
      // { label: '开户行所在地区', infoKey: 'bankAreaValue' },
      { label: '银行卡预留手机号', infoKey: 'mobileMask' }
    ]);

    // 设置表单默认值
    function formDefaults() {
      return {
        checkBaseInfo: {},
        oldBaseInfo: {},
        checkImageInfo: [],
        oldImageInfo: [],
        checkRateInfo: [],
        oldRateInfo: [],
        checkSettleAccount: {},
        oldSettleAccount: {}
      };
    }
    const data = reactive({
      form: formDefaults(),
      // 总Tab key
      activeKey: '1',
      // 费率Tab key
      activeRateKey: '0',
      previewVisible: false,
      previewTitle: '',
      previewImage: '',
      certTypeEnum,
      // 费率二维数组
      rateInfoComparisonArr: [],
      // 子代分润出款通道列表
      subChannelNos: [],
      /**
       * 操作类型
       * @prop {number}
       * 1 机构添加操作;
         2 费率变更操作;
         3 结算账户变更操作;
         4 基本信息修改操作
       */
      optType: null,
      /**
       * 用户类型
       * @prop {number}
       * 1 大区;
       * 2 运营;
       * 3 代理商
       */
      userType: null
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = formDefaults();
        data.optType = props.detail.optType;
        data.userType = props.detail.userType;

        if ([1, 4].includes(data.optType)) {
          data.activeKey = '1';
        } else if (data.optType === 2) {
          data.activeKey = '2';
        }
        data.activeRateKey = '0';

        Object.keys(data.form).forEach(k => {
          data.form[k] = props.detail[k]
            ? typeof props.detail[k] === 'string'
              ? JSON.parse(props.detail[k])
              : props.detail[k]
            : data.form[k];
        });

        ['checkBaseInfo', 'oldBaseInfo'].forEach(i => {
          const signDate = data.form[i].signDate;
          data.form[i].signDate = signDate ? dayjs(signDate).format('YYYY-MM-DD') : '';
        });

        const allChannelCodes = data.form.checkRateInfo.map(i => i.channelCode) || [];
        const allChannel = Array.from(new Set(allChannelCodes));

        data.rateInfoComparisonArr = [];

        allChannel.map(channelCode => {
          const channelItem = props.channelCodes.find(c => channelCode === c.channelCode);
          const channelName = channelItem?.channelName;

          const checkRateInfoByCode = data.form.checkRateInfo.filter(c => c.channelCode === channelCode);
          const oldRateInfoByCode = data.form.oldRateInfo.filter(c => c.channelCode === channelCode);

          [checkRateInfoByCode, oldRateInfoByCode].forEach(ratelist => {
            if (ratelist) {
              ratelist.forEach(r => {
                if (r.rateInfoDTO) {
                  r = Object.assign(r, r.rateInfoDTO);
                } else if (r.rateInfo) {
                  r = Object.assign(r, JSON.parse(r.rateInfo));
                }
              });

              ratelist.sort(function (a, b) {
                return a.templateNo - b.templateNo;
              });

              ratelist.sort(function (a, b) {
                return a.rateType - b.rateType;
              });
            }
          });

          let targetScanRateKeys = [];
          const scanRateItem = checkRateInfoByCode.find(c => c.rateType === 1);
          if (scanRateItem) {
            const rateKeys = Object.keys(scanRateItem);
            targetScanRateKeys = rateInfoMap.value[1]
              .filter(r => r.infoKey === 'divider' || rateKeys.includes(r.infoKey))
              .map(r => r.infoKey);
          }

          // 费率信息格式为 [[checkRateInfo,oldRateInfo]] 形式 ,二维数组
          data.rateInfoComparisonArr.push([
            {
              channelCode,
              channelName,
              rateDTOList: checkRateInfoByCode,
              targetScanRateKeys
            },
            {
              rateDTOList: oldRateInfoByCode
            }
          ]);
        });

        getSubChannelNos();
      }
    });

    // #region 图片预览
    const handlePreview = imagePath => {
      data.previewImage = imagePath;
      setPreviewVisible(true);
    };
    const setPreviewVisible = visible => {
      data.previewVisible = visible;
    };
    // #endregion

    /** 信息是否变更 */
    const isInfoChanged = (current, old) => {
      if ([current, old].every(i => i !== 0 && !i)) return false;
      return current !== old;
    };

    /** 图片是否变更 */
    const isImgChanged = imageType => {
      const [currentItem, oldItem] = ['checkImageInfo', 'oldImageInfo'].map(i => {
        const item = data.form[i].find(c => c.imageType === imageType);
        return item || {};
      });

      return isInfoChanged(currentItem.imagePath, oldItem.imagePath);
    };

    const getSubChannelNos = async () => {
      const list = await RemitChannelApi.findAll({ validStatus: 1, remitChannelClassify: 2 });
      data.subChannelNos = list || [];
    };

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible,
      handlePreview,
      setPreviewVisible,
      isImgChanged,
      isInfoChanged,
      baseInfoMap,
      rateInfoMap,
      fileList,
      settleInfoMap
    };
  }
};
</script>

<style lang="less" scoped>
:deep(.ant-form-item-label) {
  text-align: center;
  background-color: #f1f3f4;
}
.info-changed {
  :deep(.ant-form-item-label) {
    background-color: #fa541cc9;
  }
}
.hide-label {
  :deep(.ant-form-item-label) {
    display: none;
  }
  .ant-divider-horizontal.ant-divider-with-text {
    margin: 0;
  }
}
:deep(.ant-row) {
  align-items: flex-start;
  .ant-form-item-control-input-content {
    display: flex;
    .ant-input,
    .ant-select-selector {
      border-radius: 0;
    }
  }
}
:deep(.ant-select-disabled.ant-select:not(.ant-select-customize-input)) .ant-select-selector {
  background-color: transparent;
}
</style>
