<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="用户编号">
              <a-input v-model:value.trim="where.userNo" placeholder="请输入用户编号" allow-clear />
            </a-form-item>
            <a-form-item label="用户类型">
              <a-select v-model:value="where.userType" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">大区</a-select-option>
                <a-select-option :value="2">运营中心</a-select-option>
                <a-select-option :value="3">代理商</a-select-option>
                <a-select-option :value="5">子级代理商</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="审核状态">
              <a-select v-model:value="where.checkStatus" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">待审核</a-select-option>
                <a-select-option :value="1">审核通过</a-select-option>
                <a-select-option :value="2">审核不通过</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="操作类型">
              <a-select v-model:value="where.optType" style="width: 200px" placeholder="请选择">
                <a-select-option v-for="({ label, value }, key) in optTypeEnum" :key="key" :value="value">{{ label }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="开始日期">
              <a-date-picker v-model:value="where.createStartTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item label="结束日期">
              <a-date-picker v-model:value="where.createEndTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'checkStatus'">
              <a-tag v-if="record.checkStatus === 0" color="processing">待审核</a-tag>
              <a-tag v-else-if="record.checkStatus === 1" color="success">审核通过</a-tag>
              <a-tag v-else-if="record.checkStatus === 2" color="error">审核不通过</a-tag>
            </template>

            <template v-else-if="['userType', 'checkUserType'].includes(column.key)">
              <a-badge v-if="record[column.key] === 0" color="purple" text="平台运营" />
              <a-badge v-else-if="record[column.key] === 1" color="pink" text="大区" />
              <a-badge v-else-if="record[column.key] === 2" color="blue" text="运营中心" />
              <a-badge v-else-if="record[column.key] === 3" color="cyan" text="代理商" />
              <a-badge v-else-if="record[column.key] === 5" color="orange" text="子级代理商" />
            </template>

            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleInfoChangeDetail(record)">详情</a>
                <template v-if="record.checkStatus === 0 && loginUserType.toString() === record.checkUserType.toString()">
                  <a-divider type="vertical" />
                  <a class="ele-text-warning" @click="handleAgencyDetail(record)">审核</a>
                </template>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 信息变更详情 -->
    <InfoChangeDetail
      v-model:visible="showInfoChangeDetail"
      :detail="current"
      :channelCodes="channelCodes"
      :ratePolicyList="ratePolicyList"
    />

    <!-- 审核 -->
    <BaseInfoChangeAgency v-model:visible="showBaseInfoChangeAgency" :detail="current" :ratePolicyList="ratePolicyList" @done="reload" />

    <!-- 审核 -->
    <AgencyDetail
      v-model:visible="showAgencyDetail"
      :detail="current"
      :ratePolicyList="ratePolicyList"
      :channelCodes="channelCodes"
      @done="reload"
    />
  </div>
</template>

<script>
import AgencyDetail from './AgencyDetail.vue';
import InfoChangeDetail from './InfoChangeDetail.vue';
import BaseInfoChangeAgency from './BaseInfoChangeAgency.vue';
import { AgencyAuditApi } from '@/api/businessTeam/agency-audit/AgencyAuditApi';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import { RatePolicyApi } from '@/api/businessTeam/rate-policy/RatePolicyApi';
import { hasPurview } from '@/utils/permission';

export default {
  name: 'AgencyAudit',
  components: {
    InfoChangeDetail,
    AgencyDetail,
    BaseInfoChangeAgency
  },
  data() {
    return {
      where: {},
      current: null,
      current2: null,
      showInfoChangeDetail: false,
      showAgencyDetail: false,
      showBaseInfoChangeAgency: false,
      optTypeEnum,
      channelCodes: [],
      ratePolicyList: [],
      optType: null,
      loginUserType: localStorage.getItem('SASS_USER_TYPE'),
      //表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          align: 'center',
          width: 80,
          fixed: 'left',
          hideCol: hasPurview(['1', '2', '3'])
        },
        {
          title: '企业名称',
          dataIndex: 'companyName',
          customRender: ({ record }) => {
            let companyName;
            switch (record.userType) {
              case 1:
                companyName = record.checkBaseInfo.regionName;
                break;
              case 2:
                companyName = record.checkBaseInfo.branchName;
                break;
              case 3:
              case 5:
                companyName = record.checkBaseInfo.agentName;
                break;
            }
            return companyName || '--';
          }
        },
        {
          title: '用户类型',
          dataIndex: 'userType',
          key: 'userType',
          width: 120,
          align: 'center'
        },
        {
          title: '用户编号',
          dataIndex: 'userNo',
          align: 'center'
        },
        {
          title: '大区编号',
          dataIndex: ['checkBaseInfo', 'regionNo'],
          customRender: ({ text }) => text || '--',
          align: 'center',
          hideCol: hasPurview(['1', '2', '3'])
        },
        {
          title: '运营中心编号',
          dataIndex: ['checkBaseInfo', 'branchNo'],
          customRender: ({ text }) => text || '--',
          align: 'center',
          hideCol: hasPurview(['2', '3'])
        },
        {
          title: '审核状态',
          dataIndex: 'checkStatus',
          key: 'checkStatus',
          width: 120,
          align: 'center'
        },
        {
          title: '审核描述',
          dataIndex: 'checkDesc'
        },
        {
          title: '审核人',
          dataIndex: 'checkUserNo',
          align: 'center',
          hideCol: hasPurview(['1', '2', '3'])
        },
        {
          title: '审核用户类型',
          dataIndex: 'checkUserType',
          key: 'checkUserType',
          width: 140,
          align: 'center'
        },
        {
          title: '审核时间',
          dataIndex: 'lastModifyTime',
          customRender: ({ text }) => text || '--'
        },
        {
          title: '操作类型',
          dataIndex: 'optType',
          align: 'center',
          customRender: function ({ text }) {
            const item = optTypeEnum.find(e => e.value === text);
            return item?.label || '--';
          }
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          key: 'action',
          width: 140,
          fixed: 'right',
          align: 'center'
        }
      ].filter(i => !i.hideCol)
    };
  },
  mounted() {
    this.getChannelList();
    this.getRatePolicyList();
  },
  methods: {
    async handleInfoChangeDetail(row) {
      const data = await AgencyAuditApi.detail({ id: row.id });
      this.current = data;
      this.showInfoChangeDetail = true;
    },

    async handleAgencyDetail(row) {
      const data = await AgencyAuditApi.detail({ id: row.id });
      this.current = data;

      if (row.optType === 4) {
        this.showBaseInfoChangeAgency = true;
      } else {
        this.showAgencyDetail = true;
      }
    },

    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    async datasource({ page, limit, where, orders }) {
      const data = await AgencyAuditApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
      data.rows.forEach(i => {
        i.checkBaseInfo = i.checkBaseInfo ? JSON.parse(i.checkBaseInfo) : {};
      });
      return data;
    },

    async getChannelList() {
      const data = await ChannelManageApi.list({ validStatus: 1 });
      this.channelCodes = data;
    },

    async getRatePolicyList() {
      const ratePolicyList = await RatePolicyApi.list({ userType: 2, policyType: 1});
      this.ratePolicyList = ratePolicyList;
    }
  }
};

const optTypeEnum = [
  { label: '机构添加操作', value: 1 },
  { label: '费率变更操作', value: 2 },
  { label: '结算账户变更操作', value: 3 },
  { label: '基本信息修改操作', value: 4 }
];
</script>
