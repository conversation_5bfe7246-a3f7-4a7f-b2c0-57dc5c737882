<template>
  <a-modal
    :width="750"
    :visible="visible"
    title="审核"
    :body-style="{ paddingBottom: '8px' }"
    :mask-closable="false"
    @update:visible="updateVisible"
  >
    <a-form :layout="['0', '2'].includes(activeKey) ? 'horizontal' : 'vertical'">
      <a-tabs v-model:activeKey="activeKey">
        <a-tab-pane key="0" tab="活动政策信息">
          <a-form :label-col="{ style: { width: '130px' } }">
            <div class="card-container">
              <a-tabs v-model:activeKey="activeChannelTabKey" type="card" @change="onChangeChannelTab">
                <a-tab-pane
                  v-for="(channel, cIndex) in form.policyConfigByChannelGroup || []"
                  :tab="channel.channelName"
                  :key="String(cIndex)"
                  force-render
                >
                  <a-card style="margin-bottom: 15px">
                    <a-tabs v-model:activeKey="activeTerminalSourceTabKey">
                      <a-tab-pane v-for="tab in channel.channelData || []" :tab="tab.label" :key="String(tab.key)" force-render>
                        <a-divider orientation="left" dashed orientationMargin="0">终端服务费返现政策</a-divider>
                        <template v-if="tab.serviceFeeData?.length">
                          <template v-for="(item, key) in tab.serviceFeeData" :key="key">
                            <a-row v-if="item.show">
                              <a-col :span="12">
                                <a-form-item label="服务费金额(元)">
                                  <a-input v-model:value="item.policyName" placeholder="服务费金额" disabled />
                                </a-form-item>
                              </a-col>
                              <a-col :span="12">
                                <a-form-item label="返现金额(元)">
                                  <a-input v-model:value="item.cashbackAmt" placeholder="请输入返现金额" disabled />
                                </a-form-item>
                              </a-col>
                            </a-row>
                          </template>
                        </template>
                        <a-alert v-else message="没有政策配置哦~" banner />

                        <a-divider orientation="left" dashed orientationMargin="0">流量费返现政策</a-divider>
                        <template v-if="tab.simFeeData?.some(s => s.data.length)">
                          <div v-for="(period, idx) in tab.simFeeData" :key="idx">
                            <template v-if="period.show">
                              <div style="margin-bottom: 10px">
                                <a-typography-text strong>{{ period.name }}</a-typography-text>
                              </div>
                              <template v-for="(item, key) in period.data || []" :key="key">
                                <a-row v-if="item.show">
                                  <a-col :span="12">
                                    <a-form-item label="流量费金额(元)">
                                      <a-input v-model:value="item.policyName" placeholder="流量费金额" disabled />
                                    </a-form-item>
                                  </a-col>
                                  <a-col :span="12">
                                    <a-form-item label="返现金额(元)">
                                      <a-input v-model:value="item.cashbackAmt" placeholder="请输入返现金额" disabled />
                                    </a-form-item>
                                  </a-col>
                                </a-row>
                              </template>
                            </template>
                          </div>
                        </template>
                        <a-alert v-else message="没有政策配置哦~" banner />
                      </a-tab-pane>
                    </a-tabs>
                  </a-card>
                </a-tab-pane>
              </a-tabs>
            </div>
          </a-form>
        </a-tab-pane>

        <a-tab-pane key="1" tab="基本信息" v-if="[1, 4].includes(optType)">
          <!-- 基本信息 -->
          <a-divider orientation="left" :orientationMargin="0" dashed>基本信息</a-divider>
          <!-- 运营、代理商显示费率政策 -->
          <a-row :gutter="16" v-if="form.userType !== 1">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="费率政策" name="policyId">
                <a-select v-model:value="form.checkBaseInfo.policyId" placeholder="--" class="ele-fluid" disabled>
                  <a-select-option v-for="(item, key) in ratePolicyList" :value="Number(item.id)" :key="key">
                    {{ item.policyDesc }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="企业名称">
                <a-input v-model:value="form.checkBaseInfo[orgNamePrefixs[form.userType] + 'Name']" placeholder="企业名称" disabled />
              </a-form-item>
              <a-form-item label="统一社会信用代码" name="licenseNo">
                <a-input v-model:value="form.checkBaseInfo.licenseNo" placeholder="统一社会信用代码" disabled />
              </a-form-item>
              <a-form-item label="对公开票税点" name="taxPoint">
                <a-input-number v-model:value="form.checkBaseInfo.taxPoint" placeholder="对公开票税点" class="ele-fluid" disabled />
              </a-form-item>
              <a-form-item label="经营省市区" name="cityCode">
                <a-cascader v-model:value="areaCodeValue" :options="regionsData" :allow-clear="false" placeholder="--" disabled />
              </a-form-item>
              <a-form-item
                label="子代分润出款通道"
                name="subAgentSettleChannelCode"
                v-if="form.userType === 3 && [1, 2].includes(form.checkBaseInfo.subAgentSettleChannelWay)"
              >
                <a-select
                  v-model:value="form.checkBaseInfo.subAgentSettleChannelCode"
                  style="width: 100%"
                  placeholder="请选择"
                  :options="subChannelNos"
                  :fieldNames="{ label: 'channelName', value: 'channelNo' }"
                  disabled
                />
              </a-form-item>
              <a-form-item label="分润出款通道" name="settleChannelCode" v-if="[1, 2].includes(form.checkBaseInfo.settleChannelWay)">
                <a-select
                  v-model:value="form.checkBaseInfo.settleChannelCode"
                  style="width: 100%"
                  placeholder="请选择"
                  :options="subChannelNos"
                  :fieldNames="{ label: 'channelName', value: 'channelNo' }"
                  disabled
                />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="企业简称">
                <a-input v-model:value="form.checkBaseInfo[orgNamePrefixs[form.userType] + 'Sname']" placeholder="企业简称" disabled />
              </a-form-item>
              <a-form-item label="企业地址(营业执照注册地址)" name="licenseAddr">
                <a-textarea
                  v-model:value="form.checkBaseInfo.licenseAddr"
                  placeholder="企业地址"
                  :auto-size="{ minRows: 1, maxRows: 6 }"
                  disabled
                />
              </a-form-item>
              <a-form-item label="企业办公地址" name="officeAddr">
                <a-textarea
                  v-model:value="form.checkBaseInfo.officeAddr"
                  placeholder="企业办公地址"
                  :auto-size="{ minRows: 1, maxRows: 6 }"
                  disabled
                />
              </a-form-item>
              <a-form-item label="子代分润出款方式" name="subAgentSettleChannelWay" v-if="[3].includes(form.userType)">
                <a-select v-model:value="form.checkBaseInfo.subAgentSettleChannelWay" class="ele-fluid" disabled>
                  <a-select-option :value="3">展业平台</a-select-option>
                  <a-select-option :value="1">平台出款</a-select-option>
                  <a-select-option :value="0">交易通道</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="分润出款方式" name="settleChannelWay">
                <a-select v-model:value="form.checkBaseInfo.settleChannelWay" class="ele-fluid" disabled>
                  <a-select-option :value="3">展业平台</a-select-option>
                  <a-select-option :value="1">平台出款</a-select-option>
                  <a-select-option :value="0">交易通道</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 法人信息 -->
          <a-divider orientation="left" :orientationMargin="0" dashed>法人信息</a-divider>
          <a-row :gutter="16">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="法人姓名" name="legalName">
                <a-input v-model:value="form.checkBaseInfo.legalName" placeholder="法人姓名" disabled />
              </a-form-item>
              <a-form-item label="法人证件类型" name="legalCertType">
                <a-select v-model:value="form.checkBaseInfo.legalCertType" class="ele-fluid" placeholder="请选择" disabled>
                  <a-select-option v-for="({ label, value }, key) in certTypeEnum" :key="key" :value="value">{{ label }}</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="证件有效期">
                <a-space>
                  <a-input v-model:value="form.checkBaseInfo.legalCertStartDate" placeholder="格式: 2020-05-20" disabled />
                  -
                  <a-input v-model:value="form.checkBaseInfo.legalCertEndDate" placeholder="格式: 2020-05-20" disabled />
                </a-space>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="法人手机号" name="legalTelMask">
                <a-input v-model:value="form.checkBaseInfo.legalTelMask" placeholder="法人手机号" disabled />
              </a-form-item>
              <a-form-item label="法人证件号码" name="legalCertNoMask">
                <a-input v-model:value="form.checkBaseInfo.legalCertNoMask" placeholder="法人证件号码" disabled />
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 联系人信息 -->
          <a-divider orientation="left" :orientationMargin="0" dashed>联系人信息</a-divider>
          <a-row :gutter="16">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="联系人姓名" name="contactsName">
                <a-input v-model:value="form.checkBaseInfo.contactsName" placeholder="联系人姓名" disabled />
              </a-form-item>
              <a-form-item label="联系人证件类型" name="contactsCertType">
                <a-select v-model:value="form.checkBaseInfo.contactsCertType" placeholder="--" class="ele-fluid" disabled>
                  <a-select-option v-for="({ label, value }, key) in certTypeEnum" :key="key" :value="value">{{ label }}</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="证件有效期">
                <a-space>
                  <a-input v-model:value="form.checkBaseInfo.contactsCertStartDate" placeholder="格式: 2020-05-20" disabled />
                  -
                  <a-input v-model:value="form.checkBaseInfo.contactsCertEndDate" placeholder="格式: 2020-05-20" disabled />
                </a-space>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="联系人手机号(登录手机号)" name="contactsTelMask">
                <a-input v-model:value="form.checkBaseInfo.contactsTelMask" placeholder="联系人手机号" disabled />
              </a-form-item>
              <a-form-item label="联系人证件号码" name="contactsCertNoMask">
                <a-input v-model:value="form.checkBaseInfo.contactsCertNoMask" placeholder="联系人证件号码" disabled />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="签约日期" name="signDate">
                <a-date-picker :value="form.checkBaseInfo.signDate" valueFormat="YYYY-MM-DD" placeholder="" class="ele-fluid" disabled />
              </a-form-item>
            </a-col>
          </a-row>
        </a-tab-pane>

        <template v-if="optType === 2" #renderTabBar></template>
        <a-tab-pane key="2" tab="费率信息" v-if="[1, 2].includes(optType)">
          <a-form
            class="formItem-label__bg"
            :label-col="{ md: { span: 17 }, sm: { span: 24 } }"
            :wrapper-col="{ md: { span: 7 }, sm: { span: 24 } }"
          >
            <a-tabs v-model:activeKey="activeRateKey">
              <a-tab-pane v-for="(item, key) in form.rateDTOList || []" :tab="`${item.channelName}-${item.channelCode}`" :key="String(key)">
                <!-- 费率相关 -->
                <RateModule
                  v-for="(rateItem, key2) in item.rateInfoDTO || []"
                  :key="key2"
                  :rate-item="rateItem"
                  :bankList="bankList"
                  :showPayWallet="showPayWallet"
                  disabled
                />
              </a-tab-pane>
            </a-tabs>
          </a-form>
        </a-tab-pane>

        <a-tab-pane key="3" tab="图片信息" v-if="[1, 4].includes(optType)">
          <a-form-item v-for="(item, key) in form.checkImageInfo" :key="key" :label="item.label">
            <a-upload
              :file-list="[{ url: item.imagePath }]"
              list-type="picture-card"
              @preview="() => handlePreview(item.imagePath)"
              disabled
            />
          </a-form-item>
          <!-- 预览图片 -->
          <a-image
            :style="{ display: 'none' }"
            :src="previewImage"
            :preview="{ visible: previewVisible, onVisibleChange: setPreviewVisible }"
          />
        </a-tab-pane>

        <a-tab-pane key="4" tab="结算信息" v-if="[1].includes(optType) && settleForm.typeCode">
          <!-- 结算信息 -->
          <div style="margin-bottom: 20px">
            <span style="margin-right: 20px">结算账户类型</span>
            <a-radio-group v-model:value="settleForm.accountType" disabled>
              <a-radio value="G">对公</a-radio>
              <a-radio value="S">对私</a-radio>
            </a-radio-group>
          </div>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="银行卡账户姓名" name="bankAccountName">
                <a-input v-model:value="settleForm.bankAccountName" placeholder="请输入银行卡账户姓名" allow-clear disabled />
              </a-form-item>
              <a-form-item label="开户行总行" name="typeCode">
                <a-select v-model:value="settleForm.typeCode" style="width: 100%" placeholder="请选择" disabled>
                  <a-select-option v-for="(item, key) in bankHeadOffice" :key="key" :value="item.typeCode">
                    {{ item.typeName }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="开户行支行" name="bankChannelNo">
                <a-select v-model:value="settleForm.bankChannelNo" style="width: 100%" placeholder="请选择" disabled>
                  <a-select-option v-for="(item, key) in bankSubBranch" :key="key" :value="item.bankChannelNo">
                    {{ item.bankName }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="银行卡卡号" name="bankAccountNo">
                <a-input v-model:value="settleForm.bankAccountNoMask" placeholder="请输入银行卡卡号" disabled />
              </a-form-item>
              <a-form-item label="开户行所在地区" name="bankCity">
                <a-cascader v-model:value="bankAreaValue" :options="bankRegionsData" disabled placeholder="" />
              </a-form-item>
              <a-form-item label="银行卡预留手机号" name="mobile">
                <a-input v-model:value="settleForm.mobileMask" placeholder="请输入银行卡预留手机号" disabled />
              </a-form-item>
            </a-col>
          </a-row>
        </a-tab-pane>
      </a-tabs>

      <a-form-item label="审核意见" required>
        <a-textarea v-model:value="form.checkDesc" :auto-size="{ minRows: 3, maxRows: 5 }" />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-button type="primary" :loading="loading" @click="handleAudit(1)"><check-outlined />通过</a-button>
      <a-button type="danger" :loading="loading" @click="handleAudit(2)"><close-outlined />驳回</a-button>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';
import { message } from 'ant-design-vue';
import { AgencyAuditApi } from '@/api/businessTeam/agency-audit/AgencyAuditApi';
import { AreaApi } from '@/api/base/AreaApi';
import { BankCodeManageApi } from '@/api/base/BankCodeManageApi';
import { certTypeEnum } from '@/config/enumerate';
import RateModule from '../_components/RateModule.vue';
import dayjs from 'dayjs';
import { BankTypeApi } from '@/api/base/BankTypeApi';
import { BankInfoApi } from '@/api/base/BankInfoApi';
import { deepCopy } from '@/utils/util';
import { RemitChannelApi } from '@/api/account/remit-channel/RemitChannelApi';

const simFeePolicyPeriodGroupDef = [
  {
    name: '第一期',
    field: 'firstPeriodList',
    data: []
  },
  {
    name: '第二期',
    field: 'secondPeriodList',
    data: []
  },
  {
    name: '第三期',
    field: 'thirdPeriodList',
    data: []
  },
  {
    name: '标准期 (第四期以及后续阶段)',
    field: 'fourthPeriodList',
    data: []
  }
];

const policyConfigByTerminalSourceDef = [
  {
    label: '全款机配置',
    key: 1,
    serviceFeeData: [],
    simFeeData: []
  },
  {
    label: '分期机配置',
    key: 2,
    serviceFeeData: [],
    simFeeData: []
  }
];

export default {
  components: { RateModule },
  props: {
    visible: Boolean,
    detail: Object,
    channelCodes: Array,
    ratePolicyList: Array
  },
  emits: ['update:visible'],
  setup(props, context) {
    // 设置表单默认值
    function formDefaults() {
      return {
        checkBaseInfo: {},
        checkRateInfo: [],
        checkImageInfo: [],
        checkCashbackPolicy: {},
        checkSettleAccount: {}
      };
    }

    const data = reactive({
      settleForm: {},
      form: formDefaults(),
      activeKey: '0',
      activeRateKey: '0',
      activeTerminalSourceTabKey: '1',
      activeChannelTabKey: '0',
      previewVisible: false,
      previewImage: '',
      loading: false,
      certTypeEnum,
      bankList: [],
      areaCodeValue: [],
      regionsData: [],
      bankRegionsData: [],
      bankAreaValue: [],
      bankHeadOffice: [],
      bankSubBranch: [],
      subChannelNos: [],
      /**
       * 操作类型
       * @prop {number}
       * 1 机构添加操作;
         2 费率变更操作;
         3 结算账户变更操作;
         4 基本信息修改操作
       */
      optType: null,
      showPayWallet: false
    });

    const imageTypeEnum = [
      { label: '身份证头像面', imageType: 1 },
      { label: '身份证国徽面', imageType: 2 },
      { label: '结算卡正面', imageType: 3 },
      { label: '营业执照照片', imageType: 7 },
      { label: '税务登记证照片', imageType: 8 }
    ];

    const orgNamePrefixs = [null, 'region', 'branch', 'agent'];

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = formDefaults();
        data.optType = props.detail.optType;

        data.showPayWallet = ![5].includes(props.detail.userType);

        const detailInfo = Object.assign({}, props.detail);

        const jsonObjects = ['checkBaseInfo', 'checkRateInfo', 'checkImageInfo', 'checkCashbackPolicy', 'checkSettleAccount'];
        jsonObjects.forEach(j => {
          detailInfo[j] = detailInfo[j] ? (typeof detailInfo[j] === 'string' ? JSON.parse(detailInfo[j]) : detailInfo[j]) : data.form[j];
        });

        const simFeePolicyMap = detailInfo.checkCashbackPolicy.simFeeNewPolicyDTO || {};
        const simFeePolicyPeriodGroupMap = deepCopy(simFeePolicyPeriodGroupDef);
        simFeePolicyPeriodGroupMap.forEach(period => {
          Object.keys(simFeePolicyMap).forEach(key => {
            if (period.field === key) {
              period.data = simFeePolicyMap[key];
            }
          });
        });

        let allChannelCodes = [];
        try {
          const channelCodes = new Set(
            [...(detailInfo.checkCashbackPolicy.serviceFeePolicyDTOList || []), ...Object.values(simFeePolicyMap || {})]
              .flat(Infinity)
              .flatMap(item => (Array.isArray(item) ? item.map(subItem => subItem?.channelCode) : item?.channelCode))
              .filter(code => code?.trim())
              .map(code => code.trim())
          );
          allChannelCodes = [...channelCodes];
        } catch (error) {
          console.log(error);
        }

        let policyConfigByChannelGroup = [];
        allChannelCodes.forEach(item => {
          const channelItem = props.channelCodes.find(channel => channel.channelCode === item);
          policyConfigByChannelGroup.push({
            channelCode: item,
            channelName: channelItem?.channelName,
            channelData: deepCopy(policyConfigByTerminalSourceDef)
          });
        });

        policyConfigByChannelGroup.forEach(channel => {
          channel.channelData.forEach(tab => {
            tab.serviceFeeData = detailInfo.checkCashbackPolicy.serviceFeePolicyDTOList.filter(
              item => item.terminalSource === tab.key && item.channelCode === channel.channelCode
            );

            tab.serviceFeeData.forEach(item => {
              item.show = Number(item.parentCashbackAmt) > 0 || !(Number(item.serviceFeeAmt) > 0);
            });

            const simFeeData = simFeePolicyPeriodGroupMap.map(period => {
              return {
                ...period,
                data: period.data.filter(item => item.terminalSource === tab.key && item.channelCode === channel.channelCode)
              };
            });

            simFeeData.forEach(period => {
              period.data.forEach(item => {
                item.show = Number(item.parentCashbackAmt) > 0 || !(Number(item.simFeeAmt) > 0);
              });

              period.show = period.data.some(item => item.show);
            });

            tab.simFeeData = simFeeData;
          });
        });

        detailInfo.policyConfigByChannelGroup = policyConfigByChannelGroup;

        data.settleForm = detailInfo.checkSettleAccount;

        const signDate = detailInfo.checkBaseInfo.signDate;
        if (signDate) {
          detailInfo.checkBaseInfo.signDate = dayjs(signDate).format('YYYY-MM-DD');
        }

        const channelCodes = detailInfo.checkRateInfo?.map(i => i.channelCode) || [];
        const allChannel = Array.from(new Set(channelCodes));
        var rateDTOList = [];

        allChannel.forEach(channelCode => {
          const channelItem = props.channelCodes.find(c => channelCode === c.channelCode);
          const channelName = channelItem?.channelName;

          const checkRateInfoByCode = detailInfo.checkRateInfo.filter(c => c.channelCode === channelCode);

          checkRateInfoByCode.forEach(r => {
            if (r.rateInfo) {
              r.rateInfoDTO = JSON.parse(r.rateInfo);
            } else {
              r.rateInfoDTO = r.rateInfoDTO || {};
            }
          });

          checkRateInfoByCode.sort(function (a, b) {
            return a.rateType - b.rateType;
          });

          rateDTOList.push({
            channelCode,
            channelName,
            rateInfoDTO: checkRateInfoByCode
          });
        });

        detailInfo.rateDTOList = rateDTOList;

        detailInfo.checkImageInfo.forEach(c => {
          const item = imageTypeEnum.find(i => i.imageType === c.imageType);
          c.label = item?.label;
        });

        data.form = detailInfo;
        data.form.checkDesc = '';

        if (data.optType === 2) {
          data.activeKey = '2';
        }
        data.activeRateKey = '0';

        data.areaCodeValue = [data.form.checkBaseInfo.provinceCode, data.form.checkBaseInfo.cityCode, data.form.checkBaseInfo.districtCode];

        loadAreaData([]);

        if (data.optType === 1) {
          if (data.settleForm?.typeCode) {
            getBankHeadOffice();
            data.bankAreaValue = [data.settleForm.bankProvince, data.settleForm.bankCity];
            if (data.settleForm?.bankCity) {
              getBankSubBranch();
            }
          }
        }

        if (data.form.userType === 3) {
          getSubChannelNos();
        }
      }
    });

    /**
     * 发起审核操作
     * @param {*} checkStatus 审核状态 1 通过 ; 2 驳回
     */
    const handleAudit = async checkStatus => {
      const { id, checkDesc } = data.form;

      if (!checkDesc) return message.warning('请填写审核意见!');

      // 修改加载框为正在加载
      data.loading = true;

      AgencyAuditApi.audit({ checkStatus, id, checkDesc })
        .then(result => {
          // 移除加载框
          data.loading = false;

          // 提示修改成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          updateVisible(false);

          // 触发父组件done事件
          context.emit('done');
        })
        .catch(() => {
          data.loading = false;
        });
    };

    const handlePreview = imagePath => {
      data.previewImage = imagePath;
      setPreviewVisible(true);
    };

    const setPreviewVisible = visible => {
      data.previewVisible = visible;
    };

    const loadAreaData = async ([option]) => {
      const targetOption = option ? option : { level: 1, value: '' };
      const { level, value } = targetOption;

      const res = await AreaApi.list({ level: level + 1, status: 1, parentCode: value });
      const formatData = res.map(d => {
        return { label: d.areaName, value: d.areaCode, level: d.level };
      });

      if (level === 1) {
        data.regionsData = deepCopy(formatData);
        data.bankRegionsData = deepCopy(formatData);
      } else {
        targetOption.children = formatData;
      }

      if (!option) {
        const item = data.regionsData.find(r => r.value === data.form.checkBaseInfo.provinceCode);
        if (item) {
          await loadAreaData([item]);
          const citem = item.children.find(i => i.value === data.form.checkBaseInfo.cityCode);
          citem && (await loadAreaData([citem]));
        }

        const bankitem = data.bankRegionsData.find(r => r.value === data.settleForm?.bankProvince);
        bankitem && loadAreaData([bankitem]);
      }
    };

    const getBankList = async () => {
      const list = await BankCodeManageApi.list();
      data.bankList = list || [];
    };
    const getSubChannelNos = async () => {
      const list = await RemitChannelApi.findAll({ validStatus: 1, remitChannelClassify: 2 });
      data.subChannelNos = list || [];
    };
    getBankList();

    async function getBankHeadOffice() {
      const res = await BankTypeApi.list({ status: 1 });
      data.bankHeadOffice = res || [];
    }

    async function getBankSubBranch() {
      data.bankSubBranch = [];
      const res = await BankInfoApi.list({
        status: 1,
        typeCode: data.settleForm.typeCode,
        provinceCode: data.settleForm.bankProvince,
        cityCode: data.settleForm.bankCity
      });
      data.bankSubBranch = res || [];
    }

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    const onChangeChannelTab = () => {
      data.activeTerminalSourceTabKey = '1';
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible,
      handlePreview,
      handleAudit,
      setPreviewVisible,
      orgNamePrefixs,
      onChangeChannelTab
    };
  }
};
</script>
<style lang="less" scoped>
.rate-title {
  border-left: 5px solid;
  border-color: var(--warning-color);
  padding-left: 10px;
}

.card-title-background {
  background-color: #f5f5f5;
  height: 2em;
  line-height: 2em;
  margin-bottom: 2em;
}

::v-deep(.formItem-label__bg) .ant-form-item-label {
  text-align: center;
  background-color: #f3f5f7;
}
</style>
