<template>
  <a-modal
    :width="760"
    :visible="visible"
    :confirm-loading="loading"
    title="状态编辑"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules" layout="vertical">
      <a-row :gutter="24">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="终端SN入库开关" name="snInboundSwitch">
            <a-select v-model:value="form.snInboundSwitch" class="ele-fluid" placeholder="请选择">
              <a-select-option :value="1">开启</a-select-option>
              <a-select-option :value="0">关闭</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="终端采购协议电签状态" name="deviceSignStatus">
            <a-select v-model:value="form.deviceSignStatus" class="ele-fluid" placeholder="请选择" disabled>
              <a-select-option v-for="([value, label], key) in CommonStatusMap" :value="value" :key="key">{{ label }}</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="钉灵工认证状态(平台出款)" name="dlgAuthStatus">
            <a-select v-model:value="form.dlgAuthStatus" class="ele-fluid" placeholder="请选择" disabled>
              <a-select-option v-for="([value, label], key) in CommonStatusMap" :value="value" :key="key">{{ label }}</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="对公账户通道报备状态" name="toGReportStatus">
            <a-select v-model:value="form.toGReportStatus" class="ele-fluid" placeholder="请选择" disabled>
              <a-select-option v-for="([value, label], key) in CommonStatusMap" :value="value" :key="key">{{ label }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="支付外包服务协议电签状态" name="orgChnSignStatus">
            <a-select v-model:value="form.orgChnSignStatus" class="ele-fluid" placeholder="请选择" disabled>
              <a-select-option v-for="([value, label], key) in CommonStatusMap" :value="value" :key="key">{{ label }}</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="钉灵工签约状态(平台出款)" name="dlgSignStatus">
            <a-select v-model:value="form.dlgSignStatus" class="ele-fluid" placeholder="请选择" disabled>
              <a-select-option v-for="([value, label], key) in CommonStatusMap" :value="value" :key="key">{{ label }}</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="对私账户通道报备状态" name="toSReportStatus">
            <a-select v-model:value="form.toSReportStatus" class="ele-fluid" placeholder="请选择" disabled>
              <a-select-option v-for="([value, label], key) in CommonStatusMap" :value="value" :key="key">{{ label }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { RegionManageApi } from '@/api/businessTeam/region-center/RegionManageApi';

const CommonStatusMap = new Map([
  [0, '待处理'],
  [1, '处理中'],
  [2, '成功'],
  [3, '失败']
]);

export default {
  props: {
    visible: Boolean,
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      CommonStatusMap,
      // 表单数据
      form: {},
      rules: {
        snInboundSwitch: [{ required: true, message: '请选择' }]
      },
      // 提交状态
      loading: false
    };
  },
  mounted() {
    this.form = Object.assign({}, this.data);
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      RegionManageApi.statusEdit(this.form)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示修改成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
