<template>
  <a-modal
    :width="500"
    :visible="visible"
    title="添加通道政策"
    :body-style="{ paddingBottom: '8px' }"
    :mask-closable="false"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 4 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 20 }, sm: { span: 24 } }"
    >
      <a-form-item label="选择通道" name="channelCode">
        <a-select v-model:value="form.channelCode" class="ele-fluid" placeholder="请选择" @change="handleChangeChannel">
          <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
            {{ channelName }}
          </a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
export default {
  props: {
    visible: Boolean,
    channelCodes: Array
  },
  emits: ['update:visible'],
  data() {
    return {
      form: {},
      rules: {
        channelCode: [{ required: true, message: '请选择通道' }]
      }
    };
  },
  methods: {
    async save() {
      await this.$refs.form.validate();

      this.updateVisible(false);

      this.$emit('done', this.form);
    },

    handleChangeChannel(value, option) {
      this.form.channelName = this.channelCodes[option.key]?.channelName;
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
