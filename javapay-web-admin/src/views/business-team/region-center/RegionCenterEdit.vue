<template>
  <a-modal
    :width="800"
    :visible="visible"
    :title="modalTitle"
    :confirm-loading="loading"
    :body-style="{ paddingBottom: '8px' }"
    :mask-closable="false"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules" :layout="['2', '5'].includes(activeTabKey) ? 'horizontal' : 'vertical'">
      <a-tabs v-model:activeKey="activeTabKey" type="card">
        <a-tab-pane key="1" tab="基本信息" v-if="optType !== 2">
          <!-- 基本信息 -->
          <div class="card-title card-title-background">基本信息</div>
          <a-row :gutter="100" justify="space-around">
            <a-form-item v-for="(item, key) in [fileMap[7]]" :key="key" :label="item.label" :required="item.required">
              <a-upload
                v-model:file-list="item.fileData"
                accept=".png,.jpg,.jpeg"
                :multiple="false"
                list-type="picture-card"
                :disabled="isDisabled"
                :before-upload="file => beforeUpload(file, item)"
                @remove="() => handleRemove(item)"
                @preview="() => handlePreview(item)"
              >
                <div v-if="!item.fileData?.length">
                  <plus-outlined />
                  <div style="margin-top: 8px">上传</div>
                </div>
              </a-upload>
            </a-form-item>
            <span></span>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="企业名称" name="regionName">
                <a-input v-model:value="form.regionName" placeholder="请输入企业名称" allow-clear :disabled="isDisabled" />
              </a-form-item>
              <a-form-item label="统一社会信用代码" name="licenseNo">
                <a-input v-model:value="form.licenseNo" placeholder="请输入统一社会信用代码" allow-clear :disabled="isDisabled" />
              </a-form-item>
              <a-form-item label="经营省市区" name="cityCode">
                <a-cascader
                  v-model:value="officeAreaValue"
                  :options="officeRegionsData"
                  :load-data="options => loadAreaData(options, 3)"
                  :allow-clear="false"
                  placeholder="请选择"
                  :disabled="isDisabled"
                  @change="selectedOfficeAreaValue"
                />
              </a-form-item>
              <a-form-item label="对公开票税点(%)" name="taxPoint">
                <a-input-number
                  v-model:value="form.taxPoint"
                  placeholder="请输入对公开票税点"
                  class="ele-fluid"
                  :disabled="optType !== 0"
                />
              </a-form-item>
              <a-form-item label="APP品牌类型" name="appBrandType" v-if="[0, 3].includes(optType)">
                <a-select v-model:value="form.appBrandType" style="width: 100%" placeholder="请选择" :disabled="isDisabled">
                  <a-select-option v-for="(item, key) in appBrands" :key="key" :value="item.appBrandType">{{
                    item.appName
                  }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="企业简称" name="regionSname">
                <a-input v-model:value="form.regionSname" placeholder="请输入企业简称" allow-clear :disabled="isDisabled" />
              </a-form-item>
              <a-form-item label="企业地址(营业执照注册地址)" name="licenseAddr">
                <a-textarea
                  v-model:value="form.licenseAddr"
                  placeholder="请输入企业地址"
                  :auto-size="{ minRows: 1, maxRows: 6 }"
                  :disabled="isDisabled"
                />
              </a-form-item>
              <a-form-item label="企业办公地址" name="officeAddr">
                <a-textarea
                  v-model:value="form.officeAddr"
                  placeholder="请输入企业办公地址(详细地址)"
                  :auto-size="{ minRows: 1, maxRows: 6 }"
                  :disabled="isDisabled"
                />
              </a-form-item>
              <a-form-item label="营业执照有效期">
                <a-space>
                  <a-input v-model:value="form.licenseStartDate" placeholder="格式: 2020-05-20" allow-clear :disabled="isDisabled" />
                  -
                  <a-input v-model:value="form.licenseEndDate" placeholder="格式: 2020-05-20" allow-clear :disabled="isDisabled" />
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="24">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="分润出款方式" name="settleChannelWay">
                <a-select
                  v-model:value="form.settleChannelWay"
                  placeholder="请选择"
                  class="ele-fluid"
                  :disabled="optType !== 0"
                  @change="form.settleChannelCode = null"
                >
                  <!-- <a-select-option :value="3">展业平台</a-select-option> -->
                  <a-select-option :value="1">平台出款</a-select-option>
                  <a-select-option :value="0">交易通道</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="分润出款通道" name="settleChannelCode" v-if="[1, 2].includes(form.settleChannelWay)">
                <a-select
                  v-model:value="form.settleChannelCode"
                  style="width: 100%"
                  placeholder="请选择"
                  :options="subChannelNos"
                  :fieldNames="{ label: 'channelName', value: 'channelNo' }"
                  :disabled="optType !== 0"
                />
              </a-form-item>
              <a-form-item label="分润出款通道" name="settleChannelCode" v-if="[0].includes(form.settleChannelWay)">
                <a-select v-model:value="form.settleChannelCode" style="width: 100%" placeholder="请选择" :disabled="optType !== 0">
                  <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
                    {{ channelName }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 法人信息 -->
          <div class="card-title card-title-background">法人信息</div>
          <div class="require-mark">图片资质信息</div>
          <a-row :gutter="24" justify="space-around">
            <a-form-item v-for="(item, key) in [fileMap[1], fileMap[2]]" :key="key" :label="item.label" :required="item.required">
              <a-upload
                v-model:file-list="item.fileData"
                accept=".png,.jpg,.jpeg"
                :multiple="false"
                list-type="picture-card"
                :before-upload="file => beforeUpload(file, item)"
                :disabled="isDisabled"
                @remove="() => handleRemove(item)"
                @preview="() => handlePreview(item)"
              >
                <div v-if="!item.fileData?.length">
                  <plus-outlined />
                  <div style="margin-top: 8px">上传</div>
                </div>
              </a-upload>
            </a-form-item>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="法人姓名" name="legalName">
                <a-input v-model:value="form.legalName" placeholder="请输入法人姓名" allow-clear :disabled="isDisabled" />
              </a-form-item>
              <a-form-item label="法人证件类型" name="legalCertType">
                <a-select v-model:value="form.legalCertType" class="ele-fluid" placeholder="请选择" :disabled="isDisabled">
                  <a-select-option v-for="({ label, value }, key) in certTypeEnum" :key="key" :value="value">{{ label }}</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="证件有效期">
                <a-space>
                  <a-input v-model:value="form.legalCertStartDate" placeholder="格式: 2020-05-20" allow-clear :disabled="isDisabled" />
                  -
                  <a-input v-model:value="form.legalCertEndDate" placeholder="格式: 2020-05-20" allow-clear :disabled="isDisabled" />
                </a-space>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="法人手机号" name="legalTel">
                <a-input v-model:value="form.legalTel" placeholder="请输入法人手机号" allow-clear :disabled="isDisabled" />
              </a-form-item>
              <a-form-item label="法人证件号码" name="legalCertNo">
                <a-input v-model:value="form.legalCertNo" placeholder="请输入法人证件号码" allow-clear :disabled="isDisabled" />
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 联系人信息 -->
          <div class="card-title card-title-background">联系人信息</div>
          <!-- 快捷操作: 法人信息同步联系人信息 -->
          <div style="margin-bottom: 20px" v-if="optType === 0">
            <span class="ele-text-primary" style="margin-right: 20px"><info-circle-outlined /> 联系人信息是否同法人</span>
            <a-radio-group v-model:value="isSameLegalAndContact" name="radioGroup" @change="onSetContactByLegal">
              <a-radio :value="1">是</a-radio>
              <a-radio :value="0">否</a-radio>
            </a-radio-group>
          </div>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="联系人姓名" name="contactsName">
                <a-input v-model:value="form.contactsName" placeholder="请输入联系人姓名" allow-clear :disabled="isDisabled" />
              </a-form-item>
              <a-form-item label="联系人证件类型" name="contactsCertType">
                <a-select v-model:value="form.contactsCertType" class="ele-fluid" placeholder="请选择" :disabled="isDisabled">
                  <a-select-option v-for="({ label, value }, key) in certTypeEnum" :key="key" :value="value">{{ label }}</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="证件有效期">
                <a-space>
                  <a-input v-model:value="form.contactsCertStartDate" placeholder="格式: 2020-05-20" allow-clear :disabled="isDisabled" />
                  -
                  <a-input v-model:value="form.contactsCertEndDate" placeholder="格式: 2020-05-20" allow-clear :disabled="isDisabled" />
                </a-space>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item :label="`联系人手机号${[0, 3].includes(optType) ? '(登录手机号)' : ''}`" name="contactsTel">
                <a-input v-model:value="form.contactsTel" placeholder="请输入联系人手机号" allow-clear :disabled="optType === 3" />
              </a-form-item>
              <a-form-item label="联系人证件号码" name="contactsCertNo">
                <a-input v-model:value="form.contactsCertNo" placeholder="请输入联系人证件号码" allow-clear :disabled="isDisabled" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="签约日期" name="signDate">
                <a-date-picker
                  v-model:value="form.signDate"
                  valueFormat="YYYY-MM-DD"
                  placeholder="请选择"
                  class="ele-fluid"
                  :disabled="isDisabled"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-tab-pane>

        <!-- 费率信息 -->
        <template v-if="optType === 2" #renderTabBar></template>
        <a-tab-pane key="2" tab="费率信息" v-if="optType !== 1">
          <a-form
            ref="rateForm"
            :model="form"
            :label-col="{ md: { span: 17 }, sm: { span: 24 } }"
            :wrapper-col="{ md: { span: 7 }, sm: { span: 24 } }"
          >
            <div v-if="optType !== 3" :style="{ marginBottom: '16px' }">
              <a-button type="dashed" @click="showRateTempAddFront = true"> <plus-outlined /> 添加通道费率</a-button>
            </div>

            <a-tabs v-model:activeKey="activeRateKey" hide-add type="editable-card" @edit="onEditRateTabs">
              <a-tab-pane
                v-for="(item, key) in form.rateDTOList || []"
                :tab="`${item.channelName}-${item.channelCode}`"
                :key="String(key)"
                :closable="item.closable"
              >
                <!-- 费率模块 -->
                <template v-for="(rateItem, key2) in item.rateInfoDTO || []" :key="key2">
                  <a-checkbox v-if="optType === 2 && rateItem.isAddedTemp" v-model:checked="rateItem.checkedAddedTemp">
                    <span style="color: tomato; font-size: 13px"
                    >勾选, 即表示默认新增{{ rateItem.templateNo }}, 请检查费率信息后再提交</span
                    >
                  </a-checkbox>
                  <RateModule
                    :class="{ 'temp-added': rateItem.isAddedTemp }"
                    :rate-item="rateItem"
                    :name-prefix="['rateDTOList', key, 'rateInfoDTO', key2]"
                    :disabled="isDisabled"
                    :bankList="bankList"
                    :form-rules="rateItem.isAddedTemp && !rateItem.checkedAddedTemp ? {} : null"
                  />
                </template>
              </a-tab-pane>
            </a-tabs>
          </a-form>
        </a-tab-pane>

        <a-tab-pane key="5" tab="活动返现政策" v-if="optType === 0 || optType === 3">
          <div v-if="optType !== 3" :style="{ marginBottom: '16px' }">
            <a-button type="dashed" @click="showPolicyAddFront = true"> <plus-outlined /> 添加通道政策</a-button>
          </div>

          <a-form ref="policyForm" :model="policyForm" :label-col="{ style: { width: '130px' } }">
            <a-checkbox-group v-model:value="checkedServiceFeePolicyKey" class="ele-fluid" :name="serveceFee">
              <a-tabs v-model:activeKey="activeChannelTabKey" type="card" @change="onChangeChannelTab">
                <a-tab-pane
                  v-for="(channel, cIndex) in policyForm.policyConfigByChannelGroup || []"
                  :tab="channel.channelName"
                  :key="String(cIndex)"
                  force-render
                  class="tabpane__background"
                >
                  <a-card :bordered="false">
                    <a-tabs v-model:activeKey="activeTerminalSourceTabKey">
                      <a-tab-pane v-for="(tab, tabIndex) in channel.channelData || []" :tab="tab.label" :key="String(tab.key)" force-render>
                        <a-divider orientation="left" dashed orientationMargin="0">终端服务费返现政策</a-divider>
                        <template v-if="tab.serviceFeeData?.length">
                          <template v-for="(item, key) in tab.serviceFeeData" :key="key">
                            <a-row v-if="item.show">
                              <a-col>
                                <a-space>
                                  <a-checkbox style="margin-top: 4px" :value="item.configId" :disabled="isDisabled" />
                                  <a-divider type="vertical" />
                                </a-space>
                              </a-col>
                              <a-col :span="22">
                                <a-row :gutter="48">
                                  <a-col :span="12">
                                    <a-form-item label="服务费金额(元)">
                                      <a-input v-model:value="item.policyName" placeholder="服务费金额" disabled />
                                    </a-form-item>
                                  </a-col>
                                  <a-col :span="12">
                                    <a-form-item
                                      label="返现金额(元)"
                                      :name="[
                                        'policyConfigByChannelGroup',
                                        cIndex,
                                        'channelData',
                                        tabIndex,
                                        'serviceFeeData',
                                        key,
                                        'cashbackAmt'
                                      ]"
                                      :rules="serviceCashbackAmtRules(item.configId, item.parentCashbackAmt)"
                                      v-if="checkedServiceFeePolicyKey.includes(item.configId)"
                                    >
                                      <a-input
                                        v-model:value="item.cashbackAmt"
                                        placeholder="请输入返现金额"
                                        allow-clear
                                        :disabled="isDisabled"
                                      />
                                    </a-form-item>
                                  </a-col>
                                </a-row>
                              </a-col>
                            </a-row>
                          </template>
                        </template>
                        <a-alert v-else message="没有政策配置哦~" banner />

                        <a-divider orientation="left" dashed orientationMargin="0">流量费返现政策</a-divider>
                        <template v-if="tab.simFeeData?.some(s => s.data.length)">
                          <div v-for="(period, idx) in tab.simFeeData" :key="idx">
                            <template v-if="period.show">
                              <div style="margin-bottom: 10px">
                                <a-typography-text strong>{{ period.name }}</a-typography-text>
                              </div>
                              <a-checkbox-group v-model:value="period.checkedList" class="ele-fluid">
                                <template v-for="(item, key) in period.data || []" :key="key">
                                  <a-row v-if="item.show">
                                    <a-col>
                                      <a-space>
                                        <a-checkbox style="margin-top: 4px" :value="item.configId" :disabled="isDisabled" name="simFee" />
                                        <a-divider type="vertical" />
                                      </a-space>
                                    </a-col>
                                    <a-col :span="22">
                                      <a-row :gutter="48">
                                        <a-col :span="12">
                                          <a-form-item label="流量费金额(元)">
                                            <a-input v-model:value="item.policyName" placeholder="流量费金额" disabled />
                                          </a-form-item>
                                        </a-col>
                                        <a-col :span="12" v-if="period.checkedList.includes(item.configId)">
                                          <a-form-item
                                            label="返现金额(元)"
                                            :name="[
                                              'policyConfigByChannelGroup',
                                              cIndex,
                                              'channelData',
                                              tabIndex,
                                              'simFeeData',
                                              idx,
                                              'data',
                                              key,
                                              'cashbackAmt'
                                            ]"
                                            :rules="simFeeCashbackAmtRules(item.configId, item.parentCashbackAmt, period)"
                                          >
                                            <a-input
                                              v-model:value="item.cashbackAmt"
                                              placeholder="请输入返现金额"
                                              allow-clear
                                              :disabled="isDisabled"
                                            />
                                          </a-form-item>
                                        </a-col>
                                      </a-row>
                                    </a-col>
                                  </a-row>
                                </template>
                              </a-checkbox-group>
                            </template>
                          </div>
                        </template>
                        <a-alert v-else message="没有政策配置哦~" banner />
                      </a-tab-pane>
                    </a-tabs>
                  </a-card>
                </a-tab-pane>
              </a-tabs>
            </a-checkbox-group>
          </a-form>
        </a-tab-pane>

        <a-tab-pane key="3" tab="结算信息" v-if="optType === 0 || optType === 3">
          <a-form ref="settleForm" :model="settleForm" :rules="settleFormRules" layout="vertical">
            <div style="margin-bottom: 20px">
              <span class="require-mark" style="margin-right: 20px">结算账户类型</span>
              <a-radio-group v-model:value="settleForm.accountType" disabled>
                <a-radio value="G">对公</a-radio>
                <a-radio value="S">对私</a-radio>
              </a-radio-group>
            </div>
            <div class="require-mark">结算卡图片信息</div>
            <a-row :gutter="100" justify="space-around">
              <a-form-item v-for="(item, key) in [fileMap[16]]" :key="key" :label="item.label" :required="item.required">
                <a-upload
                  v-model:file-list="item.fileData"
                  accept=".png,.jpg,.jpeg"
                  :multiple="false"
                  list-type="picture-card"
                  :disabled="isDisabled"
                  :before-upload="file => beforeUpload(file, item)"
                  @remove="() => handleRemove(item)"
                  @preview="() => handlePreview(item)"
                >
                  <div v-if="!item.fileData?.length">
                    <plus-outlined />
                    <div style="margin-top: 8px">上传</div>
                  </div>
                </a-upload>
              </a-form-item>
              <span></span>
            </a-row>
            <a-row :gutter="24">
              <a-col :md="12" :sm="24" :xs="24">
                <a-form-item label="银行卡账户姓名" name="bankAccountName">
                  <a-input v-model:value="settleForm.bankAccountName" placeholder="请输入银行卡账户姓名" disabled />
                </a-form-item>
                <a-form-item label="开户行总行" name="typeCode">
                  <a-select
                    v-model:value="settleForm.typeCode"
                    style="width: 100%"
                    placeholder="请选择"
                    showSearch
                    :options="bankHeadOffice"
                    optionFilterProp="typeName"
                    :fieldNames="{ label: 'typeName', value: 'typeCode' }"
                    :disabled="isDisabled"
                    @change="(value, option) => selectBankHeadOffice(option)"
                  />
                </a-form-item>
                <a-form-item label="开户行支行" name="bankChannelNo">
                  <a-select
                    v-model:value="settleForm.bankChannelNo"
                    style="width: 100%"
                    placeholder="请选择"
                    showSearch
                    :options="bankSubBranch"
                    optionFilterProp="bankName"
                    :fieldNames="{ label: 'bankName', value: 'bankChannelNo' }"
                    :disabled="isDisabled || !(settleForm.typeCode && settleForm.bankCity)"
                    @change="(value, option) => selectBankSubBranch(option)"
                    @dropdownVisibleChange="open => open && getBankSubBranch()"
                  />
                </a-form-item>
              </a-col>
              <a-col :md="12" :sm="24" :xs="24">
                <a-form-item label="银行卡卡号" name="bankAccountNo">
                  <a-input v-model:value="settleForm.bankAccountNo" placeholder="请输入银行卡卡号" allow-clear :disabled="isDisabled" />
                </a-form-item>
                <a-form-item label="开户行所在地区" name="bankCity">
                  <a-cascader
                    v-model:value="bankAreaValue"
                    :options="bankRegionsData"
                    :load-data="options => loadAreaData(options, 2)"
                    :allow-clear="false"
                    :disabled="isDisabled"
                    placeholder="请选择"
                    @change="selectedBankAreaValue"
                  />
                </a-form-item>
                <a-form-item label="银行卡预留手机号" name="mobile">
                  <a-input v-model:value="settleForm.mobile" placeholder="请输入银行卡预留手机号" allow-clear :disabled="isDisabled" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-tab-pane>

        <!-- 图片信息 -->
        <a-tab-pane key="4" tab="图片信息" v-if="optType !== 2">
          <a-row :gutter="24">
            <template v-for="(item, key) in Object.values(fileMap)" :key="key">
              <a-col :span="6" v-if="item.show !== false">
                <a-form-item :label="item.label" :required="item.required">
                  <a-upload
                    v-model:file-list="item.fileData"
                    accept=".png, .jpg, .jpeg, .gif"
                    :multiple="false"
                    list-type="picture-card"
                    :before-upload="file => beforeUpload(file, item)"
                    @remove="() => handleRemove(item)"
                    @preview="() => handlePreview(item)"
                    :disabled="isDisabled"
                  >
                    <div v-if="!item.fileData?.length">
                      <plus-outlined />
                      <div style="margin-top: 8px">Upload</div>
                    </div>
                  </a-upload>
                </a-form-item>
              </a-col>
            </template>
          </a-row>

          <!-- 预览图片 -->
          <a-image
            :style="{ display: 'none' }"
            :src="previewImage"
            :preview="{ visible: previewVisible, onVisibleChange: setPreviewVisible }"
          />
        </a-tab-pane>
      </a-tabs>
    </a-form>

    <!-- 添加通道费率 -->
    <RateTempAddFront
      v-if="showRateTempAddFront"
      v-model:visible="showRateTempAddFront"
      :channelCodes="channelCodes"
      @done="onAddChannelRate"
    />

    <!-- 添加通道政策 -->
    <PolicyAddFront v-if="showPolicyAddFront" v-model:visible="showPolicyAddFront" :channelCodes="channelCodes" @done="onAddChannelPoliy" />

    <template #footer v-if="isDisabled">
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { message, Upload } from 'ant-design-vue';
import { certTypeEnum } from '@/config/enumerate';
import { RegionManageApi } from '@/api/businessTeam/region-center/RegionManageApi';
import { RateTemplateApi } from '@/api/businessTeam/rate-template/RateTemplateApi';
import { phoneReg } from '@/utils/validate';
import RateModule from '../_components/RateModule.vue';
import RateTempAddFront from './RateTempAddFront.vue';
import PolicyAddFront from './PolicyAddFront.vue';
import { deepCopy } from '@/utils/util';
import { BankTypeApi } from '@/api/base/BankTypeApi';
import { BankInfoApi } from '@/api/base/BankInfoApi';
import { ServiceFeeManageApi } from '@/api/businessTeam/activity-Config/ServiceFeeManageApi';
import { SimFeeManageApi } from '@/api/businessTeam/activity-Config/SimFeeManageApi';
import { AreaApi } from '@/api/base/AreaApi';
import { compressorImage } from '@/utils/image-compressor-util';
import { OcrApi } from '@/api/base/OcrApi';
import dayjs from 'dayjs';
import { ActivityCashbackTemplateManageApi } from '@/api/businessTeam/activity-Config/ActivityCashbackTemplateManageApi';
import { RemitChannelApi } from '@/api/account/remit-channel/RemitChannelApi';

const simFeePolicyPeriodGroupDef = [
  {
    name: '第一期',
    field: 'firstPeriodList',
    data: [],
    checkedList: []
  },
  {
    name: '第二期',
    field: 'secondPeriodList',
    data: [],
    checkedList: []
  },
  {
    name: '第三期',
    field: 'thirdPeriodList',
    data: [],
    checkedList: []
  },
  {
    name: '标准期 (第四期以及后续阶段)',
    field: 'fourthPeriodList',
    data: [],
    checkedList: []
  }
];

const policyConfigByTerminalSourceDef = [
  {
    label: '全款机配置',
    key: 1,
    serviceFeeData: [],
    simFeeData: []
  },
  {
    label: '分期机配置',
    key: 2,
    serviceFeeData: [],
    simFeeData: []
  }
];

export default {
  components: { RateModule, RateTempAddFront, PolicyAddFront },
  props: {
    visible: Boolean,
    data: Object,
    channelCodes: Array,
    bankList: Array,
    appBrands: Array,
    /**
     * @prop {Number} 操作类型 0 添加; 1 修改基本信息; 2 修改费率信息; 3 详情
     */
    optType: Number
  },
  emits: ['done', 'update:visible'],
  data() {
    const _this = this;
    return {
      activeChannelTabKey: '0',
      modalTitle: titleArr[this.optType],
      isSameLegalAndContact: 0,
      isDisabled: this.optType === 3,
      certTypeEnum,
      loading: false,
      showRateTempAddFront: false,
      showPolicyAddFront: false,
      activeTabKey: '1',
      activeRateKey: '0',
      activePolicyKey: '0',
      activeTerminalSourceTabKey: '1',
      previewImage: '',
      previewVisible: false,
      detailRate: [],
      detailRateSource: [],
      bankHeadOffice: [],
      bankSubBranch: [],
      bankAreaValue: [],
      bankRegionsData: [],
      officeAreaValue: [],
      officeRegionsData: [],
      subChannelNos: [],
      // 表单数据
      form: {
        // 证件类型默认身份证
        contactsCertType: '01',
        legalCertType: '01',
        rateDTOList: []
      },
      settleForm: { accountType: 'G' },
      policyForm: {
        serviceFeePolicyDTOList: [],
        simFeePolicyDTOList: [],
        simFeePolicyPeriodGroup: [],
        policyConfigByChannelGroup: []
      },
      checkedServiceFeePolicyKey: [],
      checkedSimFeePolicyKey: [],
      serviceFeeConf: [],
      simFeeConf: [],
      activityCashbackTemplateList: [],
      //图片列表
      fileMap: {
        1: {
          label: '身份证头像面',
          fileType: 1,
          required: true
        },
        2: {
          label: '身份证国徽面',
          fileType: 2,
          required: true
        },
        16: {
          label: '对公开户许可证',
          fileType: 16,
          required: _this.optType === 0 || _this.optType === 3,
          show: _this.optType === 0 || _this.optType === 3
        },
        7: {
          label: '营业执照照片',
          fileType: 7,
          required: false
        }
        // 8: {
        //   label: '税务登记证照片',
        //   fileType: 8,
        //   required: false
        // }
      },
      // 表单验证规则
      rules: {
        cityCode: [{ required: true, message: '请选择省市区' }],
        regionName: [{ required: true, message: '请输入企业名称' }],
        regionSname: [{ required: true, message: '请输入企业简称' }],
        taxPoint: [{ required: true, message: '请输入对公开票税点' }],
        contactsName: [{ required: true, message: '请输入联系人姓名' }],
        contactsTel: [
          { required: true, message: '请输入联系人手机号' },
          { pattern: phoneReg, message: '手机号码格式错误', trigger: 'blur' }
        ],
        contactsCertType: [{ required: true, message: '请输入联系人件类型' }],
        contactsCertNo: [{ required: true, message: '请输入联系人证件号码' }],
        officeAddr: [{ required: true, message: '请输入企业办公地址' }],
        legalName: [{ required: true, message: '请输入法人姓名' }],
        legalTel: [
          { required: true, message: '请输入法人手机号' },
          { pattern: phoneReg, message: '手机号码格式错误', trigger: 'blur' }
        ],
        legalCertType: [{ required: true, message: '请输入法人证件类型' }],
        signDate: [{ required: true, message: '请选择签约日期' }],
        legalCertNo: [{ required: true, message: '请输入法人证件号码' }],
        licenseNo: [{ required: true, message: '请输入统一社会信用代码' }],
        licenseAddr: [{ required: true, message: '请输入营业执照注册地址' }],
        appBrandType: [{ required: true, message: '请选择APP品牌类型' }],
        settleChannelWay: [{ required: true, message: '请选择分润出款方式' }],
        settleChannelCode: [{ required: true, message: '请选择' }]
      },
      settleFormRules: {
        accountType: [{ required: true, message: '请选择结算账户类型' }],
        bankAccountName: [{ required: true, message: '请输入银行卡账户姓名' }],
        typeCode: [{ required: true, message: '请选择开户行总行' }],
        bankChannelNo: [{ required: true, message: '请选择开户行支行' }],
        bankAccountNo: [{ required: true, message: '请输入银行卡卡号' }],
        bankCity: [{ required: true, message: '请选择开户行所在地区' }],
        mobile: [
          { required: true, message: '请输入银行卡预留手机号' },
          { pattern: phoneReg, message: '手机号码格式错误', trigger: 'blur' }
        ]
      }
    };
  },
  watch: {
    'form.regionName'(val) {
      this.settleForm.bankAccountName = val;
    }
  },
  created() {
    this.initData();
    this.getSubChannelNos();
  },
  methods: {
    onChangeChannelTab() {
      this.activeTerminalSourceTabKey = '1';
    },
    async getSubChannelNos() {
      const data = await RemitChannelApi.findAll({ validStatus: 1, remitChannelClassify: 2 });
      this.subChannelNos = data || [];
    },
    async getActivityCashbackTemplateList() {
      let data = await ActivityCashbackTemplateManageApi.querySelfList();

      this.activityCashbackTemplateList = data || [];

      if (this.optType === 3) {
        return;
      }

      let [{ serviceFeePolicyDTOList, simFeeNewPolicyDTO }] = data || [{}];

      serviceFeePolicyDTOList = serviceFeePolicyDTOList || [];
      serviceFeePolicyDTOList.forEach(item => {
        item.show = Number(item.parentCashbackAmt) > 0 || !(Number(item.serviceFeeAmt) > 0);

        if (!item.show) {
          item.cashbackAmt = 0;
        }
      });

      this.policyForm.serviceFeePolicyDTOList = serviceFeePolicyDTOList;
      this.checkedServiceFeePolicyKey = serviceFeePolicyDTOList.map(d => d.configId);

      const simFeePolicyPeriodGroup = deepCopy(simFeePolicyPeriodGroupDef);
      simFeePolicyPeriodGroup.forEach(period => {
        Object.keys(simFeeNewPolicyDTO || {}).forEach(key => {
          if (period.field === key) {
            period.data = deepCopy(simFeeNewPolicyDTO[key] || []);
            period.data.forEach(item => {
              item.show = Number(item.parentCashbackAmt) > 0 || !(Number(item.simFeeAmt) > 0);
              if (!item.show) {
                item.cashbackAmt = 0;
              }
            });
            period.checkedList = period.data.map(d => d.configId);
          }
        });
      });

      let allChannelCodes = [];
      try {
        const channelCodes = new Set(
          [...(this.policyForm.serviceFeePolicyDTOList || []), ...Object.values(simFeeNewPolicyDTO || {})]
            .flat(Infinity)
            .flatMap(item => (Array.isArray(item) ? item.map(subItem => subItem?.channelCode) : item?.channelCode))
            .filter(code => code?.trim())
            .map(code => code.trim())
        );
        allChannelCodes = [...channelCodes];
      } catch (error) {
        console.log(error);
      }

      let policyConfigByChannelGroup = [];
      allChannelCodes.forEach(item => {
        const channelItem = this.channelCodes.find(channel => channel.channelCode === item);
        policyConfigByChannelGroup.push({
          channelCode: item,
          channelName: channelItem?.channelName,
          channelData: deepCopy(policyConfigByTerminalSourceDef)
        });
      });

      policyConfigByChannelGroup.forEach(channel => {
        channel.channelData.forEach(tab => {
          tab.serviceFeeData = this.policyForm.serviceFeePolicyDTOList.filter(
            item => item.terminalSource === tab.key && item.channelCode === channel.channelCode
          );

          const simFeeData = simFeePolicyPeriodGroup.map(period => {
            const periodData = period.data.filter(item => item.terminalSource === tab.key && item.channelCode === channel.channelCode);
            return {
              ...period,
              data: periodData,
              show: periodData.some(item => item.show)
            };
          });

          tab.simFeeData = simFeeData;
        });
      });

      this.policyForm.policyConfigByChannelGroup = policyConfigByChannelGroup;
    },
    async getServiceFeeConf() {
      let data = await ServiceFeeManageApi.selfOpenList({ validStatus: 1 });
      data = data || [];
      data.forEach(d => {
        d.policyName = `${d.serviceFee}元`;
        d.cashbackAmt = d.serviceFee;
      });
      this.policyForm.serviceFeePolicyDTOList = data;
      this.checkedServiceFeePolicyKey = data.map(d => d.id);
    },

    async getSimFeeConf() {
      let data = await SimFeeManageApi.list({ validStatus: 1 });
      data = data || [];
      const cashBackFileds = ['firstCashbackAmt', 'secondCashbackAmt', 'thirdCashbackAmt', 'fourthCashbackAmt'];
      data.forEach(d => {
        d.policyName = `${d.simFee}元/${d.simPeriod}天`;
        cashBackFileds.forEach(f => (d[f] = d.simFee));
      });
      this.policyForm.simFeePolicyDTOList = data;
      this.checkedSimFeePolicyKey = data.map(d => d.id);
    },
    simFeeCashbackAmtRules(key, maxsCashbackAmt, period) {
      if (period.checkedList.includes(key)) {
        return [
          { required: true, message: '请输入' },
          {
            validator: async (rule, value) => {
              if (Number(value) > Number(maxsCashbackAmt)) {
                return Promise.reject(`注：返现必须低于或等于${maxsCashbackAmt}`);
              }
              return Promise.resolve();
            },
            trigger: 'blur'
          }
        ];
      }
      return [];
    },
    serviceCashbackAmtRules(key, maxsCashbackAmt) {
      if (this.checkedServiceFeePolicyKey.includes(key)) {
        return [
          { required: true, message: '请输入' },
          {
            validator: async (rule, value) => {
              if (Number(value) > Number(maxsCashbackAmt)) {
                return Promise.reject(`注：返现必须低于或等于${maxsCashbackAmt}`);
              }
              return Promise.resolve();
            },
            trigger: 'blur'
          }
        ];
      }
      return [];
    },

    async loadAreaData(selectedOptions, totalLevel = 2) {
      const targetOption = selectedOptions ? selectedOptions[selectedOptions.length - 1] : { level: 1, code: '' };
      const { level, code } = targetOption;
      const data = await AreaApi.list({ level: level + 1, status: 1, parentCode: code });
      const filterData = data.map(d => {
        return { label: d.areaName, value: d.areaCode, code: d.areaCode, isLeaf: level > totalLevel - 1, level: d.level };
      });
      if (level === 1) {
        this.officeRegionsData = deepCopy(filterData);
        this.bankRegionsData = deepCopy(filterData);
      } else {
        targetOption.children = filterData;
      }

      if (this.optType && !selectedOptions) {
        const officeItem = this.officeRegionsData.find(r => r.value === this.form.provinceCode);
        if (officeItem) {
          await this.loadAreaData([officeItem]);
          const citem = officeItem.children.find(i => i.value === this.form.cityCode);
          citem && this.loadAreaData([citem]);
        }

        const bankItem = this.bankRegionsData.find(r => r.value === this.settleForm.bankProvince);
        bankItem && this.loadAreaData([bankItem]);
      }
    },

    selectedBankAreaValue(value) {
      [this.settleForm.bankProvince, this.settleForm.bankCity] = value || [];
      this.settleForm.bankChannelNo = null;
    },

    async getBankSubBranch() {
      this.bankSubBranch = [];
      const data = await BankInfoApi.list({
        status: 1,
        typeCode: this.settleForm.typeCode,
        provinceCode: this.settleForm.bankProvince,
        cityCode: this.settleForm.bankCity
      });
      this.bankSubBranch = data || [];
    },

    selectBankSubBranch({ bankName }) {
      this.settleForm.bankSubName = bankName;
    },

    selectBankHeadOffice({ typeName }) {
      this.settleForm.typeName = typeName;
      this.settleForm.bankChannelNo = null;
    },

    async getBankHeadOffice() {
      const data = await BankTypeApi.list({ status: 1 });
      this.bankHeadOffice = data || [];
    },
    async save() {
      // 校验表单
      await this.$refs.form.validate();
      this.$refs.rateForm && (await this.$refs.rateForm.validate());
      this.$refs.policyForm && (await this.$refs.policyForm.validate());
      this.$refs.settleForm && (await this.$refs.settleForm.validate());

      // 校验图片&格式化
      if (this.optType !== 2) {
        await this.validateFileList();
        this.form.fileDTOList = Object.values(this.fileMap)
          .map(item => {
            if (item.fileData?.[0]) {
              return {
                fileName: item.fileName,
                fileData: item.fileData[0].url,
                fileType: item.fileType,
                suffixType: item.suffixType || 'png'
              };
            }
            return null;
          })
          .filter(f => !!f);
      }

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      const params = deepCopy(this.form);
      let rateList = [];

      // 执行编辑或修改方法
      switch (this.optType) {
        case 0:
          // if (!this.checkedServiceFeePolicyKey.length) {
          //   return message.warn('请至少勾选一条服务费返现政策');
          // }

          // // eslint-disable-next-line no-case-declarations
          // const isPassSimPolicyCheck = this.policyForm.simFeePolicyPeriodGroup.every(period => {
          //   return period.checkedList?.length;
          // });
          // if (!isPassSimPolicyCheck) {
          //   return message.warn('流量费返现政策每期请至少勾选一条');
          // }

          params.rateDTOList.forEach(r => {
            rateList.push(...(r.rateInfoDTO || []));
          });
          rateList.forEach(i => (i.isSame = 1));
          params.rateDTOList = rateList;
          params.orgBankCardRequest = this.settleForm;

          // eslint-disable-next-line no-case-declarations
          let serviceFeePolicyDTOList = [];
          // eslint-disable-next-line no-case-declarations
          const simPolicySubmitData = {};

          this.policyForm.policyConfigByChannelGroup.forEach(channel => {
            channel.channelData.forEach(item => {
              // 格式化服务费
              const checkedServiceFeeData = item.serviceFeeData.filter(i => this.checkedServiceFeePolicyKey.includes(i.configId));
              serviceFeePolicyDTOList = [...serviceFeePolicyDTOList, ...checkedServiceFeeData];

              // 格式化流量费
              item.simFeeData.forEach(period => {
                const checkedSimFeeData = period.data.filter(i => period.checkedList.includes(i.configId));
                simPolicySubmitData[period.field] = [...(simPolicySubmitData[period.field] || []), ...checkedSimFeeData];
              });
            });
          });

          params.serviceFeePolicyDTOList = serviceFeePolicyDTOList;
          params.simFeeNewPolicyDTO = simPolicySubmitData;

          result = RegionManageApi.add(params);
          break;
        case 1:
          result = RegionManageApi.edit(this.form);
          break;
        case 2:
          params.rateDTOList.forEach(r => {
            r.rateInfoDTO?.forEach(i => {
              i.rateType = i.rateInfoDTO.rateType;
            });

            r.rateInfoDTO = r.rateInfoDTO.filter(i => {
              if (!i.isAddedTemp) return true;
              if (i.checkedAddedTemp) return true;
              return false;
            });
            rateList.push(...(r.rateInfoDTO || []));
          });
          rateList.forEach(i => (i.isSame = 1));
          params.rateDTOList = rateList;
          result = RegionManageApi.editRate(params);
          break;
      }

      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    selectedOfficeAreaValue(value, arr) {
      [this.form.provinceCode, this.form.cityCode, this.form.districtCode] = value || [];
      const labels = arr.map(i => i.label);
      [this.form.provinceName, this.form.cityName, this.form.districtName] = labels || [];
    },

    /**
     * 初始化数据
     */
    async initData() {
      const formatBaseInfo = data => {
        // 格式化基本信息
        this.form = Object.assign({}, data);
        const fieldMark = this.optType === 1 ? 'Cipher' : 'Mask';
        const foundKeys = Object.keys(this.form).filter(k => k.endsWith(fieldMark));
        foundKeys.forEach(v => {
          this.form[v.replace(fieldMark, '')] = this.form[v];
        });

        let serviceFeePolicyDTOMap = {};
        let simFeePolicyDTOMap = {};
        this.activityCashbackTemplateList.forEach(item => {
          // 自身全部服务费
          item.serviceFeePolicyDTOList.forEach(s => {
            serviceFeePolicyDTOMap[s.configId] = s;
          });

          // 自身全部流量费
          Object.keys(item.simFeeNewPolicyDTO || {}).forEach(key => {
            simFeePolicyDTOMap[key] = Object.assign({}, simFeePolicyDTOMap[key] || {});
            item.simFeeNewPolicyDTO[key].forEach(s => {
              simFeePolicyDTOMap[key][s.configId] = s;
            });
          });
        });

        this.policyForm.serviceFeePolicyDTOList = data.serviceFeePolicyDTOList || [];
        this.policyForm.serviceFeePolicyDTOList.forEach(i => {
          this.checkedServiceFeePolicyKey.push(i.configId);

          if (!i.parentCashbackAmt) {
            const configItem = serviceFeePolicyDTOMap[i.configId] || {};
            i.parentCashbackAmt = configItem.parentCashbackAmt;
          }

          i.show = Number(i.parentCashbackAmt) > 0 || !(Number(i.serviceFeeAmt) > 0);
        });

        this.checkedSimFeePolicyKey = this.policyForm.simFeePolicyDTOList.map(i => i.configId);

        const simFeePolicyMap = data.simFeeNewPolicyDTO || {};
        const simFeePolicyPeriodGroupMap = deepCopy(simFeePolicyPeriodGroupDef);
        simFeePolicyPeriodGroupMap.forEach(period => {
          Object.keys(simFeePolicyMap).forEach(key => {
            if (period.field === key) {
              period.data = simFeePolicyMap[key];
              period.data.forEach(item => {
                period.checkedList.push(item.configId);

                if (!item.parentCashbackAmt) {
                  const configItem = (simFeePolicyDTOMap[key] && simFeePolicyDTOMap[key][item.configId]) || {};
                  item.parentCashbackAmt = configItem.parentCashbackAmt;
                }

                item.show = Number(item.parentCashbackAmt) > 0 || !(Number(item.simFeeAmt) > 0);
              });
            }
          });
        });

        let allChannelCodes = [];
        try {
          const channelCodes = new Set(
            [...(this.policyForm.serviceFeePolicyDTOList || []), ...Object.values(simFeePolicyMap || {})]
              .flat(Infinity)
              .flatMap(item => (Array.isArray(item) ? item.map(subItem => subItem?.channelCode) : item?.channelCode))
              .filter(code => code?.trim())
              .map(code => code.trim())
          );
          allChannelCodes = [...channelCodes];
        } catch (error) {
          console.log(error);
        }

        let policyConfigByChannelGroup = [];
        allChannelCodes.forEach(item => {
          const channelItem = this.channelCodes.find(channel => channel.channelCode === item);
          policyConfigByChannelGroup.push({
            channelCode: item,
            channelName: channelItem?.channelName,
            channelData: deepCopy(policyConfigByTerminalSourceDef)
          });
        });

        policyConfigByChannelGroup.forEach(channel => {
          channel.channelData.forEach(tab => {
            tab.serviceFeeData = this.policyForm.serviceFeePolicyDTOList.filter(
              item => item.terminalSource === tab.key && item.channelCode === channel.channelCode
            );

            const simFeeData = simFeePolicyPeriodGroupMap.map(period => {
              const periodData = period.data.filter(item => item.terminalSource === tab.key && item.channelCode === channel.channelCode);
              return {
                ...period,
                data: periodData,
                show: periodData.some(item => item.show)
              };
            });

            tab.simFeeData = simFeeData;
          });
        });

        this.policyForm.policyConfigByChannelGroup = policyConfigByChannelGroup;

        this.settleForm = Object.assign({}, data?.bankCard);

        const foundKeys2 = Object.keys(this.settleForm).filter(k => k.endsWith(fieldMark));
        foundKeys2.forEach(v => {
          this.settleForm[v.replace(fieldMark, '')] = this.settleForm[v];
        });

        this.officeAreaValue = [this.form.provinceCode, this.form.cityCode, this.form.districtCode];
        // 设置图片信息
        Object.values(this.fileMap).forEach(f => {
          const item = data?.fileDTOList.find(i => i.imageType === f.fileType);
          if (item) {
            // fileData设置为数组便于后续操作
            f.fileData = item.imagePath ? [{ url: item.imagePath }] : [];
            f.fileName = item.imageName;
          }
        });
      };

      const formatRateList = (allData, data) => {
        // 格式化通道费率列表
        this.form.rateDTOList = [];

        Object.keys(data).forEach(channelCode => {
          const allRateByCode = allData.filter(a => a.channelCode === channelCode);

          const setedTemplateNo = data[channelCode].map(r => r.templateNo);
          const allTemplateNo = allRateByCode.map(a => a.templateNo);
          const addedTemplateNo = allTemplateNo.filter(key => !setedTemplateNo.includes(key));

          const addedTemplate = allRateByCode.filter(i => addedTemplateNo.includes(i.templateNo));
          addedTemplate.forEach(t => {
            t.isAddedTemp = true;
          });

          data[channelCode].push(...addedTemplate);

          const formatData = this.formatRateInfo(data[channelCode]);

          const item = this.channelCodes.find(i => i.channelCode === channelCode);

          this.form.rateDTOList.push({
            channelCode,
            channelName: item?.channelName,
            closable: false,
            rateInfoDTO: formatData
          });
        });
      };

      this.loadAreaData();

      if ([0, 3].includes(this.optType)) {
        await this.getActivityCashbackTemplateList();
      }

      if (this.optType === 1) {
        formatBaseInfo(this.data);
      } else if (this.optType === 2) {
        this.activeTabKey = '2';
        this.form.id = this.data.id;
        formatRateList(this.data.allRateMap, this.data.rateMap);
      } else if (this.optType === 3) {
        formatBaseInfo(this.data);
        formatRateList([], this.data.rateMap);
      }

      if (this.optType === 0 || this.optType === 3) {
        if (this.settleForm.bankCity) {
          this.bankAreaValue = [this.settleForm.bankProvince, this.settleForm.bankCity];
          if (this.settleForm.typeCode) {
            this.getBankSubBranch();
          }
        }
        this.getBankHeadOffice();
      }
    },

    async onAddChannelRate({ channelCode, channelName }) {
      const existedChannels = this.form.rateDTOList.map(i => i.channelCode);
      if (existedChannels.includes(channelCode)) return;

      const data = await RateTemplateApi.list({ validStatus: 1, channelCode, templateType: 1 });
      const formatData = this.formatRateInfo(data);

      this.form.rateDTOList.push({
        channelCode,
        channelName,
        rateInfoDTO: formatData
      });

      this.activeRateKey = this.form.rateDTOList.length - 1 + '';
    },

    onAddChannelPoliy({ channelCode, channelName }) {
      this.policyForm.policyDTOListByChannel = this.policyForm.policyDTOListByChannel || [];
      const existedChannels = this.policyForm.policyDTOListByChannel.map(i => i.channelCode);
      if (existedChannels.includes(channelCode)) return;

      const policyConfigByChannel = deepCopy(this.policyForm.policyConfigByChannel || []);
      const configItem = policyConfigByChannel.find(i => i.channelCode === channelCode);
      if (configItem) {
        this.policyForm.policyDTOListByChannel.push(configItem);
      }

      this.activePolicyKey = this.policyForm.policyDTOListByChannel.length - 1 + '';
    },

    onEditRateTabs(targetKey) {
      this.form.rateDTOList = this.form.rateDTOList.filter((r, key) => String(key) !== targetKey);
    },

    onEditPolicyTabs(targetKey) {
      this.policyForm.policyDTOListByChannel = this.policyForm.policyDTOListByChannel.filter((r, key) => String(key) !== targetKey);
    },

    /**
     * 格式化费率信息
     * @param {Array} data 费率信息
     */
    formatRateInfo(data) {
      const formatData = data ? JSON.parse(JSON.stringify(data).replaceAll(/\brateInfo\b/g, 'rateInfoDTO')) : [];

      formatData.forEach(r => {
        r.rateInfoDTO = typeof r.rateInfoDTO === 'string' ? JSON.parse(r.rateInfoDTO) : r.rateInfoDTO;

        r.rateRatio = 100;
        const rateFields = ['withdrawRate', 'withdrawSingleFee'];
        rateFields.forEach(f => (r.rateInfoDTO[f] = 0));

        // if (!(r.rateType || r.rateInfoDTO.rateType)) {
        //   if (['1000', '1002', '1001'].includes(r.channelCode)) {
        //     r.rateInfoDTO.rateType = 1;
        //   } else if (r.channelCode === '1003') {
        //     r.rateInfoDTO.rateType = 3;
        //   }
        // }
      });

      formatData.sort(function (a, b) {
        return a.rateInfoDTO.rateType - b.rateInfoDTO.rateType;
      });

      return formatData;
    },

    /**
     * 校验图片是否上传
     */
    validateFileList() {
      return new Promise((resolve, reject) => {
        if (Object.values(this.fileMap).every(item => !item.required || item.fileData?.length)) {
          resolve();
        } else {
          message.warn('请上传完整图片信息');
          reject();
        }
      });
    },

    /**
     * 法人信息同步联系人
     */
    onSetContactByLegal(e) {
      const isSame = e.target.value;
      const legalFields = Object.keys(this.form).filter(k => k.startsWith('legal'));
      legalFields.forEach(l => {
        this.form[l.replace('legal', 'contacts')] = isSame ? this.form[l] : '';
      });

      if (!isSame) this.form.contactsCertType = '01';
    },
    async ocrIdcard(imgFile, side) {
      const data = await OcrApi.ocrIdcard({
        imgFile,
        side
      });
      const { success, name, num, startDate, endDate } = data;
      if (success) {
        switch (side) {
          case 'face':
            this.form.legalName = name;
            this.form.legalCertNo = num;
            this.form.legalCertType = '01';
            break;
          case 'back':
            this.form.legalCertStartDate = startDate ? dayjs(startDate).format('YYYY-MM-DD') : '';
            this.form.legalCertEndDate = endDate === '长期' ? '2999-12-31' : endDate ? dayjs(endDate).format('YYYY-MM-DD') : '';
            break;
        }
      }
    },
    async ocrBankcard(imgFile) {
      const data = await OcrApi.ocrBankcard({
        imgFile
      });
      const { success, cardNum } = data;
      if (success) {
        this.settleForm.bankAccountNo = cardNum;
      }
    },
    async ocarBusinessLicense(imgFile) {
      const data = await OcrApi.ocarBusinessLicense({
        imgFile
      });
      const { success, regNum, name, establishDate, validPeriod, address } = data;
      if (success) {
        this.form.regionName = name == 'FailInRecognition' ? '' : name;
        this.form.licenseNo = regNum == 'FailInRecognition' ? '' : regNum;
        this.form.licenseAddr = address == 'FailInRecognition' ? '' : address;
        this.form.licenseStartDate = establishDate ? dayjs(establishDate).format('YYYY-MM-DD') : '';
        this.form.licenseEndDate = validPeriod ? dayjs(validPeriod).format('YYYY-MM-DD') : '';
      }
    },
    beforeUpload(file, item) {
      compressorImage(file).then(({ url, mime }) => {
        item.fileData = [{ url }];
        item.fileName = file.uid + file.name;
        item.suffixType = mime.split('/')[1];

        switch (item.fileType) {
          case 1:
          case 2:
            this.ocrIdcard(url, item.fileType === 1 ? 'face' : 'back');
            break;
          case 3:
            if (this.settleForm.accountType === 'S') {
              this.ocrBankcard(url);
            }
            break;
          case 7:
            this.ocarBusinessLicense(url);
            break;
          default:
            break;
        }
      });

      return Upload.LIST_IGNORE;
    },

    handleRemove(item) {
      item.fileData = [];
      item.suffixType = '';
    },

    handlePreview({ fileData }) {
      this.previewImage = fileData[0].url;
      this.setPreviewVisible(true);
    },

    setPreviewVisible(visible) {
      this.previewVisible = visible;
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};

// 弹框标题
const titleArr = ['添加大区', '修改基本信息', '修改费率信息', '大区详情'];
</script>
<style scoped>
.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}

.card-title {
  border-left: 5px solid;
  border-color: var(--primary-color);
  padding-left: 10px;
}

.card-title-background {
  background-color: #f5f5f5;
  height: 2em;
  line-height: 2em;
  margin-bottom: 20px;
}
.temp-added {
  border: 1px dashed red;
}
.require-mark::before {
  display: inline-block;
  margin-right: 4px;
  font-family: SimSun, sans-serif;
  color: var(--highlight-color);
  font-size: 14px;
  line-height: 1;
  content: '*';
}
.tabpane__background {
  background-color: #ececec;
  padding: 15px;
}
</style>
