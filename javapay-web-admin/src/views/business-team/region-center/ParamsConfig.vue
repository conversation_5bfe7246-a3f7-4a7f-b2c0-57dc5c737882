<template>
  <a-modal
    :width="900"
    :visible="visible"
    :confirm-loading="loading"
    title="参数配置"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-button type="primary" @click="addRow">添加</a-button>
    <a-divider dashed style="margin: 20px 0" />
    <a-form ref="form" :model="form">
      <a-row v-for="(item, idx) in form.paramsMap || []" :key="idx" :gutter="18">
        <a-col :span="10">
          <a-form-item label="参数名称" :name="['paramsMap', idx, 'key']" :rules="rules.key">
            <a-textarea v-model:value.trim="item.key" placeholder="请输入参数名称" auto-size allow-clear :disabled="item.disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="10">
          <a-form-item label="参数值" :name="['paramsMap', idx, 'value']" :rules="rules.value">
            <a-textarea v-model:value.trim="item.value" placeholder="请输入参数值" auto-size allow-clear :disabled="item.disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="4">
          <a-form-item v-if="!item.disabled">
            <a-button type="danger" @click="deleteRow(idx)">删除</a-button>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script>
import { RegionManageApi } from '@/api/businessTeam/region-center/RegionManageApi';
import { message } from 'ant-design-vue';

export default {
  props: {
    visible: Boolean,
    data: Object
  },
  emits: ['update:visible', 'done'],

  data() {
    return {
      //表单数据
      form: {
        paramsMap: []
      },
      // 表单验证规则
      rules: {
        key: [
          { required: true, message: '请输入参数名称' },
          { pattern: /[A-Za-z_]/, message: '仅支持字母、下划线' }
        ],
        value: [{ required: true, message: '请输入参数值' }]
      },
      //提交状态
      loading: false
    };
  },

  mounted() {
    const paramsObj = this.data.paramsMap || {};

    let paramsList = [];
    for (let [key, value] of Object.entries(paramsObj)) {
      paramsList.push({ key, value, disabled: key === 'keyname' });
    }
    this.data.paramsMap = paramsList;

    this.form = Object.assign({}, this.data);
  },

  methods: {
    async save() {
      let { id, regionNo, paramsMap } = this.form;

      await this.$refs.form.validate();
      this.loading = true;

      let paramsObj = {};
      for (const { key, value } of paramsMap) {
        paramsObj[key] = value;
      }

      const httpParams = {
        id,
        regionNo,
        paramsMap: paramsObj
      };

      RegionManageApi.editParamConf(httpParams)
        .then(res => {
          this.loading = false;
          message.success(res.message);

          this.updateVisible(false);
        })
        .catch(() => {
          this.loading = false;
        });
    },

    addRow() {
      this.form.paramsMap.push({
        key: '',
        value: ''
      });
    },

    deleteRow(index) {
      this.form.paramsMap.splice(index, 1);
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
