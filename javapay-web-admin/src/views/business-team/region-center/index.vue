<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="大区编号">
              <a-input v-model:value.trim="where.regionNo" placeholder="请输入大区编号" allow-clear />
            </a-form-item>
            <a-form-item label="大区名称">
              <a-input v-model:value.trim="where.regionName" placeholder="请输入大区名称" allow-clear />
            </a-form-item>
            <a-form-item label="联系人号码">
              <a-input v-model:value.trim="where.contactsTel" placeholder="请输入联系人手机号码" allow-clear />
            </a-form-item>
            <a-form-item label="合同编号">
              <a-input v-model:value.trim="where.contractNo" placeholder="请输入合同编号" allow-clear />
            </a-form-item>
            <a-form-item label="清算类型">
              <a-select v-model:value="where.remitType" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">平台清算</a-select-option>
                <a-select-option :value="2">自行提现</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="结算方式">
              <a-select v-model:value="where.settleMode" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">日结</a-select-option>
                <a-select-option :value="2">月结</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="分润出款方式">
              <a-select
                v-model:value="where.settleChannelWay"
                style="width: 200px"
                placeholder="请选择"
                allow-clear
                @change="where.settleChannelCode = null"
              >
                <!-- <a-select-option :value="3">展业平台</a-select-option> -->
                <a-select-option :value="1">平台出款</a-select-option>
                <a-select-option :value="0">交易通道</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="分润出款通道" v-if="[1, 2].includes(where.settleChannelWay)">
              <a-select
                v-model:value="where.settleChannelCode"
                style="width: 200px"
                placeholder="请选择"
                :options="subChannelNos"
                :fieldNames="{ label: 'channelName', value: 'channelNo' }"
                allow-clear
              />
            </a-form-item>
            <a-form-item label="分润出款通道" v-if="[0].includes(where.settleChannelWay)">
              <a-select v-model:value="where.settleChannelCode" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
                  {{ channelName }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table
          ref="table"
          row-key="id"
          :datasource="datasource"
          :columns="columns"
          :where="where"
          :scroll="{ x: 'max-content' }"
          v-model:selection="selection"
        >
          <!-- table上边工具栏 -->
          <template #toolbar>
            <a-space wrap>
              <a-button type="primary" @click="handleAdd()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>

              <a-button @click="handleEdit({ type: 1 })">
                <template #icon>
                  <edit-outlined />
                </template>
                <span>修改基本信息</span>
              </a-button>

              <a-button @click="handleEdit({ type: 2 })">
                <template #icon>
                  <edit-outlined />
                </template>
                <span>修改费率信息</span>
              </a-button>

              <a-button v-if="hasPurview('0')" @click="handleEditActityPolicy">
                <template #icon>
                  <edit-outlined />
                </template>
                <span>修改活动政策</span>
              </a-button>

              <a-button v-if="hasPurview('0')" @click="handleEditTaxPoint">
                <template #icon>
                  <edit-outlined />
                </template>
                <span>修改税点</span>
              </a-button>

              <a-button v-if="hasPurview('0')" @click="handleEditStatus">
                <template #icon>
                  <edit-outlined />
                </template>
                <span>修改状态</span>
              </a-button>

              <a-dropdown>
                <template #overlay>
                  <a-menu @click="handleEditSettleRemit">
                    <a-menu-item key="1">单个修改</a-menu-item>
                    <a-menu-item key="2">批量修改</a-menu-item>
                  </a-menu>
                </template>
                <a-button v-purview="'0'">
                  <template #icon>
                    <edit-outlined />
                  </template>
                  <span>修改结算周期&方式</span>
                  <DownOutlined />
                </a-button>
              </a-dropdown>

              <a-button v-if="hasPurview('0')" @click="handleEditAppBrandType">
                <template #icon>
                  <edit-outlined />
                </template>
                <span>修改APP品牌类型</span>
              </a-button>
            </a-space>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'remitType'">
              <a-tag v-if="record.remitType === 1" color="cyan">平台清算</a-tag>
              <a-tag v-else-if="record.remitType === 2" color="pink">自行提现</a-tag>
              <span v-else>--</span>
            </template>

            <template v-else-if="column.key === 'settleMode'">
              <a-tag v-if="record.settleMode === 1" color="cyan">日结</a-tag>
              <a-tag v-else-if="record.settleMode === 2" color="blue">月结</a-tag>
              <span v-else>--</span>
            </template>

            <template v-else-if="column.key === 'settleChannelWay'">
              <a-tag v-if="record.settleChannelWay === 3" color="cyan">展业平台</a-tag>
              <a-tag v-else-if="record.settleChannelWay === 1" color="blue">平台出款</a-tag>
              <a-tag v-else-if="record.settleChannelWay === 0" color="orange">交易通道</a-tag>
            </template>
            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record.id)">详情</a>
                <template v-if="hasPurview('0')">
                  <a-divider type="vertical" />
                  <a @click="handleParamsConfig(record)">参数配置</a>
                </template>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <RegionCenterEdit
      v-if="showEdit"
      v-model:visible="showEdit"
      :data="current"
      :opt-type="operationType"
      :channelCodes="channelCodes"
      :bankList="bankList"
      :appBrands="appBrands"
      @done="reload"
    />

    <SettleRemitEdit v-model:visible="showEditSettleRemit" :data="current" @done="reload" />

    <EditTaxPoint v-if="showEditTaxPoint" v-model:visible="showEditTaxPoint" :data="taxPointInfo" @done="reload" />

    <EditAppBrandType
      v-if="showEditAppBrandType"
      v-model:visible="showEditAppBrandType"
      :data="appBrandInfo"
      @done="reload"
      :app-brands="appBrands"
    />

    <ActivityCashBackPolicyEdit
      v-if="showEditActivitPolicy"
      v-model:visible="showEditActivitPolicy"
      :data="current"
      :channelCodes="channelCodes"
      @done="reload"
    />

    <RegionStatusEdit v-if="showEditStatus" v-model:visible="showEditStatus" :data="statusRow" @done="reload" />

    <ParamsConfig v-if="showParamsConfig" v-model:visible="showParamsConfig" :data="current2" />
  </div>
</template>

<script>
import { message } from 'ant-design-vue';
import { RegionManageApi } from '@/api/businessTeam/region-center/RegionManageApi';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import { BankCodeManageApi } from '@/api/base/BankCodeManageApi';
import { RateTemplateApi } from '@/api/businessTeam/rate-template/RateTemplateApi';
import RegionCenterEdit from './RegionCenterEdit.vue';
import SettleRemitEdit from './SettleRemitEdit.vue';
import RegionStatusEdit from './RegionStatusEdit.vue';
import ActivityCashBackPolicyEdit from './ActivityCashBackPolicyEdit.vue';
import { ActivityCashbackTemplateManageApi } from '@/api/businessTeam/activity-Config/ActivityCashbackTemplateManageApi';
import { hasPurview } from '@/utils/permission';
import EditTaxPoint from '../_components/EditTaxPoint.vue';
import EditAppBrandType from '../_components/EditAppBrandType.vue';
import { AppBrandApi } from '@/api/base/AppBrandApi';
import ParamsConfig from './ParamsConfig.vue';
import { RemitChannelApi } from '@/api/account/remit-channel/RemitChannelApi';

export default {
  name: 'RegionManage',
  components: {
    RegionCenterEdit,
    SettleRemitEdit,
    ActivityCashBackPolicyEdit,
    EditTaxPoint,
    EditAppBrandType,
    RegionStatusEdit,
    ParamsConfig
  },
  data() {
    return {
      showParamsConfig: false,
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '大区编号',
          dataIndex: 'regionNo',
          align: 'center'
        },
        {
          title: '大区名称',
          dataIndex: 'regionName'
        },
        {
          title: '大区简称',
          dataIndex: 'regionSname'
        },
        {
          title: '法人姓名',
          dataIndex: 'legalName',
          align: 'center'
        },
        {
          title: '法人手机号码',
          dataIndex: 'legalTelMask',
          align: 'center'
        },
        {
          title: '联系人姓名',
          dataIndex: 'contactsName',
          align: 'center'
        },
        {
          title: '联系人手机号码',
          dataIndex: 'contactsTelMask',
          align: 'center'
        },
        {
          title: '清算类型',
          dataIndex: 'remitType',
          key: 'remitType',
          width: 100,
          align: 'center'
        },
        {
          title: '结算方式',
          dataIndex: 'settleMode',
          key: 'settleMode',
          width: 100,
          align: 'center'
        },
        {
          title: '分润出款方式',
          dataIndex: 'settleChannelWay',
          key: 'settleChannelWay',
          width: 180,
          align: 'center'
        },
        {
          title: '分润出款通道',
          dataIndex: 'settleChannelCode',
          width: 160,
          align: 'center',
          customRender: ({ text, record }) => {
            if ([0].includes(record.settleChannelWay)) {
              const item = this.channelCodes.find(item => item.channelCode === text);
              return item?.channelName;
            }

            if ([1, 2].includes(record.settleChannelWay)) {
              const item = this.subChannelNos.find(item => item.channelNo === text);
              return item?.channelName;
            }

            return '--';
          }
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          key: 'action',
          width: 160,
          align: 'center',
          fixed: 'right'
        }
      ],
      // 表格搜索条件
      where: {},
      selection: [],
      showEdit: false,
      current: null,
      current2: null,
      taxPointInfo: null,
      appBrandInfo: null,
      operationType: null,
      channelCodes: [],
      bankList: [],
      appBrands: [],
      showEditSettleRemit: false,
      showEditActivitPolicy: false,
      showEditTaxPoint: false,
      showEditAppBrandType: false,

      statusRow: null,
      showEditStatus: false,

      subChannelNos: []
    };
  },
  async mounted() {
    this.getBankList();
    this.geApptBrands();
    this.getSubChannelNos();

    const data = await ChannelManageApi.list({ validStatus: 1 });
    this.channelCodes = data;
  },
  methods: {
    async getSubChannelNos() {
      const data = await RemitChannelApi.findAll({ validStatus: 1, remitChannelClassify: 2 });
      this.subChannelNos = data || [];
    },
    async handleParamsConfig(row) {
      const data = await RegionManageApi.detailParamConf({ id: row.id });
      this.current2 = {
        id: row.id,
        regionNo: row.regionNo,
        paramsMap: data?.paramsMap
      };
      this.showParamsConfig = true;
    },
    hasPurview,
    handleAdd() {
      this.current = null;
      this.showEdit = true;
      this.operationType = 0;
    },
    async geApptBrands() {
      const data = await AppBrandApi.list();
      this.appBrands = data || [];
    },
    async handleEdit({ type }) {
      if (this.selection.length !== 1) return message.warn('请选择一条数据');
      this.operationType = type;
      var data;
      if (type === 2) {
        const [allRateMap, rateMap] = await Promise.all([
          RateTemplateApi.list({ validStatus: 1, templateType: 1 }),
          RegionManageApi.rateDetail({ id: this.selection[0].id })
        ]).catch(error => {
          console.log(error);
        });
        data = {
          id: this.selection[0].id,
          rateMap: rateMap || {},
          allRateMap: allRateMap || []
        };
      } else {
        data = await RegionManageApi.detail({ id: this.selection[0].id, isEditOpt: 1 });
      }
      this.current = data;
      this.showEdit = true;
    },

    async handleEditAppBrandType() {
      if (this.selection.length !== 1) return message.warn('请选择一条数据');
      const [{ id, appBrandType, regionNo }] = this.selection;
      this.appBrandInfo = {
        id,
        userType: '1',
        userNo: regionNo,
        appBrandType
      };
      this.showEditAppBrandType = true;
    },

    async handleEditActityPolicy() {
      if (this.selection.length !== 1) return message.warn('请选择一条数据');
      const [{ regionNo }] = this.selection;
      const params = {
        orgNo: regionNo
      };
      const data = await ActivityCashbackTemplateManageApi.detailCashbackPolicy(params);
      this.current = Object.assign(params, data);
      this.showEditActivitPolicy = true;
    },

    async handleEditStatus() {
      if (this.selection.length !== 1) return message.warn('请选择一条数据');

      const [{ id }] = this.selection;
      const data = await RegionManageApi.statusDetail({ id });
      this.statusRow = Object.assign({}, data);
      this.showEditStatus = true;
    },

    async handleEditTaxPoint() {
      if (this.selection.length !== 1) return message.warn('请选择一条数据');
      const [{ id, taxPoint, regionNo }] = this.selection;
      this.taxPointInfo = {
        id,
        userType: '1',
        userNo: regionNo,
        taxPoint
      };
      this.showEditTaxPoint = true;
    },

    handleEditSettleRemit({ key }) {
      if (key === '1') {
        if (this.selection.length !== 1) return message.warn('请选择一条数据');
        this.current = this.selection[0];
      } else {
        this.current = null;
      }
      this.showEditSettleRemit = true;
    },

    async handleDetail(id) {
      this.operationType = 3;
      const data = await RegionManageApi.detail({ id, isEditOpt: 0 });
      this.current = data;
      this.showEdit = true;
    },

    async getBankList() {
      const data = await BankCodeManageApi.list();
      this.bankList = data || [];
    },

    reload() {
      this.selection = [];
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.selection = [];
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    datasource({ page, limit, where }) {
      return RegionManageApi.findPage({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>
