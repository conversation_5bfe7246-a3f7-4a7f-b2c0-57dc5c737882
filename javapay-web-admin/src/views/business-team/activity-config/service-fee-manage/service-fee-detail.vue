<template>
  <a-modal
    :width="800"
    :visible="visible"
    title="详情"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
    :footer="null"
  >
    <a-descriptions bordered :column="2">
      <a-descriptions-item label="服务费金额">{{ form.serviceFee }}</a-descriptions-item>
      <a-descriptions-item label="最大返现金额">{{ form.maxCashbackAmt }}</a-descriptions-item>
      <a-descriptions-item label="支付通道">{{ form.channelName }}</a-descriptions-item>
      <a-descriptions-item label="终端来源">
        <a-tag v-if="form.terminalSource === 1" color="green">全款机</a-tag>
        <a-tag v-else-if="form.terminalSource === 2" color="purple">分期机</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="是否需要意愿核身">
        <span v-if="form.isNeedIdentityVert === 1" class="ele-text-success">需要</span>
        <span v-else class="ele-text-danger">不需要</span>
      </a-descriptions-item>
      <a-descriptions-item label="状态">
        <span v-if="form.validStatus === 1" class="ele-text-success">有效</span>
        <span v-else class="ele-text-danger">无效</span>
      </a-descriptions-item>
      <a-descriptions-item label="创建人">{{ form.createUserId }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ toDateString(form.createTime) }}</a-descriptions-item>
      <a-descriptions-item label="最后修改人">{{ form.lastModifyUserId }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ toDateString(form.lastModifyTime) }}</a-descriptions-item>
    </a-descriptions>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';
import { toDateString } from 'ele-admin-pro';

export default {
  name: 'ServiceFeeDetail',
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible,
      toDateString
    };
  }
};
</script>
