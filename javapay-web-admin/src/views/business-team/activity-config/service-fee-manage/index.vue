<template>
  <div class="ele-body">
    <!-- 搜索框内容 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="支付通道">
              <a-select v-model:value="where.channelCode" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
                  {{ channelName }}
                </a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="终端来源">
              <a-select v-model:value="where.terminalSource" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">全款机</a-select-option>
                <a-select-option :value="2">分期机</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="是否需要意愿核身">
              <a-select v-model:value="where.isNeedIdentityVert" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">需要</a-select-option>
                <a-select-option :value="0">不需要</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="有效状态">
              <a-select v-model:value="where.validStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">有效</a-select-option>
                <a-select-option :value="0">无效</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格内容 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- 表格上方的操作按钮 -->
          <template #toolbar v-if="hasPurview(['0'])">
            <a-space>
              <a-button type="primary" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>

          <!-- 表体的操作 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'validStatus'">
              <a-switch :checked="record.validStatus === 1" @change="checked => editValidStatus(checked, record)" />
            </template>

            <template v-else-if="column.key === 'isNeedIdentityVert'">
              <a-tag v-if="record.isNeedIdentityVert === 0" color="pink">不需要</a-tag>
              <a-tag v-else-if="record.isNeedIdentityVert === 1" color="blue">需要</a-tag>
            </template>

            <template v-else-if="column.key === 'terminalSource'">
              <a-tag v-if="record.terminalSource === 1" color="green">全款机</a-tag>
              <a-tag v-else-if="record.terminalSource === 2" color="purple">分期机</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <template v-if="hasPurview(['0'])">
                  <a @click="handleEdit(record)">修改</a>
                  <a-divider type="vertical" />
                </template>

                <a @click="handleShowDetail(record)">详情</a>

                <template v-if="hasPurview(['0'])">
                  <a-divider type="vertical" />
                  <a-popconfirm title="确定要删除此记录吗？" @confirm="remove(record)">
                    <a class="ele-text-danger">删除</a>
                  </a-popconfirm>
                </template>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 新增编辑 -->
    <ServiceFeeEdit v-model:visible="showEdit" :data="current" :channelCodes="channelCodes" @done="reload" />

    <!-- 详情页面 -->
    <ServiceFeeDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { ServiceFeeManageApi } from '@/api/businessTeam/activity-Config/ServiceFeeManageApi';
import { message } from 'ant-design-vue';
import ServiceFeeEdit from './service-fee-edit.vue';
import ServiceFeeDetail from './service-fee-detail.vue';
import { hasPurview } from '@/utils/permission';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';

export default {
  name: 'ServiceFeeManage',
  components: {
    ServiceFeeEdit,
    ServiceFeeDetail
  },
  data() {
    return {
      //表格查询条件
      where: {},
      //展示编辑页面传的参数
      current: null,
      //是否展示新增或者是编辑页面
      showEdit: false,
      //是否展示详情页面
      showDetail: false,
      hasPurview,
      channelCodes: [],
      //表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center'
        },
        {
          title: '服务费金额',
          dataIndex: 'serviceFee',
          align: 'center'
        },
        {
          title: '最大返现金额',
          dataIndex: 'maxCashbackAmt',
          align: 'center'
        },
        {
          title: '支付通道',
          dataIndex: 'channelCode',
          align: 'center',
          customRender: ({ record, text }) => {
            const item = this.channelCodes.find(c => c.channelCode === text);
            record.channelName = item?.channelName || '--';
            return record.channelName;
          }
        },
        {
          title: '终端来源',
          dataIndex: 'terminalSource',
          align: 'center',
          key: 'terminalSource'
        },
        {
          title: '是否需要意愿核身',
          dataIndex: 'isNeedIdentityVert',
          align: 'center',
          key: 'isNeedIdentityVert'
        },
        {
          title: '有效状态',
          dataIndex: 'validStatus',
          align: 'center',
          key: 'validStatus'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          align: 'center'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId',
          align: 'center'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime',
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 150,
          align: 'center'
        }
      ]
    };
  },
  created() {
    this.getChannelCodes();
  },
  methods: {
    async getChannelCodes() {
      const data = await ChannelManageApi.list({ validStatus: 1 });
      this.channelCodes = data || [];
    },

    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    //改变有效状态
    async editValidStatus(checked, row) {
      const id = row.id;
      const status = checked ? 1 : 0;
      const result = await ServiceFeeManageApi.changeStatus({ id, validStatus: status });
      message.success(result.message);
      row.validStatus = status;
    },
    //新建或者编辑
    handleEdit(row) {
      this.showEdit = true;
      this.current = row;
    },

    //展示详情
    handleShowDetail(row) {
      this.showDetail = true;
      this.current = row;
    },

    //删除
    async remove(row) {
      const result = await ServiceFeeManageApi.delete({ id: row.id });
      message.success(result.message);
      this.reload();
    },

    //获取数据方法
    datasource({ page, limit, where }) {
      return ServiceFeeManageApi.findPages({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>
