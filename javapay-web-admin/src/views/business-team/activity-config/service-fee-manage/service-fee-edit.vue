<template>
  <a-modal
    :width="800"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新增'"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @cancel="cancel"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 9 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 17 }, sm: { span: 24 } }"
    >
      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="服务费金额" name="serviceFee">
            <a-input v-model:value.trim="form.serviceFee" placeholder="请输入服务费金额" :disabled="isUpdate" allow-clear />
          </a-form-item>
          <a-form-item label="支付通道" name="channelCode">
            <a-select v-model:value="form.channelCode" class="ele-fluid" placeholder="请选择" allow-clear :disabled="isUpdate">
              <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode"
              >{{ channelName }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="是否需要意愿核身" name="isNeedIdentityVert">
            <a-radio-group v-model:value="form.isNeedIdentityVert" name="isNeedIdentityVert">
              <a-radio :value="1">需要</a-radio>
              <a-radio :value="0">不需要</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="最大返现金额" name="maxCashbackAmt">
            <a-input v-model:value.trim="form.maxCashbackAmt" placeholder="请输入最大返现金额" allow-clear />
          </a-form-item>
          <a-form-item label="终端来源" name="terminalSource">
            <a-radio-group v-model:value="form.terminalSource" name="terminalSource" :disabled="isUpdate">
              <a-radio :value="1">全款机</a-radio>
              <a-radio :value="2">分期机</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="有效状态" name="validStatus">
            <a-radio-group v-model:value="form.validStatus" name="validStatus">
              <a-radio :value="1">有效</a-radio>
              <a-radio :value="0">无效</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script>
import { ServiceFeeManageApi } from '@/api/businessTeam/activity-Config/ServiceFeeManageApi';
import { message } from 'ant-design-vue';

function formDefaults() {
  return {
    validStatus: 1,
    isNeedIdentityVert: 0,
    terminalSource: 1
  };
}

export default {
  name: 'ServiceFeeEdit',
  props: {
    visible: Boolean,
    data: Object,
    channelCodes: Array
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: Object.assign({}, this.data),
      // 表单验证规则
      rules: {
        serviceFee: [{ required: true, message: '请输入服务费金额' }],
        maxCashbackAmt: [{ required: true, message: '请输入最大返现金额' }],
        channelCode: [{ required: true, message: '请选择' }],
        isNeedIdentityVert: [{ required: true, message: '请选择' }],
        validStatus: [{ required: true, message: '请选择' }],
        terminalSource: [{ required: true, message: '请选择' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false
    };
  },
  watch: {
    data() {
      if (this.data) {
        this.form = Object.assign({}, this.data);
        this.isUpdate = true;
      } else {
        this.form = formDefaults();
        this.isUpdate = false;
      }
      if (this.$refs.form) {
        this.$refs.form.clearValidate();
      }
    }
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      if (Number(this.form.maxCashbackAmt) > Number(this.form.serviceFee)) {
        message.warning('最大返现金额不能大于服务费金额');
        return;
      }

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      // 执行编辑或修改方法
      if (this.isUpdate) {
        result = ServiceFeeManageApi.edit(this.form);
      } else {
        result = ServiceFeeManageApi.add(this.form);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 如果是新增，则form表单置空
          if (!this.isUpdate) {
            this.form = formDefaults();
          }

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    cancel() {
      this.form = Object.assign(formDefaults(), this.data);
      this.$refs.form.clearValidate();
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
