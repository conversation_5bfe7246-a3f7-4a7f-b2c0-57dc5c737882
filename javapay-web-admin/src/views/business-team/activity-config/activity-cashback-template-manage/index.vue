<template>
  <div class="ele-body">
    <!-- 搜索框内容 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="政策名称">
              <a-input v-model:value.trim="where.activityCashbackPolicyName" placeholder="请输入政策名称" allow-clear />
            </a-form-item>
            <template v-if="hasPurview('0')">
              <a-form-item label="政策编号">
                <a-input v-model:value.trim="where.templateNo" placeholder="请输入政策编号" allow-clear />
              </a-form-item>
              <a-form-item label="用户编号">
                <a-input v-model:value.trim="where.userNo" placeholder="请输入用户编号" allow-clear />
              </a-form-item>
            </template>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格内容 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- 表格上方的操作按钮 -->
          <template #toolbar v-if="hasPurview(['1', '2', '3', '5'])">
            <a-space>
              <a-button type="primary" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>

          <!-- 表体的操作 -->
          <template #bodyCell="{ column, record }">
            <!-- table操作栏按钮 -->
            <template v-if="column.key === 'action'">
              <a-space>
                <template v-if="hasPurview(['1', '2', '3', '5'])">
                  <a @click="handleEdit(record)">修改</a>
                  <a-divider type="vertical" />
                </template>

                <a @click="handleShowDetail(record)">详情</a>

                <template v-if="hasPurview(['1', '2', '3', '5'])">
                  <a-divider type="vertical" />
                  <a-popconfirm title="确定要删除此记录吗？" @confirm="remove(record)">
                    <a class="ele-text-danger">删除</a>
                  </a-popconfirm>
                </template>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 新增编辑 -->
    <ActivityCashbackTemplateEdit
      v-if="showEdit"
      v-model:visible="showEdit"
      :data="current"
      :allSimFeeList="allSimFeeList"
      :displayTemplateData="displayTemplateData"
      :channel-codes="channelCodes"
      @done="reload"
    />

    <!-- 详情页面 -->
    <ActivityCashbackTemplateDetail
      v-if="showDetail"
      v-model:visible="showDetail"
      :detail="current"
      :allSimFeeList="allSimFeeList"
      :displayTemplateData="displayTemplateData"
      :channel-codes="channelCodes"
    />
  </div>
</template>

<script>
import { ActivityCashbackTemplateManageApi } from '@/api/businessTeam/activity-Config/ActivityCashbackTemplateManageApi';
import { SimFeeManageApi } from '@/api/businessTeam/activity-Config/SimFeeManageApi';
import { message } from 'ant-design-vue';
import ActivityCashbackTemplateEdit from './activity-cashback-template-edit.vue';
import ActivityCashbackTemplateDetail from './activity-cashback-template-detail.vue';
import { hasPurview } from '@/utils/permission';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';

export default {
  name: 'SimFeeManage',
  components: {
    ActivityCashbackTemplateEdit,
    ActivityCashbackTemplateDetail
  },
  data() {
    const isHideCol = !hasPurview('0');
    return {
      //表格查询条件
      where: {},
      //展示编辑页面传的参数
      current: null,
      //是否展示新增或者是编辑页面
      showEdit: false,
      //是否展示详情页面
      showDetail: false,
      channelCodes: [],
      //表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center'
        },
        {
          title: '政策名称',
          dataIndex: 'activityCashbackPolicyName',
          align: 'center'
        },
        {
          title: '政策编号',
          dataIndex: 'templateNo',
          align: 'center',
          hideCol: isHideCol
        },
        {
          title: '用户编号',
          dataIndex: 'userNo',
          align: 'center',
          hideCol: isHideCol
        },
        {
          title: '创建人',
          dataIndex: 'createUserId',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          align: 'center'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId',
          align: 'center'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime',
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 150,
          align: 'center'
        }
      ].filter(i => !i.hideCol),
      allSimFeeList: [], //流量费总体的
      displayTemplateData: {}, //模版数据
      hasPurview
    };
  },
  async mounted() {
    this.getAllSimFeeList();
    this.getDisplayDetail();
    this.getChannelCodes();
  },
  methods: {
    async getChannelCodes() {
      const data = await ChannelManageApi.list({ validStatus: 1 });
      this.channelCodes = data || [];
    },

    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    //新建或者编辑
    handleEdit(row) {
      this.showEdit = true;
      this.current = row;
    },

    //展示详情
    handleShowDetail(row) {
      this.showDetail = true;
      this.current = row;
    },

    //获取模版返现数据
    async getDisplayDetail() {
      const data = await ActivityCashbackTemplateManageApi.displayDetail();
      this.displayTemplateData = data || {};
    },

    //获取流量费列表
    async getAllSimFeeList() {
      this.allSimFeeList = await SimFeeManageApi.list({ validStatus: 1 });
    },

    //删除
    async remove(row) {
      const result = await ActivityCashbackTemplateManageApi.delete({ id: row.id });
      message.success(result.message);
      this.reload();
    },

    //获取数据方法
    datasource({ page, limit, where }) {
      return ActivityCashbackTemplateManageApi.findPages({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>
