<template>
  <a-modal
    :width="1100"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新增'"
    :body-style="{ paddingBottom: '8px' }"
    :mask-closable="false"
    @update:visible="updateVisible"
    @ok="save"
  >
    <div class="block-interval">
      <a-form ref="form" :model="form">
        <a-form-item label="政策名称" name="activityCashbackPolicyName" :rules="rules.activityCashbackPolicyName">
          <a-input v-model:value.trim="form.activityCashbackPolicyName" placeholder="请输入政策名称" allow-clear :span="20" />
        </a-form-item>

        <a-tabs v-model:activeKey="activeChannleTabKey" type="card" @change="onChangeChannelTab">
          <a-tab-pane
            v-for="(channel, cIndex) in form.tabChannelDataModel || []"
            :tab="channel.channelName"
            :key="String(cIndex)"
            force-render
            class="tabpane__background"
          >
            <a-card :bordered="false">
              <a-tabs v-model:activeKey="activeTerminalSourceTabKey">
                <a-tab-pane v-for="(tab, tabIndex) in channel.channelData || []" :tab="tab.label" :key="String(tab.key)" force-render>
                  <a-divider orientationMargin="0" orientation="left" dashed>押金返现政策</a-divider>
                  <template v-if="tab.serviceFeeData?.length">
                    <div v-for="(item, index) in tab.serviceFeeData || []" :key="index">
                      <div v-if="item.show" class="row-container">
                        <a-row :gutter="16" align="middle">
                          <a-col :span="2">
                            <a-form-item label="">
                              <a-checkbox v-model:checked="item.selected">选择</a-checkbox>
                            </a-form-item>
                          </a-col>
                          <a-col :span="11">
                            <a-form-item
                              label="服务费金额(元)"
                              :name="['tabChannelDataModel', cIndex, 'channelData', tabIndex, 'serviceFeeData', index, 'configId']"
                              :rules="item.selected ? depositPolicyParamsRules.configId : []"
                              :label-col="{ md: { span: 6 }, sm: { span: 24 } }"
                            >
                              <a-input :value="item.serviceFeeAmt + '元'" placeholder="请输入" allow-clear :span="20" />
                            </a-form-item>
                          </a-col>
                          <a-col :span="11" v-if="item.selected">
                            <a-form-item
                              label="返现金额(元)"
                              :name="['tabChannelDataModel', cIndex, 'channelData', tabIndex, 'serviceFeeData', index, 'cashbackAmt']"
                              :rules="item.selected ? depositPolicyParamsRules.cashbackAmt : []"
                              :label-col="{ md: { span: 6 }, sm: { span: 24 } }"
                            >
                              <template #help>
                                <span :style="{ fontSize: '12px', color: item.isCashbackAmtError ? 'red' : 'gray' }">
                                  {{ '注:下级必须低于或等于' + item.parentCashbackAmt + '元' }}
                                </span>
                              </template>
                              <a-input
                                v-model:value.trim="item.cashbackAmt"
                                placeholder="请输入返现金额"
                                @change="handleDepositFeeCashBackAmtChange(item)"
                                :class="{ redBorder: item.isCashbackAmtError }"
                                allow-clear
                              />
                            </a-form-item>
                          </a-col>
                        </a-row>
                      </div>
                    </div>
                  </template>
                  <a-alert v-else message="没有政策配置哦~" banner />

                  <a-divider orientationMargin="0" orientation="left" dashed>流量费返现政策</a-divider>
                  <template v-if="tab.simFeeData?.some(s => s.data.length)">
                    <div v-for="(period, idx) in tab.simFeeData || []" :key="idx">
                      <template v-if="period.show">
                        <a-typography-text strong>{{ period.name }}</a-typography-text>
                        <div v-for="(item, idxi) in period.data || []" :key="idxi">
                          <div v-if="item.show" class="row-container">
                            <a-row :gutter="16" align="middle">
                              <a-col :span="2">
                                <a-form-item>
                                  <a-checkbox v-model:checked="item.selected">选择</a-checkbox>
                                </a-form-item>
                              </a-col>
                              <a-col :span="11">
                                <a-form-item
                                  label="流量费金额(元)"
                                  :name="[
                                    'tabChannelDataModel',
                                    cIndex,
                                    'channelData',
                                    tabIndex,
                                    'simFeeData',
                                    idx,
                                    'data',
                                    idxi,
                                    'configId'
                                  ]"
                                  :rules="item.selected ? simFeePolicyParamsRules.configId : []"
                                  :label-col="{ md: { span: 6 }, sm: { span: 24 } }"
                                >
                                  <a-input :value="item.policyName" placeholder="" :span="20" />
                                </a-form-item>
                              </a-col>
                              <a-col :span="11" v-if="item.selected">
                                <a-form-item
                                  label="返现金额(元)"
                                  :name="[
                                    'tabChannelDataModel',
                                    cIndex,
                                    'channelData',
                                    tabIndex,
                                    'simFeeData',
                                    idx,
                                    'data',
                                    idxi,
                                    'cashbackAmt'
                                  ]"
                                  :rules="item.selected ? simFeePolicyParamsRules.cashbackAmt : []"
                                  :label-col="{ md: { span: 6 }, sm: { span: 24 } }"
                                >
                                  <template #help>
                                    <span :style="{ fontSize: '12px', color: item.isCashbackAmtError ? 'red' : 'gray' }">
                                      {{ '注:下级必须低于或等于' + item.parentCashbackAmt + '元' }}
                                    </span>
                                  </template>
                                  <a-input
                                    v-model:value.trim="item.cashbackAmt"
                                    placeholder="请输入返现金额"
                                    @change="handleSimFeeCashBackAmtChange(item)"
                                    :class="{ redBorder: item.isCashbackAmtError }"
                                    allow-clear
                                  />
                                </a-form-item>
                              </a-col>
                            </a-row>
                          </div>
                        </div>
                      </template>
                    </div>
                  </template>
                  <a-alert v-else message="没有政策配置哦~" banner />
                </a-tab-pane>
              </a-tabs>
            </a-card>
          </a-tab-pane>
        </a-tabs>
      </a-form>
    </div>
  </a-modal>
</template>

<script>
import { ActivityCashbackTemplateManageApi } from '@/api/businessTeam/activity-Config/ActivityCashbackTemplateManageApi';
import { message } from 'ant-design-vue';
import { hasPurview } from '@/utils/permission';
import { deepCopy } from '@/utils/util';

const simFeePolicyPeriodGroupDef = [
  {
    name: '第一期',
    field: 'firstPeriodList',
    data: []
  },
  {
    name: '第二期',
    field: 'secondPeriodList',
    data: []
  },
  {
    name: '第三期',
    field: 'thirdPeriodList',
    data: []
  },
  {
    name: '标准期 (第四期以及后续阶段)',
    field: 'fourthPeriodList',
    data: []
  }
];

const tabTerminalSourceDataModelDef = [
  {
    label: '全款机配置',
    key: 1,
    serviceFeeData: [],
    simFeeData: []
  },
  {
    label: '分期机配置',
    key: 2,
    serviceFeeData: [],
    simFeeData: []
  }
];

/**
 * {
 *  channelName =》 渠道名称,
 *  channelCode =》 渠道编号,
 *  channelData =》 tabTerminalSourceDataModelDef
 * }
 */
let tabChannelDataModelDef = [];

export default {
  name: 'ActivityCashbackTemplateEdit',
  props: {
    visible: Boolean,
    data: Object,
    displayTemplateData: Object,
    channelCodes: Array
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      activeChannleTabKey: '0',
      activeTerminalSourceTabKey: '1',
      // 表单数据
      form: {},
      simFeePolicyDTOList: {}, //接收displayTemplateData里面的值
      serviceFeePolicyDTOList: [], //接收displayTemplateData里面的值
      // 表单验证规则
      rules: {
        activityCashbackPolicyName: [{ required: true, message: '请输入政策名称' }]
      },
      //键值对表格规则
      depositPolicyParamsRules: {
        configId: [{ required: true, message: '请选择' }],
        cashbackAmt: [{ required: true, message: '请输入返现金额' }]
      },
      //键值对表格规则
      simFeePolicyParamsRules: {
        configId: [{ required: true, message: '请选择' }],
        cashbackAmt: [{ required: true, message: '请输入返现金额' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false,
      hasPurview
    };
  },
  created() {
    //赋值
    this.serviceFeePolicyDTOList = this.displayTemplateData.serviceFeePolicyDTOList;
    this.simFeePolicyDTOList = this.displayTemplateData.simFeeNewPolicyDTO || {};

    let allChannelCodes = [];
    try {
      const channelCodes = new Set(
        [...(this.serviceFeePolicyDTOList || []), ...Object.values(this.simFeePolicyDTOList || {})]
          .flat(Infinity)
          .flatMap(item => (Array.isArray(item) ? item.map(subItem => subItem?.channelCode) : item?.channelCode))
          .filter(code => code?.trim())
          .map(code => code.trim())
      );
      allChannelCodes = [...channelCodes];
    } catch (error) {
      console.log(error);
    }

    tabChannelDataModelDef = [];
    allChannelCodes.forEach(item => {
      const channelItem = this.channelCodes.find(channel => channel.channelCode === item);
      tabChannelDataModelDef.push({
        channelCode: item,
        channelName: channelItem?.channelName,
        channelData: []
      });
    });

    if (this.data) {
      this.form = Object.assign({}, this.data);
      //对于修改的,要先对数据处理,因为原来的数据可能不包含所有的活动,所以需要通过全部的活动进行一次,把未添加的也添加进去,并且设置为未选中,已有的设置为已选中
      this.handleDataForEditMethod();
      this.isUpdate = true;
    } else {
      this.form = {
        //接收押金返现键值对数组
        depositPolicyParamsArr: [],
        //接收流量费返现周期组
        simFeePolicyPeriodGroup: [],
        tabTerminalSourceDataModel: tabTerminalSourceDataModelDef
      };
      this.isUpdate = false;
      this.handleServiceFeeAndSimFeeData();
    }
  },
  methods: {
    onChangeChannelTab() {
      this.activeTerminalSourceTabKey = '1';
    },

    //根据服务费和流量费模版,来添加数组,然后再页面展示
    handleServiceFeeAndSimFeeData() {
      const tabChannelDataModel = deepCopy(tabChannelDataModelDef);

      tabChannelDataModel.forEach(channel => {
        channel.channelData = deepCopy(tabTerminalSourceDataModelDef);

        channel.channelData.forEach(tab => {
          this.serviceFeePolicyDTOList?.forEach(item => {
            if (item.terminalSource === tab.key && item.channelCode === channel.channelCode) {
              const show = Number(item.parentCashbackAmt) > 0 || !(Number(item.serviceFeeAmt) > 0);
              tab.serviceFeeData.push({
                configId: parseInt(item.configId),
                serviceFeeAmt: item.serviceFeeAmt,
                cashbackAmt: show ? '' : 0,
                parentCashbackAmt: item.parentCashbackAmt,
                terminalSource: item.terminalSource,
                selected: !show,
                isCashbackAmtError: false,
                channelCode: item.channelCode,
                show
              });
            }
          });

          const simFeePolicyPeriodGroup = deepCopy(simFeePolicyPeriodGroupDef);
          simFeePolicyPeriodGroup.forEach(period => {
            Object.keys(this.simFeePolicyDTOList).forEach(key => {
              if (period.field === key) {
                const sameTerminalSourceData = this.simFeePolicyDTOList[key].filter(
                  item => item.terminalSource === tab.key && item.channelCode === channel.channelCode
                );
                const formatData = sameTerminalSourceData.map(item => {
                  const show = Number(item.parentCashbackAmt) > 0 || !(Number(item.simFeeAmt) > 0);
                  return {
                    ...item,
                    configId: parseInt(item.configId),
                    terminalSource: item.terminalSource,
                    selected: !show,
                    isCashbackAmtError: false,
                    show: show,
                    cashbackAmt: show ? '' : 0
                  };
                });
                period.data = deepCopy(formatData);
                period.show = period.data.some(item => item.show);
              }
            });
          });
          tab.simFeeData = simFeePolicyPeriodGroup;
        });
      });

      this.form.tabChannelDataModel = tabChannelDataModel;
    },

    //对于修改的,先去处理数据
    handleDataForEditMethod() {
      const tabChannelDataModel = deepCopy(tabChannelDataModelDef);

      this.form.depositPolicyParamsArr = JSON.parse(this.data.depositPolicy);

      //对于修改的,要先对数据处理,因为原来的数据可能不包含所有的活动,所以需要通过全部的活动进行一次,把未添加的也添加进去,并且设置为未选中,已有的设置为已选中
      for (let i = 0; i < this.serviceFeePolicyDTOList?.length; i++) {
        const item1 = this.serviceFeePolicyDTOList[i];
        let found = false;
        for (let j = 0; j < this.form.depositPolicyParamsArr?.length; j++) {
          const item2 = this.form.depositPolicyParamsArr[j];
          if (parseInt(item1.configId) === item2.configId) {
            item2.selected = true;
            found = true;
            //发现相同的之后,要把item1的那些上级的返现金额赋值给item2的,用于页面展示和比较
            item2.parentCashbackAmt = item1.parentCashbackAmt;
            item2.terminalSource = item1.terminalSource;
            item2.channelCode = item1.channelCode;
            item2.show = Number(item1.parentCashbackAmt) > 0 || !(Number(item1.serviceFeeAmt) > 0);
            break;
          }
        }
        if (found === false) {
          const show = Number(item1.parentCashbackAmt) > 0 || !(Number(item1.serviceFeeAmt) > 0);
          this.form.depositPolicyParamsArr.push({
            configId: parseInt(item1.configId),
            serviceFeeAmt: item1.serviceFeeAmt,
            cashbackAmt: show ? '' : 0,
            parentCashbackAmt: item1.parentCashbackAmt,
            terminalSource: item1.terminalSource,
            channelCode: item1.channelCode,
            selected: !show,
            isCashbackAmtError: false,
            show: show
          });
        }
      }

      const simFeePolicyMap = JSON.parse(this.data.simFeeNewPolicy || '{}');
      const simFeePolicyPeriodGroupMap = deepCopy(simFeePolicyPeriodGroupDef);

      // 设置自身的值
      simFeePolicyPeriodGroupMap.forEach(period => {
        Object.keys(simFeePolicyMap).forEach(key => {
          if (period.field === key) {
            period.data = simFeePolicyMap[key]?.map(item => {
              return {
                ...item,
                selected: true,
                isCashbackAmtError: false
              };
            });
          }
        });
      });

      simFeePolicyPeriodGroupMap.forEach(period => {
        Object.keys(this.simFeePolicyDTOList).forEach(key => {
          if (period.field === key) {
            period.data.forEach(item => {
              this.simFeePolicyDTOList[key].forEach(i => {
                const show = Number(i.parentCashbackAmt) > 0 || !(Number(i.simFeeAmt) > 0);
                if (item.configId === Number(i.configId)) {
                  item.simFeeAmt = i.simFeeAmt;
                  item.policyName = i.policyName;
                  item.parentCashbackAmt = i.parentCashbackAmt;
                  item.terminalSource = i.terminalSource;
                  item.channelCode = i.channelCode;
                  item.show = show;
                }
              });
            });

            const selfConfigIdList = period.data?.map(item => item.configId);
            const allConfigIdList = this.simFeePolicyDTOList[key]?.map(item => item.configId);
            const diffConfigIdList = allConfigIdList.filter(aId => !selfConfigIdList.includes(Number(aId)));
            const diffList = this.simFeePolicyDTOList[key]?.filter(item => diffConfigIdList.includes(item.configId));
            const formatDiffList = diffList?.map(item => {
              const show = Number(item.parentCashbackAmt) > 0 || !(Number(item.simFeeAmt) > 0);
              return {
                ...item,
                configId: Number(item.configId),
                selected: !show,
                isCashbackAmtError: false,
                show: show,
                cashbackAmt: show ? '' : 0
              };
            });

            period.data = [...period.data, ...formatDiffList];
          }
        });
      });

      tabChannelDataModel.forEach(channel => {
        channel.channelData = deepCopy(tabTerminalSourceDataModelDef);
        channel.channelData.forEach(tab => {
          this.form.depositPolicyParamsArr.forEach(item => {
            if (item.terminalSource === tab.key && item.channelCode === channel.channelCode) {
              tab.serviceFeeData.push(item);
            }
          });

          const simFeeData = simFeePolicyPeriodGroupMap.map(period => {
            const periodData = period.data.filter(item => item.terminalSource === tab.key && item.channelCode === channel.channelCode);
            return {
              ...period,
              data: periodData,
              show: periodData.some(item => item.show)
            };
          });

          tab.simFeeData = simFeeData;
        });
      });

      this.form.tabChannelDataModel = tabChannelDataModel;
    },

    //押金返现金额变化之后
    handleDepositFeeCashBackAmtChange(item) {
      this.judgeDepositFeeRule(item);
    },
    //判断
    judgeDepositFeeRule(item) {
      const parentCashbackAmt = item.parentCashbackAmt ? parseInt(item.parentCashbackAmt) : 0;
      const cashBackAmt = item.cashbackAmt ? parseInt(item.cashbackAmt) : 0;

      cashBackAmt > parentCashbackAmt ? (item.isCashbackAmtError = true) : (item.isCashbackAmtError = false);
      if (cashBackAmt > parentCashbackAmt) {
        return false;
      }
      return true;
    },

    //流量费返现金额变化之后
    handleSimFeeCashBackAmtChange(item) {
      this.judgeSimFeeRule(item);
    },
    //判断
    judgeSimFeeRule(item) {
      const parentCashbackAmt = item.parentCashbackAmt ? parseInt(item.parentCashbackAmt) : 0;
      const cashBackAmt = item.cashbackAmt ? parseInt(item.cashbackAmt) : 0;

      cashBackAmt > parentCashbackAmt ? (item.isCashbackAmtError = true) : (item.isCashbackAmtError = false);
      if (cashBackAmt > parentCashbackAmt) {
        return false;
      }
      return true;
    },

    async save() {
      // 校验表单
      await this.$refs.form.validate();

      let depositPolicy = [];
      let simFeePolicy = [];

      this.form.tabChannelDataModel.forEach(channel => {
        channel.channelData.forEach(tab => {
          depositPolicy = [...depositPolicy, ...tab.serviceFeeData];
          simFeePolicy = [...simFeePolicy, ...tab.simFeeData];
        });
      });

      //筛选出选中的数据
      this.form.depositPolicy = depositPolicy.filter(item => item.selected);
      this.form.simFeePolicy = simFeePolicy
        .map(item => {
          const checkedList = item.data.filter(item => item.selected);
          return checkedList;
        })
        .flat();

      // 校验返现金额是否符合规则
      for (const item of this.form.depositPolicy) {
        if (!this.judgeDepositFeeRule(item)) {
          return; // 立即停止函数执行
        }
      }

      // 校验返现金额是否符合规则
      for (const item of this.form.simFeePolicy) {
        if (!this.judgeSimFeeRule(item)) {
          return; // 立即停止函数执行
        }
      }

      //筛选需要上传的数据,因为里面有很多不需要上传的字段,筛选一遍
      const depositPolicySubmitData = this.form.depositPolicy.map(item => ({
        configId: item.configId,
        serviceFeeAmt: item.serviceFeeAmt,
        cashbackAmt: item.cashbackAmt,
        terminalSource: item.terminalSource,
        channelCode: item.channelCode
      }));

      const simPolicySubmitData = {};
      this.form.tabChannelDataModel.forEach(channel => {
        channel.channelData.forEach(tab => {
          tab.simFeeData.forEach(period => {
            const checkedPeriodData = period.data.filter(item => item.selected);
            const currentFieldData = checkedPeriodData.map(item => ({
              configId: item.configId,
              simFeeAmt: item.simFeeAmt,
              cashbackAmt: item.cashbackAmt,
              terminalSource: item.terminalSource,
              channelCode: item.channelCode
            }));
            simPolicySubmitData[period.field] = simPolicySubmitData[period.field] ? [...simPolicySubmitData[period.field]] : [];
            simPolicySubmitData[period.field] = [...simPolicySubmitData[period.field], ...currentFieldData];
          });
        });
      });

      // 修改加载框为正在加载
      this.loading = true;
      let result = null;

      // 执行编辑或修改方法
      if (this.isUpdate) {
        result = ActivityCashbackTemplateManageApi.edit({
          id: this.form.id,
          templateNo: this.form.templateNo,
          activityCashbackPolicyName: this.form.activityCashbackPolicyName,
          depositPolicy: depositPolicySubmitData,
          simFeeNewPolicyDTO: simPolicySubmitData
        });
      } else {
        result = ActivityCashbackTemplateManageApi.add({
          activityCashbackPolicyName: this.form.activityCashbackPolicyName,
          depositPolicy: depositPolicySubmitData,
          simFeeNewPolicyDTO: simPolicySubmitData
        });
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>

<style scoped lang="less">
.empty-col {
  flex: 1;
}

.row-container {
  margin-top: 10px;
  margin-bottom: 15px; /* 适当调整间距值 */
}

.col-container {
  margin-bottom: 10px; /* 适当调整间距值 */
}

.redBorder {
  border-color: red;
}

.tabpane__background {
  background-color: #ececec;
  padding: 15px;

  :deep(.ant-card) {
    border-radius: 8px;
  }
}
</style>
