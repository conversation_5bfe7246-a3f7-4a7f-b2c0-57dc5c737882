<template>
  <a-modal :width="600" :visible="visible" title="详情" :body-style="{ paddingBottom: '30px' }" @update:visible="updateVisible">
    <a-descriptions :column="2">
      <a-descriptions-item label="政策名称">{{ form.activityCashbackPolicyName }}</a-descriptions-item>
      <template v-if="hasPurview('0')">
        <a-descriptions-item label="政策编号">{{ form.templateNo }}</a-descriptions-item>
        <a-descriptions-item label="用户编号">{{ form.userNo }}</a-descriptions-item>
      </template>
    </a-descriptions>

    <a-tabs v-model:activeKey="activeChannleTabKey" type="card" @change="onChangeChannelTab">
      <a-tab-pane v-for="(channel, cIndex) in form.tabChannelDataModel || []" :tab="channel.channelName" :key="String(cIndex)">
        <a-tabs v-model:activeKey="activeTerminalSourceKey">
          <a-tab-pane v-for="tab in channel.channelData || []" :tab="tab.label" :key="String(tab.key)">
            <a-divider orientationMargin="0" orientation="left" dashed>押金返现政策</a-divider>
            <template v-if="tab.serviceFeeData?.length">
              <template v-for="(item, index) in tab.serviceFeeData" :key="index">
                <div v-if="item.show">
                  <a-descriptions :column="2">
                    <a-descriptions-item label="服务费金额(元)">{{ item.serviceFeeAmt }}</a-descriptions-item>
                    <a-descriptions-item label="返现金额(元)">{{ item.cashbackAmt }}</a-descriptions-item>
                  </a-descriptions>
                </div>
              </template>
            </template>
            <a-alert v-else message="没有配置政策哦~" banner />

            <a-divider orientationMargin="0" orientation="left" dashed>流量费返现政策</a-divider>
            <template v-if="tab.simFeeData?.some(s => s.data.length)">
              <div v-for="(period, idx) in tab.simFeeData || []" :key="idx">
                <template v-if="period.show">
                  <a-typography-text strong>{{ period.name }}</a-typography-text>
                  <a-descriptions :column="2">
                    <template v-for="(item, idxi) in period.data || []" :key="idxi">
                      <template v-if="item.show">
                        <a-descriptions-item label="流量费金额(元)">{{ item.simFeeAmt }}</a-descriptions-item>
                        <a-descriptions-item label="返现金额(元)">{{ item.cashbackAmt }}</a-descriptions-item>
                      </template>
                    </template>
                  </a-descriptions>
                </template>
              </div>
            </template>
            <a-alert v-else message="没有配置政策哦~" banner />
          </a-tab-pane>
        </a-tabs>
      </a-tab-pane>
    </a-tabs>

    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { ActivityCashbackTemplateManageApi } from '@/api/businessTeam/activity-Config/ActivityCashbackTemplateManageApi';
import { hasPurview } from '@/utils/permission';
import { deepCopy } from '@/utils/util';

const simFeePolicyPeriodGroupDef = [
  {
    name: '第一期',
    field: 'firstPeriodList',
    data: []
  },
  {
    name: '第二期',
    field: 'secondPeriodList',
    data: []
  },
  {
    name: '第三期',
    field: 'thirdPeriodList',
    data: []
  },
  {
    name: '标准期 (第四期以及后续阶段)',
    field: 'fourthPeriodList',
    data: []
  }
];

const tabTerminalSourceDataModelDef = [
  {
    label: '全款机配置',
    key: 1,
    serviceFeeData: [],
    simFeeData: []
  },
  {
    label: '分期机配置',
    key: 2,
    serviceFeeData: [],
    simFeeData: []
  }
];

/**
 * {
 *  channelName =》 渠道名称
 *  channelCode =》 渠道编号
 *  channelData =》 tabTerminalSourceDataModelDef
 * }
 */
let tabChannelDataModelDef = [];

export default {
  name: 'ActivityCashbackTemplateDetail',
  props: {
    visible: Boolean,
    detail: Object,
    allSimFeeList: Array,
    displayTemplateData: Object,
    channelCodes: Array
  },
  emits: ['update:visible'],
  data() {
    return {
      activeTerminalSourceKey: '1',
      activeChannleTabKey: '0',
      form: {
        depositPolicy: [],
        simFeePolicy: [],
        simFeePolicyPeriodGroup: []
      },
      simFeePolicyArr: [],
      activityCashbackTemplateList: []
    };
  },
  async mounted() {
    this.form = Object.assign({}, this.detail);

    await this.getActivityCashbackTemplateList();

    if (this.form.simFeePolicy !== undefined) {
      this.simFeePolicyArr = JSON.parse(this.form.simFeePolicy);
    }
    //查找出对应的流量周期
    for (let i = 0; i < this.simFeePolicyArr.length; i++) {
      // 获取当前 simFeePolicyArr 对象的 id
      let currentId = this.simFeePolicyArr[i].configId;

      // 在 allSimFeeList 中查找与当前 simFeePolicyArr 对象相同 id 的对象
      for (let j = 0; j < this.allSimFeeList.length; j++) {
        if (parseInt(this.allSimFeeList[j].id) === currentId) {
          // 如果找到相同 id 的对象，则将 simPeriod 赋值给 simFeePolicyArr 对象
          this.simFeePolicyArr[i].simPeriod = this.allSimFeeList[j].simPeriod;
          this.simFeePolicyArr[i].terminalSource = this.allSimFeeList[j].terminalSource;
          this.simFeePolicyArr[i].channelCode = this.allSimFeeList[j].channelCode;
          break; // 找到匹配的对象后，跳出内层循环
        }
      }
    }

    const simFeePolicyMap = JSON.parse(this.detail.simFeeNewPolicy || '{}');
    const simFeePolicyPeriodGroupMap = simFeePolicyPeriodGroupDef;

    // 设置自身的值
    simFeePolicyPeriodGroupMap.forEach(period => {
      Object.keys(simFeePolicyMap).forEach(key => {
        if (period.field === key) {
          period.data = simFeePolicyMap[key];
        }
      });

      Object.keys(this.displayTemplateData.simFeeNewPolicyDTO || {}).forEach(key => {
        if (period.field === key) {
          period.data.forEach(item => {
            this.displayTemplateData.simFeeNewPolicyDTO[key].forEach(i => {
              if (item.configId === Number(i.configId)) {
                item.policyName = i.policyName;
                item.terminalSource = i.terminalSource;
                item.channelCode = i.channelCode;
              }
            });
          });
        }
      });
    });

    let serviceFeePolicyDTOMap = {};
    let simFeePolicyDTOMap = {};
    this.activityCashbackTemplateList.forEach(item => {
      // 自身全部服务费
      item.serviceFeePolicyDTOList.forEach(s => {
        serviceFeePolicyDTOMap[s.configId] = s;
      });

      // 自身全部流量费
      Object.keys(item.simFeeNewPolicyDTO || {}).forEach(key => {
        simFeePolicyDTOMap[key] = Object.assign({}, simFeePolicyDTOMap[key] || {});
        item.simFeeNewPolicyDTO[key].forEach(s => {
          simFeePolicyDTOMap[key][s.configId] = s;
        });
      });
    });

    const depositPolicy = this.form.depositPolicy ? JSON.parse(this.form.depositPolicy) : [];
    depositPolicy.forEach(item => {
      if (serviceFeePolicyDTOMap[item.configId]) {
        item.parentCashbackAmt = serviceFeePolicyDTOMap[item.configId].parentCashbackAmt;
        item.show = Number(item.parentCashbackAmt) > 0 || !(Number(item.serviceFeeAmt) > 0);
      }
    });

    let allChannelCodes = [];
    try {
      const channelCodes = new Set(
        [...(depositPolicy || []), ...Object.values(simFeePolicyMap || {})]
          .flat(Infinity)
          .flatMap(item => (Array.isArray(item) ? item.map(subItem => subItem?.channelCode) : item?.channelCode))
          .filter(code => code?.trim())
          .map(code => code.trim())
      );
      allChannelCodes = [...channelCodes];
    } catch (error) {
      console.log(error);
    }

    tabChannelDataModelDef = [];
    allChannelCodes.forEach(item => {
      const channelItem = this.channelCodes.find(channel => channel.channelCode === item);
      tabChannelDataModelDef.push({
        channelCode: item,
        channelName: channelItem?.channelName,
        channelData: deepCopy(tabTerminalSourceDataModelDef)
      });
    });

    const tabChannelDataModel = tabChannelDataModelDef;
    tabChannelDataModel.forEach(channel => {
      channel.channelData.forEach(tab => {
        tab.serviceFeeData =
          depositPolicy.filter(item => item.terminalSource === tab.key && item.channelCode === channel.channelCode) || [];

        const simFeeData = simFeePolicyPeriodGroupMap.map(period => {
          return {
            ...period,
            data: period.data.filter(item => item.terminalSource === tab.key && item.channelCode === channel.channelCode)
          };
        });

        simFeeData.forEach(period => {
          period.data.forEach(item => {
            const configItem = (simFeePolicyDTOMap[period.field] && simFeePolicyDTOMap[period.field][item.configId]) || null;
            if (configItem) {
              item.parentCashbackAmt = configItem.parentCashbackAmt;
              item.show = Number(item.parentCashbackAmt) > 0 || !(Number(item.simFeeAmt) > 0);
            }
          });
          period.show = period.data.some(item => item.show);
        });

        tab.simFeeData = simFeeData;
      });
    });

    this.form.tabChannelDataModel = tabChannelDataModel;
  },
  methods: {
    async getActivityCashbackTemplateList() {
      if (hasPurview('0')) {
        let data = await ActivityCashbackTemplateManageApi.querySelfList();
        this.activityCashbackTemplateList = data || [];
      } else {
        const data = await ActivityCashbackTemplateManageApi.displayDetail();
        this.activityCashbackTemplateList = data ? [{ ...data }] : [];
      }
    },
    onChangeChannelTab() {
      console.log(1);
      this.activeTerminalSourceKey = '1';
    },
    updateVisible(value) {
      this.$emit('update:visible', value);
    },
    hasPurview
  }
};
</script>
