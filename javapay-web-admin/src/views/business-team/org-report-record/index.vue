<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="机构全称">
              <a-input v-model:value.trim="where.fullName" placeholder="请输入机构全称" allow-clear />
            </a-form-item>
            <a-form-item label="机构编号">
              <a-input v-model:value.trim="where.orgNo" placeholder="请输入机构编号" allow-clear />
            </a-form-item>
            <a-form-item label="机构简称">
              <a-input v-model:value.trim="where.shortName" placeholder="请输入机构简称" allow-clear />
            </a-form-item>
            <a-form-item label="通道机构状态">
              <a-select v-model:value="where.chnAgentStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">正常</a-select-option>
                <a-select-option :value="1">禁用</a-select-option>
                <a-select-option :value="2">睡眠</a-select-option>
                <a-select-option :value="3">注销</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="机构角色">
              <a-select v-model:value="where.orgType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">大区</a-select-option>
                <a-select-option :value="2">运营中心</a-select-option>
                <a-select-option :value="3">代理商</a-select-option>
                <a-select-option :value="5">子级代理商</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="报备状态(对公账户)">
              <a-select v-model:value="where.toGReportStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">待报备</a-select-option>
                <a-select-option :value="1">报备中</a-select-option>
                <a-select-option :value="2">报备完成</a-select-option>
                <a-select-option :value="3">报备失败</a-select-option>
                <a-select-option :value="4">重新报备</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="报备状态(对私账户)">
              <a-select v-model:value="where.toSReportStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">待报备</a-select-option>
                <a-select-option :value="1">报备中</a-select-option>
                <a-select-option :value="2">报备完成</a-select-option>
                <a-select-option :value="3">报备失败</a-select-option>
                <a-select-option :value="4">重新报备</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="报备类型">
              <a-select v-model:value="where.reportType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">报备</a-select-option>
                <a-select-option :value="1">修改</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="外包服务签约状态">
              <a-select v-model:value="where.serviceSignStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">待签约</a-select-option>
                <a-select-option :value="1">签约中</a-select-option>
                <a-select-option :value="2">签约完成</a-select-option>
                <a-select-option :value="3">签约失败</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="终端采购签约状态">
              <a-select v-model:value="where.deviceSignStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">待签约</a-select-option>
                <a-select-option :value="1">签约中</a-select-option>
                <a-select-option :value="2">签约完成</a-select-option>
                <a-select-option :value="3">签约失败</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'reportType'">
              <a-tag v-if="record.reportType === 0" color="purple">报备</a-tag>
              <a-tag v-else-if="record.reportType === 1" color="blue">修改</a-tag>
            </template>
            <template v-else-if="column.key === 'chnAgentStatus'">
              <a-tag v-if="record.chnAgentStatus === '0'" color="success">正常</a-tag>
              <a-tag v-else-if="record.chnAgentStatus === '1'" color="red">禁用</a-tag>
              <a-tag v-else-if="record.chnAgentStatus === '2'" color="pink">睡眠</a-tag>
              <a-tag v-else-if="record.chnAgentStatus === '3'">注销</a-tag>
            </template>
            <template v-else-if="column.key === 'serviceSignStatus'">
              <a-tag v-if="record.serviceSignStatus === 0" color="blue">待签约</a-tag>
              <a-tag v-else-if="record.serviceSignStatus === 1" color="orange">签约中</a-tag>
              <a-tag v-else-if="record.serviceSignStatus === 2" color="success">签约完成</a-tag>
              <a-tag v-else-if="record.serviceSignStatus === 3" color="red">签约失败</a-tag>
            </template>
            <template v-else-if="column.key === 'deviceSignStatus'">
              <a-tag v-if="record.deviceSignStatus === 0" color="blue">待签约</a-tag>
              <a-tag v-else-if="record.deviceSignStatus === 1" color="orange">签约中</a-tag>
              <a-tag v-else-if="record.deviceSignStatus === 2" color="success">签约完成</a-tag>
              <a-tag v-else-if="record.deviceSignStatus === 3" color="red">签约失败</a-tag>
            </template>
            <template v-else-if="column.key === 'toGReportStatus'">
              <a-tag v-if="record.toGReportStatus === 0" color="blue">待报备</a-tag>
              <a-tag v-else-if="record.toGReportStatus === 1" color="orange">报备中</a-tag>
              <a-tag v-else-if="record.toGReportStatus === 2" color="success">报备完成</a-tag>
              <a-tag v-else-if="record.toGReportStatus === 3" color="red">报备失败</a-tag>
              <a-tag v-else-if="record.toGReportStatus === 4" color="cyan">重新报备</a-tag>
            </template>
            <template v-else-if="column.key === 'toSReportStatus'">
              <a-tag v-if="record.toSReportStatus === 0" color="blue">待报备</a-tag>
              <a-tag v-else-if="record.toSReportStatus === 1" color="orange">报备中</a-tag>
              <a-tag v-else-if="record.toSReportStatus === 2" color="success">报备完成</a-tag>
              <a-tag v-else-if="record.toSReportStatus === 3" color="red">报备失败</a-tag>
              <a-tag v-else-if="record.toSReportStatus === 4" color="cyan">重新报备</a-tag>
            </template>
            <template v-else-if="column.key === 'orgType'">
              <a-badge v-if="record.orgType === 1" color="pink" text="大区" />
              <a-badge v-else-if="record.orgType === 2" color="blue" text="运营中心" />
              <a-badge v-else-if="record.orgType === 3" color="orange" text="代理商" />
              <a-badge v-else-if="record.orgType === 5" color="orange" text="子级代理商" />
            </template>
            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <OrgReportRecordDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { OrgReportRecordApi } from '@/api/businessTeam/org-report-record/OrgReportRecordApi';
import OrgReportRecordDetail from './OrgReportRecordDetail.vue';
import { message } from 'ant-design-vue';

export default {
  name: 'OrgReportRecord',
  components: {
    OrgReportRecordDetail
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '机构全称',
          dataIndex: 'fullName'
        },
        {
          title: '机构编号',
          dataIndex: 'orgNo'
        },
        {
          title: '机构简称',
          dataIndex: 'shortName'
        },
        {
          title: '机构角色',
          dataIndex: 'orgType',
          key: 'orgType',
          align: 'center'
        },
        {
          title: '机构层级',
          dataIndex: 'orgLevel',
          align: 'center'
        },
        {
          title: '报备类型',
          dataIndex: 'reportType',
          key: 'reportType',
          align: 'center'
        },
        {
          title: '对公账户通道机构号',
          dataIndex: 'toGChnOrgNo'
        },
        {
          title: '报备状态（对公账户）',
          dataIndex: 'toGReportStatus',
          key: 'toGReportStatus',
          align: 'center'
        },
        {
          title: '报备描述（对公账户）',
          dataIndex: 'toGReportMessage'
        },
        {
          title: '对私账户通道机构号',
          dataIndex: 'toSChnOrgNo'
        },
        {
          title: '报备状态（对私账户）',
          dataIndex: 'toSReportStatus',
          key: 'toSReportStatus',
          align: 'center'
        },
        {
          title: '报备描述（对私账户）',
          dataIndex: 'toSReportMessage'
        },
        {
          title: '外包服务协议签约状态',
          dataIndex: 'serviceSignStatus',
          key: 'serviceSignStatus',
          align: 'center'
        },
        {
          title: '外包服务协议签约描述',
          dataIndex: 'serviceSignMessage'
        },
        {
          title: '终端采购协议签约状态',
          dataIndex: 'deviceSignStatus',
          key: 'deviceSignStatus',
          align: 'center'
        },
        {
          title: '终端采购协议签约描述',
          dataIndex: 'deviceSignMessage'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '通道机构状态',
          dataIndex: 'chnAgentStatus',
          key: 'chnAgentStatus',
          align: 'center'
        },
        {
          title: '备注',
          dataIndex: 'remark'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          align: 'center'
        }
      ],
      // 表格搜索条件
      where: {},
      current: null,
      showDetail: false
    };
  },
  methods: {
    async handleChangeReportStatus(id) {
      const result = await OrgReportRecordApi.changeReportStatus({ id });
      message.success(result.message);
      this.reload();
    },

    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where, orders }) {
      return OrgReportRecordApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
