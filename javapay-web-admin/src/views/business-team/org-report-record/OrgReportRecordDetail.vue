<template>
  <a-modal
    :width="1000"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="机构全称">{{ form.fullName }}</a-descriptions-item>
      <a-descriptions-item label="机构编号">{{ form.orgNo }}</a-descriptions-item>
      <a-descriptions-item label="机构简称">{{ form.shortName }}</a-descriptions-item>
      <a-descriptions-item label="机构角色">
        <a-badge v-if="form.orgType === 1" color="pink" text="大区" />
        <a-badge v-else-if="form.orgType === 2" color="blue" text="运营中心" />
        <a-badge v-else-if="form.orgType === 3" color="orange" text="代理商" />
        <a-badge v-else-if="form.orgType === 5" color="orange" text="子级代理商" />
      </a-descriptions-item>
      <a-descriptions-item label="机构层级">{{ form.orgLevel }}</a-descriptions-item>
      <a-descriptions-item label="报备类型">
        <a-tag v-if="form.reportType === 0" color="purple">报备</a-tag>
        <a-tag v-else-if="form.reportType === 1" color="blue">修改</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="对公账户通道机构号">{{ form.toGChnOrgNo }}</a-descriptions-item>
      <a-descriptions-item label="对私账户通道机构号">{{ form.toSChnOrgNo }}</a-descriptions-item>
      <a-descriptions-item label="报备状态(对公账户)">
        <a-tag v-if="form.toGReportStatus === 0" color="blue">待报备</a-tag>
        <a-tag v-else-if="form.toGReportStatus === 1" color="orange">报备中</a-tag>
        <a-tag v-else-if="form.toGReportStatus === 2" color="success">报备完成</a-tag>
        <a-tag v-else-if="form.toGReportStatus === 3" color="red">报备失败</a-tag>
        <a-tag v-else-if="form.toGReportStatus === 4" color="cyan">重新报备</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="报备描述(对公账户)">{{ form.toGReportMessage }}</a-descriptions-item>
      <a-descriptions-item label="报备状态(对私账户)">
        <a-tag v-if="form.toSReportStatus === 0" color="blue">待报备</a-tag>
        <a-tag v-else-if="form.toSReportStatus === 1" color="orange">报备中</a-tag>
        <a-tag v-else-if="form.toSReportStatus === 2" color="success">报备完成</a-tag>
        <a-tag v-else-if="form.toSReportStatus === 3" color="red">报备失败</a-tag>
        <a-tag v-else-if="form.toSReportStatus === 4" color="cyan">重新报备</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="报备描述(对私账户)">{{ form.toSReportMessage }}</a-descriptions-item>
      <a-descriptions-item label="外包服务协议签约状态">
        <a-tag v-if="form.serviceSignStatus === 0" color="blue">待签约</a-tag>
        <a-tag v-else-if="form.serviceSignStatus === 1" color="orange">签约中</a-tag>
        <a-tag v-else-if="form.serviceSignStatus === 2" color="success">签约完成</a-tag>
        <a-tag v-else-if="form.serviceSignStatus === 3" color="red">签约失败</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="外包服务协议签约描述">{{ form.serviceSignMessage }}</a-descriptions-item>
      <a-descriptions-item label="外包服务协议签约地址">{{ form.serviceProtocolSignUrl }}</a-descriptions-item>
      <a-descriptions-item label="外包服务协议签约完成查看地址">{{ form.serviceProtocolUrl }}</a-descriptions-item>
      <a-descriptions-item label="终端采购协议签约状态">
        <a-tag v-if="form.deviceSignStatus === 0" color="blue">待签约</a-tag>
        <a-tag v-else-if="form.deviceSignStatus === 1" color="orange">签约中</a-tag>
        <a-tag v-else-if="form.deviceSignStatus === 2" color="success">签约完成</a-tag>
        <a-tag v-else-if="form.deviceSignStatus === 3" color="red">签约失败</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="终端采购协议签约描述">{{ form.deviceSignMessage }}</a-descriptions-item>
      <a-descriptions-item label="终端采购协议签约地址">{{ form.deviceProtocolSignUrl }}</a-descriptions-item>
      <a-descriptions-item label="终端采购协议签约完成查看地址">{{ form.deviceProtocolUrl }}</a-descriptions-item>
      
      <a-descriptions-item label="通道机构状态">
        <a-tag v-if="form.chnAgentStatus === '0'" color="success">正常</a-tag>
        <a-tag v-else-if="form.chnAgentStatus === '1'" color="red">禁用</a-tag>
        <a-tag v-else-if="form.chnAgentStatus === '2'" color="pink">睡眠</a-tag>
        <a-tag v-else-if="form.chnAgentStatus === '3'">注销</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="备注">{{ form.remark }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
