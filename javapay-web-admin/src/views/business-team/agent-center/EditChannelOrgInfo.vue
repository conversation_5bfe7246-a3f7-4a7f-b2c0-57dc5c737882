<template>
  <a-modal
    :width="500"
    :visible="visible"
    :confirm-loading="loading"
    title="关联通道机构信息"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules" layout="vertical">
      <a-form-item label="机构编号">
        <a-input v-model:value="form.orgNo" placeholder="机构编号" disabled />
      </a-form-item>
      <a-form-item label="机构类型">
        <a-select v-model:value="form.orgType" placeholder="请选择" disabled>
          <a-select-option :value="1">大区</a-select-option>
          <a-select-option :value="2">运营中心</a-select-option>
          <a-select-option :value="3">一级代理商</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="通道机构编号" prop="toGChnOrgNo">
        <a-input v-model:value="form.toGChnOrgNo" placeholder="请输入通道机构编号" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { AgentCenterApi } from '@/api/businessTeam/agent-center/AgentCenterApi';

export default {
  props: {
    visible: Boolean,
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {
        orgType:3
      },
      rules: {
        toGChnOrgNo: [{ required: true, message: '请输入通道机构编号' }]
      },
      // 提交状态
      loading: false
    };
  },
  mounted() {
    if (this.data) {
      this.form.orgNo = this.data.agentNo;
    }
    this.getOrgReportRecord()
  },
  methods: {
    async getOrgReportRecord(){
      const data = await AgentCenterApi.getOrgReportRecord(this.form)
      this.form.toGChnOrgNo = data?.toGChnOrgNo
    },
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      AgentCenterApi.editOrgReportRecord(this.form)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示修改成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
