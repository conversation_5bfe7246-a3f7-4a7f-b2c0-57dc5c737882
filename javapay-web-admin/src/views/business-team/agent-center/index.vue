<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="账户标识" v-if="hasPurview(['0'])">
              <a-input v-model:value.trim="where.accountUuidStr" placeholder="请输入账户标识" allow-clear />
            </a-form-item>
            <a-form-item label="代理商编号">
              <a-input v-model:value.trim="where.agentNo" placeholder="请输入代理商编号" allow-clear />
            </a-form-item>
            <a-form-item label="代理商名称">
              <a-input v-model:value.trim="where.agentName" placeholder="请输入代理商名称" allow-clear />
            </a-form-item>
            <a-form-item label="代理商简称">
              <a-input v-model:value.trim="where.agentSname" placeholder="请输入代理商简称" allow-clear />
            </a-form-item>
            <a-form-item label="归属大区编号" v-if="hasPurview(['0'])">
              <a-input v-model:value.trim="where.regionNo" placeholder="请输入归属大区编号" allow-clear />
            </a-form-item>
            <a-form-item label="归属运营中心编号" v-if="hasPurview(['0', '1'])">
              <a-input v-model:value.trim="where.branchNo" placeholder="请输入归属运营中心编号" allow-clear />
            </a-form-item>
            <a-form-item label="一级代理编号" v-if="hasPurview(['0', '1', '2'])">
              <a-input v-model:value.trim="where.oneLevelAgentNo" placeholder="一级代理编号" allow-clear />
            </a-form-item>
            <a-form-item label="团队代理编号" v-if="hasPurview(['3', '5'])">
              <a-input v-model:value.trim="where.teamAgentNo" placeholder="支持查团队下所有代理" allow-clear />
            </a-form-item>
            <a-form-item label="直属机构编号">
              <a-input v-model:value.trim="where.parentAgentNo" placeholder="直属机构编号" allow-clear />
            </a-form-item>
            <a-form-item label="联系人姓名">
              <a-input v-model:value.trim="where.contactsName" placeholder="请输入联系人姓名" allow-clear />
            </a-form-item>
            <a-form-item label="联系人号码">
              <a-input v-model:value.trim="where.contactsTel" placeholder="请输入联系人手机号码" allow-clear />
            </a-form-item>
            <!-- <a-form-item label="费率政策" v-if="!hasPurview(['1', '0'])">
              <a-select v-model:value="where.policyNo" placeholder="请选择" style="width: 200px" allow-clear>
                <a-select-option v-for="(item, key) in ratePolicyList" :value="item.policyNo" :key="key">
                  {{ item.policyDesc }}
                </a-select-option>
              </a-select>
            </a-form-item> -->
            <a-form-item label="清算类型">
              <a-select v-model:value="where.remitType" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">平台清算</a-select-option>
                <a-select-option :value="2">自行提现</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="结算方式">
              <a-select v-model:value="where.settleMode" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">日结</a-select-option>
                <a-select-option :value="2">月结</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="分润出款方式">
              <a-select
                v-model:value="where.settleChannelWay"
                style="width: 200px"
                placeholder="请选择"
                allow-clear
                @change="where.settleChannelCode = null"
              >
                <!-- <a-select-option :value="3">展业平台</a-select-option> -->
                <a-select-option :value="1">平台出款</a-select-option>
                <!-- <a-select-option :value="0">交易通道</a-select-option> -->
              </a-select>
            </a-form-item>
            <a-form-item label="分润出款通道" v-if="[1, 2].includes(where.settleChannelWay)">
              <a-select
                v-model:value="where.settleChannelCode"
                style="width: 200px"
                placeholder="请选择"
                :options="subChannelNos"
                :fieldNames="{ label: 'channelName', value: 'channelNo' }"
                allow-clear
              />
            </a-form-item>
            <a-form-item label="分润出款通道" v-if="[0].includes(where.settleChannelWay)">
              <a-select v-model:value="where.settleChannelCode" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
                  {{ channelName }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="审核状态">
              <a-select v-model:value="where.checkStatus" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="3">正常</a-select-option>
                <a-select-option :value="4">审核中</a-select-option>
                <a-select-option :value="5">驳回待编辑</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="机构类型">
              <a-select v-model:value="where.orgType" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">个人</a-select-option>
                <a-select-option :value="1">企业</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table
          ref="table"
          row-key="id"
          :datasource="datasource"
          :columns="columns"
          :where="where"
          :scroll="{ x: 'max-content' }"
          v-model:selection="selection"
        >
          <!-- table上边的工具栏 -->
          <template #toolbar>
            <a-space wrap>
              <a-button type="primary" @click="handleAdd()" v-if="hasPurview(['2', '3', '5'])">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>

              <a-button v-if="hasPurview('0')" @click="handleEditTaxPoint">
                <template #icon>
                  <edit-outlined />
                </template>
                <span>修改税点</span>
              </a-button>

              <a-button v-if="hasPurview('0')" @click="handleEditStatus">
                <template #icon>
                  <edit-outlined />
                </template>
                <span>修改状态</span>
              </a-button>

              <a-dropdown>
                <template #overlay>
                  <a-menu @click="handleEditSettleRemit">
                    <a-menu-item key="1">单个修改</a-menu-item>
                    <a-menu-item key="2">批量修改</a-menu-item>
                  </a-menu>
                </template>
                <a-button v-purview="'0'">
                  <template #icon>
                    <edit-outlined />
                  </template>
                  <span>修改结算周期&方式</span>
                  <DownOutlined />
                </a-button>
              </a-dropdown>

              <a-button v-if="hasPurview('0')" @click="handleEditAppBrandType">
                <template #icon>
                  <edit-outlined />
                </template>
                <span>修改APP品牌类型</span>
              </a-button>

              <a-button v-if="hasPurview('0')" @click="handleEditChannelOrgInfo">
                <template #icon>
                  <edit-outlined />
                </template>
                <span>关联通道机构信息</span>
              </a-button>
            </a-space>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'remitType'">
              <a-tag v-if="record.remitType === 1" color="cyan">平台清算</a-tag>
              <a-tag v-else-if="record.remitType === 2" color="pink">自行提现</a-tag>
              <span v-else>--</span>
            </template>

            <template v-else-if="column.key === 'settleMode'">
              <a-tag v-if="record.settleMode === 1" color="cyan">日结</a-tag>
              <a-tag v-else-if="record.settleMode === 2" color="blue">月结</a-tag>
              <span v-else>--</span>
            </template>

            <template v-else-if="column.key === 'settleChannelWay'">
              <a-tag v-if="record.settleChannelWay === 3" color="orange">展业平台</a-tag>
              <a-tag v-else-if="record.settleChannelWay === 1" color="blue">平台出款</a-tag>
              <a-tag v-else-if="record.settleChannelWay === 0" color="purple">交易通道</a-tag>
            </template>

            <template v-else-if="column.key === 'subAgentSettleChannelWay'">
              <a-tag v-if="record.subAgentSettleChannelWay === 3" color="orange">展业平台</a-tag>
              <a-tag v-else-if="record.subAgentSettleChannelWay === 1" color="blue">平台出款</a-tag>
              <a-tag v-else-if="record.subAgentSettleChannelWay === 0" color="purple">交易通道</a-tag>
            </template>

            <template v-else-if="column.key === 'orgType'">
              <a-tag v-if="record.orgType === 1" color="orange">企业</a-tag>
              <a-tag v-else-if="record.orgType === 0" color="blue">个人</a-tag>
            </template>

            <template v-else-if="column.key === 'agentStatus'">
              <a-tag v-if="record.agentStatus?.checkStatus === 3" color="success">
                <template #icon> <check-circle-outlined /> </template>正常</a-tag
              >
              <a-tag v-else-if="record.agentStatus?.checkStatus === 4" color="processing">
                <template #icon> <sync-outlined :spin="true" /> </template> 审核中</a-tag
              >
              <a-tag v-else-if="record.agentStatus?.checkStatus === 5" color="error">
                <template #icon> <close-circle-outlined /> </template> 驳回待编辑</a-tag
              >
            </template>

            <!-- table操作按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record.id, record)">详情 </a>
                <template
                  v-if="
                    record.agentStatus?.checkStatus === 3 &&
                      (record.parentAgentNo === loginUser.orgCode || ['0', '1', '2'].includes(loginUser.userType))
                  "
                >
                  <a-divider type="vertical" />
                  <a @click="handleEdit(record.id)"> 基本信息修改 </a>
                </template>
                <template v-if="[4, 5].includes(record.agentStatus?.checkStatus) && record.parentAgentNo === loginUser.orgCode">
                  <a-divider type="vertical" />
                  <a @click="handleRejectEdit(record.id)">驳回编辑</a>
                </template>
                <template v-if="hasPurview('0')">
                  <a-divider type="vertical" />
                  <a @click="handleParamsConfig(record)">参数配置</a>
                </template>
                <template v-if="record.agentStatus?.checkStatus === 3 && record.parentAgentNo === loginUser.orgCode">
                  <a-divider type="vertical" />
                  <a @click="handleEditActivitPolicy(record)">修改活动政策 </a>
                </template>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <ActivityCashBackPolicyEdit
      v-if="showEditActivitPolicy"
      v-model:visible="showEditActivitPolicy"
      :channelCodes="channelCodes"
      :data="current"
      @done="reload"
    />

    <SettleRemitEdit v-model:visible="showEditSettleRemit" :data="current" @done="reload" />

    <AddAgentCenter
      v-if="showEdit"
      v-model:visible="showEdit"
      :data="current"
      :opt-type="operationType"
      @done="reload"
      :ratePolicyList="ratePolicyList"
      :channelCodes="channelCodes"
      :activityCashbackTemplateList="activityCashbackTemplateList"
    />

    <ParamsConfig v-if="showParamsConfig" v-model:visible="showParamsConfig" :data="current" />

    <EditTaxPoint v-if="showEditTaxPoint" v-model:visible="showEditTaxPoint" :data="taxPointInfo" @done="reload" />

    <EditAppBrandType
      v-if="showEditAppBrandType"
      v-model:visible="showEditAppBrandType"
      :data="appBrandInfo"
      @done="reload"
      :app-brands="appBrands"
    />

    <AgentStatusEdit v-if="showEditStatus" v-model:visible="showEditStatus" :data="statusRow" @done="reload" />

    <EditChannelOrgInfo v-if="showEditChannelOrgInfo" v-model:visible="showEditChannelOrgInfo" :data="current" @done="reload" />
  </div>
</template>

<script>
import { AgentCenterApi } from '@/api/businessTeam/agent-center/AgentCenterApi';
import ActivityCashBackPolicyEdit from './ActivityCashBackPolicyEdit.vue';
import ParamsConfig from './ParamsConfig.vue';
import SettleRemitEdit from './SettleRemitEdit.vue';
import AgentStatusEdit from './AgentStatusEdit.vue';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import { RatePolicyApi } from '@/api/businessTeam/rate-policy/RatePolicyApi';
import { useUserStore } from '@/store/modules/user';
import { hasPurview } from '@/utils/permission';
import { message } from 'ant-design-vue';
import AddAgentCenter from './AddAgentCenter.vue';
import { ActivityCashbackTemplateManageApi } from '@/api/businessTeam/activity-Config/ActivityCashbackTemplateManageApi';
import EditTaxPoint from '../_components/EditTaxPoint.vue';
import EditAppBrandType from '../_components/EditAppBrandType.vue';
import { AppBrandApi } from '@/api/base/AppBrandApi';
import EditChannelOrgInfo from './EditChannelOrgInfo.vue';
import { RemitChannelApi } from '@/api/account/remit-channel/RemitChannelApi';

export default {
  name: 'AgentCenter',
  components: {
    ActivityCashBackPolicyEdit,
    SettleRemitEdit,
    ParamsConfig,
    AddAgentCenter,
    EditTaxPoint,
    EditAppBrandType,
    AgentStatusEdit,
    EditChannelOrgInfo
  },
  data() {
    return {
      showAddAgent: true,
      where: {},
      operationType: null,
      showEdit: false,
      showEditSettleRemit: false,
      showEditActivitPolicy: false,
      showParamsConfig: false,
      current: null,
      channelCodes: [],
      ratePolicyList: [],
      activityCashbackTemplateList: [],
      selection: [],
      showEditTaxPoint: false,
      taxPointInfo: null,
      appBrandInfo: null,
      appBrands: [],
      showEditAppBrandType: false,
      statusRow: null,
      showEditStatus: false,
      showEditChannelOrgInfo: false,
      subChannelNos: [],
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left',
          hideCol: hasPurview(['1', '2'])
        },
        {
          title: '代理商编号',
          dataIndex: 'agentNo',
          align: 'center'
        },
        {
          title: '代理商名称',
          dataIndex: 'agentName'
        },
        {
          title: '代理商简称',
          dataIndex: 'agentSname'
        },
        {
          title: '机构类型',
          dataIndex: 'orgType',
          key: 'orgType'
        },
        {
          title: '直属机构编号',
          dataIndex: 'parentAgentNo'
        },
        {
          title: '直属机构名称',
          dataIndex: 'parentAgentName'
        },
        {
          title: '所属一代编号',
          dataIndex: 'oneLevelAgentNo',
          hideCol: !hasPurview(['0', '1', '2'])
        },
        {
          title: '所属一代名称',
          dataIndex: 'oneLevelAgentName',
          hideCol: !hasPurview(['0', '1', '2'])
        },
        {
          title: '归属大区编号',
          dataIndex: 'regionNo',
          align: 'center',
          hideCol: !hasPurview(['0'])
        },
        {
          title: '归属大区名称',
          dataIndex: 'regionName',
          align: 'center',
          hideCol: !hasPurview(['0'])
        },
        {
          title: '归属运营中心编号',
          dataIndex: 'branchNo',
          align: 'center',
          hideCol: hasPurview(['2', '3', '5'])
        },
        {
          title: '归属运营中心名称',
          dataIndex: 'branchName',
          align: 'center',
          hideCol: hasPurview(['2', '3', '5'])
        },
        {
          title: '费率政策',
          dataIndex: 'policyDesc',
          hideCol: hasPurview('1')
        },
        {
          title: '法人姓名',
          dataIndex: 'legalName',
          align: 'center'
        },
        {
          title: '法人电话号码',
          dataIndex: 'legalTelMask',
          align: 'center'
        },
        {
          title: '联系人姓名',
          dataIndex: 'contactsName',
          align: 'center'
        },
        {
          title: '联系人电话号码',
          dataIndex: 'contactsTelMask',
          align: 'center'
        },
        {
          title: '清算类型',
          dataIndex: 'remitType',
          key: 'remitType',
          align: 'center',
          width: 100
        },
        {
          title: '结算方式',
          dataIndex: 'settleMode',
          key: 'settleMode',
          align: 'center',
          width: 100
        },
        {
          title: '分润出款方式',
          dataIndex: 'settleChannelWay',
          key: 'settleChannelWay',
          width: 180,
          align: 'center'
        },
        {
          title: '分润出款通道',
          dataIndex: 'settleChannelCode',
          width: 160,
          align: 'center',
          customRender: ({ text, record }) => {
            let item;
            if ([0].includes(record.settleChannelWay)) {
              item = this.channelCodes.find(item => item.channelCode === text);
            }

            if ([1, 2].includes(record.settleChannelWay)) {
              item = this.subChannelNos.find(item => item.channelNo === text);
            }

            record.subChannelName = item?.channelName || '--';

            return record.subChannelName;
          }
        },
        {
          title: '子代分润出款方式',
          dataIndex: 'subAgentSettleChannelWay',
          key: 'subAgentSettleChannelWay',
          width: 180,
          align: 'center'
        },
        {
          title: '子代分润出款通道',
          dataIndex: 'subAgentSettleChannelCode',
          width: 160,
          align: 'center',
          customRender: ({ text, record }) => {
            if ([0].includes(record.subAgentSettleChannelWay)) {
              const item = this.channelCodes.find(item => item.channelCode === text);
              return item?.channelName;
            }

            if ([1, 2].includes(record.subAgentSettleChannelWay)) {
              const item = this.subChannelNos.find(item => item.channelNo === text);
              return item?.channelName;
            }

            return '--';
          }
        },
        {
          title: '审核状态',
          dataIndex: 'agentStatus',
          key: 'agentStatus',
          align: 'center',
          width: 100
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 280,
          align: 'center'
        }
      ].filter(i => !i.hideCol)
    };
  },
  computed: {
    // 当前登录用户信息
    loginUser() {
      const userStore = useUserStore();
      return userStore.$state.info;
    }
  },
  mounted() {
    this.getChannelCodes();
    this.getRatePolicyList();
    this.getActivityCashbackTemplateList();
    if (hasPurview('0')) {
      this.geApptBrands();
    }
    this.getSubChannelNos();
  },
  methods: {
    async getSubChannelNos() {
      const data = await RemitChannelApi.findAll({ validStatus: 1, remitChannelClassify: 2 });
      this.subChannelNos = data || [];
    },

    async handleEditStatus() {
      if (this.selection.length !== 1) return message.warn('请选择一条数据');

      const [{ id }] = this.selection;
      const data = await AgentCenterApi.statusDetail({ id });
      this.statusRow = Object.assign({}, data);
      this.showEditStatus = true;
    },

    async geApptBrands() {
      const data = await AppBrandApi.list();
      this.appBrands = data || [];
    },
    async handleEditAppBrandType() {
      if (this.selection.length !== 1) return message.warn('请选择一条数据');
      const [{ id, agentNo, appBrandType, orgType, agentStatus }] = this.selection;
      if (orgType !== 1) {
        message.warn('请选择一级代理商(企业)');
        return;
      }
      if (agentStatus?.checkStatus !== 3) {
        message.warn('请选择审核状态为“正常”的代理商!');
        return;
      }
      this.appBrandInfo = {
        id,
        userType: '3',
        userNo: agentNo,
        appBrandType
      };
      this.showEditAppBrandType = true;
    },
    async getActivityCashbackTemplateList() {
      let data = await ActivityCashbackTemplateManageApi.querySelfList();
      this.activityCashbackTemplateList = data || [];
    },

    async handleParamsConfig(row) {
      const data = await AgentCenterApi.detailParamConf({ id: row.id });
      this.current = data || {
        id: row.id,
        agentNo: row.agentNo
      };
      this.showParamsConfig = true;
    },

    async handleAdd() {
      this.operationType = 0;
      this.showEdit = true;
    },

    async handleEdit(id) {
      const data = await AgentCenterApi.detail({ id, isEditOpt: 1 });
      this.current = data;
      this.operationType = 1;
      this.showEdit = true;
    },

    async handleEditActivitPolicy(record) {
      const params = {
        orgNo: record.agentNo // 机构编号 （选中的记录）
      };
      const data = await AgentCenterApi.detailCashbackPolicy(params);
      this.current = Object.assign(params, data);
      this.showEditActivitPolicy = true;
    },

    async handleDetail(id, row) {
      const data = await AgentCenterApi.detail({ id, isEditOpt: 0 });
      data.subChannelName = row.subChannelName;
      data.subChannelName2 = row.subChannelName2;
      this.current = data;
      this.operationType = 2;
      this.showEdit = true;
    },

    async handleRejectEdit(id) {
      const data = await AgentCenterApi.detailReject({ id, isEditOpt: 1 });
      this.current = data;
      this.operationType = 3;
      this.showEdit = true;
    },

    handleEditChannelOrgInfo() {
      if (this.selection.length !== 1) return message.warn('请选择一条数据');
      const [{ orgType, agentStatus }] = this.selection;
      if (orgType !== 1) {
        message.warn('请选择一级代理商(企业)');
        return;
      }
      if (agentStatus?.checkStatus !== 3) {
        message.warn('请选择审核状态为“正常”的代理商!');
        return;
      }
      this.current = this.selection[0];
      this.showEditChannelOrgInfo = true;
    },
    async handleEditTaxPoint() {
      if (this.selection.length !== 1) return message.warn('请选择一条数据');
      const [{ id, taxPoint, agentNo, orgType, agentStatus }] = this.selection;
      if (orgType !== 1) {
        message.warn('请选择一级代理商(企业)');
        return;
      }
      if (agentStatus?.checkStatus !== 3) {
        message.warn('请选择审核状态为“正常”的代理商!');
        return;
      }
      this.taxPointInfo = {
        id,
        userType: '3',
        userNo: agentNo,
        taxPoint
      };
      this.showEditTaxPoint = true;
    },

    handleEditSettleRemit({ key }) {
      if (key === '1') {
        if (this.selection.length !== 1) return message.warn('请选择一条数据');
        this.current = this.selection[0];
      } else {
        this.current = null;
      }
      this.showEditSettleRemit = true;
    },

    reload() {
      this.selection = [];
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.selection = [];
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    //获取数据
    datasource({ page, limit, where }) {
      return AgentCenterApi.findPage({ ...where, pageNo: page, pageSize: limit });
    },

    async getChannelCodes() {
      const data = await ChannelManageApi.list({ validStatus: 1 });
      this.channelCodes = data;
    },

    async getRatePolicyList() {
      const policyUserType = ['0', '1', '2'].includes(this.loginUser.userType) ? 2 : 3;
      const ratePolicyList = await RatePolicyApi.list({ userType: policyUserType, policyType: 1 });
      this.ratePolicyList = ratePolicyList || [];
    },
    hasPurview
  }
};
</script>
