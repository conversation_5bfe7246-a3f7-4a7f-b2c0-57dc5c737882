<template>
  <div class="ele-body">
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="机构编号" v-if="hasPurview(['0', '1', '2'])">
              <a-input v-model:value.trim="where.orgNo" placeholder="机构编号" allow-clear />
            </a-form-item>
            <a-form-item label="规则类型">
              <a-select v-model:value="where.ruleType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">未激活</a-select-option>
                <a-select-option :value="2">伪激活</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- table上边工具栏 -->
          <template #toolbar>
            <a-space>
              <a-button v-if="hasPurview(['1'])" type="primary" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>添加</span>
              </a-button>
            </a-space>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'ruleType'">
              <a-tag v-if="record.ruleType === 1" color="orange">未激活</a-tag>
              <a-tag v-else-if="record.ruleType === 2" color="blue">伪激活</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
                <template v-if="hasPurview(['1', '3'])">
                  <a-divider type="vertical" />
                  <a @click="handleEdit(record)">修改</a>
                </template>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <ActiveRuleConfigDetail v-model:visible="showDetail" :detail="current" />

    <!-- 编辑 -->
    <ActiveRuleConfigEdit v-if="showEdit" v-model:visible="showEdit" :data="current" @done="reload" />
  </div>
</template>

<script>
import { ActiveRuleConfigApi } from '@/api/businessTeam/active-rule-config/ActiveRuleConfigApi';
import ActiveRuleConfigDetail from './modules/active-rule-config-detail.vue';
import ActiveRuleConfigEdit from './modules/active-rule-config-edit.vue';
import { hasPurview } from '@/utils/permission';

export default {
  name: 'ActiveRuleConfig',
  components: {
    ActiveRuleConfigDetail,
    ActiveRuleConfigEdit
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          align: 'center',
          width: 80,
          fixed: 'left'
        },
        {
          title: '机构编号',
          dataIndex: 'orgNo',
          align: 'center',
          hideCol: !hasPurview(['0', '1', '2'])
        },
        {
          title: '规则类型',
          dataIndex: 'ruleType',
          key: 'ruleType',
          align: 'center'
        },
        {
          title: '时间周期(天)',
          dataIndex: 'timeCycle',
          align: 'center'
        },
        {
          title: '扣款金额(元)',
          dataIndex: 'deductAmount',
          align: 'center'
        },
        {
          title: '交易标准金额(元)',
          dataIndex: 'tradeVolume',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 120,
          align: 'center'
        }
      ].filter(i => !i.hideCol),
      // 表格搜索条件
      where: {
        orgNo: hasPurview('3') ? localStorage.getItem('SASS_ORG_CODE') : ''
      },
      // 当前编辑数据
      current: null,
      // 是否显示编辑弹窗
      showDetail: false,
      showEdit: false
    };
  },
  methods: {
    hasPurview,

    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {
        orgNo: hasPurview('3') ? localStorage.getItem('SASS_ORG_CODE') : ''
      };
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where, orders }) {
      return ActiveRuleConfigApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
