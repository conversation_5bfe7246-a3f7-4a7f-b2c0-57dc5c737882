<template>
  <a-modal :width="800" :visible="visible" title="详情" :body-style="{ paddingBottom: '8px' }" @update:visible="updateVisible">
    <a-descriptions :column="2">
      <a-descriptions-item :span="2" v-if="hasPurview(['0', '1', '2'])" label="机构编号">{{ form.orgNo }}</a-descriptions-item>
      <a-descriptions-item label="规则类型">
        <a-tag v-if="form.ruleType === 1" color="orange">未激活</a-tag>
        <a-tag v-else-if="form.ruleType === 2" color="blue">伪激活</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="时间周期(天)">{{ form.timeCycle }}</a-descriptions-item>
      <a-descriptions-item label="扣款金额(元)">{{ form.deductAmount }}</a-descriptions-item>
      <a-descriptions-item label="交易标准金额(元)">{{ form.tradeVolume }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';
import { hasPurview } from '@/utils/permission';

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible,
      hasPurview
    };
  }
};
</script>
