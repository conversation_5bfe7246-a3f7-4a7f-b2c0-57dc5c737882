<template>
  <a-modal
    :width="800"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新增'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <div style="margin-bottom:20px">
      <a-alert
        type="info"
        message="激活规则说明:"
      >
        <template #description>
          未激活条件：①开通激活奖励条件下，若未达到激活标准；②入库后周期内未绑定商户;
          <br />
          伪激活条件：终端激活后，周期内信用卡交易金额未达标;
        </template>
      </a-alert>
    </div>

    <a-form ref="form" :model="form" :rules="rules" :labelCol="{ style: { width: '140px' } }">
      <a-row :gutter="24">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="规则类型" name="ruleType">
            <a-select v-model:value="form.ruleType" style="width: 100%" placeholder="请选择" :disabled="isUpdate">
              <a-select-option :value="1">未激活</a-select-option>
              <a-select-option :value="2">伪激活</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="时间周期(天)" name="timeCycle">
            <a-input-number v-model:value="form.timeCycle" placeholder="请输入时间周期" class="ele-fluid" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="扣款金额(元)" name="deductAmount">
            <a-input-number v-model:value="form.deductAmount" placeholder="请输入扣款金额" class="ele-fluid" />
          </a-form-item>
          <a-form-item label="交易标准金额(元)" name="tradeVolume" v-if="form.ruleType === 2">
            <a-input-number v-model:value="form.tradeVolume" placeholder="请输入交易标准金额" class="ele-fluid" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { ActiveRuleConfigApi } from '@/api/businessTeam/active-rule-config/ActiveRuleConfigApi';

export default {
  props: {
    visible: Boolean,
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {},
      // 表单验证规则
      rules: {
        timeCycle: [{ required: true, message: '请输入时间周期' }],
        deductAmount: [{ required: true, message: '请输入扣款金额' }],
        tradeVolume: [{ required: true, message: '请输入交易标准金额' }],
        ruleType: [{ required: true, message: '请选择' }]
      },
      loading: false,
      isUpdate: false
    };
  },
  mounted() {
    if (this.data) {
      this.isUpdate = true;
      this.form = Object.assign({}, this.data);
    }
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      // 执行编辑或修改方法
      if (this.isUpdate) {
        result = ActiveRuleConfigApi.edit(this.form);
      } else {
        result = ActiveRuleConfigApi.add(this.form);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
<style scoped>
::v-deep(.ant-form-item-label) {
  text-align: center;
  background-color: #fafafa;
}
</style>
