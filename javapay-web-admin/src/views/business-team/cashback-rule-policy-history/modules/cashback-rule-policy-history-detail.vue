<template>
  <a-modal :width="800" :visible="visible" title="详情" :body-style="{ paddingBottom: '8px' }" @update:visible="updateVisible">
    <a-descriptions :column="2">
      <a-descriptions-item label="机构编号">{{ form.orgNo }}</a-descriptions-item>
      <a-descriptions-item label="上级机构编号">{{ form.parentOrgNo }}</a-descriptions-item>
      <a-descriptions-item label="政策名称">{{ form.policyName }}</a-descriptions-item>
      <a-descriptions-item label="政策编号">{{ form.policyNo }}</a-descriptions-item>
      <a-descriptions-item label="返现政策类型">
        <a-tag v-if="form.cashbackType === 0" color="purple">激活返现政策</a-tag>
        <a-tag v-else-if="form.cashbackType === 1" color="blue">达标返现政策</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="达标计算方式">
        <a-tag v-if="form.cashCalculateType === 0" color="purple">交易量优先</a-tag>
        <a-tag v-else-if="form.cashCalculateType === 1" color="blue">时间优先</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="版本号">{{ form.version }}</a-descriptions-item>
      <a-descriptions-item label="规则序号">{{ form.ruleNo }}</a-descriptions-item>
      <a-descriptions-item label="统计周期-开始天数">{{ form.timeCycleStart }}</a-descriptions-item>
      <a-descriptions-item label="统计周期-结束天数">{{ form.timeCycleEnd }}</a-descriptions-item>
      <a-descriptions-item label="交易标准金额(元)">{{ form.tradeVolume }}</a-descriptions-item>
      <a-descriptions-item label="返现金额(元)">{{ form.cashbackAmount }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';
import { hasPurview } from '@/utils/permission';

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible,
      hasPurview
    };
  }
};
</script>
