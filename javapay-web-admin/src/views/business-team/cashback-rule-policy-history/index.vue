<template>
  <div class="ele-body">
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="机构编号">
              <a-input v-model:value.trim="where.orgNo" placeholder="机构编号" allow-clear />
            </a-form-item>
            <a-form-item label="返现政策类型">
              <a-select v-model:value="where.cashbackType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">激活返现政策</a-select-option>
                <a-select-option :value="1">达标返现政策</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'cashbackType'">
              <a-tag v-if="record.cashbackType === 0" color="purple">激活返现政策</a-tag>
              <a-tag v-else-if="record.cashbackType === 1" color="blue">达标返现政策</a-tag>
            </template>

            <template v-else-if="column.key === 'cashCalculateType'">
              <a-tag v-if="record.cashCalculateType === 0" color="purple">交易量优先</a-tag>
              <a-tag v-else-if="record.cashCalculateType === 1" color="blue">时间优先</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <CashbackRulePolicyHistoryDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { CashbackRulePolicyApi } from '@/api/businessTeam/cashback-rule-policy/CashbackRulePolicyApi';
import CashbackRulePolicyHistoryDetail from './modules/cashback-rule-policy-history-detail.vue';
import { hasPurview } from '@/utils/permission';

export default {
  name: 'CashbackRulePolicyHistory',
  components: {
    CashbackRulePolicyHistoryDetail
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          align: 'center',
          width: 80,
          fixed: 'left'
        },
        {
          title: '机构编号',
          dataIndex: 'orgNo',
          align: 'center'
        },
        {
          title: '上级机构编号',
          dataIndex: 'parentOrgNo',
          align: 'center'
        },
        {
          title: '政策名称',
          dataIndex: 'policyName',
          align: 'center'
        },
        {
          title: '返现政策类型',
          dataIndex: 'cashbackType',
          key: 'cashbackType',
          align: 'center'
        },
        {
          title: '达标计算方式',
          dataIndex: 'cashCalculateType',
          key: 'cashCalculateType',
          align: 'center'
        },
        {
          title: '版本号',
          dataIndex: 'version',
          align: 'center'
        },
        {
          title: '规则序号',
          dataIndex: 'ruleNo',
          align: 'center'
        },
        {
          title: '统计周期-开始天数',
          dataIndex: 'timeCycleStart',
          align: 'center'
        },
        {
          title: '统计周期-结束天数',
          dataIndex: 'timeCycleEnd',
          align: 'center'
        },
        {
          title: '交易标准金额(元)',
          dataIndex: 'tradeVolume',
          align: 'center'
        },
        {
          title: '返现金额(元)',
          dataIndex: 'cashbackAmount',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 120,
          align: 'center'
        }
      ],
      // 表格搜索条件
      where: {},
      // 当前编辑数据
      current: null,
      // 是否显示编辑弹窗
      showDetail: false,
      showEdit: false
    };
  },
  methods: {
    hasPurview,

    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where, orders }) {
      return CashbackRulePolicyApi.findHisPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
