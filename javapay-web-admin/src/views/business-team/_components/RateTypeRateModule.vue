<template>
  <!-- 公共费率模块 -->
  <div class="rate-module">
    <div v-if="rateItem.rateType === 1">
      <!--* 扫码交易(微信/支付宝/银联二维码) -->
      <a-divider orientation="left" :orientationMargin="0" dashed>线下收单</a-divider>
      <!-- 按组遍历 -->
      <a-row v-for="(groupList, groupIndex) in Object.values(groupBy(allScanRateFields, 'group_id'))" :key="groupIndex" :gutter="24">
        <template v-for="(groupItem, groupItemIndex) in groupList" :key="groupItemIndex">
          <template v-if="targetRateKeys.includes(groupItem.field)">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item :label="groupItem.name" :name="['rateInfoDTO', groupItem.field]" :rules="makeItemRules(groupItem)">
                <a-input v-model:value="rateItem.rateInfoDTO[groupItem.field]" :placeholder="placeholder" :disabled="disabled" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24" v-if="groupItem.single === 1" />
          </template>
        </template>
      </a-row>
    </div>

    <div v-else-if="rateItem.rateType === 2">
      <!--* POS刷卡交易 -->
      <a-divider orientation="left" :orientationMargin="0" dashed>POS收单交易</a-divider>
      <a-row :gutter="24">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="银联标准贷记卡费率(%)" :name="['rateInfoDTO', 'creditRate']" :rules="rules.rateValidate">
            <a-input v-model:value="rateItem.rateInfoDTO.creditRate" :placeholder="placeholder" :disabled="disabled" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="银联标准借记卡费率(%)" :name="['rateInfoDTO', 'debitRate']" :rules="rules.rateValidate">
            <a-input v-model:value="rateItem.rateInfoDTO.debitRate" :placeholder="placeholder" :disabled="disabled" />
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="银联标准借记卡手续费封顶(元)" :name="['rateInfoDTO', 'debitFeeMax']" :rules="rules.feeMax">
            <a-input v-model:value="rateItem.rateInfoDTO.debitFeeMax" :placeholder="placeholder" :disabled="disabled" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="银联云闪付贷记卡费率(%)" :name="['rateInfoDTO', 'nfcCreditRate']" :rules="rules.rateValidate">
            <a-input v-model:value="rateItem.rateInfoDTO.nfcCreditRate" :placeholder="placeholder" :disabled="disabled" />
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="银联云闪付借记卡费率(%)" :name="['rateInfoDTO', 'nfcDebitRate']" :rules="rules.rateValidate">
            <a-input v-model:value="rateItem.rateInfoDTO.nfcDebitRate" :placeholder="placeholder" :disabled="disabled" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="刷卡贷记卡D0附加费率(%)" :name="['rateInfoDTO', 'creditD0Rate']" :rules="rules.rateValidate">
            <a-input v-model:value="rateItem.rateInfoDTO.creditD0Rate" :placeholder="placeholder" :disabled="disabled" />
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="刷卡贷记卡D0单笔附加费用(元)" :name="['rateInfoDTO', 'creditD0SingleFee']" :rules="rules.amountValidate">
            <a-input v-model:value="rateItem.rateInfoDTO.creditD0SingleFee" :placeholder="placeholder" :disabled="disabled" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="刷卡借记卡D0附加费率(%)" :name="['rateInfoDTO', 'debitD0Rate']" :rules="rules.rateValidate">
            <a-input v-model:value="rateItem.rateInfoDTO.debitD0Rate" :placeholder="placeholder" :disabled="disabled" />
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="刷卡借记卡D0附加单笔费用(元)" :name="['rateInfoDTO', 'debitD0SingleFee']" :rules="rules.amountValidate">
            <a-input v-model:value="rateItem.rateInfoDTO.debitD0SingleFee" :placeholder="placeholder" :disabled="disabled" />
          </a-form-item>
        </a-col>
      </a-row>
    </div>

    <div v-if="rateItem.rateType === 3">
      <!--* 无卡支付 -->
      <a-divider orientation="left" :orientationMargin="0" dashed>无卡交易</a-divider>
      <a-row :gutter="24">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="无卡贷记卡费率(%)" :name="['rateInfoDTO', 'creditEposRate']" :rules="rules.rateValidate">
            <a-input v-model:value="rateItem.rateInfoDTO.creditEposRate" :placeholder="placeholder" :disabled="disabled" />
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="无卡借记卡费率(%)" :name="['rateInfoDTO', 'debitEposRate']" :rules="rules.rateValidate">
            <a-input v-model:value="rateItem.rateInfoDTO.debitEposRate" :placeholder="placeholder" :disabled="disabled" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="无卡贷记卡D0附加费率(%)" :name="['rateInfoDTO', 'creditEposD0Rate']" :rules="rules.rateValidate">
            <a-input v-model:value="rateItem.rateInfoDTO.creditEposD0Rate" :placeholder="placeholder" :disabled="disabled" />
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="无卡贷记卡D0附加单笔费用(元)" :name="['rateInfoDTO', 'creditEposD0SingleFee']" :rules="rules.amountValidate">
            <a-input v-model:value="rateItem.rateInfoDTO.creditEposD0SingleFee" :placeholder="placeholder" :disabled="disabled" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="无卡借记卡D0附加费率(%)" :name="['rateInfoDTO', 'debitEposD0Rate']" :rules="rules.rateValidate">
            <a-input v-model:value="rateItem.rateInfoDTO.debitEposD0Rate" :placeholder="placeholder" :disabled="disabled" />
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="无卡借记卡D0附加单笔费用(元)" :name="['rateInfoDTO', 'debitEposD0SingleFee']" :rules="rules.amountValidate">
            <a-input v-model:value="rateItem.rateInfoDTO.debitEposD0SingleFee" :placeholder="placeholder" :disabled="disabled" />
          </a-form-item>
        </a-col>
      </a-row>
    </div>

    <!--* 提现费率 -->
    <template v-if="rateItem.rateType === 4">
      <a-divider orientation="left" :orientationMargin="0" dashed>机构出款费率{{ rateItem.templateNo }}</a-divider>
      <a-row :gutter="24">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="分润对公提现税率(%)" :name="['rateInfoDTO', 'pfWalletPubWdRate']" :rules="rules.rateValidate">
            <a-input v-model:value="rateItem.rateInfoDTO.pfWalletPubWdRate" :placeholder="placeholder" :disabled="disabled" />
          </a-form-item>
          <a-form-item label="分润对私提现税率(%)" :name="['rateInfoDTO', 'pfWalletPriWdRate']" :rules="rules.rateValidate">
            <a-input v-model:value="rateItem.rateInfoDTO.pfWalletPriWdRate" :placeholder="placeholder" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="分润对公提现单笔费用(元)" :name="['rateInfoDTO', 'pfWalletPubWdSingleFee']" :rules="rules.amountValidate">
            <a-input v-model:value="rateItem.rateInfoDTO.pfWalletPubWdSingleFee" :placeholder="placeholder" :disabled="disabled" />
          </a-form-item>
          <a-form-item label="分润对私提现单笔费用(元)" :name="['rateInfoDTO', 'pfWalletPriWdSingleFee']" :rules="rules.amountValidate">
            <a-input v-model:value="rateItem.rateInfoDTO.pfWalletPriWdSingleFee" :placeholder="placeholder" :disabled="disabled" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="代付对公提现税率(%)" :name="['rateInfoDTO', 'payWalletPubWdRate']" :rules="rules.rateValidate">
            <a-input v-model:value="rateItem.rateInfoDTO.payWalletPubWdRate" :placeholder="placeholder" :disabled="disabled" />
          </a-form-item>
          <a-form-item label="代付对私提现税率(%)" :name="['rateInfoDTO', 'payWalletPriWdRate']" :rules="rules.rateValidate">
            <a-input v-model:value="rateItem.rateInfoDTO.payWalletPriWdRate" :placeholder="placeholder" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="代付对公提现单笔费用(元)" :name="['rateInfoDTO', 'payWalletPubWdSingleFee']" :rules="rules.amountValidate">
            <a-input v-model:value="rateItem.rateInfoDTO.payWalletPubWdSingleFee" :placeholder="placeholder" :disabled="disabled" />
          </a-form-item>
          <a-form-item label="代付对私提现单笔费用(元)" :name="['rateInfoDTO', 'payWalletPriWdSingleFee']" :rules="rules.amountValidate">
            <a-input v-model:value="rateItem.rateInfoDTO.payWalletPriWdSingleFee" :placeholder="placeholder" :disabled="disabled" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="营销活动对公提现税率(%)" :name="['rateInfoDTO', 'rewardWalletPubWdRate']" :rules="rules.rateValidate">
            <a-input v-model:value="rateItem.rateInfoDTO.rewardWalletPubWdRate" :placeholder="placeholder" :disabled="disabled" />
          </a-form-item>
          <a-form-item label="营销活动对私提现税率(%)" :name="['rateInfoDTO', 'rewardWalletPriWdRate']" :rules="rules.rateValidate">
            <a-input v-model:value="rateItem.rateInfoDTO.rewardWalletPriWdRate" :placeholder="placeholder" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item
            label="营销活动对公提现单笔费用(元)"
            :name="['rateInfoDTO', 'rewardWalletPubWdSingleFee']"
            :rules="rules.amountValidate"
          >
            <a-input v-model:value="rateItem.rateInfoDTO.rewardWalletPubWdSingleFee" :placeholder="placeholder" :disabled="disabled" />
          </a-form-item>
          <a-form-item
            label="营销活动对私提现单笔费用(元)"
            :name="['rateInfoDTO', 'rewardWalletPriWdSingleFee']"
            :rules="rules.amountValidate"
          >
            <a-input v-model:value="rateItem.rateInfoDTO.rewardWalletPriWdSingleFee" :placeholder="placeholder" :disabled="disabled" />
          </a-form-item>
        </a-col>
      </a-row>
    </template>
  </div>
</template>
<script>
import { rateReg, amountReg } from '@/utils/validate';
import { groupBy } from 'lodash-es';

// 扫码相关的字段
const allScanRateFieldsMap = [
  { field: 'creditRate', name: '银联标准贷记卡费率(%)', group_id: 1, single: 1 },
  { field: 'debitRate', name: '银联标准借记卡费率(%)', group_id: 1 },
  { field: 'debitFeeMax', name: '银联标准借记卡手续费封顶(元)', group_id: 1 },
  { field: 'nfcCreditRate', name: '银联云闪付贷记卡费率(%)', group_id: 2 },
  { field: 'nfcDebitRate', name: '银联云闪付借记卡费率(%)', group_id: 2 },
  { field: 'aliPayRate', name: '支付宝扫码费率(%)', group_id: 3 },
  { field: 'aliPayLargeRate', name: '支付宝大额费率(%)', group_id: 3 },
  { field: 'wechatRate', name: '微信扫码费率(%)', group_id: 3 },
  { field: 'creditQrD0Rate', name: '贷记卡D0附加费率(%)', group_id: 4 },
  { field: 'creditQrD0SingleFee', name: '贷记卡D0附加单笔(元)', group_id: 4 },
  { field: 'debitQrD0Rate', name: '借记卡D0附加费率(%)', group_id: 4 },
  { field: 'debitQrD0SingleFee', name: '借记卡D0附加单笔(元)', group_id: 4 }
];

export default {
  name: 'RateTypeRateModule',
  props: {
    // 费率信息
    rateItem: Object,
    // 是否禁用 只读时设置为true
    disabled: Boolean,
    formRules: Object
  },
  data() {
    return {
      allScanRateFields: allScanRateFieldsMap,
      targetRateKeys: [],
      // 表单项占位符
      placeholder: !this.disabled ? '请填写' : '--',
      // 校验规则
      rules: this.formRules || {
        rateValidate: [
          { required: true, message: '必填项' },
          { pattern: rateReg, message: '正数且小数点后最多四位', trigger: 'blur' }
        ],
        feeMax: [
          { required: true, message: '必填项' },
          { pattern: amountReg, message: '正数且小数点后最多两位', trigger: 'blur' }
        ],
        amountValidate: [
          { required: true, message: '必填项' },
          { pattern: amountReg, message: '正数且小数点后最多两位', trigger: 'blur' }
        ]
      }
    };
  },
  watch: {
    rateItem: {
      handler(val) {
        if (val?.rateType === 1 && val?.rateInfoDTO) {
          // this.setRateDefValue();
          const rateKeys = Object.keys(val.rateInfoDTO);
          this.targetRateKeys = this.allScanRateFields.filter(item => rateKeys.includes(item.field)).map(item => item.field);
        }
      },
      immediate: true
    }
  },
  methods: {
    groupBy,

    makeItemRules(item) {
      const field = item.field || '';

      if (field.endsWith('Rate')) {
        return this.rules.rateValidate;
      } else if (field.endsWith('FeeMax')) {
        return this.rules.feeMax;
      } else if (field.endsWith('Fee')) {
        return this.rules.amountValidate;
      }
      return [];
    },

    onInputChangeRate(e) {
      const value = e.target.value;

      const rateKeys = ['nfcCreditRate', 'nfcDebitRate', 'wechatRate', 'aliPayRate'];
      rateKeys.forEach(key => {
        this.rateItem.rateInfoDTO[key] = value;
      });
    },

    setRateDefValue() {
      this.onInputChangeRate({ target: { value: this.rateItem.rateInfoDTO.wechatRate } });

      const rateKeys = ['creditQrD0Rate', 'debitQrD0Rate', 'creditQrD0SingleFee', 'debitQrD0SingleFee'];
      rateKeys.forEach(key => {
        this.rateItem.rateInfoDTO[key] = 0;
      });
    }
  }
};
</script>
<style scoped>
::v-deep(.ant-row) {
  align-items: flex-start;
}

::v-deep(.ant-form-item-label) {
  text-align: center;
  background-color: #f1f3f4;
}
</style>
