<template>
  <a-modal
    :width="500"
    :visible="visible"
    :confirm-loading="loading"
    :title="`修改税点(${data.userNo})`"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules">
      <a-form-item label="税点" prop="taxPoint">
        <a-input v-model:value="form.taxPoint" placeholder="请输入税点" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { AgentCenterApi } from '@/api/businessTeam/agent-center/AgentCenterApi';
import { OperationCenterApi } from '@/api/businessTeam/operation-center/OperationCenterApi';
import { RegionManageApi } from '@/api/businessTeam/region-center/RegionManageApi';
import { message } from 'ant-design-vue';

export default {
  props: {
    data: Object,
    visible: Boolean
  },
  emits: ['update:visible', 'done'],
  data() {
    return {
      //表单数据
      form: {},
      //提交状态
      loading: false,
      //表单规则
      rules: {
        taxPoint: [{ required: true, message: '请输入税点' }]
      }
    };
  },
  created() {
    this.form = Object.assign(this.data, {});
  },
  methods: {
    //提交
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      const params = {
        id: this.form.id,
        taxPoint: this.form.taxPoint
      };

      let result = null;
      switch (this.data.userType) {
        case '1':
          result = RegionManageApi.editTaxPoint(params);
          break;
        case '2':
          result = OperationCenterApi.editTaxPoint(params);
          break;
        case '3':
          result = AgentCenterApi.editTaxPoint(params);
          break;
      }
      result
        .then(res => {
          this.loading = false;
          message.success(res.message);
          this.updateVisible(false);
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
