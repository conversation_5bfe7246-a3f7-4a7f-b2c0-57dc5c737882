<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="银行户名">
              <a-input v-model:value.trim="where.bankAccountName" placeholder="请输入银行户名" allow-clear />
            </a-form-item>
            <a-form-item label="用户账户">
              <a-input v-model:value.trim="where.accountUuid" placeholder="请输入用户账户" allow-clear />
            </a-form-item>
            <a-form-item label="用户编号">
              <a-input v-model:value.trim="where.userNo" placeholder="请输入用户编号" allow-clear />
            </a-form-item>
            <a-form-item label="用户钱包">
              <a-input v-model:value.trim="where.walletUuid" placeholder="请输入用户钱包" allow-clear />
            </a-form-item>
            <a-form-item label="出款批次">
              <a-input v-model:value.trim="where.withdrawBatchNo" placeholder="请输入出款批次" allow-clear />
            </a-form-item>
            <a-form-item label="提现单号">
              <a-input v-model:value.trim="where.withdrawFlowNo" placeholder="请输入提现单号" allow-clear />
            </a-form-item>
            <a-form-item label="清算通道类型">
              <a-select v-model:value="where.remitChannelType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">支付通道</a-select-option>
                <a-select-option :value="1">代付通道</a-select-option>
                <a-select-option :value="2">税筹通道</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="清算通道">
              <a-select v-model:value="where.remitChannelCode" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option v-for="({ channelName, channelNo }, key) in channelCodes" :key="key" :value="channelNo">
                  {{ channelName }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="通道订单编号">
              <a-input v-model:value.trim="where.channelOrderNo" placeholder="请输入通道订单编号" allow-clear />
            </a-form-item>
            <a-form-item label="出款方式">
              <a-select v-model:value="where.withdrawMethod" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">单笔代付</a-select-option>
                <a-select-option :value="2">批量代付</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="出款金额来源">
              <a-select v-model:value="where.withdrawSource" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">机构钱包余额提现</a-select-option>
                <a-select-option :value="2">勾选分润结算账单提现</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="到账状态">
              <a-select v-model:value="where.receivedStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">已创建</a-select-option>
                <a-select-option :value="1">出款中</a-select-option>
                <a-select-option :value="2">已到账</a-select-option>
                <a-select-option :value="3">出款失败</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="用户类型" v-if="hasPurview('0')">
              <a-select v-model:value="where.userType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">大区</a-select-option>
                <a-select-option :value="2">运营中心</a-select-option>
                <a-select-option :value="3">代理商</a-select-option>
                <a-select-option :value="5">子级代理商</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="钱包类型">
              <a-select v-model:value="where.walletType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option value="300">营销奖励钱包</a-select-option>
                <a-select-option value="600">分润钱包</a-select-option>
                <a-select-option value="800">代付D0钱包</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="开始日期">
              <a-date-picker v-model:value="where.searchBeginTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item label="结束日期">
              <a-date-picker v-model:value="where.searchEndTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #toolbar>
            <a-descriptions title="提现信息" :column="4" size="small" style="margin-bottom: 10px">
              <a-descriptions-item label="提现总金额(元)">{{ summary.sumAmount }}</a-descriptions-item>
              <a-descriptions-item label="提现手续费总额(元)">{{ summary.sumFee }}</a-descriptions-item>
              <a-descriptions-item label="提现到账总金额(元)">{{ summary.sumReceived }}</a-descriptions-item>
              <a-descriptions-item label="提现总笔数">{{ summary.withdrawCount }}</a-descriptions-item>
              <a-descriptions-item label="通道总成本(元)" v-if="hasPurview('0')">{{ summary.sumChannelFee }}</a-descriptions-item>
            </a-descriptions>
            <a-space>
              <a-button v-if="hasPurview(['0'])" type="primary" @click="showGenerateBatch = true">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>生成出款批次</span>
              </a-button>
              <a-button v-if="hasPurview(['2', '3', '5'])" type="danger" @click="showInitiateWithdrawal = true">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>发起提现</span>
              </a-button>
              <a-button v-if="hasPurview(['1'])" type="danger" @click="showInitiateWithdrawalRegion = true">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>发起提现</span>
              </a-button>
            </a-space>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'bankAccountType'">
              <a-tag v-if="record.bankAccountType === 'G'" color="orange">对公</a-tag>
              <a-tag v-else-if="record.bankAccountType === 'S'" color="blue">对私</a-tag>
            </template>
            <template v-else-if="column.key === 'isWalletDeduct'">
              <a-tag v-if="record.isWalletDeduct === 1" color="blue">是</a-tag>
              <a-tag v-else-if="record.isWalletDeduct === 0">否</a-tag>
            </template>
            <template v-else-if="column.key === 'isCalculateFee'">
              <a-tag v-if="record.isCalculateFee === 1" color="blue">是</a-tag>
              <a-tag v-else-if="record.isCalculateFee === 0">否</a-tag>
            </template>
            <template v-else-if="column.key === 'channelWithdrawStatus'">
              <a-tag v-if="record.channelWithdrawStatus === '0'">初始化</a-tag>
              <a-tag v-if="record.channelWithdrawStatus === '1'" color="blue">提交成功</a-tag>
              <a-tag v-if="record.channelWithdrawStatus === '2'" color="pink">提交失败</a-tag>
              <a-tag v-if="record.channelWithdrawStatus === '3'" color="green">出款成功</a-tag>
              <a-tag v-if="record.channelWithdrawStatus === '4'" color="red">出款失败</a-tag>
              <a-tag v-if="record.channelWithdrawStatus === '5'" color="blue">处理中</a-tag>
              <a-tag v-if="record.channelWithdrawStatus === '6'" color="red">确定失败</a-tag>
              <a-tag v-if="record.channelWithdrawStatus === '7'" color="blue">重新出款</a-tag>
            </template>
            <template v-else-if="column.key === 'receivedStatus'">
              <a-tag v-if="record.receivedStatus === 0">已创建</a-tag>
              <a-tag v-else-if="record.receivedStatus === 1" color="blue">出款中</a-tag>
              <a-tag v-else-if="record.receivedStatus === 2" color="green">已到账</a-tag>
              <a-tag v-else-if="record.receivedStatus === 3" color="red">出款失败</a-tag>
            </template>
            <template v-else-if="column.key === 'invoiceCheckStatus'">
              <a-tag v-if="record.invoiceCheckStatus === 0">待审核</a-tag>
              <a-tag v-else-if="record.invoiceCheckStatus === 1" color="green">审核通过</a-tag>
              <a-tag v-else-if="record.invoiceCheckStatus === 2" color="red">审核不通过</a-tag>
            </template>
            <template v-else-if="column.key === 'withdrawMethod'">
              <a-tag v-if="record.withdrawMethod === 1">单笔代付</a-tag>
              <a-tag v-else-if="record.withdrawMethod === 2" color="blue">批量代付</a-tag>
            </template>
            <template v-else-if="column.key === 'walletType'">
              <a-tag v-if="record.walletType === '300'">营销奖励钱包</a-tag>
              <a-tag v-else-if="record.walletType === '600'">分润钱包</a-tag>
              <a-tag v-else-if="record.walletType === '800'">代付D0钱包</a-tag>
            </template>
            <template v-else-if="column.key === 'withdrawSource'">
              <a-tag v-if="record.withdrawSource === 1">机构钱包余额提现</a-tag>
              <a-tag v-else-if="record.withdrawSource === 2">勾选分润结算账单提现</a-tag>
            </template>
            <template v-else-if="column.key === 'userType'">
              <a-badge v-if="record.userType === 1" color="pink" text="大区" />
              <a-badge v-else-if="record.userType === 2" color="blue" text="运营中心" />
              <a-badge v-else-if="record.userType === 3" color="cyan" text="代理商" />
              <a-badge v-else-if="record.userType === 5" color="orange" text="子级代理商" />
            </template>
            <template v-else-if="column.key === 'remitChannelType'">
              <a-badge v-if="record.remitChannelType === 0" color="pink" text="支付通道" />
              <a-badge v-else-if="record.remitChannelType === 1" color="blue" text="代付通道" />
              <a-badge v-else-if="record.remitChannelType === 2" color="cyan" text="税筹通道" />
            </template>
            <template v-else-if="column.key === 'subAgentSettleChannel'">
              <a-badge v-if="record.subAgentSettleChannel === 1" color="pink" text="平台税筹账户" />
              <a-badge v-else-if="record.subAgentSettleChannel === 0" color="purple" text="交易通道" />
              <a-badge v-else-if="record.subAgentSettleChannel === 2" color="blue" text="一代税筹账户" />
              <a-badge v-else-if="record.subAgentSettleChannel === 3" color="cyan" text="展业平台" />
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
                <template v-if="record.isCalculateFee === 1">
                  <a-divider type="vertical" />
                  <a @click="handleTransFeeDetail(record)">手续费详情</a>
                </template>
                <template v-if="hasPurview('0') && record.receivedStatus === 1">
                  <a-divider type="vertical" />
                  <a @click="handleResult(record)">人工处理</a>
                </template>
                <template
                  v-if="
                    hasPurview('0') && [2, 3].includes(record.userType) && record.bankAccountType === 'G' && record.invoiceCheckStatus === 0
                  "
                >
                  <a-divider type="vertical" />
                  <a @click="handleCheck(record)">发票审核</a>
                </template>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <WithdrawDetail v-model:visible="showDetail" :detail="current" />
    <!-- 生成批次号 -->
    <GenerateBatchNo v-model:visible="showGenerateBatch" v-if="showGenerateBatch" @done="reload" :channel-codes="channelCodes" />
    <ResultManualHandle v-model:visible="showHandleResult" v-if="showHandleResult" @done="reload" :data="current2" />
    <!-- 下载 -->
    <FileDownload v-model:visible="showFileDownload" v-if="showFileDownload" />
    <!-- 上传 -->
    <FileUpload v-model:visible="showFileUpload" v-if="showFileUpload" @done="reload" :channel-codes="channelCodes" />
    <TransFeesDetail v-model:visible="showTransFeeDetail" :detail="feeDetail" />

    <InitiateWithdrawal v-model:visible="showInitiateWithdrawal" v-if="showInitiateWithdrawal" @done="reload" />
    <InitiateWithdrawalRegion v-model:visible="showInitiateWithdrawalRegion" v-if="showInitiateWithdrawalRegion" @done="reload" />

    <InvoiceCheck v-model:visible="showCheck" v-if="showCheck" @done="reload" :detail="current" />
  </div>
</template>

<script>
import { WithdrawManageApi } from '@/api/businessTeam/withdraw-manage/WithdrawManageApi';
import WithdrawDetail from './WithdrawDetail.vue';
import TransFeesDetail from './trans-fees-detail.vue';
import GenerateBatchNo from './GenerateBatchNo.vue';
import InitiateWithdrawal from './InitiateWithdrawal.vue';
import InitiateWithdrawalRegion from './InitiateWithdrawalRegion.vue';
import ResultManualHandle from './ResultManualHandle.vue';
import InvoiceCheck from './InvoiceCheck.vue';
import { RemitChannelApi } from '@/api/account/remit-channel/RemitChannelApi';
import { hasPurview } from '@/utils/permission';
import dayjs from 'dayjs';

export default {
  name: 'OrgWithdrawManage',
  components: {
    WithdrawDetail,
    GenerateBatchNo,
    TransFeesDetail,
    ResultManualHandle,
    InitiateWithdrawal,
    InitiateWithdrawalRegion,
    InvoiceCheck
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '用户编号',
          dataIndex: 'userNo'
        },
        {
          title: '用户类型',
          dataIndex: 'userType',
          key: 'userType',
          align: 'center',
          hideCol: !hasPurview(['0'])
        },
        {
          title: '一级代理商编号',
          dataIndex: 'oneLevelAgentNo',
          align: 'center',
          hideCol: hasPurview(['3', '5'])
        },
        {
          title: '一级代理商ID',
          dataIndex: 'oneLevelAgentId',
          hideCol: hasPurview(['3', '5'])
        },
        {
          title: '银行账号',
          dataIndex: 'bankAccountNoMask'
        },
        {
          title: '银行名称',
          dataIndex: 'bankName'
        },
        {
          title: '银行户名',
          dataIndex: 'bankAccountName'
        },
        {
          title: '银行账户类型',
          dataIndex: 'bankAccountType',
          key: 'bankAccountType',
          align: 'center'
        },
        {
          title: '提现费率(%)',
          dataIndex: 'withdrawRate'
        },
        {
          title: '手续费金额(元)',
          dataIndex: 'feeAmount'
        },
        {
          title: '提现单笔(元)',
          dataIndex: 'withdrawSingleFee'
        },
        {
          title: '提现金额(元)',
          dataIndex: 'withdrawAmount'
        },
        {
          title: '到账金额(元)',
          dataIndex: 'receivedAmount'
        },
        {
          title: '到账状态',
          dataIndex: 'receivedStatus',
          key: 'receivedStatus',
          align: 'center'
        },
        {
          title: '到账时间',
          dataIndex: 'receivedTime'
        },
        {
          title: '出款批次',
          dataIndex: 'withdrawBatchNo'
        },
        {
          title: '出款方式',
          dataIndex: 'withdrawMethod',
          key: 'withdrawMethod',
          align: 'center'
        },
        {
          title: '子代分润出款方式',
          dataIndex: 'subAgentSettleChannel',
          key: 'subAgentSettleChannel',
          align: 'center'
        },
        {
          title: '出款金额来源',
          dataIndex: 'withdrawSource',
          key: 'withdrawSource',
          align: 'center'
        },
        {
          title: '是否钱包扣除',
          dataIndex: 'isWalletDeduct',
          key: 'isWalletDeduct',
          align: 'center'
        },
        {
          title: '是否计算手续费',
          dataIndex: 'isCalculateFee',
          key: 'isCalculateFee',
          align: 'center'
        },
        {
          title: '提现单号',
          dataIndex: 'withdrawFlowNo'
        },
        {
          title: '提现返回编码',
          dataIndex: 'resCode'
        },
        {
          title: '提现返回描述',
          dataIndex: 'resDesc',
          width: 200
        },
        {
          title: '用途备注',
          dataIndex: 'usageRemark'
        },
        {
          title: '结算账单ID列表',
          dataIndex: 'settleBillIds',
          align: 'center'
        },
        {
          title: '机构代付费率',
          dataIndex: 'payToAnotherRate',
          hideCol: hasPurview(['5'])
        },
        {
          title: '机构代付单笔费用(元)',
          dataIndex: 'payToAnotherSingleFee',
          hideCol: hasPurview(['5'])
        },
        {
          title: '机构代付手续费(元)',
          dataIndex: 'payToAnotherFee',
          hideCol: hasPurview(['5'])
        },
        {
          title: '机构代付应付金额(元)',
          dataIndex: 'payableAmount',
          hideCol: hasPurview(['5'])
        },
        {
          title: '清算通道',
          dataIndex: 'remitChannelName'
        },
        {
          title: '清算通道类型',
          dataIndex: 'remitChannelType',
          key: 'remitChannelType',
          align: 'center'
        },
        {
          title: '通道订单编号',
          dataIndex: 'channelOrderNo'
        },
        {
          title: '通道出款状态',
          dataIndex: 'channelWithdrawStatus',
          key: 'channelWithdrawStatus',
          align: 'center'
        },
        {
          title: '通道费率ID',
          dataIndex: 'channelRateId',
          hideCol: !hasPurview(['0'])
        },
        {
          title: '通道费率',
          dataIndex: 'channelRate',
          hideCol: !hasPurview(['0'])
        },
        {
          title: '通道单笔费用(元)',
          dataIndex: 'channelSingleFee',
          hideCol: !hasPurview(['0'])
        },
        {
          title: '通道手续费',
          dataIndex: 'channelFee',
          hideCol: !hasPurview(['0'])
        },
        {
          title: '通道机构账户号',
          dataIndex: 'channelAccountNo',
          align: 'center'
        },
        {
          title: '通道返回编码',
          dataIndex: 'channelResCode'
        },
        {
          title: '通道返回描述',
          dataIndex: 'channelResDesc',
          width: 200
        },
        {
          title: '钱包类型',
          dataIndex: 'walletType',
          key: 'walletType'
        },
        {
          title: '钱包扣除流水号',
          dataIndex: 'walletDeductFlowNo'
        },
        {
          title: '用户钱包',
          dataIndex: 'walletUuid'
        },
        {
          title: '用户账户',
          dataIndex: 'accountUuid'
        },
        {
          title: '发票审核状态',
          dataIndex: 'invoiceCheckStatus',
          key: 'invoiceCheckStatus',
          align: 'center',
          hideCol: !hasPurview(['0'])
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 220,
          align: 'center'
        }
      ].filter(i => !i.hideCol),
      // 表格搜索条件
      where: {},
      current: null,
      current2: null,
      feeDetail: null,
      showDetail: false,
      showHandleResult: false,
      showGenerateBatch: false,
      showFileDownload: false,
      showFileUpload: false,
      showTransFeeDetail: false,
      channelCodes: [],
      showInitiateWithdrawal: false,
      showInitiateWithdrawalRegion: false,
      summary: {},
      showCheck: false
    };
  },
  async mounted() {
    this.getSummaryData();
    const data = await RemitChannelApi.findAll({ validStatus: 1 });
    this.channelCodes = data || [];
  },
  methods: {
    handleResult(row) {
      const { createTime, withdrawFlowNo } = row;
      this.current2 = { searchBeginTime: dayjs(createTime).format('YYYY-MM-DD'), withdrawFlowNo };
      this.showHandleResult = true;
    },
    async handleTransFeeDetail(row) {
      const data = await WithdrawManageApi.feeDetail({ id: row.id, feeType: 1 });
      this.feeDetail = data || null;
      this.showTransFeeDetail = true;
    },

    reload() {
      this.getSummaryData();
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
      this.getSummaryData();
    },

    handleCheck(row) {
      this.current = row;
      this.showCheck = true;
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    //列表查询统计
    async getSummaryData() {
      this.summary = (await WithdrawManageApi.sum(this.where)) || {};
    },

    datasource({ page, limit, where, orders }) {
      return WithdrawManageApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    },

    hasPurview
  }
};
</script>
