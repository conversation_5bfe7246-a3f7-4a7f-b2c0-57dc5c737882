<template>
  <a-modal
    :width="600"
    :visible="visible"
    title="钉灵工签约"
    :mask-closable="false"
    :body-style="{ paddingBottom: '30px' }"
    @update:visible="updateVisivle"
  >
    <a-descriptions :column="1" bordered>
      <a-descriptions-item label="姓名">{{ form.name }}</a-descriptions-item>
      <a-descriptions-item label="身份证">{{ form.idCardNoMask }}</a-descriptions-item>
      <a-descriptions-item label="手机号">
        <a-typography-paragraph v-model:content="form.mobile" editable />
      </a-descriptions-item>
    </a-descriptions>

    <a-collapse :active-key="['1']">
      <a-collapse-panel key="1" header="身份证图片">
        <a-form :layout="'vertical'">
          <a-row :gutter="[16, 16]">
            <template v-for="(item, key) in fileList" :key="key">
              <a-col :md="6" :sm="24" :xs="24">
                <a-form-item :label="`${item.label}`">
                  <a-upload
                    v-model:file-list="item.fileData"
                    accept=".png, .jpg, .jpeg"
                    :max-count="1"
                    list-type="picture-card"
                    @preview="handlePreviewFile"
                    :before-upload="file => handleSelectFile(file, item)"
                  >
                    <div v-if="!item.fileData?.length">
                      <plus-outlined />
                      <div style="margin-top: 8px">Upload</div>
                    </div>
                  </a-upload>
                </a-form-item>
              </a-col>
            </template>
          </a-row>
          <!-- 预览图片 -->
          <a-image
            :style="{ display: 'none' }"
            :src="previewImage"
            :preview="{ visible: previewVisible, onVisibleChange: setPreviewVisible }"
          />
        </a-form>
      </a-collapse-panel>
    </a-collapse>

    <template #footer>
      <a-button @click="updateVisivle(false)">关闭</a-button>
      <a-button type="primary" :disabled="disabled" :loading="loading" @click="save">确定</a-button>
    </template>
  </a-modal>
</template>

<script>
import { WithdrawManageApi } from '@/api/businessTeam/withdraw-manage/WithdrawManageApi';
import { ProfitVerifyApi } from '@/api/transactionManage/ProfitVerifyApi';
import { useUserStore } from '@/store/modules/user';
import { compressorImageSpecifySize } from '@/utils/image-compressor-util';
import { message, Upload } from 'ant-design-vue';

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  data() {
    return {
      form: {},
      // 预览图片
      previewImage: '',
      previewVisible: false,
      //图片数组
      fileList: [
        {
          label: '身份证正面',
          fileType: 1,
          fileData: []
        },
        {
          label: '身份证背面',
          fileType: 2,
          fileData: []
        }
      ],

      disabled: true
    };
  },
  computed: {
    // 当前登录用户信息
    loginUser() {
      const userStore = useUserStore();
      return userStore.$state.info;
    }
  },
  created() {
    this.queryOrgAuthInfo();
  },
  methods: {
    async save() {
      const mobileRegex = /^1[3456789]\d{9}$/; // 正则表达式匹配以1开头的11位数字
      if (!mobileRegex.test(this.form.mobile)) {
        message.warning('请输入正确的手机号');
        return;
      }

      await this.validateFileList();

      // 上传图片
      await this.uploadImages();

      // 修改加载框为正在加载
      this.loading = true;
      this.disabled = true;

      WithdrawManageApi.signDlg({
        name: this.form.name, // 姓名
        idCardNo: this.form.idCardNo, // 身份证
        mobile: this.form.mobile // 手机号
      })
        .then(result => {
          // 移除加载框
          this.loading = false;

          this.disabled = false;

          // 提示修改成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisivle(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
          this.disabled = false;
        });
    },

    /**
     * 选中文件
     * @param {*} file
     * @param {*} item 当前图片项
     */
    handleSelectFile(file, item) {
      compressorImageSpecifySize(file).then(({ url }) => {
        item.fileData = [{ url, suffixType: 'png' }];
      });
      return Upload.LIST_IGNORE;
    },

    /**
     * 校验图片是否上传
     */
    validateFileList() {
      return new Promise((resolve, reject) => {
        if (this.fileList.every(f => !!f.fileData?.length)) {
          resolve();
        } else {
          message.warn('请上传所有图片');
          reject();
        }
      });
    },

    /**
     * 上传图片
     */
    async uploadImages() {
      const fileListHasVal = this.fileList.filter(i => i.fileData?.length);
      const noChangeFiles = [];
      const changeedFiles = [];
      fileListHasVal.forEach(i => {
        i.fileData.forEach(j => {
          if (/^(https?:)/.test(j.url)) {
            noChangeFiles.push({
              id: j.id,
              imageType: i.fileType,
              imagePath: j.url
            });
          } else {
            changeedFiles.push({
              fileType: i.fileType,
              suffixType: j.suffixType,
              fileData: j.url
            });
          }
        });
      });

      let imageJsonList = [];
      if (changeedFiles.length) {
        const data = await ProfitVerifyApi.uploadOrgImages({
          fileDTOList: changeedFiles,
          orgNo: this.loginUser.orgCode,
          orgType: this.loginUser.userType
        });
        imageJsonList = data.imageJsonList;
      }

      return Promise.resolve();

      // this.form.imageJsonList = [...noChangeFiles, ...imageJsonList];
    },

    async queryOrgAuthInfo() {
      const data = await WithdrawManageApi.queryOrgAuthInfo();
      this.form = Object.assign({}, data);

      // 处理图片
      const fileListMap = data.imageList || [];
      fileListMap.forEach(fileItem => {
        const findItem = this.fileList.find(item => item.fileType === fileItem.imageType);
        if (findItem) {
          findItem.fileData = fileItem.imagePath ? [...findItem.fileData, { url: fileItem.imagePath, id: fileItem.id }] : findItem.fileData;
        }
      });

      this.disabled = false;
    },

    handlePreviewFile(file) {
      this.previewImage = file?.url;
      this.setPreviewVisible(true);
    },

    setPreviewVisible(visible) {
      this.previewVisible = visible;
    },

    updateVisivle(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
