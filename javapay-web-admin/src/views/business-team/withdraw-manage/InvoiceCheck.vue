<template>
  <a-modal
    :width="500"
    :visible="visible"
    :confirm-loading="loading"
    title="发票审核"
    :body-style="{ paddingBottom: '8px' }"
    :mask-closable="false"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 6 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 18 }, sm: { span: 24 } }"
    >
      <a-form-item label="审核状态" name="invoiceCheckStatus">
        <a-select v-model:value="form.invoiceCheckStatus" style="width: 100%" placeholder="请选择" allow-clear>
          <a-select-option :value="1">通过</a-select-option>
          <a-select-option :value="2">不通过</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="审核备注" name="resDesc">
        <a-textarea v-model:value="form.resDesc" placeholder="请输入审核备注" :auto-size="{ minRows: 1 }" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { WithdrawManageApi } from '@/api/businessTeam/withdraw-manage/WithdrawManageApi';

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {},
      // 表单验证规则
      rules: {
        invoiceCheckStatus: [{ required: true, message: '请选择' }],
        resDesc: [{ required: true, message: '请输入审核备注' }]
      },
      // 提交状态
      loading: false
    };
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      this.form.withdrawId = this.detail.id;
      WithdrawManageApi.invoiceCheck(this.form)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
