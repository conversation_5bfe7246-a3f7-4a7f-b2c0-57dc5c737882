<template>
  <a-modal
    :width="500"
    :visible="visible"
    :confirm-loading="loading"
    title="人工处理"
    :body-style="{ paddingBottom: '8px' }"
    :mask-closable="false"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 7 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 17 }, sm: { span: 24 } }"
    >
      <a-descriptions :column="2" bordered layout="vertical" size="small">
        <a-descriptions-item label="提现流水单号">{{ form.withdrawFlowNo }} </a-descriptions-item>
        <a-descriptions-item label="提现发起日期">{{ form.searchBeginTime }} </a-descriptions-item>
      </a-descriptions>
      <a-divider style="margin: 17px 0" dashed />
      <a-form-item label="出款结果状态" name="receivedStatus">
        <a-select v-model:value="form.receivedStatus" style="width: 100%" placeholder="请选择" allow-clear>
          <a-select-option :value="2">已到账</a-select-option>
          <a-select-option :value="3">出款失败</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="人工处理备注" name="resDesc">
        <a-textarea v-model:value="form.resDesc" placeholder="请输入人工处理备注" :auto-size="{ minRows: 2, maxRows: 5 }" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { WithdrawManageApi } from '@/api/businessTeam/withdraw-manage/WithdrawManageApi';

export default {
  props: {
    data: Object,
    visible: Boolean,
    channelCodes: Array
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {},
      // 表单验证规则
      rules: {
        receivedStatus: [{ required: true, message: '请选择出款结果状态' }],
        resDesc: [{ required: true, message: '请输入人工处理备注' }]
      },
      // 提交状态
      loading: false
    };
  },
  mounted() {
    if (this.data) {
      this.form = Object.assign({}, this.data);
    }
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      WithdrawManageApi.resultManualHandle(this.form)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
<style scoped>
::v-deep(.ant-form-item-label) {
  text-align: center;
  background-color: #fafafa;
}
</style>
