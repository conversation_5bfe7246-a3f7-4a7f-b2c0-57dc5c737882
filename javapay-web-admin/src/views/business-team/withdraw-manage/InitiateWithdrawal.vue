<template>
  <a-modal
    :width="880"
    :visible="visible"
    title="提现"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
  >
    <!-- 操作步骤 -->
    <a-steps v-model:current="currentStep" type="navigation" size="small">
      <a-step title="提现申请" />
      <a-step title="提现确认" disabled />
    </a-steps>

    <a-card v-if="currentStep === 0" :bordered="false">
      <a-descriptions :column="2" size="small" bordered>
        <a-descriptions-item label="机构账户余额(元)" :span="2">{{ currentChannel.balanceAmount }}</a-descriptions-item>
        <!-- <a-descriptions-item label="开票可用额度">{{ currentChannel.invoiceAvailableLimit }}</a-descriptions-item> -->
        <a-descriptions-item label="D0单笔最小限额(元)">{{ currentChannel.minLimitAmount }}</a-descriptions-item>
        <a-descriptions-item label="D0单笔最大限额(元)">{{ currentChannel.maxLimitAmount }}</a-descriptions-item>
      </a-descriptions>

      <a-divider dashed style="margin: 22px 0" />

      <a-form ref="form" :model="form" :rules="rules" layout="vertical">
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item label="出款通道" name="remitChannelNo">
              <a-select v-model:value="form.remitChannelNo" class="ele-fluid" placeholder="请选择" @change="onChangeChannel" disabled>
                <a-select-option v-for="({ remitChannelName, remitChannelNo }, key) in remitChannels" :key="key" :value="remitChannelNo">
                  {{ remitChannelName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item label="业务类型" name="walletType">
              <a-select v-model:value="form.walletType" style="width: 100%" placeholder="请选择" @change="onChangeWalletType">
                <a-select-option :value="600">交易分润</a-select-option>
                <a-select-option :value="300">营销活动钱包</a-select-option>
                <!-- <a-select-option :value="800">代付账户代付</a-select-option> -->
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item label="提现金额(元)" name="withdrawAmount">
              <a-input-number
                v-model:value="form.withdrawAmount"
                prefix="￥"
                style="width: 100%"
                :min="Number(currentChannel.minLimitAmount || 0)"
                :max="Number(currentChannel.maxLimitAmount || '999999')"
              />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item label="到账银行卡" name="settleCardId">
              <a-textarea :value="settleCardStr" placeholder="请选择" auto-size @click="showSelectSettleCard = true" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <a-card v-if="currentStep === 1" :bordered="false">
      <a-descriptions v-if="currentStep === 1" :column="2" bordered>
        <a-descriptions-item label="出款通道">{{ currentChannel.remitChannelName }}</a-descriptions-item>
        <a-descriptions-item label="业务类型">{{ walletTypeMap[form.walletType] }}</a-descriptions-item>
        <a-descriptions-item label="到账银行卡" :span="2">{{ settleCardStr }}</a-descriptions-item>
        <a-descriptions-item label="提现金额(元)">{{ form.withdrawAmount }}</a-descriptions-item>
        <a-descriptions-item label="提现税率(%)">{{ taxInfo.rate }}</a-descriptions-item>
        <a-descriptions-item label="提现单笔费用(元)">{{ taxInfo.singleFee }}</a-descriptions-item>
        <a-descriptions-item label="提现税额(元)">{{ taxInfo.taxFee }}</a-descriptions-item>
      </a-descriptions>
    </a-card>

    <!-- 自定义页脚按钮 -->
    <template #footer>
      <a-button key="back" @click="updateVisible(false)">取消</a-button>
      <a-button v-if="currentStep === 0" key="next" type="primary" @click="nextStep" :disabled="disabled">下一步</a-button>
      <a-button v-else key="submit" type="primary" :loading="loading" @click="save">确定</a-button>
    </template>

    <SelectSettleCard v-model:visible="showSelectSettleCard" v-if="showSelectSettleCard" @done="handleSelectSettleCard" :where="where" />

    <DlgSign v-model:visible="showDlgSign" v-if="showDlgSign" :detail="data" />
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import SelectSettleCard from './SelectSettleCard.vue';
import { WithdrawManageApi } from '@/api/businessTeam/withdraw-manage/WithdrawManageApi';
import { hasPurview } from '@/utils/permission';
import DlgSign from './DlgSign.vue';

const walletTypeMap = {
  600: '交易分润',
  300: '营销活动钱包',
  800: '代付账户代付'
};

export default {
  components: {
    SelectSettleCard,
    DlgSign
  },
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      walletTypeMap,
      currentStep: 0,
      // 表单数据
      form: {
        walletType: 600,
        settleCardId: ''
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false,
      // 表单验证规则
      rules: {
        remitChannelNo: [{ required: true, message: '请选择' }],
        walletType: [{ required: true, message: '请选择' }],
        withdrawAmount: [
          { required: true, message: '请填写提现金额' },
          {
            validator: (rule, value) => {
              if (Number(value) > Number(this.currentChannel.balanceAmount)) {
                return Promise.reject('提现金额不能大于机构账户余额');
              }
              return Promise.resolve();
            }
          }
        ],
        settleCardId: [{ required: true, message: '请选择' }]
      },

      profitBalance: '0.00',
      remitChannels: [],

      showSelectSettleCard: false,
      settleCardStr: '',

      taxInfo: {},

      currentChannel: {},

      where: {
        accountType: ''
      },

      disabled: true,

      showDlgSign: false
    };
  },
  created() {
    this.getProfitBalance();
  },
  methods: {
    onChangeWalletType() {
      this.getProfitBalance();
    },

    onChangeChannel() {
      this.currentChannel = this.remitChannels.find(item => item.remitChannelNo === this.form.remitChannelNo);
    },

    handleSelectSettleCard(row) {
      if (row) {
        this.form.settleCardId = row.id;
        this.form.bankAccountType = row.accountType;
        this.settleCardStr = `${row.bankSubName}-${row.bankAccountNoMask}-${row.accountType === 'G' ? '对公' : '对私'}`;
      }
    },

    async getWithdrawTaxInfo() {
      const data = await WithdrawManageApi.getWithdrawTaxInfo(this.form);
      this.taxInfo = Object.assign({}, data);
    },

    async getProfitBalance() {
      this.disabled = true;

      const data = await WithdrawManageApi.getProfitBalance({
        walletType: this.form.walletType
      });

      this.profitBalance = data.amount || '0.00';

      this.form.bankAccountType = data.bankAccountType || '';

      this.where.accountType = data.bankAccountType;

      if(this.form.walletType === 300) {
        this.where.accountType = 'S';
        this.form.bankAccountType = 'S';
      }

      this.remitChannels = data ? [data] : [];
      this.form.remitChannelNo = data.remitChannelNo || null;
      this.currentChannel = data;

      // 重置
      this.form.withdrawAmount = '';
      this.settleCardStr = '';
      this.form.settleCardId = '';

      this.disabled = false;
    },

    /**
     * 保存
     */
    async save() {
      if (this.form.bankAccountType === 'S') {
        let data = await WithdrawManageApi.getOrgStatus();
        data = data || {};
        const { dlgSignStatus, dlgAuthStatus } = data;
        if ([0, 3].includes(dlgSignStatus) || [0, 3].includes(dlgAuthStatus)) {
          this.showDlgSign = true;
          return;
        }
        if (dlgAuthStatus === 1) {
          message.info('钉灵工认证中，请稍后再试');
          return;
        }
        if (dlgSignStatus === 1) {
          message.info('钉灵工签约中，请稍后再试');
          return;
        }
      }

      // 修改加载框为正在加载
      this.loading = true;

      WithdrawManageApi.walletWithdraw(this.form)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    /**
     * 点击下一步
     */
    async nextStep() {
      // 校验基本信息表单
      await this.$refs.form.validate();

      this.getWithdrawTaxInfo();
      this.currentStep = 1;
    },

    hasPurview,

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
