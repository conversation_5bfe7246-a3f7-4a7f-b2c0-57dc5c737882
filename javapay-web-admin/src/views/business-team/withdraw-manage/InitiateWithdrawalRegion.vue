<template>
  <a-modal
    :width="1000"
    :visible="visible"
    title="发起提现"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
  >
    <a-card :bordered="false">
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="机构账户余额(元)">{{ currentChannel.balanceAmount }}</a-descriptions-item>
        <a-descriptions-item label="开票可用额度(元)">{{ currentChannel.invoiceAvailableLimit }}</a-descriptions-item>
        <!-- <a-descriptions-item label="财税商户余额">{{ currentChannel.taxMerchBalanceAmt }}</a-descriptions-item> -->
        <a-descriptions-item label="D0单笔最小限额(元)">{{ currentChannel.minLimitAmount }}</a-descriptions-item>
        <a-descriptions-item label="D0单笔最大限额(元)">{{ currentChannel.maxLimitAmount }}</a-descriptions-item>
      </a-descriptions>

      <a-divider dashed style="margin: 22px 0" />

      <a-form ref="form" :model="form" :rules="rules" layout="vertical">
        <a-row :gutter="24">
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item label="出款通道" name="remitChannelNo">
              <a-select v-model:value="form.remitChannelNo" class="ele-fluid" placeholder="请选择" disabled>
                <a-select-option v-for="({ remitChannelName, remitChannelNo }, key) in remitChannels" :key="key" :value="remitChannelNo">
                  {{ remitChannelName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item label="业务类型" name="walletType">
              <a-select v-model:value="form.walletType" style="width: 100%" placeholder="请选择" disabled>
                <a-select-option :value="600">交易分润</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item label="提现金额(元)" name="withdrawAmount">
              <a-input-number
                v-model:value="form.withdrawAmount"
                prefix="￥"
                style="width: 100%"
                :min="Number(currentChannel.minLimitAmount || 0)"
                :max="Number(currentChannel.maxLimitAmount || '999999')"
              />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item label="到账银行卡" name="settleCardId">
              <a-textarea :value="settleCardStr" placeholder="请选择" auto-size @click="showSelectSettleCard = true" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <!-- 自定义页脚按钮 -->
    <template #footer>
      <a-button key="back" @click="updateVisible(false)">关闭</a-button>
      <a-button key="submit" type="primary" :loading="loading" @click="save" :disabled="disabled">确定</a-button>
    </template>

    <SelectSettleCard v-model:visible="showSelectSettleCard" v-if="showSelectSettleCard" @done="handleSelectSettleCard" :where="where" />

    <DlgSign v-model:visible="showDlgSign" v-if="showDlgSign" :detail="data" />
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { WithdrawManageApi } from '@/api/businessTeam/withdraw-manage/WithdrawManageApi';
import SelectSettleCard from './SelectSettleCard.vue';
import DlgSign from './DlgSign.vue';

export default {
  components: {
    SelectSettleCard,
    DlgSign
  },
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {
        walletType: 600
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false,
      // 表单验证规则
      rules: {
        remitChannelNo: [{ required: true, message: '请选择' }],
        walletType: [{ required: true, message: '请选择' }],
        withdrawAmount: [
          { required: true, message: '请填写金额' },
          {
            validator: (rule, value) => {
              if (Number(value) > Number(this.currentChannel.balanceAmount)) {
                return Promise.reject('金额不能大于机构账户余额');
              }
              return Promise.resolve();
            }
          }
        ],
        settleCardId: [{ required: true, message: '请选择' }]
      },

      remitChannels: [],

      currentChannel: {},

      disabled: true,

      showSelectSettleCard: false,

      where: {
        accountType: ''
      },

      showDlgSign: false
    };
  },
  created() {
    this.getProfitBalance();
  },
  methods: {
    handleSelectSettleCard(row) {
      if (row) {
        this.form.settleCardId = row.id;
        this.form.bankAccountType = row.accountType;
        this.settleCardStr = `${row.bankSubName}-${row.bankAccountNoMask}-${row.accountType === 'G' ? '对公' : '对私'}`;
      }
    },

    async getProfitBalance() {
      const data = await WithdrawManageApi.getRegionAccountBalance({});

      this.remitChannels = data ? [data] : [];
      this.form.remitChannelNo = data.remitChannelNo || null;
      this.currentChannel = data;

      this.form.bankAccountType = data.bankAccountType || '';

      this.where.accountType = data.bankAccountType;

      // 重置
      this.form.withdrawAmount = '';
      this.settleCardStr = '';
      // this.form.settleCardId = '';

      this.disabled = false;
    },

    /**
     * 保存
     */
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      if (this.form.bankAccountType === 'S') {
        let data = await WithdrawManageApi.getOrgStatus();
        data = data || {};
        const { dlgSignStatus, dlgAuthStatus } = data;
        if ([0, 3].includes(dlgSignStatus) || [0, 3].includes(dlgAuthStatus)) {
          this.showDlgSign = true;
          return;
        }
        if (dlgAuthStatus === 1) {
          message.info('钉灵工认证中，请稍后再试');
          return;
        }
        if (dlgSignStatus === 1) {
          message.info('钉灵工签约中，请稍后再试');
          return;
        }
      }

      // 修改加载框为正在加载
      this.loading = true;

      WithdrawManageApi.walletWithdraw(this.form)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
