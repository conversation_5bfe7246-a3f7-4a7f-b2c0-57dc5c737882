<template>
  <a-modal
    :width="600"
    :visible="visible"
    :confirm-loading="loading"
    title="生成出款批次"
    :body-style="{ paddingBottom: '8px' }"
    :mask-closable="false"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 5 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 19 }, sm: { span: 24 } }"
    >
      <a-form-item label="清算通道">
        <a-select v-model:value="form.remitChannelCode" class="ele-fluid" placeholder="请选择" allow-clear>
          <a-select-option v-for="({ channelName, channelNo }, key) in channelCodes" :key="key" :value="channelNo">{{
            channelName
          }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="开始日期" name="searchBeginTime">
        <a-date-picker v-model:value="form.searchBeginTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
      </a-form-item>
      <a-form-item label="结束日期" name="searchEndTime">
        <a-date-picker v-model:value="form.searchEndTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { WithdrawManageApi } from '@/api/businessTeam/withdraw-manage/WithdrawManageApi';

export default {
  props: {
    visible: Boolean,
    channelCodes: Array
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {},
      // 表单验证规则
      rules: {
        remitChannelCode: [{ required: true, message: '请选择通道' }],
        searchBeginTime: [{ required: true, message: '请选择开始日期' }],
        searchEndTime: [{ required: true, message: '请选择结束日期' }],
      },
      // 提交状态
      loading: false
    };
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      WithdrawManageApi.generateBatchNo(this.form)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
