<template>
  <a-modal
    :width="1000"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="用户编号">{{ form.userNo }}</a-descriptions-item>
      <a-descriptions-item label="用户类型" v-if="hasPurview('0')">
        <a-badge v-if="form.userType === 1" color="pink" text="大区" />
        <a-badge v-else-if="form.userType === 2" color="blue" text="运营中心" />
        <a-badge v-else-if="form.userType === 3" color="cyan" text="代理商" />
        <a-badge v-else-if="form.userType === 5" color="orange" text="子级代理商" />
      </a-descriptions-item>
      <a-descriptions-item label="一级代理商编号" v-if="hasPurview(['0', '1', '2'])">{{ form.oneLevelAgentNo }}</a-descriptions-item>
      <a-descriptions-item label="一级代理商ID" v-if="hasPurview(['0', '1', '2'])">{{ form.oneLevelAgentId }}</a-descriptions-item>
      <a-descriptions-item label="代理商组织结构" v-if="hasPurview('0') && [3, 5].includes(form.userType)">{{
        form.agentOrgInfo
      }}</a-descriptions-item>
      <a-descriptions-item label="银行账号">{{ form.bankAccountNoMask }}</a-descriptions-item>
      <a-descriptions-item label="银行名称">{{ form.bankName }}</a-descriptions-item>
      <a-descriptions-item label="银行户名">{{ form.bankAccountName }}</a-descriptions-item>
      <a-descriptions-item label="银行账户类型">
        <a-tag v-if="form.bankAccountType === 'G'" color="orange">对公</a-tag>
        <a-tag v-else-if="form.bankAccountType === 'S'" color="blue">对私</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="银行联行号">{{ form.bankChannelNo }}</a-descriptions-item>
      <a-descriptions-item label="开户行名称">{{ form.bankSubName }}</a-descriptions-item>
      <a-descriptions-item label="清算行行号">{{ form.clearChannelNo }}</a-descriptions-item>
      <a-descriptions-item label="银行预留身份证号">{{ form.idCardNoMask }}</a-descriptions-item>
      <a-descriptions-item label="银行预留手机号">{{ form.mobileMask }}</a-descriptions-item>
      <a-descriptions-item label="提现费率(%)">{{ form.withdrawRate }}</a-descriptions-item>
      <a-descriptions-item label="手续费金额(元)">{{ form.feeAmount }}</a-descriptions-item>
      <a-descriptions-item label="提现单笔(元)">{{ form.withdrawSingleFee }}</a-descriptions-item>
      <a-descriptions-item label="提现金额(元)">{{ form.withdrawAmount }}</a-descriptions-item>
      <a-descriptions-item label="到账金额(元)">{{ form.receivedAmount }}</a-descriptions-item>
      <a-descriptions-item label="到账状态">
        <a-tag v-if="form.receivedStatus === 0">已创建</a-tag>
        <a-tag v-else-if="form.receivedStatus === 1" color="blue">出款中</a-tag>
        <a-tag v-else-if="form.receivedStatus === 2" color="green">已到账</a-tag>
        <a-tag v-else-if="form.receivedStatus === 3" color="red">出款失败</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="到账时间">{{ form.receivedTime || '-' }}</a-descriptions-item>
      <a-descriptions-item label="出款金额来源">
        <a-tag v-if="form.withdrawSource === 1">机构钱包余额提现</a-tag>
        <a-tag v-else-if="form.withdrawSource === 2">勾选分润结算账单提现</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="出款批次">{{ form.withdrawBatchNo || '-' }}</a-descriptions-item>
      <a-descriptions-item label="出款方式">
        <a-tag v-if="form.withdrawMethod === 1">单笔代付</a-tag>
        <a-tag v-else-if="form.withdrawMethod === 2" color="blue">批量代付</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="子代分润出款方式">
        <a-badge v-if="form.subAgentSettleChannel === 1" color="pink" text="平台税筹账户" />
        <a-badge v-else-if="form.subAgentSettleChannel === 0" color="purple" text="交易通道" />
        <a-badge v-else-if="form.subAgentSettleChannel === 2" color="blue" text="一代税筹账户" />
        <a-badge v-else-if="form.subAgentSettleChannel === 3" color="cyan" text="展业平台" />
      </a-descriptions-item>
      <a-descriptions-item label="是否钱包扣除">
        <a-tag v-if="form.isWalletDeduct === 1" color="blue">是</a-tag>
        <a-tag v-else-if="form.isWalletDeduct === 0">否</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="是否计算手续费">
        <a-tag v-if="form.isCalculateFee === 1" color="blue">是</a-tag>
        <a-tag v-else-if="form.isCalculateFee === 0">否</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="提现单号">{{ form.withdrawFlowNo }}</a-descriptions-item>
      <a-descriptions-item label="提现返回编码">{{ form.resCode || '-' }}</a-descriptions-item>
      <a-descriptions-item label="提现返回描述">{{ form.resDesc || '-' }}</a-descriptions-item>
      <a-descriptions-item label="用途备注">{{ form.usageRemark }}</a-descriptions-item>
      <a-descriptions-item label="钱包类型">
        <a-tag v-if="form.walletType === '300'">营销奖励钱包</a-tag>
        <a-tag v-else-if="form.walletType === '600'">分润钱包</a-tag>
        <a-tag v-else-if="form.walletType === '800'">代付D0钱包</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="钱包扣除流水号">{{ form.walletDeductFlowNo }}</a-descriptions-item>
      <a-descriptions-item label="结算账单ID列表">{{ form.settleBillIds }}</a-descriptions-item>
      <a-descriptions-item label="机构代付费率" v-if="!hasPurview(['5'])">{{ form.payToAnotherRate }}</a-descriptions-item>
      <a-descriptions-item label="机构代付单笔费用(元)" v-if="!hasPurview(['5'])">{{ form.payToAnotherSingleFee }}</a-descriptions-item>
      <a-descriptions-item label="机构代付手续费(元)" v-if="!hasPurview(['5'])">{{ form.payToAnotherFee }}</a-descriptions-item>
      <a-descriptions-item label="机构代付应付金额(元)" v-if="!hasPurview(['5'])">{{ form.payableAmount }}</a-descriptions-item>

      <a-descriptions-item label="用户钱包">{{ form.walletUuid }}</a-descriptions-item>
      <a-descriptions-item label="清算通道">{{ form.remitChannelName }}</a-descriptions-item>
      <a-descriptions-item label="清算通道类型">
        <a-badge v-if="form.remitChannelType === 0" color="pink" text="支付通道" />
        <a-badge v-else-if="form.remitChannelType === 1" color="blue" text="代付通道" />
        <a-badge v-else-if="form.remitChannelType === 2" color="cyan" text="税筹通道" />
      </a-descriptions-item>
      <a-descriptions-item label="通道出款状态">
        <a-tag v-if="form.channelWithdrawStatus === '0'">初始化</a-tag>
        <a-tag v-if="form.channelWithdrawStatus === '1'" color="blue">提交成功</a-tag>
        <a-tag v-if="form.channelWithdrawStatus === '2'" color="pink">提交失败</a-tag>
        <a-tag v-if="form.channelWithdrawStatus === '3'" color="green">出款成功</a-tag>
        <a-tag v-if="form.channelWithdrawStatus === '4'" color="red">出款失败</a-tag>
        <a-tag v-if="form.channelWithdrawStatus === '5'" color="blue">处理中</a-tag>
        <a-tag v-if="form.channelWithdrawStatus === '6'" color="red">确定失败</a-tag>
        <a-tag v-if="form.channelWithdrawStatus === '7'" color="blue">重新出款</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="通道订单编号">{{ form.channelOrderNo }}</a-descriptions-item>
      <a-descriptions-item label="通道费率ID" v-if="hasPurview(['0'])">{{ form.channelRateId }}</a-descriptions-item>
      <a-descriptions-item label="通道费率" v-if="hasPurview(['0'])">{{ form.channelRate }}</a-descriptions-item>
      <a-descriptions-item label="通道单笔费用(元)" v-if="hasPurview(['0'])">{{ form.channelSingleFee }}</a-descriptions-item>
      <a-descriptions-item label="通道手续费" v-if="hasPurview(['0'])">{{ form.channelFee }}</a-descriptions-item>
      <a-descriptions-item label="通道机构账户号">{{ form.channelAccountNo }}</a-descriptions-item>
      <a-descriptions-item label="通道返回编码">{{ form.channelResCode }}</a-descriptions-item>
      <a-descriptions-item label="通道返回描述">{{ form.channelResDesc }}</a-descriptions-item>
      <a-descriptions-item label="是否同行转账">{{ form.isIntraTransfer === 1 ? '是' : '否' }}</a-descriptions-item>
      <a-descriptions-item label="是否同城出款">{{ form.isSameCity === 1 ? '是' : '否' }}</a-descriptions-item>
      <a-descriptions-item label="用户账户">{{ form.accountUuid }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';
import { hasPurview } from '@/utils/permission';

export default {
  props: {
    visible: Boolean,
    detail: Object,
    channelCodes: Array
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      hasPurview,
      updateVisible
    };
  }
};
</script>
