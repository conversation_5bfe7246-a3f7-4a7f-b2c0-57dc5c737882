<template>
  <a-modal
    :width="750"
    :visible="visible"
    :confirm-loading="loading"
    title="费率政策同步下级"
    :body-style="{ paddingBottom: '8px' }"
    :mask-closable="false"
    @update:visible="updateVisible"
    @ok="save"
  >
    <SyncSubList v-model:selection="selectedSubs" :policyId="policyId" />
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { RatePolicyApi } from '@/api/businessTeam/rate-policy/RatePolicyApi';
import SyncSubList from './SyncSubList.vue';

export default {
  components: { SyncSubList },
  props: {
    visible: Boolean,
    policyId: String
  },
  emits: ['update:visible'],
  data() {
    return {
      loading: false,
      selectedSubs: []
    };
  },
  methods: {
    async save() {
      if (!this.selectedSubs.length) return message.warn('请选择下级');

      // 修改加载框为正在加载
      this.loading = true;

      const orgBaseInfoDTOList = this.selectedSubs.map(s => {
        return {
          userId: s.userId,
          userLevel: s.userLevel,
          userNo: s.userNo,
          userType: s.userType
        };
      });
      RatePolicyApi.syncRatePolicy({ id: this.policyId, orgBaseInfoDTOList })
        .then(result => {
          this.loading = false;

          message.success(result.message);

          this.updateVisible(false);
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
