<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="政策编号">
              <a-input v-model:value.trim="where.policyNo" placeholder="请输入政策编号" allow-clear />
            </a-form-item>
            <a-form-item label="用户编号" v-purview="'0'">
              <a-input v-model:value.trim="where.userNo" placeholder="请输入用户编号" allow-clear />
            </a-form-item>
            <a-form-item label="上级用户编号" v-purview="'0'">
              <a-input v-model:value.trim="where.parentUserNo" placeholder="请输入上级用户编号" allow-clear />
            </a-form-item>
            <a-form-item v-purview="'0'" label="用户类型">
              <a-select v-model:value="where.userType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">大区</a-select-option>
                <a-select-option :value="2">运营中心</a-select-option>
                <a-select-option :value="3">代理商</a-select-option>
                <a-select-option :value="5">子级代理商</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- table上边工具栏 -->
          <template #toolbar>
            <a-space>
              <a-button v-purview="['1', '2', '3', '5']" type="primary" @click="handleAdd()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'userType'">
              <a-badge v-if="record.userType === 1" color="pink" text="大区" />
              <a-badge v-else-if="record.userType === 2" color="blue" text="运营中心" />
              <a-badge v-else-if="record.userType === 3" color="orange" text="代理商" />
              <a-badge v-else-if="record.userType === 5" color="cyan" text="子级代理商" />
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record.id)">详情</a>
                <a v-purview="['1', '2', '3', '5']" @click="handleEdit(record.id)">编辑</a>
                <a v-purview="['1', '2']" @click="handleSyncRate(record.id)">同步下级费率</a>
                <a v-purview="['3', '5']" @click="handleSyncRate(record.id)">同步下级政策</a>
                <a-popconfirm title="确定要删除此记录吗？" @confirm="remove(record)">
                  <a class="ele-text-danger">删除</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 修改费率政策 -->
    <RatePolicyEdit
      v-if="showEdit"
      v-model:visible="showEdit"
      :data="current"
      :channelCodes="channelCodes"
      :bankList="bankList"
      :opt-type="optType"
      :policy-type="1"
      @done="policyEditDone"
    />

    <RatePolicySync v-if="showSyncRate" v-model:visible="showSyncRate" :policyId="policyId" :bankList="bankList" />
  </div>
</template>

<script>
import { message, Modal } from 'ant-design-vue';
import { RatePolicyApi } from '@/api/businessTeam/rate-policy/RatePolicyApi';
import { BankCodeManageApi } from '@/api/base/BankCodeManageApi';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import RatePolicyEdit from './RatePolicyEdit.vue';
import { hasPurview } from '@/utils/permission';
import RatePolicySync from './RatePolicySync.vue';

export default {
  name: 'RatePolicy',
  components: {
    RatePolicyEdit,
    RatePolicySync
  },
  data() {
    const isHideCol = !hasPurview('0');
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left',
          hideCol: isHideCol
        },
        {
          title: '政策编号',
          dataIndex: 'policyNo',
          align: 'center',
          hideIntable: true
        },
        {
          title: '政策名称',
          dataIndex: 'policyDesc'
        },
        {
          title: '版本号',
          dataIndex: 'version',
          width: 100,
          align: 'center'
        },
        {
          title: '用户类型',
          dataIndex: 'userType',
          key: 'userType',
          width: 120,
          align: 'center',
          hideCol: isHideCol
        },
        {
          title: '用户编号',
          dataIndex: 'userNo',
          align: 'center',
          hideCol: isHideCol
        },
        {
          title: '上级用户编号',
          dataIndex: 'parentUserNo',
          align: 'center',
          hideCol: isHideCol
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '修改人',
          dataIndex: 'lastModifyUserId'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          width: 250,
          align: 'center',
          fixed: 'right'
        }
      ].filter(i => !i.hideCol),
      // 表格搜索条件
      where: {
        policyType: 1
      },
      current: null,
      channelCodes: [],
      bankList: [],
      showEdit: false,
      showSyncRate: false,
      optType: 0,
      policyId: ''
    };
  },
  async mounted() {
    this.getBankList();

    const data = await ChannelManageApi.list({ validStatus: 1 });
    this.channelCodes = data;
  },
  methods: {
    hasPurview,

    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    policyEditDone(optType) {
      this.reload();

      if (optType) {
        Modal.confirm({
          title: '提示',
          content: '费率政策修改成功，需要进行政策同步才能生效，是否立即同步政策?',
          onOk: async () => {
            try {
              this.handleSyncRate(this.current.id);
            } catch {
              return console.log('Oops errors!');
            }
          }
        });
      }
    },

    reset() {
      this.where = { policyType: 1 };
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    async handleAdd() {
      const { rateMap } = await RatePolicyApi.personDetail();
      this.current = { allRateList: rateMap || {} };
      this.optType = 0;
      this.showEdit = true;
    },

    async handleEdit(id) {
      const [personDetail, detail] = await Promise.all([RatePolicyApi.personDetail(), RatePolicyApi.detail({ id })]).catch(error => {
        console.log(error);
      });
      this.current = {
        id,
        policyDesc: detail.policyDesc,
        rateList: detail.rateDTOMap || {},
        allRateList: personDetail.rateMap || {},
        userType: detail.userType
      };
      this.optType = 1;
      this.showEdit = true;
    },

    async handleDetail(id) {
      const { rateDTOMap, policyDesc, userType } = await RatePolicyApi.detail({ id });
      this.current = { allRateList: {}, rateList: rateDTOMap || {}, policyDesc, userType };
      this.optType = 2;
      this.showEdit = true;
    },

    handleSyncRate(id) {
      this.policyId = id;
      this.showSyncRate = true;
    },

    async getBankList() {
      const data = await BankCodeManageApi.list();
      this.bankList = data || [];
    },

    async remove(row) {
      const result = await RatePolicyApi.delete({ id: row.id });
      message.success(result.message);
      this.reload();
    },

    datasource({ page, limit, where, orders }) {
      return RatePolicyApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
