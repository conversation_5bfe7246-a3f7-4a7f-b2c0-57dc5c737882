<template>
  <a-modal
    :width="760"
    :visible="visible"
    :confirm-loading="loading"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <template #title>
      {{ modalTitle }}
      <!-- <span v-if="optType === 1 && userType === '3'" style="color: tomato; font-size: 13px">
        (已添加的费率信息不支持编辑，只支持新增；若需要修改存量商户的费率请至“商户列表”页面进行操作)
      </span> -->
    </template>
    <a-form
      ref="form"
      :model="form"
      :label-col="{ md: { span: 17 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 7 }, sm: { span: 24 } }"
    >
      <a-form-item
        label="政策名称"
        name="policyDesc"
        :label-col="{ md: { span: 8 }, sm: { span: 24 } }"
        :wrapper-col="{ md: { span: 16 }, sm: { span: 24 } }"
        :rules="[{ required: true, message: '请填写政策名称' }]"
      >
        <a-input v-model:value="form.policyDesc" placeholder="请填写政策名称" :disabled="isDisabled" />
      </a-form-item>

      <template v-if="optType !== 2">
        <div class="card-title card-title-background">通道费率选择</div>
        <a-checkbox-group
          :value="checkedChannelList"
          name="checkboxgroup"
          :options="checkboxOptions"
          @change="changeCheckedChannel"
          style="margin-bottom: 1em"
        />
      </template>

      <a-tabs v-model:activeKey="activeRateKey">
        <template v-for="(item, key) in form.rateDTOList || []" :key="key">
          <a-tab-pane v-if="checkedChannelList.includes(item.channelCode)" :tab="`${item.channelName}-${item.channelCode}`" :key="item.key">
            <!-- 费率相关 -->
            <template v-for="(rateItem, key2) in item.rateInfoDTO || []" :key="key2">
              <a-checkbox v-if="optType === 1 && rateItem.isAddedTemp" v-model:checked="rateItem.checkedAddedTemp">
                <span style="color: tomato; font-size: 13px">勾选, 即表示默认新增{{ rateItem.templateNo }}, 请检查费率信息后再提交</span>
              </a-checkbox>
              <RateModule
                :class="{ 'temp-added': rateItem.isAddedTemp }"
                :rate-item="rateItem"
                :name-prefix="['rateDTOList', key, 'rateInfoDTO', key2]"
                :disabled="isDisabled"
                :bankList="bankList"
              />
            </template>
          </a-tab-pane>
        </template>
      </a-tabs>
    </a-form>

    <template #footer v-if="isDisabled">
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { RatePolicyApi } from '@/api/businessTeam/rate-policy/RatePolicyApi';
import { RateTemplateApi } from '@/api/businessTeam/rate-template/RateTemplateApi';
import RateModule from '../../_components/RateModule.vue';
import { useUserStore } from '@/store/modules/user';
import { deepCopy } from '@/utils/util';

export default {
  components: { RateModule },
  props: {
    visible: Boolean,
    data: Object,
    channelCodes: Array,
    bankList: Array,
    optType: Number,
    policyType: Number
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      value1: '',
      modalTitle: titleArr[this.optType],
      // 表单数据
      form: {},
      checkedChannel: null,
      checkedChannelList: [],
      checkboxOptions: [],
      activeRateKey: null,
      isDisabled: this.optType === 2,
      loading: false,
      userType: useUserStore().info.userType
    };
  },
  async created() {
    const { id, policyDesc, rateList, allRateList } = this.data;
    let userType = useUserStore().info.userType;

    var merchRateTempList = [];

    if (['3','5'].includes(userType)) {
      const tempList = await RateTemplateApi.list({ validStatus: 1, templateType: 2 });
      // eslint-disable-next-line no-unused-vars
      merchRateTempList = tempList || [];
    }

    if (this.optType) {
      this.form.id = id;
      this.form.policyDesc = policyDesc || '';

      const [allChannel, setedChannel] = [Object.keys(allRateList), Object.keys(rateList)];

      const addedChannel = allChannel.filter(key => !setedChannel.includes(key));

      setedChannel.forEach(code => {
        if (allChannel.includes(code)) {
          const setedTemplateNo = rateList[code].map(r => r.templateNo);
          const allTemplateNo = allRateList[code].map(a => a.templateNo);
          const addedTemplateNo = allTemplateNo.filter(key => !setedTemplateNo.includes(key));

          const addedTemplate = allRateList[code].filter(i => addedTemplateNo.includes(i.templateNo));
          addedTemplate.forEach(t => {
            t.isAddedTemp = true;
          });

          rateList[code].push(...addedTemplate);
        }
      });

      const rateListMap = setedChannel.map(channelCode => {
        if (['3','5'].includes(userType)) {
          rateList[channelCode].forEach(c => {
            if (!c.isAddedTemp) {
              c.isDisabled = true;
            }
          });
        }

        return {
          channelCode,
          noAllowCancel: true,
          rateInfoDTO: rateList[channelCode]
        };
      });

      addedChannel.forEach(e => {
        rateListMap.push({
          channelCode: e,
          rateInfoDTO: allRateList[e]
        });
      });
      rateListMap.forEach(m => {
        m.rateInfoDTO.forEach(a => {
          a.rateInfoDTO.rateType = a.rateType || a.rateInfoDTO.rateType;

          if (['3','5'].includes(userType)) {
            if (a.isAddedTemp || !m.noAllowCancel) {
              const rateTempByChannel = merchRateTempList.filter(m => m.channelCode === a.channelCode);
              rateTempByChannel.forEach(r => {
                if (a.rateInfoDTO.rateType === r.rateInfoDTO.rateType) {
                  if (this.policyType === 2) {
                    a.rateInfoDTO = Object.assign({}, r.rateInfoDTO);
                  }
                }
              });
            }
          }
        });

        m.rateInfoDTO.sort(function (a, b) {
          return a.rateInfoDTO.rateType - b.rateInfoDTO.rateType;
        });
      });

      this.form.rateDTOList = rateListMap;
    } else {
      this.form.rateDTOList = Object.keys(allRateList).map(channelCode => {
        allRateList[channelCode].forEach(a => {
          a.rateInfoDTO.rateType = a.rateType || a.rateInfoDTO.rateType;

          if (['3','5'].includes(userType)) {
            const rateTempByChannel = merchRateTempList.filter(m => m.channelCode === channelCode);
            rateTempByChannel.forEach(r => {
              if (a.rateInfoDTO.rateType === r.rateInfoDTO.rateType) {
                if (this.policyType === 2) {
                  a.rateInfoDTO = Object.assign({}, r.rateInfoDTO);
                }
              }
            });
          }
        });

        allRateList[channelCode].sort(function (a, b) {
          return a.rateInfoDTO.rateType - b.rateInfoDTO.rateType;
        });
        return {
          channelCode,
          rateInfoDTO: allRateList[channelCode]
        };
      });
    }

    const rateChannelList = [];
    this.form.rateDTOList.forEach((r, key) => {
      r.key = String(key);

      r.rateInfoDTO = r.rateInfoDTO.filter(ra => ra.rateType !== 4);

      if (r.rateInfoDTO.length) {
        r.rateInfoDTO.forEach(d => {
          d.rateRatio = 100;
          d.isSame = 1;
          const rateFields = ['withdrawRate', 'withdrawSingleFee'];
          rateFields.forEach(f => (d.rateInfoDTO[f] = 0));
        });
      }

      const item = this.channelCodes.find(i => i.channelCode === r.channelCode);
      if (item) {
        const { channelName, channelCode } = item;
        if (r.rateInfoDTO?.length) {
          rateChannelList.push({
            label: channelName,
            value: channelCode,
            key: String(key),
            disabled: r.noAllowCancel || false
          });
        }
        r.channelName = channelName;
      }
    });

    this.checkboxOptions = rateChannelList;

    if (this.optType) {
      this.checkedChannelList = Object.keys(rateList);
    } else {
      this.checkedChannelList = rateChannelList.map(r => r.value);
    }

    this.activeRateKey = this.form.rateDTOList[0]?.key;
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      if (!this.checkedChannelList.length) return message.warn('请至少提交一个通道费率信息');
      this.form.rateDTOList = this.form.rateDTOList.filter(r => this.checkedChannelList.includes(r.channelCode));

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      const params = deepCopy(this.form);
      let rateList = [];

      params.rateDTOList.forEach(r => {
        r.rateInfoDTO?.forEach(i => {
          i.rateType = i.rateInfoDTO.rateType;
        });

        r.rateInfoDTO = r.rateInfoDTO.filter(i => {
          if (!i.isAddedTemp) return true;
          if (i.checkedAddedTemp) return true;
          return false;
        });
        rateList.push(...(r.rateInfoDTO || []));
      });
      params.rateDTOList = rateList;

      params.policyType = this.policyType;

      if (this.optType) {
        result = RatePolicyApi.edit(params);
      } else {
        result = RatePolicyApi.add(params);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示修改成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done', this.optType);
        })
        .catch(() => {
          this.loading = false;
        });
    },

    changeCheckedChannel(value) {
      // 取差集 (A-B)∪(B-A), 即变化的项
      const a = value;
      const b = this.checkedChannelList;
      const diffValue = a.reduce((u, va) => (u.every(vu => vu != va) ? u.concat(va) : u.filter(vu => vu != va)), b);
      const diffItem = this.checkboxOptions.find(c => c.value === diffValue[0]);

      const isAdd = value.length > this.checkedChannelList.length;
      if (isAdd) {
        this.activeRateKey = diffItem.key;
      } else if (diffItem.key === this.activeRateKey) {
        const index = b.indexOf(diffItem.value);
        let nextTab = b[index + 1] || b[index - 1];
        if (nextTab) {
          const nextTabItem = this.checkboxOptions.find(c => c.value === nextTab);
          this.activeRateKey = nextTabItem.key;
        }
      }

      this.checkedChannelList = value;
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};

// 弹框标题
const titleArr = ['添加政策', '修改政策', '政策详情'];
</script>
<style scoped>
.card-title {
  border-left: 5px solid;
  border-color: var(--primary-color);
  padding-left: 10px;
}
.card-title-background {
  background-color: #f5f5f5;
  height: 2em;
  line-height: 2em;
  margin-bottom: 20px;
}

::v-deep(.ant-form-item-label) {
  text-align: center;
  background-color: #f1f3f4;
}

.temp-added {
  border: 1px dashed red;
}
</style>
