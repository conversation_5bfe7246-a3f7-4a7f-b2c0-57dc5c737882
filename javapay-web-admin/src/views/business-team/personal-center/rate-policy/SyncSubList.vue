<template>
  <div>
    <!-- 搜索表单 -->
    <div>
      <a-card :bordered="false" class="form-card">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 8]">
            <a-form-item label="用户编号">
              <a-input v-model:value.trim="where.userNo" placeholder="请输入用户编号" allow-clear />
            </a-form-item>
            <a-form-item label="用户名称">
              <a-input v-model:value.trim="where.userName" placeholder="请输入用户名称" allow-clear />
            </a-form-item>
            <a-form-item label="费率政策">
              <a-select v-model:value="where.policyNo" placeholder="请选择" style="width: 200px" allow-clear>
                <a-select-option v-for="(item, key) in ratePolicyList" :value="item.policyNo" :key="key">
                  {{ item.policyDesc }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table
          ref="table"
          row-key="userNo"
          :need-page="false"
          :where="where"
          :datasource="datasource"
          :columns="columns"
          size="small"
          v-model:selection="selection"
          :row-selection="rowSelection"
          :scroll="{ x: 'max-content' }"
        />
      </a-card>
    </div>
  </div>
</template>

<script>
import { RatePolicyApi } from '@/api/businessTeam/rate-policy/RatePolicyApi';

export default {
  props: ['policyId'],
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: '政策名称',
          dataIndex: 'policyDesc',
          align: 'center'
        },
        {
          title: '政策编号',
          dataIndex: 'policyNo',
          align: 'center'
        },
        {
          title: '用户名称',
          dataIndex: 'userName'
        },
        {
          title: '用户编号',
          dataIndex: 'userNo'
        }
      ],
      // 表格搜索条件
      where: {},
      selection: [],
      rowSelection: {
        getCheckboxProps: record => {
          return {
            disabled: record.markPolicySame === 1
          };
        },
        preserveSelectedRowKeys: true
      },
      ratePolicyList: []
    };
  },
  watch: {
    selection: {
      handler(value) {
        this.$emit('update:selection', value);
      },
      deep: true
    }
  },
  methods: {
    reload() {
      this.$refs.table.reload();
    },

    reset() {
      this.where.userNo = '';
      this.where.userName = '';
      this.where.policyNo = null;
      this.reload();
    },

    async datasource({ where }) {
      if (!this.ratePolicyList.length) {
        const ratePolicyList = await RatePolicyApi.list({ policyType: 1 });
        this.ratePolicyList = ratePolicyList || [];
      }

      const tableData = await RatePolicyApi.listBranchAndMarkNoSame({ id: this.policyId });

      this.selection = [];
      tableData.forEach(t => {
        if (t.markPolicySame === 1) this.selection.push(t);
      });

      const hasValueFields = Object.keys(where)
        .map(k => where[k] && k)
        .filter(i => !!i);

      const filterTableData = tableData.filter(d => {
        var isEligible = true;
        hasValueFields.forEach(f => {
          isEligible = isEligible && d[f] === where[f];
        });

        return isEligible;
      });

      filterTableData.forEach(d => {
        const item = this.ratePolicyList.find(r => r.policyNo === d.policyNo);
        d.policyDesc = item?.policyDesc || '--';
      });

      return filterTableData || [];
    }
  }
};
</script>
<style scoped>
::v-deep(.form-card) .ant-card-body {
  padding: 0 24px;
}
</style>
