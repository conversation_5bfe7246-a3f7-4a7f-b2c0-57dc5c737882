<template>
  <div class="ele-body ele-body-card">
    <a-row :gutter="16">
      <a-col :lg="18" :md="16" :sm="24" :xs="24">
        <a-card :bordered="false" class="table-height">
          <a-form :layout="activeKey === '2' || activeKey === '0' || activeKey === '5' ? 'horizontal' : 'vertical'">
            <a-tabs v-model:activeKey="activeKey">
              <a-tab-pane key="1" tab="基本信息">
                <!-- 基本信息 -->
                <a-divider orientation="left" :orientationMargin="0" dashed>基本信息</a-divider>
                <a-row :gutter="24">
                  <a-col :md="12" :sm="24" :xs="24">
                    <a-form-item label="企业名称">
                      <a-input v-model:value="form[orgNamePrefixs[Number(loginUser.userType)] + 'Name']" placeholder="企业名称" disabled />
                    </a-form-item>
                    <a-form-item label="统一社会信用代码" name="licenseNo">
                      <a-input v-model:value="form.licenseNo" placeholder="统一社会信用代码" disabled />
                    </a-form-item>
                    <a-form-item label="对公开票税点" name="taxPoint">
                      <a-input-number v-model:value="form.taxPoint" placeholder="对公开票税点" class="ele-fluid" disabled />
                    </a-form-item>
                    <a-form-item label="经营省市区" name="cityCode">
                      <a-cascader v-model:value="areaCodeValue" :options="regionsData" :allow-clear="false" placeholder="--" disabled />
                    </a-form-item>
                  </a-col>
                  <a-col :md="12" :sm="24" :xs="24">
                    <a-form-item label="企业简称">
                      <a-input v-model:value="form[orgNamePrefixs[Number(loginUser.userType)] + 'Sname']" placeholder="企业简称" disabled />
                    </a-form-item>
                    <a-form-item label="企业地址(营业执照注册地址)" name="licenseAddr">
                      <a-textarea
                        v-model:value="form.licenseAddr"
                        placeholder="企业地址"
                        :auto-size="{ minRows: 1, maxRows: 6 }"
                        disabled
                      />
                    </a-form-item>
                    <a-form-item label="企业办公地址" name="officeAddr">
                      <a-textarea
                        v-model:value="form.officeAddr"
                        placeholder="企业办公地址"
                        :auto-size="{ minRows: 1, maxRows: 6 }"
                        disabled
                      />
                    </a-form-item>
                  </a-col>
                </a-row>

                <!-- 法人信息 -->
                <a-divider orientation="left" :orientationMargin="0" dashed>法人信息</a-divider>
                <a-row :gutter="24">
                  <a-col :md="12" :sm="24" :xs="24">
                    <a-form-item label="法人姓名" name="legalName">
                      <a-input v-model:value="form.legalName" placeholder="法人姓名" disabled />
                    </a-form-item>
                    <a-form-item label="法人证件类型" name="legalCertType">
                      <a-select v-model:value="form.legalCertType" class="ele-fluid" placeholder="--" disabled>
                        <a-select-option v-for="({ label, value }, key) in certTypeEnum" :key="key" :value="value">{{
                          label
                        }}</a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :md="12" :sm="24" :xs="24">
                    <a-form-item label="法人手机号" name="legalTelMask">
                      <a-input v-model:value="form.legalTelMask" placeholder="法人手机号" disabled />
                    </a-form-item>
                    <a-form-item label="法人证件号码" name="legalCertNoMask">
                      <a-input v-model:value="form.legalCertNoMask" placeholder="法人证件号码" disabled />
                    </a-form-item>
                  </a-col>
                </a-row>

                <!-- 联系人信息 -->
                <a-divider orientation="left" :orientationMargin="0" dashed>联系人信息</a-divider>
                <a-row :gutter="24">
                  <a-col :md="12" :sm="24" :xs="24">
                    <a-form-item label="联系人姓名" name="contactsName">
                      <a-input v-model:value="form.contactsName" placeholder="联系人姓名" disabled />
                    </a-form-item>
                    <a-form-item label="联系人证件类型" name="contactsCertType">
                      <a-select v-model:value="form.contactsCertType" placeholder="--" class="ele-fluid" disabled>
                        <a-select-option v-for="({ label, value }, key) in certTypeEnum" :key="key" :value="value">{{
                          label
                        }}</a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :md="12" :sm="24" :xs="24">
                    <a-form-item label="联系人手机号(登录手机号)" name="contactsTelMask">
                      <a-input v-model:value="form.contactsTelMask" placeholder="联系人手机号" disabled />
                    </a-form-item>
                    <a-form-item label="联系人证件号码" name="contactsCertNoMask">
                      <a-input v-model:value="form.contactsCertNoMask" placeholder="联系人证件号码" disabled />
                    </a-form-item>
                  </a-col>
                </a-row>

                <a-row :gutter="24">
                  <a-col :md="12" :sm="24" :xs="24">
                    <a-form-item label="签约日期" name="signDate">
                      <a-date-picker :value="form.signDate" valueFormat="YYYY-MM-DD" placeholder="" class="ele-fluid" disabled />
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-tab-pane>

              <a-tab-pane key="2" tab="费率信息">
                <a-form
                  hideRequiredMark
                  :label-col="{ md: { span: 17 }, sm: { span: 24 } }"
                  :wrapper-col="{ md: { span: 7 }, sm: { span: 24 } }"
                >
                  <a-tabs v-model:activeKey="activeRateKey" type="card">
                    <a-tab-pane
                      v-for="(item, key) in form.rateList || []"
                      :tab="`${item.channelName}-${item.channelCode}`"
                      :key="String(key)"
                    >
                      <!-- 费率相关 -->
                      <RateModule
                        v-for="(rateItem, key2) in item.rateInfoDTO || []"
                        :key="key2"
                        :rate-item="rateItem"
                        :bankList="bankList"
                        :showPayWallet="loginUser.userType !== '5'"
                        disabled
                      />
                    </a-tab-pane>
                  </a-tabs>
                </a-form>
              </a-tab-pane>

              <a-tab-pane key="0" tab="政策信息">
                <a-form :label-col="{ style: { width: '130px' } }">
                  <a-tabs v-model:activeKey="activeChannelTabKey" type="card" @change="onChangeChannelTab">
                    <a-tab-pane
                      v-for="(channel, cIndex) in form.policyConfigByChannelGroup || []"
                      :tab="channel.channelName"
                      :key="String(cIndex)"
                      force-render
                      class="tabpane__background"
                    >
                      <a-card :bordered="false">
                        <a-tabs v-model:activeKey="activeTerminalSourceTabKey">
                          <a-tab-pane v-for="tab in channel.channelData || []" :tab="tab.label" :key="String(tab.key)" force-render>
                            <a-divider orientation="left" dashed orientationMargin="0">终端服务费返现政策</a-divider>
                            <template v-if="tab.serviceFeeData?.length">
                              <template v-for="(item, key) in tab.serviceFeeData" :key="key">
                                <a-row v-if="item.show">
                                  <a-col :span="12">
                                    <a-form-item label="服务费金额(元)">
                                      <a-input v-model:value="item.policyName" placeholder="服务费金额" disabled />
                                    </a-form-item>
                                  </a-col>
                                  <a-col :span="12">
                                    <a-form-item label="返现金额(元)">
                                      <a-input v-model:value="item.cashbackAmt" placeholder="请输入返现金额" disabled />
                                    </a-form-item>
                                  </a-col>
                                </a-row>
                              </template>
                            </template>
                            <a-alert v-else message="没有政策配置哦~" banner />

                            <a-divider orientation="left" dashed orientationMargin="0">流量费返现政策</a-divider>
                            <template v-if="tab.simFeeData?.some(s => s.data.length)">
                              <div v-for="(period, idx) in tab.simFeeData" :key="idx">
                                <template v-if="period.show">
                                  <div style="margin-bottom: 10px">
                                    <a-typography-text strong>{{ period.name }}</a-typography-text>
                                  </div>
                                  <template v-for="(item, key) in period.data || []" :key="key">
                                    <a-row v-if="item.show">
                                      <a-col :span="12">
                                        <a-form-item label="流量费金额(元)">
                                          <a-input v-model:value="item.policyName" placeholder="流量费金额" disabled />
                                        </a-form-item>
                                      </a-col>
                                      <a-col :span="12">
                                        <a-form-item label="返现金额(元)">
                                          <a-input v-model:value="item.cashbackAmt" placeholder="请输入返现金额" disabled />
                                        </a-form-item>
                                      </a-col>
                                    </a-row>
                                  </template>
                                </template>
                              </div>
                            </template>
                            <a-alert v-else message="没有政策配置哦~" banner />
                          </a-tab-pane>
                        </a-tabs>
                      </a-card>
                    </a-tab-pane>
                  </a-tabs>
                </a-form>
              </a-tab-pane>

              <a-tab-pane key="3" tab="图片信息">
                <a-row :gutter="24">
                  <a-col :span="6" v-for="(item, key) in form.fileDTOList || []" :key="key">
                    <a-form-item :label="item.label">
                      <a-upload
                        :file-list="[{ url: item.imagePath }]"
                        list-type="picture-card"
                        @preview="() => handlePreview(item.imagePath)"
                        disabled
                      />
                    </a-form-item>
                  </a-col>
                </a-row>
                <!-- 预览图片 -->
                <a-image
                  :style="{ display: 'none' }"
                  :src="previewImage"
                  :preview="{ visible: previewVisible, onVisibleChange: setPreviewVisible }"
                />
              </a-tab-pane>

              <a-tab-pane key="4" tab="结算信息" v-if="settleForm">
                <a-form ref="settleForm" :model="settleForm" layout="vertical">
                  <!-- 结算信息 -->
                  <div style="margin-bottom: 20px">
                    <span style="margin-right: 20px">结算账户类型</span>
                    <a-radio-group v-model:value="settleForm.accountType" disabled>
                      <a-radio value="G">对公</a-radio>
                      <a-radio value="S">对私</a-radio>
                    </a-radio-group>
                  </div>
                  <a-row :gutter="24">
                    <a-col :md="12" :sm="24" :xs="24">
                      <a-form-item label="银行卡账户姓名" name="bankAccountName">
                        <a-input v-model:value="settleForm.bankAccountName" placeholder="银行卡账户姓名" allow-clear disabled />
                      </a-form-item>
                      <a-form-item label="开户行总行" name="typeCode">
                        <a-select
                          v-model:value="settleForm.typeCode"
                          style="width: 100%"
                          placeholder="请选择"
                          :options="bankHeadOffice"
                          :fieldNames="{ label: 'typeName', value: 'typeCode' }"
                          disabled
                        />
                      </a-form-item>
                      <a-form-item label="开户行支行" name="bankChannelNo">
                        <a-select
                          v-model:value="settleForm.bankChannelNo"
                          style="width: 100%"
                          placeholder="请选择"
                          :options="bankSubBranch"
                          :fieldNames="{ label: 'bankName', value: 'bankChannelNo' }"
                          disabled
                        />
                      </a-form-item>
                    </a-col>
                    <a-col :md="12" :sm="24" :xs="24">
                      <a-form-item label="银行卡卡号" name="bankAccountNo">
                        <a-input v-model:value="settleForm.bankAccountNoMask" disabled />
                      </a-form-item>
                      <a-form-item label="开户行所在地区" name="bankCity">
                        <a-cascader v-model:value="bankAreaValue" :options="bankRegionsData" disabled />
                      </a-form-item>
                      <a-form-item label="银行卡预留手机号" name="mobile">
                        <a-input v-model:value="settleForm.mobileMask" disabled />
                      </a-form-item>
                    </a-col>
                  </a-row>
                </a-form>
              </a-tab-pane>

              <a-tab-pane key="5" tab="参数配置" v-if="agentParamConf">
                <a-row v-for="(item, idx) in agentParamConf || []" :key="idx" :gutter="18">
                  <a-col :span="12">
                    <a-form-item label="参数名称">
                      <a-textarea v-model:value.trim="item.key" placeholder="" auto-size disabled />
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item label="参数值">
                      <a-textarea v-model:value.trim="item.value" placeholder="" auto-size disabled />
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-tab-pane>
            </a-tabs>
          </a-form>
        </a-card>
      </a-col>
      <a-col :lg="6" :md="8" :sm="24" :xs="24">
        <a-card :bordered="false" class="table-height">
          <div class="ele-text-center">
            <div class="user-info-avatar-group">
              <a-avatar :size="110" :src="loginUser.avatar" />
            </div>
            <h1>{{ loginUser.username }}</h1>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { AreaApi } from '@/api/base/AreaApi';
import { RegionManageApi } from '@/api/businessTeam/region-center/RegionManageApi';
import { OperationCenterApi } from '@/api/businessTeam/operation-center/OperationCenterApi';
import { AgentCenterApi } from '@/api/businessTeam/agent-center/AgentCenterApi';
import { BankCodeManageApi } from '@/api/base/BankCodeManageApi';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import { certTypeEnum } from '@/config/enumerate';
import RateModule from '../../_components/RateModule.vue';
import { useUserStore } from '@/store/modules/user';
import { BankTypeApi } from '@/api/base/BankTypeApi';
import { BankInfoApi } from '@/api/base/BankInfoApi';
import { deepCopy } from '@/utils/util';
import { ActivityCashbackTemplateManageApi } from '@/api/businessTeam/activity-Config/ActivityCashbackTemplateManageApi';

const simFeePolicyPeriodGroupDef = [
  {
    name: '第一期',
    field: 'firstPeriodList',
    data: []
  },
  {
    name: '第二期',
    field: 'secondPeriodList',
    data: []
  },
  {
    name: '第三期',
    field: 'thirdPeriodList',
    data: []
  },
  {
    name: '标准期 (第四期以及后续阶段)',
    field: 'fourthPeriodList',
    data: []
  }
];

const policyConfigByTerminalSourceDef = [
  {
    label: '全款机配置',
    key: 1,
    serviceFeeData: [],
    simFeeData: []
  },
  {
    label: '分期机配置',
    key: 2,
    serviceFeeData: [],
    simFeeData: []
  }
];

export default {
  name: 'PersonalInfo',
  components: { RateModule },
  data() {
    return {
      form: {},
      settleForm: {},
      activeKey: '1',
      activeRateKey: '0',
      activeTerminalSourceTabKey: '1',
      activeChannelTabKey: '0',
      previewVisible: false,
      bankList: [],
      previewImage: '',
      certTypeEnum,
      regionsData: [],
      areaCodeValue: [],
      bankAreaValue: [],
      bankHeadOffice: [],
      bankSubBranch: [],
      bankRegionsData: [],
      agentParamConf: null,
      imageTypeEnum: [
        { label: '身份证头像面', imageType: 1 },
        { label: '身份证国徽面', imageType: 2 },
        { label: '结算卡正面', imageType: 3 },
        { label: '营业执照照片', imageType: 7 },
        { label: '税务登记证照片', imageType: 8 }
      ],
      orgNamePrefixs: ['', 'region', 'branch', 'agent', '', 'agent'],
      activityCashbackTemplateList: []
    };
  },
  computed: {
    // 当前登录用户信息
    loginUser() {
      const userStore = useUserStore();
      return userStore.$state.info;
    }
  },
  async mounted() {
    // 获取通道列表
    const channelCodes = (await ChannelManageApi.list({ validStatus: 1 })) || [];

    // 获取详情
    const ajaxMethods = ['', RegionManageApi, OperationCenterApi, AgentCenterApi, '', AgentCenterApi];
    const detail = (await ajaxMethods[Number(this.loginUser.userType)].personDetail()) || {};

    if (detail.rateMap) {
      this.form.rateList = [];

      Object.keys(detail.rateMap).forEach(channelCode => {
        const item = channelCodes.find(i => i.channelCode === channelCode);
        const formatData = this.formatRateInfo(detail.rateMap[channelCode]);

        this.form.rateList.push({
          channelCode,
          channelName: item?.channelName,
          rateInfoDTO: formatData
        });
      });
    }

    if (detail.fileDTOList) {
      detail.fileDTOList.forEach(c => {
        const item = this.imageTypeEnum.find(i => i.imageType === c.imageType);
        c.label = item?.label;
      });
    }

    await this.getActivityCashbackTemplateList();

    let serviceFeePolicyDTOMap = {};
    let simFeePolicyDTOMap = {};
    this.activityCashbackTemplateList.forEach(item => {
      // 自身全部服务费
      item.serviceFeePolicyDTOList.forEach(s => {
        serviceFeePolicyDTOMap[s.configId] = s;
      });

      // 自身全部流量费
      Object.keys(item.simFeeNewPolicyDTO || {}).forEach(key => {
        simFeePolicyDTOMap[key] = Object.assign({}, simFeePolicyDTOMap[key] || {});
        item.simFeeNewPolicyDTO[key].forEach(s => {
          simFeePolicyDTOMap[key][s.configId] = s;
        });
      });
    });

    const serviceFeePolicyDTOListMap = detail.serviceFeePolicyDTOList || [];
    serviceFeePolicyDTOListMap.forEach(i => {
      if (!i.parentCashbackAmt) {
        const configItem = serviceFeePolicyDTOMap[i.configId] || {};
        i.parentCashbackAmt = configItem.parentCashbackAmt;
      }

      i.show = Number(i.parentCashbackAmt) > 0 || !(Number(i.serviceFeeAmt) > 0);
    });

    const simFeePolicyMap = detail.simFeeNewPolicyDTO || {};
    const simFeePolicyPeriodGroupMap = deepCopy(simFeePolicyPeriodGroupDef);
    simFeePolicyPeriodGroupMap.forEach(period => {
      Object.keys(simFeePolicyMap).forEach(key => {
        if (period.field === key) {
          period.data = simFeePolicyMap[key] || [];
          period.data.forEach(item => {
            if (!item.parentCashbackAmt) {
              const configItem = (simFeePolicyDTOMap[key] && simFeePolicyDTOMap[key][item.configId]) || {};
              item.parentCashbackAmt = configItem.parentCashbackAmt;
            }

            item.show = Number(item.parentCashbackAmt) > 0 || !(Number(item.simFeeAmt) > 0);
          });
        }
      });
    });

    let allChannelCodes = [];
    try {
      const channelCodes = new Set(
        [...(detail.serviceFeePolicyDTOList || []), ...Object.values(simFeePolicyMap || {})]
          .flat(Infinity)
          .flatMap(item2 => (Array.isArray(item2) ? item2.map(subItem => subItem?.channelCode) : item2?.channelCode))
          .filter(code => code?.trim())
          .map(code => code.trim())
      );
      allChannelCodes = [...channelCodes];
    } catch (error) {
      console.log(error);
    }

    let policyConfigByChannelGroup = [];
    allChannelCodes.forEach(item => {
      const channelItem = channelCodes.find(channel => channel.channelCode === item);
      policyConfigByChannelGroup.push({
        channelCode: item,
        channelName: channelItem?.channelName,
        channelData: deepCopy(policyConfigByTerminalSourceDef)
      });
    });

    policyConfigByChannelGroup.forEach(channel => {
      channel.channelData.forEach(tab => {
        tab.serviceFeeData = serviceFeePolicyDTOListMap.filter(
          item => item.terminalSource === tab.key && item.channelCode === channel.channelCode
        );

        const simFeeData = simFeePolicyPeriodGroupMap.map(period => {
          const periodData = period.data.filter(item => item.terminalSource === tab.key && item.channelCode === channel.channelCode);
          return {
            ...period,
            data: periodData,
            show: periodData.some(item => item.show)
          };
        });

        tab.simFeeData = simFeeData;
      });
    });

    detail.policyConfigByChannelGroup = policyConfigByChannelGroup;

    this.form = Object.assign(detail, this.form);

    this.settleForm = detail.bankCard;
    if (this.settleForm?.typeCode) {
      this.getBankHeadOffice();
      this.bankAreaValue = [this.settleForm.bankProvince, this.settleForm.bankCity];
      if (this.settleForm?.bankCity) {
        this.getBankSubBranch();
      }
    }

    this.areaCodeValue = [detail.provinceCode, detail.cityCode, detail.districtCode];

    // 加载地区
    this.loadAreaData();

    this.getBankList();

    if (detail?.agentParamConf?.paramsMap) {
      const paramsObj = detail.agentParamConf.paramsMap || {};
      let paramsList = [];
      for (let [key, value] of Object.entries(paramsObj)) {
        paramsList.push({ key, value, disabled: key === 'keyname' });
      }
      this.agentParamConf = paramsList;
    }
  },
  methods: {
    async getActivityCashbackTemplateList() {
      const data = await ActivityCashbackTemplateManageApi.displayDetail();
      this.activityCashbackTemplateList = data ? [{ ...data }] : [];
    },

    onChangeChannelTab() {
      this.activeTerminalSourceTabKey = '1';
    },

    handlePreview(imagePath) {
      this.previewImage = imagePath;
      this.setPreviewVisible(true);
    },

    setPreviewVisible(visible) {
      this.previewVisible = visible;
    },

    async loadAreaData(selectedOptions) {
      const targetOption = selectedOptions ? selectedOptions[selectedOptions.length - 1] : { level: 1, code: '' };
      const { level, code } = targetOption;
      const data = await AreaApi.list({ level: level + 1, status: 1, parentCode: code });
      const filterData = data.map(d => {
        return { label: d.areaName, value: d.areaCode, code: d.areaCode, level: d.level, isLeaf: false };
      });

      if (level === 1) {
        this.regionsData = deepCopy(filterData);
        this.bankRegionsData = deepCopy(filterData);
      } else {
        targetOption.children = filterData;
      }

      if (!selectedOptions) {
        const item = this.regionsData.find(r => r.value === this.form.provinceCode);
        if (item) {
          await this.loadAreaData([item]);
          const citem = item.children.find(i => i.value === this.form.cityCode);
          citem && (await this.loadAreaData([citem]));
        }

        const bankItem = this.bankRegionsData.find(r => r.value === this.settleForm?.bankProvince);
        bankItem && this.loadAreaData([bankItem]);
      }
    },
    /**
     * 格式化费率信息
     * @param {Array} data 费率信息
     */
    formatRateInfo(data) {
      const formatData = data ? JSON.parse(JSON.stringify(data).replaceAll(/\brateInfo\b/g, 'rateInfoDTO')) : [];

      formatData.forEach(r => {
        if (r.rateInfo) {
          r.rateInfoDTO = JSON.parse(r.rateInfo);
        } else {
          r.rateInfoDTO = r.rateInfoDTO || {};
        }

        // if (!(r.rateType || r.rateInfoDTO.rateType)) {
        //   if (['1000', '1002', '1001'].includes(r.channelCode)) {
        //     r.rateInfoDTO.rateType = 1;
        //   } else if (r.channelCode === '1003') {
        //     r.rateInfoDTO.rateType = 3;
        //   }
        // }
      });

      formatData.sort(function (a, b) {
        return a.rateInfoDTO.rateType - b.rateInfoDTO.rateType;
      });

      return formatData;
    },
    async getBankHeadOffice() {
      const data = await BankTypeApi.list({ status: 1 });
      this.bankHeadOffice = data || [];
    },

    async getBankSubBranch() {
      this.bankSubBranch = [];
      const data = await BankInfoApi.list({
        status: 1,
        typeCode: this.settleForm.typeCode,
        provinceCode: this.settleForm.bankProvince,
        cityCode: this.settleForm.bankCity
      });
      this.bankSubBranch = data || [];
    },

    async getBankList() {
      const data = await BankCodeManageApi.list();
      this.bankList = data || [];
    }
  }
};
</script>
<style scoped>
/* 用户资料卡片 */
.user-info-avatar-group {
  margin: 16px 0;
  display: inline-block;
  position: relative;
}

.user-info-avatar-group + h1 {
  margin-bottom: 8px;
}

.tabpane__background {
  background-color: #ececec;
  padding: 15px;
}
</style>
