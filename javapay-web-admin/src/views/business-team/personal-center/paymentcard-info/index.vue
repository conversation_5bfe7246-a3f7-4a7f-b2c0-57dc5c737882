<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="银行户名">
              <a-input v-model:value.trim="where.bankAccountName" placeholder="银行卡户名" allow-clear />
            </a-form-item>
            <a-form-item label="银行卡号">
              <a-input v-model:value.trim="where.bankAccountNo" placeholder="银行卡卡号" allow-clear />
            </a-form-item>
            <a-form-item label="联行号">
              <a-input v-model:value.trim="where.bankChannelNo" placeholder="联行号" allow-clear />
            </a-form-item>
            <a-form-item label="清算行行号">
              <a-input v-model:value.trim="where.clearChannelNo" placeholder="清算行行号" allow-clear />
            </a-form-item>
            <a-form-item label="银行名称">
              <a-input v-model:value.trim="where.bankName" placeholder="银行名称" allow-clear />
            </a-form-item>
            <a-form-item label="支行名称">
              <a-input v-model:value.trim="where.bankSubName" placeholder="银行支行名称" allow-clear />
            </a-form-item>
            <a-form-item label="银行预留手机号">
              <a-input v-model:value.trim="where.mobile" placeholder="银行预留手机号" allow-clear />
            </a-form-item>
            <a-form-item label="银行预留身份证号">
              <a-input v-model:value.trim="where.idCardNo" placeholder="银行预留身份证号" allow-clear />
            </a-form-item>
            <a-form-item label="用户编号" v-purview="'0'">
              <a-input v-model:value.trim="where.userNo" placeholder="用户编号" allow-clear />
            </a-form-item>
            <a-form-item label="账户类型">
              <a-select v-model:value="where.accountType" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option value="G">对公</a-select-option>
                <a-select-option value="S">对私</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="卡类型">
              <a-select v-model:value="where.cardType" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">借记卡</a-select-option>
                <a-select-option :value="2">贷记卡</a-select-option>
                <a-select-option :value="3">准贷记卡</a-select-option>
                <a-select-option :value="4">预付费卡</a-select-option>
              </a-select>
            </a-form-item>
            <!-- <a-form-item label="是否结算卡">
              <a-select v-model:value="where.isSettleCard" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">是</a-select-option>
                <a-select-option :value="0">否</a-select-option>
              </a-select>
            </a-form-item> -->
            <a-form-item label="是否签约">
              <a-select v-model:value="where.isSign" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">是</a-select-option>
                <a-select-option :value="0">否</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="有效状态">
              <a-select v-model:value="where.validStatus" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">有效</a-select-option>
                <a-select-option :value="0">无效</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table
          ref="table"
          row-key="id"
          :datasource="datasource"
          :columns="columns"
          :where="where"
          :scroll="{ x: 'max-content' }"
          :need-page="userType === '0'"
        >
          <!-- table上边工具栏 -->
          <template #toolbar>
            <a-space>
              <a-button v-if="cardOptType > 0" type="primary" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新增结算卡</span>
              </a-button>
            </a-space>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'cardType'">
              <a-tag v-if="record.cardType === 1" color="pink">借记卡</a-tag>
              <a-tag v-else-if="record.cardType === 2" color="blue">贷记卡</a-tag>
              <a-tag v-else-if="record.cardType === 3" color="cyan">准贷记卡</a-tag>
              <a-tag v-else-if="record.cardType === 4" color="purple">预付费卡</a-tag>
            </template>
            <template v-else-if="column.key === 'accountType'">
              <a-tag v-if="record.accountType === 'G'" color="orange">对公</a-tag>
              <a-tag v-else-if="record.accountType === 'S'" color="cyan">对私</a-tag>
            </template>
            <template v-else-if="column.key === 'isSettleCard'">
              <a-tag v-if="record.isSettleCard === 1" color="success">是</a-tag>
              <a-tag v-else>否</a-tag>
            </template>
            <template v-else-if="column.key === 'isSign'">
              <a-tag v-if="record.isSign === 1" color="success">是</a-tag>
              <a-tag v-else>否</a-tag>
            </template>
            <template v-else-if="column.key === 'validStatus'">
              <a-tag color="green" v-if="record.validStatus === 1">
                <template #icon> <check-circle-outlined /> </template>有效
              </a-tag>
              <a-tag color="red" v-else>
                <template #icon> <close-circle-outlined /> </template>无效
              </a-tag>
            </template>
            <template v-else-if="column.key === 'userType'">
              <a-badge v-if="record.userType === 1" color="pink" text="大区" />
              <a-badge v-else-if="record.userType === 2" color="blue" text="运营中心" />
              <a-badge v-else-if="record.userType === 3" color="cyan" text="代理商" />
              <a-badge v-else-if="record.userType === 5" color="cyan" text="子级代理商" />
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
                <template v-if="!hasPurview('0')">
                  <a-divider type="vertical" />
                  <a @click="handleEdit(record)">修改</a>
                </template>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 修改结算卡信息 -->
    <PaymentCardEdit
      v-model:visible="showEdit"
      v-if="showEdit"
      :data="current"
      :accountDetail="accountDetail"
      :cardOptType="cardOptType"
      @done="reload"
    />

    <!-- 详情 -->
    <PaymentCardDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import PaymentCardEdit from './PaymentCardEdit.vue';
import PaymentCardDetail from './PaymentCardDetail.vue';
import { PaymentCardApi } from '@/api/businessTeam/paymentcard/PaymentCardApi';
import { RegionManageApi } from '@/api/businessTeam/region-center/RegionManageApi';
import { OperationCenterApi } from '@/api/businessTeam/operation-center/OperationCenterApi';
import { AgentCenterApi } from '@/api/businessTeam/agent-center/AgentCenterApi';
import { hasPurview } from '@/utils/permission';
import { useUserStore } from '@/store/modules/user';

export default {
  name: 'PaymentCardInfo',
  components: {
    PaymentCardEdit,
    PaymentCardDetail
  },
  data() {
    const isHideCol = !hasPurview('0');
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '银行卡户名',
          dataIndex: 'bankAccountName'
        },
        {
          title: '银行卡卡号',
          dataIndex: 'bankAccountNoMask'
        },
        {
          title: '账户类型',
          key: 'accountType',
          dataIndex: 'accountType',
          align: 'center'
        },
        {
          title: '卡类型',
          key: 'cardType',
          dataIndex: 'cardType',
          align: 'center'
        },
        {
          title: '银行名称',
          dataIndex: 'bankName'
        },
        {
          title: '银行行别代码',
          dataIndex: 'typeCode',
          align: 'center'
        },
        {
          title: '银行支行名称',
          dataIndex: 'bankSubName'
        },
        {
          title: '联行号',
          dataIndex: 'bankChannelNo'
        },
        {
          title: ' 银行预留手机号',
          dataIndex: 'mobileMask',
          align: 'center'
        },
        {
          title: '银行预留身份证号',
          dataIndex: 'idCardNoMask',
          align: 'center'
        },
        // {
        //   title: '是否结算卡',
        //   key: 'isSettleCard',
        //   dataIndex: 'isSettleCard',
        //   align: 'center'
        // },
        // {
        //   title: '是否签约',
        //   key: 'isSign',
        //   dataIndex: 'isSign',
        //   align: 'center'
        // },
        // {
        //   title: '开户行所在地',
        //   dataIndex: 'bankProvince',
        //   align: 'center'
        // },
        {
          title: '用户编号',
          dataIndex: 'userNo',
          align: 'center',
          hideCol: isHideCol
        },
        {
          title: '用户类型',
          key: 'userType',
          dataIndex: 'userType',
          align: 'center',
          hideCol: isHideCol
        },
        // {
        //   title: '有效状态',
        //   dataIndex: 'validStatus',
        //   key: 'validStatus',
        //   width: 100,
        //   align: 'center'
        // },
        {
          title: '操作',
          key: 'action',
          width: 160,
          align: 'center',
          fixed: 'right'
        }
      ].filter(i => !i.hideCol),
      // 表格搜索条件
      where: {},
      current: null,
      showEdit: false,
      showDetail: false,
      userType: useUserStore().info.userType,
      accountDetail: {},
      cardOptType: -1
    };
  },
  async mounted() {
    if (this.userType !== '0') {
      const ajaxMethods = ['', RegionManageApi, OperationCenterApi, AgentCenterApi, '', AgentCenterApi];
      const orgNamePrefixs = ['', 'region', 'branch', 'agent', '', 'agent'];
      const detail = (await ajaxMethods[Number(this.userType)].personDetail()) || {};
      this.accountDetail.orgName = detail[orgNamePrefixs[Number(this.userType)] + 'Name'];
      this.accountDetail.legalName = detail.legalName;
      this.accountDetail.legalCertNoMask = detail.legalCertNoMask;
    }
  },
  methods: {
    hasPurview,
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    async datasource({ page, limit, where }) {
      const data = await PaymentCardApi.findPage({ ...where, pageNo: page, pageSize: limit });
      if (!hasPurview('0')) {
        const list = data || [];
        const cardTypeEnumArr = Array.from(new Set(list.map(i => i.accountType).filter(i => !!i)));
        if (!hasPurview('5')) {
          if (cardTypeEnumArr.length) {
            if (cardTypeEnumArr.length == 2) {
              this.cardOptType = -1;
            } else {
              if (cardTypeEnumArr[0] === 'G') {
                this.cardOptType = 1;
              } else {
                this.cardOptType = 2;
              }
            }
          } else {
            this.cardOptType = 3;
          }
        } else {
          if (cardTypeEnumArr.length) {
            this.cardOptType = -1;
          } else {
            this.cardOptType = 4;
          }
        }
      }
      return data;
    }
  }
};
</script>
