<template>
  <a-modal
    :width="900"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="银行卡户名">{{ form.bankAccountName }}</a-descriptions-item>
      <a-descriptions-item label="银行卡卡号">{{ form.bankAccountNoMask }}</a-descriptions-item>
      <a-descriptions-item label="账户类型">
        <a-tag v-if="form.accountType === 'G'" color="orange">对公</a-tag>
        <a-tag v-else-if="form.accountType === 'S'" color="cyan">对私</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="卡类型">
        <a-tag v-if="form.cardType === 1" color="pink">借记卡</a-tag>
        <a-tag v-else-if="form.cardType === 2" color="blue">贷记卡</a-tag>
        <a-tag v-else-if="form.cardType === 3" color="cyan">准贷记卡</a-tag>
        <a-tag v-else-if="form.cardType === 4" color="purple">预付费卡</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="银行名称">{{ form.bankName }}</a-descriptions-item>
      <a-descriptions-item label="银行行别代码">{{ form.typeCode }}</a-descriptions-item>
      <a-descriptions-item label="支行名称">{{ form.bankSubName }}</a-descriptions-item>
      <a-descriptions-item label="联行号">{{ form.bankChannelNo }}</a-descriptions-item>
      <!-- <a-descriptions-item label="清算行行号">{{ form.clearChannelNo }}</a-descriptions-item> -->
      <a-descriptions-item label="银行预留手机号">{{ form.mobileMask }} </a-descriptions-item>
      <a-descriptions-item label="银行预留身份证号">{{ form.idCardNoMask }}</a-descriptions-item>
      <!-- <a-descriptions-item label="是否结算卡">
        <a-tag v-if="form.isSettleCard === 1" color="success">是</a-tag>
        <a-tag v-else>否</a-tag>
      </a-descriptions-item> -->
      <!-- <a-descriptions-item label="是否签约">
        <a-tag v-if="form.isSign === 1" color="success">是</a-tag>
        <a-tag v-else>否</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="开户行所在地">{{ form.bankProvince }}</a-descriptions-item> -->
      <!-- <a-descriptions-item v-any-role="['000', 'superAdmin']" label="用户编号">{{ form.userNo }}</a-descriptions-item>
      <a-descriptions-item v-any-role="['000', 'superAdmin']" label="用户类型">
        <a-badge v-if="form.userType === 1" color="pink" text="大区" />
        <a-badge v-else-if="form.userType === 2" color="blue" text="运营中心" />
        <a-badge v-else-if="form.userType === 3" color="cyan" text="代理商" />
      </a-descriptions-item> -->
      <!-- <a-descriptions-item label="有效状态">
        <a-tag color="green" v-if="form.validStatus === 1">
          <template #icon> <check-circle-outlined /> </template>有效
        </a-tag>
        <a-tag color="red" v-else>
          <template #icon> <close-circle-outlined /> </template>无效
        </a-tag>
      </a-descriptions-item> -->

      <template v-for="(item, key) in fileList" :key="key">
        <a-descriptions-item v-if="item.show" :label="item.label">
          <a-upload v-model:file-list="item.fileData" list-type="picture-card" disabled @preview="() => handlePreview(item)">
            <div v-if="!item.fileData?.length">
              <plus-outlined />
              <div style="margin-top: 8px">图片</div>
            </div>
          </a-upload>
        </a-descriptions-item>
      </template>
    </a-descriptions>

    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>

    <!-- 预览图片 -->
    <a-image :style="{ display: 'none' }" :src="previewImage" :preview="{ visible: previewVisible, onVisibleChange: setPreviewVisible }" />
  </a-modal>
</template>

<script>
import { computed, reactive, toRefs, watchEffect } from 'vue';

export default {
  name: 'PaymentCardDetail',
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {},
      // 文件列表
      fileList: [
        {
          label: '银行卡正面',
          fileType: 3,
          fileData: [],
          show: computed(() => data.form.accountType === 'S')
        },
        {
          label: '对公开户许可证',
          fileType: 16,
          fileData: [],
          show: computed(() => data.form.accountType === 'G')
        }
      ],
      previewImage: '',
      previewVisible: false
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);


        // 处理图片
      const fileListMap = data.form.imageList || [];
      fileListMap.forEach(fileItem => {
        const findItem = data.fileList.find(item => item.fileType === fileItem.imageType);
        if (findItem) {
          findItem.fileData = fileItem.imagePath ? [...findItem.fileData, { url: fileItem.imagePath, id: fileItem.id }] : findItem.fileData;
        }
      });
      }
    });

    const setPreviewVisible = visible => {
      data.previewVisible = visible;
    };

    const handlePreview = ({ fileData }) => {
      data.previewImage = fileData[0].url;
      setPreviewVisible(true);
    };

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible,
      handlePreview,
      setPreviewVisible
    };
  }
};
</script>
