<template>
  <a-modal
    :width="600"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改结算卡' : '新增结算卡'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 6 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 18 }, sm: { span: 24 } }"
    >
      <template v-for="(item, key) in fileList" :key="key">
        <a-form-item v-if="item.show" :label="item.label" required>
          <a-upload
            v-model:file-list="item.fileData"
            accept=".png,.jpg,.jpeg"
            :multiple="false"
            list-type="picture-card"
            :before-upload="file => beforeUpload(file, item)"
            @remove="() => handleRemove(item)"
            @preview="() => handlePreview(item)"
          >
            <div v-if="!item.fileData?.length">
              <plus-outlined />
              <div style="margin-top: 8px">上传</div>
            </div>
          </a-upload>
        </a-form-item>
      </template>

      <a-form-item label="账户类型" name="accountType">
        <a-radio-group v-model:value="form.accountType" name="accountType" :disabled="isUpdate || [1, 2, 4].includes(cardOptType)">
          <a-radio value="G">对公</a-radio>
          <a-radio value="S">对私</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item :label="form.accountType === 'G' ? '企业名称' : '法人姓名'">
        <a-input :value="accountDetail[form.accountType === 'G' ? 'orgName' : 'legalName']" placeholder="" disabled />
      </a-form-item>
      <a-form-item v-if="form.accountType === 'S'" label="法人身份证号">
        <a-input :value="accountDetail.legalCertNoMask" placeholder="" disabled />
      </a-form-item>
      <a-form-item label="银行卡卡号" name="bankAccountNo">
        <a-input v-model:value="form.bankAccountNo" placeholder="请输入银行卡卡号" allow-clear />
      </a-form-item>
      <a-form-item label="开户行所在地" name="bankCity">
        <a-cascader
          v-model:value="areaCodeValue"
          :options="regionsData"
          :load-data="loadAreaData"
          placeholder="请选择"
          :allow-clear="false"
          @change="selectedAreaValue"
        />
      </a-form-item>
      <a-form-item label="开户行总行" name="typeCode">
        <a-select
          v-model:value="form.typeCode"
          style="width: 100%"
          placeholder="请选择"
          @change="typeCodeChange"
          showSearch
          :options="headOffice"
          optionFilterProp="typeName"
          :fieldNames="{ label: 'typeName', value: 'typeCode' }"
        />
      </a-form-item>
      <a-form-item label="开户行支行" name="bankChannelNo">
        <a-select
          v-model:value="form.bankChannelNo"
          style="width: 100%"
          placeholder="请选择"
          :disabled="!(form.bankCity && form.typeCode)"
          @change="(value, option) => selectSubBranch(option)"
          @dropdownVisibleChange="open => open && getSubBranch()"
          showSearch
          :options="subBranch"
          optionFilterProp="bankName"
          :fieldNames="{ label: 'bankName', value: 'bankChannelNo' }"
        />
      </a-form-item>
      <a-form-item label="银行预留手机号" name="mobile">
        <a-input v-model:value="form.mobile" placeholder="请输入银行预留手机号" allow-clear />
      </a-form-item>
    </a-form>

    <!-- 预览图片 -->
    <a-image :style="{ display: 'none' }" :src="previewImage" :preview="{ visible: previewVisible, onVisibleChange: setPreviewVisible }" />
  </a-modal>
</template>

<script>
import { message, Upload } from 'ant-design-vue';
import { AreaApi } from '@/api/base/AreaApi';
import { BankTypeApi } from '@/api/base/BankTypeApi';
import { BankInfoApi } from '@/api/base/BankInfoApi';
import { PaymentCardApi } from '@/api/businessTeam/paymentcard/PaymentCardApi';
import { phoneReg } from '@/utils/validate';
import { computed } from 'vue';
import { compressorImage } from '@/utils/image-compressor-util';
import { OcrApi } from '@/api/base/OcrApi';
import { ProfitVerifyApi } from '@/api/transactionManage/ProfitVerifyApi';
import { useUserStore } from '@/store/modules/user';

export default {
  props: {
    visible: Boolean,
    data: Object,
    accountDetail: Object,
    cardOptType: Number
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: { accountType: 'G' },
      // 表单验证规则
      rules: {
        accountType: [{ required: true, message: '请选择账户类型' }],
        bankAccountNo: [{ required: true, message: '请输入银行卡卡号' }],
        typeCode: [{ required: true, message: '请选择' }],
        bankChannelNo: [{ required: true, message: '请选择' }],
        bankCity: [{ required: true, message: '请选择银行所属地区' }],
        mobile: [
          { required: true, message: '请输入银行预留手机号' },
          { pattern: phoneReg, message: '手机号格式错误', trigger: 'blur' }
        ]
      },
      // 提交状态
      loading: false,
      isUpdate: false,
      regionsData: [],
      areaCodeValue: [],
      headOffice: [],
      subBranch: [],
      // 文件列表
      fileList: [
        {
          label: '银行卡正面',
          fileType: 3,
          fileData: [],
          show: computed(() => this.form.accountType === 'S')
        },
        {
          label: '对公开户许可证',
          fileType: 16,
          fileData: [],
          show: computed(() => this.form.accountType === 'G')
        }
      ],
      previewImage: '',
      previewVisible: false
    };
  },
  computed: {
    // 当前登录用户信息
    loginUser() {
      const userStore = useUserStore();
      return userStore.$state.info;
    }
  },
  async mounted() {
    if (this.data) {
      this.isUpdate = true;
      this.form = Object.assign({}, this.data);
      this.areaCodeValue = [this.form.bankProvince, this.form.bankCity];
      this.form.typeName = this.form.bankName;
      this.subBranch = [
        {
          bankChannelNo: this.form.bankChannelNo,
          bankName: this.form.bankSubName
        }
      ];

      // 处理图片
      const fileListMap = this.data.imageList || [];
      fileListMap.forEach(fileItem => {
        const findItem = this.fileList.find(item => item.fileType === fileItem.imageType);
        if (findItem) {
          findItem.fileData = fileItem.imagePath ? [...findItem.fileData, { url: fileItem.imagePath, id: fileItem.id }] : findItem.fileData;
        }
      });
    } else {
      if ([1, 4].includes(this.cardOptType)) {
        this.form.accountType = 'S';
      }
    }

    this.getHeadOffice();

    await this.loadAreaData();
    if (this.isUpdate) {
      const provinceItem = this.regionsData.find(r => r.value === this.form.bankProvince);
      if (provinceItem) {
        await this.loadAreaData([provinceItem]);
      }
    }
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      await this.validateFileList();

      // 上传图片
      await this.uploadImages();

      this.form.bankAccountName = this.form.accountType === 'G' ? this.accountDetail.orgName : this.accountDetail.legalName;

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      // 执行编辑或修改方法
      if (this.isUpdate) {
        result = PaymentCardApi.edit(this.form);
      } else {
        result = PaymentCardApi.add(this.form);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    /**
     * 上传图片
     */
    async uploadImages() {
      const fileListHasVal = this.fileList.filter(i => i.fileData?.length);
      const noChangeFiles = [];
      const changeedFiles = [];
      fileListHasVal.forEach(i => {
        i.fileData.forEach(j => {
          if (/^(https?:)/.test(j.url)) {
            noChangeFiles.push({
              id: j.id,
              imageType: i.fileType,
              imagePath: j.url
            });
          } else {
            changeedFiles.push({
              fileType: i.fileType,
              suffixType: j.suffixType,
              fileData: j.url
            });
          }
        });
      });

      let imageJsonList = [];
      if (changeedFiles.length) {
        const data = await ProfitVerifyApi.uploadOrgImages({
          fileDTOList: changeedFiles,
          orgNo: this.loginUser.orgCode,
          orgType: this.loginUser.userType
        });
        imageJsonList = data.imageJsonList;
      }

      this.form.imageJsonList = [...noChangeFiles, ...imageJsonList];
    },

    /**
     * 校验图片是否上传
     */
    validateFileList() {
      return new Promise((resolve, reject) => {
        if (this.fileList.every(item => !item.show || item.fileData?.length)) {
          resolve();
        } else {
          message.warn('请上传图片信息');
          reject();
        }
      });
    },

    async ocrBankcard(imgFile) {
      const data = await OcrApi.ocrBankcard({
        imgFile
      });
      const { success, cardNum } = data;
      if (success) {
        this.form.bankAccountNo = cardNum;
      }
    },

    beforeUpload(file, item) {
      compressorImage(file).then(({ url, mime }) => {
        item.fileData = [{ url }];
        item.fileName = file.uid + file.name;
        item.suffixType = mime.split('/')[1];

        switch (item.fileType) {
          case 3:
            this.ocrBankcard(url);
            break;
          default:
            break;
        }
      });

      return Upload.LIST_IGNORE;
    },

    handleRemove(item) {
      item.fileData = [];
      item.suffixType = '';
    },

    handlePreview({ fileData }) {
      this.previewImage = fileData[0].url;
      this.setPreviewVisible(true);
    },

    setPreviewVisible(visible) {
      this.previewVisible = visible;
    },

    async getHeadOffice() {
      const data = await BankTypeApi.list({ status: 1 });
      this.headOffice = data || [];
    },

    async getSubBranch() {
      const data = await BankInfoApi.list({
        status: 1,
        typeCode: this.form.typeCode,
        provinceCode: this.form.bankProvince,
        cityCode: this.form.bankCity
      });
      this.subBranch = data || [];
    },

    selectSubBranch(option) {
      this.form.bankSubName = option?.bankName;
    },

    async loadAreaData(selectedOptions) {
      const targetOption = selectedOptions ? selectedOptions[selectedOptions.length - 1] : { level: 1, code: '' };
      const { level, code } = targetOption;

      const data = await AreaApi.list({ level: level + 1, status: 1, parentCode: code });
      const filterData = data.map(d => {
        return { label: d.areaName, value: d.areaCode, code: d.areaCode, isLeaf: level > 1, level: d.level };
      });

      if (level === 1) {
        this.regionsData = filterData;
      } else {
        targetOption.children = filterData;
      }
    },

    selectedAreaValue(value) {
      [this.form.bankProvince, this.form.bankCity] = value || [];
      this.form.bankChannelNo = null;
    },

    typeCodeChange(value, option) {
      this.form.typeName = option.typeName;
      this.form.bankChannelNo = null;
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>

<style>
.require-mark::before {
  display: inline-block;
  margin-right: 4px;
  font-family: SimSun, sans-serif;
  color: var(--highlight-color);
  font-size: 14px;
  line-height: 1;
  content: '*';
}
</style>
