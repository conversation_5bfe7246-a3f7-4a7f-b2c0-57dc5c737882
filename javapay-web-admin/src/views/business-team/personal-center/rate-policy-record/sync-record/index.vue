<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="原政策编号">
              <a-input v-model:value.trim="where.oldPolicyNo" placeholder="请输入原政策编号" allow-clear />
            </a-form-item>
            <a-form-item label="政策编号">
              <a-input v-model:value.trim="where.policyNo" placeholder="请输入政策编号" allow-clear />
            </a-form-item>
            <a-form-item label="下级用户编号">
              <a-input v-model:value.trim="where.subUserNo" placeholder="请输入下级用户编号" allow-clear />
            </a-form-item>
            <a-form-item v-purview="'0'" label="用户编号">
              <a-input v-model:value.trim="where.userNo" placeholder="请输入用户编号" allow-clear />
            </a-form-item>
            <a-form-item v-purview="'0'" label="用户类型">
              <a-select v-model:value="where.userType" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">大区</a-select-option>
                <a-select-option :value="2">运营中心</a-select-option>
                <a-select-option :value="3">代理商</a-select-option>
                <a-select-option :value="5">子级代理商</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item v-purview="'0'" label="下级用户类型">
              <a-select v-model:value="where.subUserType" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">大区</a-select-option>
                <a-select-option :value="2">运营中心</a-select-option>
                <a-select-option :value="3">代理商</a-select-option>
                <a-select-option :value="4">商户</a-select-option>
                <a-select-option :value="5">子级代理商</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="同步状态">
              <a-select v-model:value="where.syncStatus" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">未同步</a-select-option>
                <a-select-option :value="1">同步成功</a-select-option>
                <a-select-option :value="2">同步失败</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <template v-if="['userType', 'subUserType'].includes(column.key)">
              <a-badge v-if="record[column.key] === 1" color="pink" text="大区" />
              <a-badge v-else-if="record[column.key] === 2" color="blue" text="运营中心" />
              <a-badge v-else-if="record[column.key] === 3" color="cyan" text="代理商" />
              <a-badge v-else-if="record[column.key] === 4" color="orange" text="商户" />
              <a-badge v-else-if="record[column.key] === 5" color="purple" text="子级代理商" />
            </template>

            <template v-else-if="column.key === 'syncStatus'">
              <a-tag v-if="record.syncStatus === 0">未同步</a-tag>
              <a-tag v-else-if="record.syncStatus === 1" color="success">同步成功</a-tag>
              <a-tag v-else-if="record.syncStatus === 2" color="error">同步失败</a-tag>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>
  </div>
</template>

<script>
import { RatePolicyApi } from '@/api/businessTeam/rate-policy/RatePolicyApi';
import { hasPurview } from '@/utils/permission';

export default {
  name: 'RatePolicySyncRecord',
  components: {},
  data() {
    const isHideCol = !hasPurview('0');
    return {
      where: {},
      // 表格列配置
      columns: [
        {
          title: '原政策编号',
          dataIndex: 'oldPolicyNo'
        },
        {
          title: '原政策名称',
          dataIndex: 'oldPolicyDesc'
        },
        {
          title: '政策编号',
          dataIndex: 'policyNo'
        },
        {
          title: '政策名称',
          dataIndex: 'policyDesc'
        },
        {
          title: '下级用户编号',
          dataIndex: 'subUserNo',
          align: 'center'
        },
        {
          title: '用户编号',
          dataIndex: 'userNo',
          align: 'center',
          hideCol: isHideCol
        },
        {
          title: '下级用户类型',
          dataIndex: 'subUserType',
          key: 'subUserType',
          align: 'center',
          hideCol: isHideCol
        },
        {
          title: '用户类型',
          dataIndex: 'userType',
          key: 'userType',
          align: 'center',
          hideCol: isHideCol
        },
        {
          title: '同步状态',
          dataIndex: 'syncStatus',
          key: 'syncStatus',
          align: 'center'
        },
        {
          title: '同步描述',
          dataIndex: 'syncDesc'
        },
        {
          title: '旧版本号',
          dataIndex: 'oldVersion',
          align: 'center'
        },
        {
          title: '同步的版本号',
          dataIndex: 'version',
          align: 'center'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '修改人',
          dataIndex: 'lastModifyUserId'
        },
        {
          title: '修改时间',
          dataIndex: 'lastModifyTime'
        }
      ].filter(i => !i.hideCol)
    };
  },
  methods: {
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    datasource({ page, limit, where }) {
      return RatePolicyApi.findSyncPage({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>
