<template>
  <a-modal
    :width="760"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
  >
    <a-form :label-col="{ md: { span: 7 }, sm: { span: 24 } }" :wrapper-col="{ md: { span: 17 }, sm: { span: 24 } }">
      <a-tabs v-model:activeKey="activeKey" type="card">
        <a-tab-pane key="1" tab="基本信息">
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="政策名称" name="policyDesc">
                <a-input v-model:value="form.policyDesc" disabled />
              </a-form-item>
              <a-form-item v-purview="'0'" label="用户编号" name="userNo">
                <a-input v-model:value="form.userNo" disabled />
              </a-form-item>
              <a-form-item v-purview="'0'" label="用户类型" name="userType">
                <a-select v-model:value="form.userType" disabled>
                  <a-select-option :value="1">大区</a-select-option>
                  <a-select-option :value="2">运营中心</a-select-option>
                  <a-select-option :value="3">代理商</a-select-option>
                  <a-select-option :value="5">子级代理商</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="政策编号" name="policyNo">
                <a-input v-model:value="form.policyNo" disabled />
              </a-form-item>
              <a-form-item v-purview="'0'" label="上级用户编号" name="parentUserNo">
                <a-input v-model:value="form.parentUserNo" disabled />
              </a-form-item>
              <a-form-item label="版本号" name="version">
                <a-input v-model:value="form.version" disabled />
              </a-form-item>
            </a-col>
          </a-row>
        </a-tab-pane>

        <a-tab-pane key="2" tab="费率信息">
          <a-form :label-col="{ md: { span: 17 }, sm: { span: 24 } }" :wrapper-col="{ md: { span: 7 }, sm: { span: 24 } }">
            <a-tabs v-model:activeKey="activeRateKey">
              <a-tab-pane v-for="(item, key) in form.rateList || []" :tab="`${item.channelName}-${item.channelCode}`" :key="String(key)">
                <!-- 费率相关 -->
                <RateModule
                  v-for="(rateItem, key2) in item.rateInfoDTO || []"
                  :key="key2"
                  :rate-item="rateItem"
                  :bankList="bankList"
                  :showPayWallet="showPayWallet"
                  disabled
                />
              </a-tab-pane>
            </a-tabs>
          </a-form>
        </a-tab-pane>
      </a-tabs>
    </a-form>

    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import { BankCodeManageApi } from '@/api/base/BankCodeManageApi';
import { deepCopy } from '@/utils/util';
import RateModule from '@/views/business-team/_components/RateModule.vue';
export default {
  components: { RateModule },
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {},
      channelCodes: [],
      bankList: [],
      activeKey: '1',
      activeRateKey: '0',
      showPayWallet: true
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        const detailInfo = deepCopy(props.detail);

        const channelCodes = detailInfo.rateDTOList?.map(i => i.channelCode) || [];

        const allChannel = Array.from(new Set(channelCodes));

        var rateList = [];

        allChannel.forEach(channelCode => {
          const rateDTOList = detailInfo.rateDTOList.filter(r => r.channelCode === channelCode);

          // rateDTOList.forEach(r => {
          //   if (!(r.rateType || r.rateInfoDTO.rateType)) {
          //     if (['1000', '1002', '1001'].includes(r.channelCode)) {
          //       r.rateInfoDTO.rateType = 1;
          //     } else if (r.channelCode === '1003') {
          //       r.rateInfoDTO.rateType = 3;
          //     }
          //   }
          // });

          rateDTOList.sort(function (a, b) {
            return a.rateInfoDTO.rateType - b.rateInfoDTO.rateType;
          });

          const item = data.channelCodes.find(i => i.channelCode === channelCode);

          rateList.push({
            channelCode,
            channelName: item?.channelName,
            rateInfoDTO: rateDTOList
          });
        });

        detailInfo.rateList = rateList;

        data.form = detailInfo;
        data.activeKey = '1';
        data.activeRateKey = '0';

        data.showPayWallet = ![3, 5].includes(detailInfo.userType);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    const getChannels = async () => {
      const res = await ChannelManageApi.list({ validStatus: 1 });
      data.channelCodes = res || [];
    };
    getChannels();

    const getBankList = async () => {
      const list = await BankCodeManageApi.list();
      data.bankList = list || [];
    };
    getBankList();

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
<style scoped>
.ant-divider-horizontal.ant-divider-with-text {
  margin-top: 0;
}
</style>
