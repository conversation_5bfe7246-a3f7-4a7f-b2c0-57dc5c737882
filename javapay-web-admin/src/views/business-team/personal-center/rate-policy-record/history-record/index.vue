<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="政策编号">
              <a-input v-model:value.trim="where.policyNo" placeholder="请输入政策编号" allow-clear />
            </a-form-item>
            <a-form-item v-purview="'0'" label="上级用户编号">
              <a-input v-model:value.trim="where.parentUserNo" placeholder="请输入上级用户编号" allow-clear />
            </a-form-item>
            <a-form-item label="用户编号" v-purview="'0'">
              <a-input v-model:value.trim="where.userNo" placeholder="请输入用户编号" allow-clear />
            </a-form-item>
            <a-form-item label="版本号">
              <a-input v-model:value.trim="where.version" placeholder="例: 1.0.0" allow-clear />
            </a-form-item>
            <a-form-item v-purview="'0'" label="用户类型">
              <a-select v-model:value="where.userType" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">大区</a-select-option>
                <a-select-option :value="2">运营中心</a-select-option>
                <a-select-option :value="3">代理商</a-select-option>
                <a-select-option :value="5">子级代理商</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'userType'">
              <a-badge v-if="record.userType === 1" color="pink" text="大区" />
              <a-badge v-else-if="record.userType === 2" color="blue" text="运营中心" />
              <a-badge v-else-if="record.userType === 3" color="cyan" text="代理商" />
              <a-badge v-else-if="record.userType === 5" color="orange" text="子级代理商" />
            </template>
            <!-- table操作栏按钮 -->
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <HistoryRecordDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { RatePolicyApi } from '@/api/businessTeam/rate-policy/RatePolicyApi';
import HistoryRecordDetail from './HistoryRecordDetail.vue';
import { hasPurview } from '@/utils/permission';
const isHideCol = !hasPurview('0');

export default {
  name: 'RatePolicyHisRecord',
  components: {
    HistoryRecordDetail
  },
  data() {
    return {
      where: {},
      current: null,
      showDetail: false,
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left',
          hideCol: isHideCol
        },
        {
          title: '政策编号',
          dataIndex: 'policyNo',
          align: 'center'
        },
        {
          title: '政策名称',
          dataIndex: 'policyDesc'
        },
        {
          title: '版本号',
          dataIndex: 'version',
          align: 'center'
        },
        {
          title: '上级用户编号',
          dataIndex: 'parentUserNo',
          align: 'center',
          hideCol: isHideCol
        },
        {
          title: '用户编号',
          dataIndex: 'userNo',
          align: 'center',
          hideCol: isHideCol
        },
        {
          title: '用户类型',
          dataIndex: 'userType',
          key: 'userType',
          align: 'center',
          hideCol: isHideCol
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '修改人',
          dataIndex: 'lastModifyUserId'
        },
        {
          title: '修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          align: 'center',
          width: 100
        }
      ].filter(i => !i.hideCol)
    };
  },
  methods: {
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    async handleDetail(row) {
      const res = await RatePolicyApi.detailHis({ id: row.id });
      this.current = res || {};
      this.showDetail = true;
    },

    datasource({ page, limit, where, orders }) {
      return RatePolicyApi.findHisPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
