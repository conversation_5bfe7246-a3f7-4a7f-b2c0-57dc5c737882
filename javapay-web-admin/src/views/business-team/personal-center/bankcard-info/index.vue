<template>
  <div class="ele-body">
    <a-card :bordered="false" class="table-height">
      <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :need-page="false" :scroll="{ x: 'max-content' }">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'cardType'">
            <a-tag v-if="record.cardType === 1" color="pink">借记卡</a-tag>
            <a-tag v-else-if="record.cardType === 2" color="blue">贷记卡</a-tag>
            <a-tag v-else-if="record.cardType === 3" color="cyan">准贷记卡</a-tag>
            <a-tag v-else-if="record.cardType === 4" color="purple">预付费卡</a-tag>
          </template>

          <template v-else-if="column.key === 'isSettleCard'">
            <span v-if="record.isSettleCard === 1" class="ele-text-success">是</span>
            <span v-else class="ele-text-danger">否</span>
          </template>

          <template v-else-if="column.key === 'isSign'">
            <span v-if="record.isSign === 1" class="ele-text-success">是</span>
            <span v-else class="ele-text-danger">否</span>
          </template>

          <template v-else-if="column.key === 'validStatus'">
            <a-tag color="green" v-if="record.validStatus === 1">
              <template #icon> <check-circle-outlined /> </template>有效
            </a-tag>
            <a-tag color="red" v-else>
              <template #icon> <close-circle-outlined /> </template>无效
            </a-tag>
          </template>

          <template v-else-if="column.key === 'userType'">
            <a-badge v-if="record.userType === 1" color="pink" text="大区" />
            <a-badge v-else-if="record.userType === 2" color="blue" text="运营中心" />
            <a-badge v-else-if="record.userType === 3" color="cyan" text="代理商" />
          </template>

          <template v-else-if="column.key === 'action'">
            <a-space>
              <a @click="handleDetail(record)">详情</a>
            </a-space>
          </template>
        </template>
      </ele-pro-table>
    </a-card>

    <!-- 详情 -->
    <BankCardInfoDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { RatePolicyApi } from '@/api/businessTeam/rate-policy/RatePolicyApi';
import BankCardInfoDetail from './BankCardInfoDetail.vue';

export default {
  name: 'BankCardInfo',
  components: { BankCardInfoDetail },
  data() {
    return {
      current: null,
      showDetail: false,
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '银行户名',
          dataIndex: 'bankAccountName'
        },
        {
          title: '银行账号',
          dataIndex: 'bankAccountNoMask'
        },
        {
          title: '联行号',
          dataIndex: 'bankChannelNo'
        },
        {
          title: '银行名称',
          dataIndex: 'bankName'
        },
        {
          title: '卡类型',
          dataIndex: 'cardType',
          key: 'cardType',
          align: 'center',
          width: 140
        },
        {
          title: '清算行行号',
          dataIndex: 'clearChannelNo'
        },
        {
          title: '银行预留身份证号',
          dataIndex: 'idCardNoMask'
        },
        {
          title: '是否结算卡',
          dataIndex: 'isSettleCard',
          key: 'isSettleCard',
          align: 'center',
          width: 140
        },
        {
          title: '是否签约',
          dataIndex: 'isSign',
          key: 'isSign',
          align: 'center',
          width: 140
        },
        {
          title: '银行预留手机号',
          dataIndex: 'mobileMask'
        },
        {
          title: '名称',
          dataIndex: 'regionName'
        },
        {
          title: '银行行别代码',
          dataIndex: 'typeCode'
        },
        {
          title: '用户编号',
          dataIndex: 'userNo'
        },
        {
          title: '用户类型',
          dataIndex: 'userType',
          key: 'userType',
          align: 'center',
          width: 140
        },
        {
          title: '有效状态',
          dataIndex: 'validStatus',
          key: 'validStatus',
          align: 'center',
          width: 140
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          align: 'center',
          width: 80
        }
      ]
    };
  },
  methods: {
    async datasource() {
      const data = await RatePolicyApi.accountDetail();
      return data || [];
    },
    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    }
  }
};
</script>
