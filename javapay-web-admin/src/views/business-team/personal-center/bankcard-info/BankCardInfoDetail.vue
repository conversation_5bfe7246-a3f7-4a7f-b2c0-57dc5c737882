<template>
  <a-modal
    :width="1000"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
    :footer="null"
  >
    <a-descriptions bordered :column="2">
      <a-descriptions-item label="银行户名">{{ form.bankAccountName }}</a-descriptions-item>
      <a-descriptions-item label="银行账号">{{ form.bankAccountNoMask }}</a-descriptions-item>
      <a-descriptions-item label="联行号">{{ form.bankChannelNo }}</a-descriptions-item>
      <a-descriptions-item label="银行名称">{{ form.bankName }}</a-descriptions-item>
      <a-descriptions-item label="卡类型">
        <a-tag v-if="form.cardType === 1" color="pink">借记卡</a-tag>
        <a-tag v-else-if="form.cardType === 2" color="blue">贷记卡</a-tag>
        <a-tag v-else-if="form.cardType === 3" color="cyan">准贷记卡</a-tag>
        <a-tag v-else-if="form.cardType === 4" color="purple">预付费卡</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="清算行行号">{{ form.clearChannelNo }}</a-descriptions-item>
      <a-descriptions-item label="银行预留身份证号">{{ form.idCardNoMask }}</a-descriptions-item>
      <a-descriptions-item label="银行预留手机号">{{ form.mobileMask }}</a-descriptions-item>
      <a-descriptions-item label="是否结算卡">
        <span v-if="form.isSettleCard === 1" class="ele-text-success">是</span>
        <span v-else class="ele-text-danger">否</span>
      </a-descriptions-item>
      <a-descriptions-item label="是否签约">
        <span v-if="form.isSign === 1" class="ele-text-success">是</span>
        <span v-else class="ele-text-danger">否</span>
      </a-descriptions-item>
      <a-descriptions-item label="银行行别代码">{{ form.typeCode }}</a-descriptions-item>
      <a-descriptions-item label="有效状态">
        <a-tag color="green" v-if="form.validStatus === 1">
          <template #icon> <check-circle-outlined /> </template>有效
        </a-tag>
        <a-tag color="red" v-else>
          <template #icon> <close-circle-outlined /> </template>无效
        </a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="名称">{{ form.regionName }}</a-descriptions-item>
      <a-descriptions-item label="用户编号">{{ form.userNo }}</a-descriptions-item>
      <a-descriptions-item label="用户类型">
        <a-badge v-if="form.userType === 1" color="pink" text="大区" />
        <a-badge v-else-if="form.userType === 2" color="blue" text="运营中心" />
        <a-badge v-else-if="form.userType === 3" color="cyan" text="代理商" />
      </a-descriptions-item>
      <a-descriptions-item label="账户标识">{{ form.accountUuid }}</a-descriptions-item>
    </a-descriptions>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

export default {
  name: 'BankCardInfoDetail',
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
