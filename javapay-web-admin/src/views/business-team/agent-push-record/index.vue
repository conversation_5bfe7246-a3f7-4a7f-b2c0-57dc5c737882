<template>
  <div class="ele-body">
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="通道商户编号">
              <a-input v-model:value.trim="where.chnMerchNo" placeholder="请输入通道商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="终端SN">
              <a-input v-model:value.trim="where.terminalSn" placeholder="请输入终端SN" allow-clear />
            </a-form-item>
            <a-form-item label="终端编号">
              <a-input v-model:value.trim="where.terminalNo" placeholder="请输入终端编号" allow-clear />
            </a-form-item>
            <a-form-item label="订单号">
              <a-input v-model:value.trim="where.orderNo" placeholder="请输入订单号" allow-clear />
            </a-form-item>
            <a-form-item label="接入编号">
              <a-input v-model:value.trim="where.accessNo" placeholder="请输入接入编号" allow-clear />
            </a-form-item>
            <a-form-item label="推送类型">
              <a-select v-model:value="where.pushType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option v-for="([value, key], index) in PushTypeMap" :value="value" :key="index">{{
                  key
                }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="推送状态">
              <a-select v-model:value="where.pushStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">推送失败</a-select-option>
                <a-select-option :value="2">推送成功</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="开始日期">
              <a-date-picker v-model:value="where.searchBeginTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item label="结束日期">
              <a-date-picker v-model:value="where.searchEndTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'pushStatus'">
              <a-tag v-if="record.pushStatus === 1" color="red">推送失败</a-tag>
              <a-tag v-else-if="record.pushStatus === 2" color="green">推送成功</a-tag>
            </template>
            <!-- table操作栏按钮 -->
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
                <a-popconfirm title="确定要重新推送此记录吗？" @confirm="singlePush(record)">
                  <a v-if="hasPurview('0') && record.pushStatus === 1">重推</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <AgentPushRecordDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { AgentPushRecordApi } from '@/api/businessTeam/agent-push-record/AgentPushRecordApi';
import AgentPushRecordDetail from './agent-push-record-detail.vue';
import { message } from 'ant-design-vue';
import { hasPurview } from '@/utils/permission';
import dayjs from 'dayjs';

export default {
  name: 'AgentPushRecord',
  components: {
    AgentPushRecordDetail
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          align: 'center',
          width: 80,
          fixed: 'left'
        },
        {
          title: '接入编号',
          dataIndex: 'accessNo',
          align: 'center'
        },
        {
          title: '用户编号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '通道商户编号',
          dataIndex: 'chnMerchNo',
          align: 'center'
        },
        {
          title: '终端SN',
          dataIndex: 'terminalSn',
          align: 'center'
        },
        {
          title: '终端编号',
          dataIndex: 'terminalNo',
          align: 'center'
        },
        {
          title: '订单号',
          dataIndex: 'orderNo',
          align: 'center'
        },
        {
          title: '推送类型',
          dataIndex: 'pushType',
          align: 'center',
          customRender: ({ text }) => PushTypeMap.get(text) || text
        },
        {
          title: ' 已推送次数',
          dataIndex: 'pushCount',
          align: 'center'
        },
        {
          title: '推送状态',
          dataIndex: 'pushStatus',
          key: 'pushStatus',
          align: 'center'
        },
        {
          title: '通知地址',
          dataIndex: 'httpUrl',
          align: 'center'
        },
        {
          title: '通道key',
          dataIndex: 'accessKey',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 160,
          align: 'center'
        }
      ],
      // 表格搜索条件
      where: {
        searchBeginTime: dayjs().format('YYYY-MM-DD'),
        searchEndTime: dayjs().format('YYYY-MM-DD')
      },
      // 当前编辑数据
      current: null,
      // 是否显示编辑弹窗
      showDetail: false,
      PushTypeMap
    };
  },
  methods: {
    async singlePush(row) {
      const result = await AgentPushRecordApi.singlePush({ id: row.id });
      message.success(result.message);
      this.reload();
    },

    hasPurview,

    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {
        searchBeginTime: dayjs().format('YYYY-MM-DD'),
        searchEndTime: dayjs().format('YYYY-MM-DD')
      };
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where, orders }) {
      return AgentPushRecordApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};

const PushTypeMap = new Map([
  [1, '商户报备通知'],
  [2, '终端绑定通知'],
  [3, '终端撤机通知'],
  [4, '商户交易通知'],
  [5, '订单支付通知'],
  [6, '变更通道商户结算信息通知'],
  [7, '商户提现通知']
]);
</script>
