<template>
  <a-modal :width="800" :visible="visible" title="详情" :body-style="{ paddingBottom: '8px' }" @update:visible="updateVisible">
    <a-descriptions :column="2">
      <a-descriptions-item label="接入编号">{{ form.accessNo }}</a-descriptions-item>
      <a-descriptions-item label="通道key">{{ form.accessKey }}</a-descriptions-item>
      <a-descriptions-item label="推送类型">{{ PushTypeMap.get(form.pushType) || form.pushType }}</a-descriptions-item>
      <a-descriptions-item label="推送状态">
        <a-tag v-if="form.pushStatus === 1" color="red">推送失败</a-tag>
        <a-tag v-else-if="form.pushStatus === 2" color="green">推送成功</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="已推送次数">{{ form.pushCount }}</a-descriptions-item>
      <a-descriptions-item label="通知地址" :span="2">{{ form.httpUrl }}</a-descriptions-item>
      <a-descriptions-item label="推送内容" :span="2">{{ form.pushData }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

const PushTypeMap = new Map([
  [1, '商户报备通知'],
  [2, '终端绑定通知'],
  [3, '终端撤机通知'],
  [4, '商户交易通知'],
  [5, '订单支付通知'],
  [6, '变更通道商户结算信息通知'],
  [7, '商户提现通知']
]);
export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {},
      PushTypeMap
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
