<template>
  <a-modal
    :width="1000"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="付款人用户编号">{{ form.payerUserNo }}</a-descriptions-item>
      <a-descriptions-item label="付款人用户类型">
        <a-badge v-if="form.payerUserType === 1" color="pink" text="大区" />
        <a-badge v-else-if="form.payerUserType === 2" color="blue" text="运营中心" />
        <a-badge v-else-if="form.payerUserType === 3" color="cyan" text="代理商" />
        <a-badge v-else-if="form.payerUserType === 5" color="orange" text="子级代理商" />
      </a-descriptions-item>
      <a-descriptions-item label="收款人用户编号">{{ form.payeeUserNo }}</a-descriptions-item>
      <a-descriptions-item label="收款人用户类型">
        <a-badge v-if="form.payeeUserType === 1" color="pink" text="大区" />
        <a-badge v-else-if="form.payeeUserType === 2" color="blue" text="运营中心" />
        <a-badge v-else-if="form.payeeUserType === 3" color="cyan" text="代理商" />
        <a-badge v-else-if="form.payeeUserType === 5" color="orange" text="子级代理商" />
      </a-descriptions-item>
      <a-descriptions-item label="一级代理商编号">{{ form.oneLevelAgentNo }}</a-descriptions-item>
      <a-descriptions-item label="银行账号">{{ form.bankAccountNoMask }}</a-descriptions-item>
      <a-descriptions-item label="银行名称">{{ form.bankName }}</a-descriptions-item>
      <a-descriptions-item label="银行户名">{{ form.bankAccountName }}</a-descriptions-item>
      <a-descriptions-item label="银行账户类型">
        <a-tag v-if="form.bankAccountType === 'G'" color="orange">对公</a-tag>
        <a-tag v-else-if="form.bankAccountType === 'S'" color="blue">对私</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="代付金额">{{ form.payAmount }}</a-descriptions-item>
      <a-descriptions-item label="代付费率">{{ form.payRate }}</a-descriptions-item>
      <a-descriptions-item label="代付单笔费用">{{ form.paySingleFee }}</a-descriptions-item>
      <a-descriptions-item label="代付手续费">{{ form.payFee }}</a-descriptions-item>
      <a-descriptions-item label="应付金额">{{ form.payableAmount }}</a-descriptions-item>
      <a-descriptions-item label="到账状态">
        <a-tag v-if="form.receivedStatus === 0">已创建</a-tag>
        <a-tag v-else-if="form.receivedStatus === 1" color="blue">出款中</a-tag>
        <a-tag v-else-if="form.receivedStatus === 2" color="green">已到账</a-tag>
        <a-tag v-else-if="form.receivedStatus === 3" color="red">出款失败</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="到账时间">{{ form.receivedTime }}</a-descriptions-item>
      <a-descriptions-item label="代付记录来源">
        <a-tag v-if="form.paySource === 1">机构钱包提现</a-tag>
        <a-tag v-else-if="form.paySource === 2">APP机构代付</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="代付流水单号">{{ form.payFlowNo }}</a-descriptions-item>
      <a-descriptions-item label="是否钱包扣除">
        <a-tag v-if="form.isWalletDeduct === 1" color="blue">是</a-tag>
        <a-tag v-else-if="form.isWalletDeduct === 0">否</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="是否计算手续费">
        <a-tag v-if="form.isCalculateFee === 1" color="blue">是</a-tag>
        <a-tag v-else-if="form.isCalculateFee === 0">否</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="返回编码">{{ form.resCode }}</a-descriptions-item>
      <a-descriptions-item label="返回描述">{{ form.resDesc }}</a-descriptions-item>
      <a-descriptions-item label="代付通道">{{ form.payChannelName }}</a-descriptions-item>
      <a-descriptions-item label="代付通道出款状态">
        <a-tag v-if="form.payChannelStatus === '0'">初始化</a-tag>
        <a-tag v-if="form.payChannelStatus === '1'" color="blue">提交成功</a-tag>
        <a-tag v-if="form.payChannelStatus === '2'" color="pink">提交失败</a-tag>
        <a-tag v-if="form.payChannelStatus === '3'" color="green">出款成功</a-tag>
        <a-tag v-if="form.payChannelStatus === '4'" color="red">出款失败</a-tag>
        <a-tag v-if="form.payChannelStatus === '5'" color="blue">处理中</a-tag>
        <a-tag v-if="form.payChannelStatus === '6'" color="red">确定失败</a-tag>
        <a-tag v-if="form.payChannelStatus === '7'" color="blue">重新出款</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="通道订单编号">{{ form.channelOrderNo }}</a-descriptions-item>
      <a-descriptions-item label="通道机构账户号">{{ form.channelAccountNo }}</a-descriptions-item>
      <a-descriptions-item label="通道订单编号">{{ form.channelOrderNo }}</a-descriptions-item>
      <a-descriptions-item label="通道返回编码">{{ form.channelResCode }}</a-descriptions-item>
      <a-descriptions-item label="通道返回描述">{{ form.channelResDesc }}</a-descriptions-item>
      <a-descriptions-item label="付款用户钱包">{{ form.walletUuid }}</a-descriptions-item>
      <a-descriptions-item label="付款用户账户">{{ form.accountUuid }}</a-descriptions-item>
      <a-descriptions-item label="付款用户钱包类型">
        <a-tag v-if="form.walletType === '300'">营销奖励钱包</a-tag>
        <a-tag v-else-if="form.walletType === '600'">分润钱包</a-tag>
        <a-tag v-else-if="form.walletType === '800'">代付D0钱包</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="钱包扣除流水号">{{ form.walletDeductFlowNo }}</a-descriptions-item>
      <a-descriptions-item label="代付用途备注">{{ form.usageRemark }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';
import { hasPurview } from '@/utils/permission';

export default {
  props: {
    visible: Boolean,
    detail: Object,
    channelCodes: Array
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      hasPurview,
      updateVisible
    };
  }
};
</script>
