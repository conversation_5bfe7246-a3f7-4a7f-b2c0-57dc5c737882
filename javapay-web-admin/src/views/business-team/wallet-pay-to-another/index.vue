<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="收款人用户编号">
              <a-input v-model:value.trim="where.payeeUserNo" placeholder="请输入收款人用户编号" allow-clear />
            </a-form-item>
            <a-form-item label="收款人银行户名">
              <a-input v-model:value.trim="where.bankAccountName" placeholder="请输入收款人银行户名" allow-clear />
            </a-form-item>
            <a-form-item label="付款人用户类型" v-if="hasPurview['0']">
              <a-select v-model:value="where.payerUserType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">大区</a-select-option>
                <a-select-option :value="2">运营中心</a-select-option>
                <a-select-option :value="3">代理商</a-select-option>
                <a-select-option :value="5">子级代理商</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="付款人用户编号">
              <a-input v-model:value.trim="where.payerUserNo" placeholder="请输入付款人用户编号" allow-clear />
            </a-form-item>
            <a-form-item label="代付流水单号">
              <a-input v-model:value.trim="where.payFlowNo" placeholder="请输入代付流水单号" allow-clear />
            </a-form-item>
            <a-form-item label="通道订单编号">
              <a-input v-model:value.trim="where.channelOrderNo" placeholder="请输入通道订单编号" allow-clear />
            </a-form-item>
            <a-form-item label="代付记录来源">
              <a-select v-model:value="where.paySource" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">机构钱包提现</a-select-option>
                <a-select-option :value="2">APP机构代付</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="到账状态">
              <a-select v-model:value="where.receivedStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">已创建</a-select-option>
                <a-select-option :value="1">出款中</a-select-option>
                <a-select-option :value="2">已到账</a-select-option>
                <a-select-option :value="3">出款失败</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="代付通道">
              <a-select v-model:value="where.payChannelCode" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option v-for="({ channelName, channelNo }, key) in channelCodes" :key="key" :value="channelNo">
                  {{ channelName }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="是否钱包扣除">
              <a-select v-model:value="where.isWalletDeduct" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">是</a-select-option>
                <a-select-option :value="0">否</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="是否计算手续费">
              <a-select v-model:value="where.isCalculateFee" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">是</a-select-option>
                <a-select-option :value="0">否</a-select-option>
              </a-select>
            </a-form-item>
            <!-- <a-form-item label="开始日期">
              <a-date-picker v-model:value="where.searchBeginTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item label="结束日期">
              <a-date-picker v-model:value="where.searchEndTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item> -->
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #toolbar>
            <a-descriptions title="提现信息" :column="4" size="small" style="margin-bottom: 10px">
              <a-descriptions-item label="代付总笔数">{{ summary.payToAnotherCount }}</a-descriptions-item>
              <a-descriptions-item label="代付总金额(元)">{{ summary.sumAmount }}</a-descriptions-item>
              <a-descriptions-item label="代付总手续费(元)">{{ summary.sumFee }}</a-descriptions-item>
              <a-descriptions-item label="应付总金额(元)">{{ summary.sumPayable }}</a-descriptions-item>
            </a-descriptions>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'bankAccountType'">
              <a-tag v-if="record.bankAccountType === 'G'" color="orange">对公</a-tag>
              <a-tag v-else-if="record.bankAccountType === 'S'" color="blue">对私</a-tag>
            </template>
            <template v-else-if="column.key === 'isWalletDeduct'">
              <a-tag v-if="record.isWalletDeduct === 1" color="blue">是</a-tag>
              <a-tag v-else-if="record.isWalletDeduct === 0">否</a-tag>
            </template>
            <template v-else-if="column.key === 'isCalculateFee'">
              <a-tag v-if="record.isCalculateFee === 1" color="blue">是</a-tag>
              <a-tag v-else-if="record.isCalculateFee === 0">否</a-tag>
            </template>
            <template v-else-if="column.key === 'receivedStatus'">
              <a-tag v-if="record.receivedStatus === 0">已创建</a-tag>
              <a-tag v-else-if="record.receivedStatus === 1" color="blue">出款中</a-tag>
              <a-tag v-else-if="record.receivedStatus === 2" color="green">已到账</a-tag>
              <a-tag v-else-if="record.receivedStatus === 3" color="red">出款失败</a-tag>
            </template>
            <template v-else-if="column.key === 'paySource'">
              <a-tag v-if="record.paySource === 1">机构钱包提现</a-tag>
              <a-tag v-else-if="record.paySource === 2">APP机构代付</a-tag>
            </template>
            <template v-else-if="column.key === 'walletType'">
              <a-tag v-if="record.walletType === '300'">营销奖励钱包</a-tag>
              <a-tag v-else-if="record.walletType === '600'">分润钱包</a-tag>
              <a-tag v-else-if="record.walletType === '800'">代付D0钱包</a-tag>
            </template>
            <template v-else-if="column.key === 'payerUserType'">
              <a-badge v-if="record.payerUserType === 1" color="pink" text="大区" />
              <a-badge v-else-if="record.payerUserType === 2" color="blue" text="运营中心" />
              <a-badge v-else-if="record.payerUserType === 3" color="cyan" text="代理商" />
              <a-badge v-else-if="record.payerUserType === 5" color="orange" text="子级代理商" />
            </template>
            <template v-else-if="column.key === 'payeeUserType'">
              <a-badge v-if="record.payeeUserType === 1" color="pink" text="大区" />
              <a-badge v-else-if="record.payeeUserType === 2" color="blue" text="运营中心" />
              <a-badge v-else-if="record.payeeUserType === 3" color="cyan" text="代理商" />
              <a-badge v-else-if="record.payeeUserType === 5" color="orange" text="子级代理商" />
            </template>
            <template v-else-if="column.key === 'payChannelStatus'">
              <a-tag v-if="record.payChannelStatus === '0'">初始化</a-tag>
              <a-tag v-if="record.payChannelStatus === '1'" color="blue">提交成功</a-tag>
              <a-tag v-if="record.payChannelStatus === '2'" color="pink">提交失败</a-tag>
              <a-tag v-if="record.payChannelStatus === '3'" color="green">出款成功</a-tag>
              <a-tag v-if="record.payChannelStatus === '4'" color="red">出款失败</a-tag>
              <a-tag v-if="record.payChannelStatus === '5'" color="blue">处理中</a-tag>
              <a-tag v-if="record.payChannelStatus === '6'" color="red">确定失败</a-tag>
              <a-tag v-if="record.payChannelStatus === '7'" color="blue">重新出款</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
                <template v-if="record.isCalculateFee === 1">
                  <a-divider type="vertical" />
                  <a @click="handleTransFeeDetail(record)">手续费详情</a>
                </template>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <WalletPayToAnotherDetail v-model:visible="showDetail" :detail="current" />
    <!-- 手续费详情 -->
    <TransFeesDetail v-model:visible="showTransFeeDetail" :detail="feeDetail" />
  </div>
</template>

<script>
import { WalletPayToAnotherApi } from '@/api/businessTeam/wallet-pay-to-another/WalletPayToAnotherApi';
import WalletPayToAnotherDetail from './wallet-pay-to-another-detail.vue';
import TransFeesDetail from './trans-fees-detail.vue';
import { RemitChannelApi } from '@/api/account/remit-channel/RemitChannelApi';
import { hasPurview } from '@/utils/permission';
import { WithdrawManageApi } from '@/api/businessTeam/withdraw-manage/WithdrawManageApi';

export default {
  name: 'WalletPayToAnother',
  components: {
    WalletPayToAnotherDetail,
    TransFeesDetail
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '付款人用户编号',
          dataIndex: 'payerUserNo'
        },
        {
          title: '付款人用户类型',
          dataIndex: 'payerUserType',
          key: 'payerUserType',
          align: 'center',
          hideCol: !hasPurview(['0'])
        },
        {
          title: '收款人用户编号',
          dataIndex: 'payeeUserNo'
        },
        {
          title: '收款人用户类型',
          dataIndex: 'payeeUserType',
          key: 'payeeUserType',
          align: 'center',
          hideCol: !hasPurview(['0'])
        },
        {
          title: '一级代理商编号',
          dataIndex: 'oneLevelAgentNo',
          align: 'center',
          hideCol: hasPurview(['3', '5'])
        },
        {
          title: '银行账号',
          dataIndex: 'bankAccountNoMask'
        },
        {
          title: '银行名称',
          dataIndex: 'bankName'
        },
        {
          title: '银行户名',
          dataIndex: 'bankAccountName'
        },
        {
          title: '银行账户类型',
          dataIndex: 'bankAccountType',
          key: 'bankAccountType',
          align: 'center'
        },
        {
          title: '代付金额',
          dataIndex: 'payAmount',
          align: 'center'
        },
        {
          title: '代付费率',
          dataIndex: 'payRate',
          align: 'center'
        },
        {
          title: '代付单笔费用',
          dataIndex: 'paySingleFee',
          align: 'center'
        },
        {
          title: '代付手续费',
          dataIndex: 'payFee',
          align: 'center'
        },
        {
          title: '应付金额',
          dataIndex: 'payableAmount',
          align: 'center'
        },
        {
          title: '到账状态',
          dataIndex: 'receivedStatus',
          key: 'receivedStatus',
          align: 'center'
        },
        {
          title: '到账时间',
          dataIndex: 'receivedTime'
        },
        {
          title: '代付记录来源',
          dataIndex: 'paySource',
          key: 'paySource',
          align: 'center'
        },
        {
          title: '代付流水单号',
          dataIndex: 'payFlowNo'
        },
        {
          title: '是否钱包扣除',
          dataIndex: 'isWalletDeduct',
          key: 'isWalletDeduct',
          align: 'center'
        },
        {
          title: '是否计算手续费',
          dataIndex: 'isCalculateFee',
          key: 'isCalculateFee',
          align: 'center'
        },
        {
          title: '返回编码',
          dataIndex: 'resCode'
        },
        {
          title: '返回描述',
          dataIndex: 'resDesc',
          width: 200
        },
        {
          title: '代付通道',
          dataIndex: 'payChannelName'
        },
        {
          title: '代付通道出款状态',
          dataIndex: 'payChannelStatus',
          key: 'payChannelStatus',
          align: 'center'
        },
        {
          title: '通道订单编号',
          dataIndex: 'channelOrderNo'
        },
        {
          title: '通道机构账户号',
          dataIndex: 'channelAccountNo'
        },
        {
          title: '通道订单编号',
          dataIndex: 'channelOrderNo'
        },
        {
          title: '通道返回编码',
          dataIndex: 'channelResCode'
        },
        {
          title: '通道返回描述',
          dataIndex: 'channelResDesc',
          width: 200
        },
        {
          title: '付款用户钱包',
          dataIndex: 'walletUuid'
        },
        {
          title: '付款用户账户',
          dataIndex: 'accountUuid'
        },
        {
          title: '付款用户钱包类型',
          dataIndex: 'walletType',
          key: 'walletType'
        },
        {
          title: '钱包扣除流水号',
          dataIndex: 'walletDeductFlowNo'
        },
        {
          title: '代付用途备注',
          dataIndex: 'usageRemark'
        },

        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 180,
          align: 'center'
        }
      ].filter(i => !i.hideCol),
      // 表格搜索条件
      where: {},
      current: null,
      showDetail: false,
      channelCodes: [],
      summary: {},
      feeDetail: null,
      showTransFeeDetail: false
    };
  },
  async mounted() {
    this.getSummaryData();
    const data = await RemitChannelApi.findAll({ validStatus: 1, remitChannelClassify: 2 });
    this.channelCodes = data || [];
  },
  methods: {
    reload() {
      this.getSummaryData();
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
      this.getSummaryData();
    },
    async handleTransFeeDetail(row) {
      const data = await WithdrawManageApi.feeDetail({ id: row.id, feeType: 2 });
      this.feeDetail = data || {};
      this.showTransFeeDetail = true;
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    /** 统计信息 */
    async getSummaryData() {
      this.summary = (await WalletPayToAnotherApi.sum(this.where)) || {};
    },

    datasource({ page, limit, where, orders }) {
      return WalletPayToAnotherApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    },

    hasPurview
  }
};
</script>
