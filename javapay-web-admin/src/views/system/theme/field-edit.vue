<template>
  <a-modal
    :width="600"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改属性' : '新建属性'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form
      layout="horizontal"
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 4 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 20 }, sm: { span: 24 } }"
    >
      <a-form-item label="属性名称:" name="fieldName">
        <a-input v-model:value="form.fieldName" placeholder="请输入属性名称" allow-clear autocomplete="off" />
      </a-form-item>

      <a-form-item label="属性编码:" name="fieldCode">
        <a-input v-model:value="form.fieldCode" placeholder="请输入属性编码" allow-clear autocomplete="off" :disabled="isUpdate" />
      </a-form-item>

      <a-form-item label="属性类型:" name="fieldType">
        <a-select v-model:value="form.fieldType" placeholder="请选择属性类型" allow-clear autocomplete="off">
          <a-select-option value="string">字符类型</a-select-option>
          <a-select-option value="file">文件类型</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="是否必填" name="fieldRequired">
        <a-select v-model:value="form.fieldRequired" placeholder="请选择是否必填" allow-clear autocomplete="off">
          <a-select-option value="Y">必填</a-select-option>
          <a-select-option value="N">非必填</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="属性长度" name="fieldLength" v-if="showFieldLength">
        <a-input-number v-model:value="form.fieldLength" style="width: 100%" palceholder="请输入属性长度" allow-clear autocomplete />
      </a-form-item>

      <a-form-item label="属性描述" name="fieldDescription">
        <a-textarea v-model:value="form.fieldDescription" placeholder="请输入属性描述" :auto-size="{ minRows: 3, maxRows: 5 }" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { ThemeTemplateFieldApi } from '@/api/system/theme/ThemeTemplateFieldApi';

export default {
  name: 'ThemeTemplateFieldEdit',
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: Object.assign({}, this.data),
      // 表单验证规则
      rules: {
        fieldName: [{ required: true, message: '请输入属性名称', type: 'string', trigger: 'blur' }],
        fieldCode: [{ required: true, message: '请输入属性编码', type: 'string', trigger: 'blur' }],
        fieldType: [{ required: true, message: '请输入属性类型', type: 'string', trigger: 'blur' }],
        fieldRequired: [{ required: true, message: '请选择是否必填', type: 'string', trigger: 'blur' }],
        fieldLength: [{ message: '请输入属性长度', type: 'number', trigger: 'blur' }],
        fieldDescription: [{ message: '请输入属性描述', type: 'string', trigger: 'blur' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false
    };
  },
  computed: {
    /**
     * 属性长度是否禁用，当点击文件类型时禁用
     *
     * <AUTHOR>
     * @date 2022/1/1 14:53
     */
    showFieldLength() {
      if (this.form.fieldType === 'file') {
        return false;
      } else {
        return true;
      }
    }
  },
  watch: {
    data() {
      if (this.data) {
        this.form = Object.assign({}, this.data);
        this.isUpdate = true;
      } else {
        this.form = {};
        this.isUpdate = false;
      }
      if (this.$refs.form) {
        this.$refs.form.clearValidate();
      }
    }
  },
  methods: {
    /**
     * 保存和修改属性
     *
     * <AUTHOR>
     * @date 2021/12/27 11:43:02
     */
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      // 执行编辑或修改
      if (this.isUpdate) {
        result = ThemeTemplateFieldApi.edit(this.form);
      } else {
        result = ThemeTemplateFieldApi.add(this.form);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 如果是新增，则form表单置空
          if (!this.isUpdate) {
            this.form = {};
          }

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    /**
     * 更新编辑界面的弹框是否显示
     *
     * <AUTHOR>
     * @date 2021/12/27 11:43:44
     */
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>

<style scoped></style>
