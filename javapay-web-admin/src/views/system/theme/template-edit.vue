<template>
  <a-modal
    :width="600"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改模板' : '新建模板'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form
      layout="horizontal"
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 4 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 20 }, sm: { span: 24 } }"
    >
      <a-form-item label="模板名称:" name="templateName">
        <a-input v-model:value="form.templateName" placeholder="请输入模板名称" allow-clear autocomplete="off" />
      </a-form-item>

      <a-form-item label="模板编码:" name="templateCode">
        <a-input v-model:value="form.templateCode" placeholder="请输入模板编码" allow-clear autocomplete="off" />
      </a-form-item>

      <a-form-item label="模板类型:" name="templateType">
        <a-select v-model:value="form.templateType" placeholder="请选择模板类型" allow-clear autocomplete="off">
          <a-select-option :value="1">系统类型</a-select-option>
          <a-select-option :value="2">业务类型</a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { ThemeTemplateApi } from '@/api/system/theme/ThemeTemplateApi';

export default {
  name: 'TemplateEdit',
  components: {},
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: Object.assign({}, this.data),
      rules: {
        templateName: [{ required: true, message: '请输入模板名称', type: 'string', trigger: 'blur' }],
        templateCode: [{ required: true, message: '请输入模板编码', type: 'string', trigger: 'blur ' }],
        templateType: [{ required: true, message: '请输入模板类型', type: 'number', trigger: 'blur' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false
    };
  },
  watch: {
    data() {
      if (this.data) {
        this.form = Object.assign({}, this.data);
        this.isUpdate = true;
      } else {
        this.form = {};
        this.isUpdate = false;
      }
      if (this.$refs.form) {
        this.$refs.form.clearValidate();
      }
    }
  },
  methods: {
    /**
     * 保存和修改模板
     *
     * <AUTHOR>
     * @date 2021/12/21 14:07:08
     */
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      // 执行编辑或修改
      if (this.isUpdate) {
        result = ThemeTemplateApi.edit(this.form);
      } else {
        result = ThemeTemplateApi.add(this.form);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 如果是新增，则form表单置空
          if (!this.isUpdate) {
            this.form = {};
          }

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    /**
     * 更新修改界面的弹框是否显示
     *
     * <AUTHOR>
     * @date 2021/12/21 14:07:33
     */
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>

<style scoped></style>
