<!-- 组织机构编辑弹窗 -->
<template>
  <a-modal
    :width="1100"
    :visible="visible"
    :confirm-loading="loading"
    :forceRender="true"
    :title="isUpdate ? '编辑公司' : '新建公司'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <div class="card-title card-title-background">基础信息</div>
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 4 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 20 }, sm: { span: 24 } }"
    >
      <a-form-item label="上级公司:" name="orgParentId">
        <a-tree-select
          :disabled="form.orgParentId === '-1'"
          v-model:value="form.orgParentId"
          style="width: 100%"
          :tree-data="orgList"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          placeholder="请选择上级公司"
          :fieldNames="{ children: 'children', label: 'title', key: 'id', value: 'id' }"
          allow-clear
          tree-default-expand-all
        />
      </a-form-item>
      <a-form-item label="公司名称:" name="orgName">
        <a-input v-model:value="form.orgName" placeholder="请输入公司名称" allow-clear />
      </a-form-item>
      <a-form-item label="公司编码:" name="orgCode">
        <a-input v-model:value="form.orgCode" placeholder="请输入公司编码" allow-clear />
      </a-form-item>
      <a-form-item label="级别:" name="orgSort">
        <a-select v-model:value="form.orgSort" placeholder="请选择级别" style="width: 100%" allow-clear>
          <a-select-option v-for="key in 7" :key="key" :value="key">{{ key }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="备注:" name="orgRemark">
        <a-input v-model:value="form.orgRemark" placeholder="请输入备注" allow-clear />
      </a-form-item>
    </a-form>
    <div class="card-title card-title-background">详细信息</div>
    <field-expand-form ref="fieldExpandForm" expand-code="org_expand" />
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { UserApi } from '@/api/system/user/UserApi';
import FieldExpandForm from '@/components/FieldExpand/FieldExpandForm.vue';

export default {
  name: 'CompanyEdit',
  components: { FieldExpandForm },
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 接收上级传过来的组织机构信息
    data: Object,
    // 组织机构列表
    orgList: Array,
    // 是否是编辑界面
    isUpdate: Boolean
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: Object.assign({}),
      // 表单验证规则
      rules: {
        orgName: [{ required: true, message: '请输入组织机构名称', type: 'string', trigger: 'blur' }],
        orgCode: [{ required: true, message: '请输入组织机构编码', type: 'string', trigger: 'blur' }],
        orgType: [{ required: true, message: '请选择机构类型', type: 'number', trigger: 'blur' }],
        orgSort: [{ required: true, message: '请选择级别', type: 'number', trigger: 'blur' }]
      },
      // 提交状态
      loading: false
    };
  },
  watch: {
    visible() {
      if (this.visible) {
        if (this.data) {
          this.form = Object.assign({}, this.data);
          // 加载表单数据
          this.$refs.fieldExpandForm.loadForm(this.form.orgId);
        } else {
          this.form = {};
          // 加载表单数据
          this.$refs.fieldExpandForm.loadForm();
        }
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
        }
      }
    }
  },
  methods: {
    /**
     * 保存和编辑
     *
     * <AUTHOR>
     * @date 2021/4/7 11:00
     */
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 校验拓展表单并获取数据
      await this.$refs.fieldExpandForm.validate();

      // 采集动态表单数据
      this.form.expandDataInfo = this.$refs.fieldExpandForm.getData();

      // 修改加载框为正在加载
      this.loading = true;

      // 如果父级公司没有选，则就是顶级公司，设置位父级id为-1
      if (!this.form.orgParentId) {
        this.form.orgParentId = -1;
      }

      let result;

      // 设置机构类型为公司
      this.form.orgType = 1;

      // 执行编辑或修改用户方法
      if (this.isUpdate) {
        result = UserApi.editOrg(this.form);
      } else {
        result = UserApi.addOrg(this.form);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 如果是新增，则form表单置空
          if (!this.isUpdate) {
            this.form = {};
          }

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    /**
     * 更新编辑界面的弹框是否显示
     *
     * @param value true-显示，false-隐藏
     * <AUTHOR>
     * @date 2021/4/7 11:00
     */
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>

<style lang="less" scoped>
.card-title {
  border-left: 5px solid;
  border-color: var(--primary-color);
  padding-left: 10px;
}

.card-title-background {
  background-color: #f5f5f5;
  height: 2em;
  line-height: 2em;
  margin-bottom: 2em;
}
</style>
