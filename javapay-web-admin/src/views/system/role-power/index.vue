<template>
  <div class="ele-body">
    <a-card :bordered="false" class="table-height">
      <a-tabs v-model:activeKey="activeKey" tab-position="left" animated @change="leftChange">
        <a-tab-pane :key="item.roleId" :tab="item.roleName" v-for="item in userList">
          <div v-if="activeKey" class="right">
            <div class="right-top">
              <a-tabs v-model:activeKey="rightActiveKey" animated @change="rightChange">
                <a-tab-pane key="menu" tab="分配菜单"></a-tab-pane>
                <a-tab-pane key="api" tab="分配接口"></a-tab-pane>
                <a-tab-pane key="data" tab="数据权限"></a-tab-pane>
              </a-tabs>
            </div>
            <div class="right-bottom">
              <a-spin :spinning="authLoading">
                <div v-if="rightActiveKey == 'menu'">
                  <a-table
                    v-if="authData && authData.length"
                    :dataSource="authData"
                    :columns="menuColumns"
                    :pagination="false"
                    rowKey="id"
                    :defaultExpandAllRows="true"
                  >
                    <template #bodyCell="{ column, record }">
                      <template v-if="column.key === 'menu'">
                        <a-checkbox v-model:checked="record.checked" @change="checkedGroup($event, record)">
                          {{ record.name }}
                        </a-checkbox>
                      </template>
                      <template v-else-if="column.key === 'button'">
                        <a-checkbox-group
                          :options="record.totalNodes"
                          v-model:value="record.selectedNodes"
                          @change="checkedItem($event, record)"
                        />
                      </template>
                    </template>
                  </a-table>
                </div>
                <div v-if="rightActiveKey == 'api'">
                  <a-table :dataSource="dataSource" :columns="apiColumns" :pagination="false" rowKey="code" childrenColumnName="other">
                    <template #bodyCell="{ column, record }">
                      <template v-if="column.key === 'controller'">
                        <a-checkbox
                          v-model:checked="record.checked"
                          :indeterminate="record.indeterminate"
                          @change="apiCheckedGroup($event, record)"
                        >
                          {{ record.nodeName }}
                        </a-checkbox>
                      </template>
                      <template v-else-if="column.key === 'apis'">
                        <a-checkbox-group
                          :options="record.totalNodes"
                          v-model:value="record.selectedNodes"
                          @change="apiCheckedItem($event, record)"
                        />
                      </template>
                    </template>
                  </a-table>
                </div>
                <div v-if="rightActiveKey == 'data'">
                  <a-form
                    ref="formRef"
                    :model="form"
                    :rules="rules"
                    style="width: 50%"
                    :label-col="{ md: { span: 3 }, sm: { span: 24 } }"
                    :wrapper-col="{ md: { span: 21 }, sm: { span: 24 } }"
                  >
                    <a-form-item label="数据范围:" name="dataScopeType">
                      <a-select show-search v-model:value="form.dataScopeType" placeholder="请选择数据范围" allow-clear>
                        <a-select-option v-for="item in scopeData" :key="item.code">
                          {{ item.name }}
                        </a-select-option>
                      </a-select>
                    </a-form-item>
                    <a-form-item label="机构范围:" v-show="form.dataScopeType === 40">
                      <a-tree
                        checkable
                        :checkStrictly="true"
                        :tree-data="orgData"
                        :v-show="form.dataScopeType === 1"
                        :fieldNames="{ children: 'children', title: 'name', key: 'id' }"
                        v-model:expandedKeys="expandKeys"
                        v-model:checkedKeys="checkedKeys"
                      />
                    </a-form-item>
                    <div style="text-align: center">
                      <a-button type="primary" @click="saveData">保存</a-button>
                    </div>
                  </a-form>
                </div>
              </a-spin>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script>
import { defineComponent, onMounted, reactive, toRefs, ref, nextTick } from 'vue';
import { UserPowerApi } from '@/api/system/user-power/UserPowerApi';
import { message } from 'ant-design-vue';
import { SysRoleApi } from '@/api/system/role/SysRoleApi';
import { SysMenuApi } from '@/api/common/SysMenuApi';
import { eachTreeData } from 'ele-admin-pro';
import { SysResourceApi } from '@/api/common/SysResourceApi';
import { OrganizationApi } from '@/api/system/organization/OrganizationApi';
import { deepCopy } from '@/utils/util';
export default defineComponent({
  setup() {
    const data = reactive({
      userList: [],
      //   左侧默认选中
      activeKey: '',
      //   右侧默认选中
      rightActiveKey: 'menu',
      // 右侧加载laod
      authLoading: false,
      // menu权限数据
      authData: [],
      // menu表格列配置
      menuColumns: [
        {
          title: '菜单名称',
          key: 'menu',
          width: 200,
          dataIndex: 'menu'
        },
        {
          title: '按钮集合',
          key: 'button',
          dataIndex: 'button'
        }
      ],
      // api表格列配置
      apiColumns: [
        {
          title: '接口分类',
          key: 'controller',
          dataIndex: 'controller',
          width: 200
        },
        {
          title: '接口列表',
          key: 'apis',
          dataIndex: 'apis',
          width: 900
        }
      ],
      // api表格数据接口
      dataSource: [],
      // 表单验证规则
      rules: {
        dataScopeType: [
          {
            required: true,
            message: '请选择数据范围',
            type: 'number',
            trigger: 'blur'
          }
        ]
      },
      scopeData: [
        { code: 10, name: '仅本人数据' },
        { code: 20, name: '本部门数据' },
        { code: 30, name: '本部门及以下数据' },
        { code: 40, name: '指定部门数据' },
        { code: 50, name: '全部数据' }
      ],
      //   表单数据
      form: {},
      // 权限展开的keys
      expandKeys: [],
      // 角色权限选中的keys
      checkedKeys: [],
      // 部门数据
      orgData: [],
      copyList: []
    });
    onMounted(() => {
      getRoleSelectList();
    });

    // 获取角色列表
    const getRoleSelectList = async () => {
      data.userList = await UserPowerApi.getRoleSelectList();
      data.activeKey = data.userList[0].roleId;
      loadAuthData();
    };

    /**
     * 菜单处理选中组的事件
     *
     * <AUTHOR>
     * @date 2021/8/8 23:35
     */
    const checkedGroup = async (event, record) => {
      data.authLoading = true;
      let arr = deepCopy(data.authData);
      let grantMenuIdList = []; //菜单选中id列表
      let modularButtonIds = []; //所有的按钮集合
      let selectedButtonIds = []; //选中的按钮集合
      grantMenuIdList.push(record.id);
      if (record.buttons.length > 0) {
        if (record.checked) {
          record.selectedNodes = record.totalNodes.map(item => item.value);
        } else {
          // 不全选
          record.selectedNodes = [];
        }
        record.totalNodes.forEach(butItem => {
          modularButtonIds.push(butItem.value);
        });
      }
      if (record.children && record.children.length > 0) {
        record.children = setChildren(record.children, record.checked);
      }
      // 按钮选中列表
      if (record.checked) {
        selectedButtonIds = [...modularButtonIds];
      } else {
        selectedButtonIds = [];
      }
      // 参数
      let params = {
        roleId: data.activeKey,
        selectBindFlag: record.checked,
        grantMenuIdList: grantMenuIdList,
        modularButtonIds: modularButtonIds,
        selectedButtonIds: selectedButtonIds
      };
      let res = await SysRoleApi.grantMenusAndButtons(params);
      // 遍历树节点每个节点，处理增加属性totalNodes和selectedNodes
      eachTreeData(
        res.data,
        item => {
          // 获取所有的子节点
          let totalNodes = [];
          for (const subItem of item.buttons) {
            totalNodes.push({ label: subItem.name, value: subItem.id });
          }
          item.totalNodes = totalNodes;

          // 获取选中的子节点
          item.selectedNodes = item.buttons
            .filter(value => {
              return value.checked;
            })
            .map(value => value.id);
        },
        'children'
      );

      // 去掉没用的children
      eachTreeData(res.data, d => {
        if (d?.children.length === 0) {
          d.children = null;
        }
      });
      data.authData = res.data;
      message.success('分配菜单成功');
      data.authLoading = false;

      /**
       * 递归遍历
       * @arr 传进来的数组children
       * @flag true 或 false
       */
      function setChildren(arr, flag) {
        arr.forEach(item => {
          // 不需要标识
          item.checked = record.checked;
          // 接口需要的菜单id
          grantMenuIdList.push(item.id);
          // 获取所有按钮的id集合
          item.totalNodes.forEach(butItem => {
            modularButtonIds.push(butItem.value);
          });
          // 按钮全选
          if (flag) {
            item.selectedNodes = item.totalNodes.map(item => item.value);
          } else {
            // 不全选
            item.selectedNodes = [];
          }

          if (item.children && item.children.length > 0) {
            item.children = setChildren(item.children, flag);
          }
        });

        return arr;
      }
    };

    /**
     * 菜单处理选中的节点数据
     *
     * <AUTHOR>
     * @date 2021/8/8 23:35
     */
    const checkedItem = async (checkedValue, record) => {
      let modularButtonIds = record.totalNodes.map(item => item.value);
      let selectedButtonIds = record.selectedNodes;
      await SysRoleApi.grantButton({ roleId: data.activeKey, modularButtonIds, selectedButtonIds });
      message.success('分配按钮成功');
    };

    /**
     * 菜单查询权限数据
     *
     * <AUTHOR>
     * @date 2021/8/10 21:20
     */
    const loadAuthData = async () => {
      data.authData = [];
      data.expandKeys = [];
      data.checkedKeys = [];
      data.authLoading = true;

      data.authData = await SysMenuApi.getMenuList({ roleId: data.activeKey });

      // 遍历树节点每个节点，处理增加属性totalNodes和selectedNodes
      eachTreeData(
        data.authData,
        item => {
          // 获取所有的子节点
          let totalNodes = [];
          for (const subItem of item.buttons) {
            totalNodes.push({ label: subItem.name, value: subItem.id });
          }
          item.totalNodes = totalNodes;

          // 获取选中的子节点
          item.selectedNodes = item.buttons
            .filter(value => {
              return value.checked;
            })
            .map(value => value.id);
        },
        'children'
      );

      // 去掉没用的children
      eachTreeData(data.authData, d => {
        if (d?.children.length === 0) {
          d.children = null;
        }
      });

      data.authLoading = false;
    };

    /**
     * api处理选中组的事件
     *
     * <AUTHOR>
     * @date 2021/8/8 23:35
     */
    const apiCheckedGroup = async (event, record) => {
      // 如果是全选事件，则变为全选，如果是非全选事件则变为非全选
      if (event.target.checked) {
        record.indeterminate = false;
        record.selectedNodes = record.totalNodes.map(item => item.value);
      } else {
        record.selectedNodes = [];
      }

      // 开启加载
      data.authLoading = true;

      // 将选中的资源请求后端
      let modularTotalResource = record.totalNodes.map(item => item.value);
      let selectedResource = record.selectedNodes;
      await SysRoleApi.grantResource({
        roleId: data.activeKey,
        modularTotalResource,
        selectedResource
      });

      // 关闭加载
      data.authLoading = false;
    };

    /**
     * api处理选中的节点数据
     *
     * <AUTHOR>
     * @date 2021/8/8 23:35
     */
    const apiCheckedItem = async (checkedValue, record) => {
      let totalSelectFlag = true;

      // 获取当前行所有的code
      const totalNodes = record.totalNodes.map(item => item.value);
      for (const node of totalNodes) {
        if (!record.selectedNodes.includes(node)) {
          totalSelectFlag = false;
        }
      }

      // 如果全部选中
      if (totalSelectFlag) {
        record.checked = true;
        record.indeterminate = false;
      } else {
        record.checked = false;
        record.indeterminate = true;
      }

      // 如果全部未选中
      if (record.selectedNodes.length === 0) {
        record.indeterminate = false;
      }

      // 将选中的资源请求后端
      let modularTotalResource = record.totalNodes.map(item => item.value);
      let selectedResource = record.selectedNodes;
      SysRoleApi.grantResource({
        roleId: data.activeKey,
        modularTotalResource,
        selectedResource
      });
    };

    /**
     * api加载资源列表
     *
     * <AUTHOR>
     * @date 2021/8/8 23:34
     */
    const loadMenuResource = async () => {
      // 开启加载
      data.authLoading = true;

      // 获取资源
      data.dataSource = await SysResourceApi.getResourceList({ roleId: data.activeKey });

      // 资源整理成map
      for (const item of data.dataSource) {
        // 获取所有的子节点
        let totalNodes = [];
        for (const subItem of item.children) {
          totalNodes.push({ label: subItem.nodeName, value: subItem.code });
        }
        item.totalNodes = totalNodes;

        // 获取选中的子节点
        item.selectedNodes = item.children
          .filter(value => {
            return value.checked;
          })
          .map(value => value.code);
      }

      // 关闭加载
      data.authLoading = false;
    };

    // 右侧tab点击
    const rightChange = key => {
      if (key == 'menu') {
        loadAuthData();
      } else if (key == 'api') {
        loadMenuResource();
      } else {
        data.userList.forEach(item => {
          if (item.roleId == data.activeKey) {
            nextTick(() => {
              data.form = item;
            });
          }
        });
        query();
      }
    };

    // 数据加载tree
    const query = () => {
      let roleId = data.activeKey;
      OrganizationApi.tree({ roleId })
        .then(res => {
          // 加载框
          data.authLoading = false;

          if (res.success) {
            let treeExpandKeys = [],
              treeCheckedKeys = [];

            // 只展开组织机构树的顶级节点
            res.data.forEach(data => {
              treeExpandKeys.push(data.id);
            });

            // 组织机构数据赋值
            data.orgData = res.data;

            // 遍历所有节点，所有选中的节点搜集起来
            eachTreeData(data.orgData, d => {
              if (d.checked) {
                treeCheckedKeys.push(d.id);
              }
            });

            data.expandKeys = treeExpandKeys;
            data.checkedKeys = treeCheckedKeys;
          } else {
            message.error(res.data.message);
          }
        })
        .catch(e => {
          data.authLoading = false;
          message.error(e.message);
        });
    };

    // 保存编辑
    const saveData = async () => {
      // 校验表单
      if (!data.form.dataScopeType) return message.warning('请选择数据范围！');
      // 加载中
      data.authLoading = true;

      // 获取部门树选择数据
      let ids = data.checkedKeys?.checked;
      await SysRoleApi.grantDataScope({
        roleId: data.activeKey,
        grantOrgIdList: ids,
        dataScopeType: data.form.dataScopeType
      })
        .then(res => {
          // 移除加载框
          data.authLoading = false;
          if (res.success) {
            message.success(res.message);
          } else {
            message.error('操作失败：' + res.message);
          }
        })
        .catch(e => {
          data.authLoading = false;
          message.error(e.message);
        });
    };

    // 左侧点击
    const leftChange = key => {
      data.rightActiveKey = 'menu';
      loadAuthData();
    };

    return {
      ...toRefs(data),
      getRoleSelectList,
      checkedGroup,
      leftChange,
      apiCheckedItem,
      checkedItem,
      loadMenuResource,
      apiCheckedGroup,
      rightChange,
      loadAuthData,
      query,
      saveData
    };
  }
});
</script>

<style scoped lang="less">
.right {
  height: calc(90vh - 108px);
  overflow-y: auto;
}
.right-top {
  margin-top: -10px;
}
</style>
