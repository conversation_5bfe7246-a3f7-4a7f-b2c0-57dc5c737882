<template>
  <a-modal
    :width='800'
    :visible='visible'
    :confirm-loading='loading'
    :forceRender="true"
    :title="isUpdate ? '修改' : '新建'"
    :body-style="{ paddingBottom: '8px' }"
    :mask-closable="false"
    @update:visible='updateVisible'
    @cancel="cancel"
    @ok='save'
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :gutter="24"
      :layout="'vertical'"
    >
      <a-row :gutter="24">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="渠道编号" name="channelNo">
            <a-input v-model:value="form.channelNo" placeholder="请选择" allow-clear autocomplete="off" />
          </a-form-item>
          <a-form-item label="是否默认D0" name="isDefaultD0">
            <a-select v-model:value="form.isDefaultD0" style="width: 250px" placeholder="请选择">
              <a-select-option :value="1">是</a-select-option>
              <a-select-option :value="0">否</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label='是否支持D0' name='isSupportD0'>
            <a-select v-model:value="form.isSupportD0" style="width: 250px" placeholder="请选择">
              <a-select-option :value="1">是</a-select-option>
              <a-select-option :value="0">否</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label='是否支持批量代付' name='isSupportBatch'>
            <a-select v-model:value="form.isSupportBatch" style="width: 250px" placeholder="请选择">
              <a-select-option :value="1">是</a-select-option>
              <a-select-option :value="0">否</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label='D0单笔最大付款金额' name='maxSingleAmountD0'>
            <a-input-number v-model:value='form.maxSingleAmountD0' :min="0" class="remit-channel-input-number" placeholder='请输入D0单笔最大付款金额' allow-clear autocomplete='off' />
          </a-form-item>
          <a-form-item label='T1单笔最大付款金额' name='maxSingleAmountT1'>
            <a-input-number v-model:value='form.maxSingleAmountT1' :min="0" class="remit-channel-input-number" placeholder='请输入T1单笔最大付款金额' allow-clear autocomplete='off' />
          </a-form-item>
          <a-form-item label='优先级' name='priority'>
            <a-input-number v-model:value='form.priority' :min="0" class="remit-channel-input-number" placeholder='请输入优先级' allow-clear autocomplete='off' />
          </a-form-item>
          <a-form-item label='清算日累计限制' name='remitDayLimit'>
            <a-input-number v-model:value='form.remitDayLimit' :min="0" class="remit-channel-input-number" placeholder='请输入清算日累计限制' allow-clear autocomplete='off' />
          </a-form-item>
          <a-form-item label='业务类型' name='businessType'>
            <a-select v-model:value="form.businessType" style="width: 250px" placeholder="请选择">
              <a-select-option :value="0">全部</a-select-option>
              <a-select-option :value="100">POS</a-select-option>
              <a-select-option :value="200">扫码</a-select-option>
              <a-select-option :value="300">营销费用</a-select-option>
              <a-select-option :value="400">EPOS</a-select-option>
              <a-select-option :value="600">分润</a-select-option>
              <a-select-option :value="800">代付</a-select-option>
              <a-select-option :value="900">聚合</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label='渠道名称' name='channelName'>
            <a-input v-model:value='form.channelName' placeholder='请输入渠道名称' allow-clear autocomplete='off' />
          </a-form-item>
          <a-form-item label='是否默认T1' name='isDefaultT1'>
            <a-select v-model:value="form.isDefaultT1" style="width: 250px" placeholder="请选择">
              <a-select-option :value="1">是</a-select-option>
              <a-select-option :value="0">否</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label='是否支持T1' name='isSupportT1'>
            <a-select v-model:value="form.isSupportT1" style="width: 250px" placeholder="请选择">
              <a-select-option :value="1">是</a-select-option>
              <a-select-option :value="0">否</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label='是否支持单笔代付' name="isSupportSingle">
            <a-select v-model:value="form.isSupportSingle" style="width: 250px" placeholder="请选择">
              <a-select-option :value="1">是</a-select-option>
              <a-select-option :value="0">否</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label='D0单笔最小付款金额' name='minSingleAmountD0'>
            <a-input-number v-model:value='form.minSingleAmountD0' :min="0" class="remit-channel-input-number" placeholder='请输入D0单笔最小付款金额' allow-clear autocomplete='off' />
          </a-form-item>
          <a-form-item label='T1单笔最小付款金额' name='minSingleAmountT1'>
            <a-input-number v-model:value='form.minSingleAmountT1' :min="0" class="remit-channel-input-number" placeholder='请输入T1单笔最小付款金额' allow-clear autocomplete='off' />
          </a-form-item>
          <a-form-item label="同批次最大付款数量" name="maxBatchCount">
            <a-input-number v-model:value='form.maxBatchCount' :min="0" class="remit-channel-input-number" placeholder='请输入同批次最大付款数量' allow-clear autocomplete="off" />
          </a-form-item>
          <a-form-item label='账户类型' name='bankAccountType'>
            <a-select v-model:value="form.bankAccountType" style="width: 250px" placeholder="请选择">
              <a-select-option :value="0">全部</a-select-option>
              <a-select-option :value="1">对公</a-select-option>
              <a-select-option :value="2">对私</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-divider orientation="left" :orientationMargin="0" dashed>参数</a-divider>
      <div class="block-interval">
        <a-button type="primary" @click="addRow">添加</a-button>
        <a-form :gutter="24" v-for="(item, index) in paramsArr" :key="index" :rules="paramsRules" :model="item" :layout="'vertical'">
          <a-row :md="12" :sm="24" :xs="24">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="参数名称" name="parKey">
                <a-input style="width: 280px" v-model:value.trim="item.parKey" placeholder="请输入参数名称" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="参数值" name="parValue">
                <a-input style="width: 280px" v-model:value.trim="item.parValue" placeholder="请输入参数值" allow-clear />
                <a-button type="danger" @click="deleteRow(index)" class="remit-channel-delete">删除</a-button>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { RemitChannelApi } from '@/api/account/remit-channel/RemitChannelApi';

export default {
  name: 'RemitChannelEdit',
  components: {
  },
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    showData: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: Object.assign({}, this.showData),
      //接收键值对数组
      paramsArr: [],
      // 表单验证规则
      rules: {
        channelNo: [{ required: true, message: '请输入渠道编号', type: 'string', trigger: 'blur' }],
        channelName: [{ required: true, message: '请输入渠道名称', type: 'string', trigger: 'blur' }],
        isDefaultD0: [{ required: true, message: '请选择', type: 'number', trigger: 'blur' }],
        isDefaultT1: [{ required: true, message: '请选择', type: 'number', trigger: 'change' }],
        isSupportD0: [{ required: true, message: '请选择', type: 'number', trigger: 'change' }],
        isSupportT1: [{ required: true, message: '请选择', type: 'number', trigger: 'change' }],
        isSupportBatch: [{ required: true, message: '请选择', type: 'number', trigger: 'change' }],
        isSupportSingle: [{ required: true, message: '请选择', type: 'number', trigger: 'change' }],
        maxSingleAmountD0: [{ required: true, message: '请输入D0单笔最大付款金额', type: 'number', trigger: 'blur'}],
        minSingleAmountD0: [{ required: true, message: '请输入D0单笔最小付款金额', type: 'number', trigger: 'blur' }],
        maxSingleAmountT1: [{ required: true, message: '请输入T1单笔最大付款金额', type: 'number', trigger: 'blur' }],
        minSingleAmountT1: [{ required: true, message: '请输入T1单笔最小付款金额', type: 'number', trigger: 'blur' }],
        priority: [{ required: true, message: '请输入优先级', type: 'number', trigger: 'blur' }],
        remitDayLimit: [{ required: true, message: '请输入清算日累计限制', type: 'number', trigger: 'blur' }],
        maxBatchCount: [{ required: true, message: '请输入同批次最大付款数量', type: 'number', trigger: 'blur' }],
        bankAccountType: [{ required: true, message: '请选择', type: 'number', trigger: 'change' }],
        businessType: [{ required: true, message: '请选择', type: 'number', trigger: 'change' }],
      },
      //键值对表格规则
      paramsRules: {
        parKey: [{ required: true, message: '请输入参数名称' }],
        parValue: [{ required: true, message: '请输入参数值' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false
    };
  },
  watch: {
    visible() {
      if (this.visible) {
        if (this.showData) {
          this.form = Object.assign({}, this.showData);
          // 先将paramArr清空，避免使用历史数据
          this.paramsArr = [];
          //创建新字典型数据用于接收 param数组值
          const paramdict = JSON.parse(this.showData.params);
          //将json串转换成数组
          Object.keys(paramdict).forEach(key => this.paramsArr.push(
            {"parKey": key, "parValue": paramdict[key]}
          ))
          this.isUpdate = true;
        } else {
          this.form = {
            isDefaultD0: 0,
            isDefaultT1: 0,
            isSupportD0: 1,
            isSupportT1: 1,
            isSupportSingle: 1,
            isSupportBatch: 1,
            maxSingleAmountD0: 200000,
            minSingleAmountD0: 10,
            maxSingleAmountT1: 1000000,
            minSingleAmountT1: 0.01,
            priority: 1,
            maxBatchCount: 1000,
            remitDayLimit: *********,
            bankAccountType: 0,
          };
          this.paramsArr = []
          this.isUpdate = false;
        }
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
        }
      }
    }
  },
  methods: {
    /**
     * 保存和编辑
     *
     * <AUTHOR>
     * @date 2023/06/05 14:28
     */
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      //创建新字典型数据用于接收 param数组值
      const paramdict = {};
      //将数组提取然后转换成json串
      this.paramsArr.forEach(item => {
        if (item.parKey && item.parKey !== 0 && item.parValue && item.parValue !== 0) {
          paramdict[item.parKey] = item.parValue;
        }
      });
      
      this.form.paramsObj = paramdict;

      let result = null;

      // 执行编辑或修改
      if (this.isUpdate) {
        result = RemitChannelApi.edit(this.form);
      } else {
        result = RemitChannelApi.add(this.form);
      }
      result.then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 如果是新增，则form表单置空
          if (!this.isUpdate) {
            this.form = {};
          }

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);
          
          // 将paramsArr清空
          this.paramsArr = [];

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    /**
     * 更新编辑界面的弹框是否显示
     *
     * @param value true-显示，false-隐藏
     * <AUTHOR>
     * @date 2023/06/28 15:38
     */
    updateVisible(value) {
      this.$emit('update:visible', value);
    },
    //添加键值对
    addRow() {
      this.paramsArr.push({
        parKey: '',
        parValue: ''
      });
    },
    //删除键值对
    deleteRow(index) {
      this.paramsArr.splice(index, 1);
    },
    //取消事件
    cancel() {
      this.form = Object.assign({}, this.showData);
      this.$refs.form.clearValidate();
      // 如果是新增，则form表单置空
      if (!this.isUpdate) {
        this.paramsArr = [];
      }
    }
  }
};
</script>
<style scoped>
.remit-channel-delete{
  margin-left: 10px;
}
.remit-channel-input-number{
  width: 250px;
}
</style>