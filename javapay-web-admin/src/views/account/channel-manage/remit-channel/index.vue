<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="渠道编号">
              <a-input v-model:value.trim="where.channelNo" placeholder="请输入渠道编号" allow-clear />
            </a-form-item>
            <a-form-item label="渠道名称">
              <a-input v-model:value.trim="where.channelName" placeholder="请输入渠道名称" allow-clear />
            </a-form-item>
            <a-form-item label="状态">
              <a-select v-model:value="where.status" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">无效</a-select-option>
                <a-select-option :value="1">有效</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="业务类型">
              <a-select v-model:value="where.businessType" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">全部</a-select-option>
                <a-select-option :value="100">POS</a-select-option>
                <a-select-option :value="200">扫码</a-select-option>
                <a-select-option :value="300">营销费用</a-select-option>
                <a-select-option :value="400">EPOS</a-select-option>
                <a-select-option :value="600">分润</a-select-option>
                <a-select-option :value="800">代付</a-select-option>
                <a-select-option :value="900">聚合</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table
          ref="table"
          row-key="id"
          :datasource="datasource"
          :columns="columns"
          :where="where"
          v-model:selection="selection"
          :scroll="{ x: 'max-content' }"
        >
          <!-- table上边工具栏 -->
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="openEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-switch :checked="record.status === 1" @change="checked => editState(checked, record)" />
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleEdit(record)">修改</a>
                <a-divider type="vertical" />
                <a-popconfirm title="确定要更改状态吗？" @confirm="handleStatus(record)">
                  <a class="ele-text-danger">{{ record.status===1 ? "停用":"开启" }}</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 编辑弹窗 -->
    <remit-channel-edit v-model:visible="showEdit" :showData="current" @done="reload" />
  </div>
</template>

<script>
import { RemitChannelApi } from '@/api/account/remit-channel/RemitChannelApi';
import RemitChannelEdit from "./remit_channel_edit.vue"


export default {
  name: 'RemitChannel',
  components: {
    RemitChannelEdit,
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '渠道编号',
          dataIndex: 'channelNo',
          key: "channelNo",
          align: 'center'
        },
        {
          title: '渠道名称',
          dataIndex: 'channelName',
          align: 'center'
        },
        {
          title: '渠道状态',
          dataIndex: 'status',
          align: 'center',
          customRender: ({text}) => (Number(text) === 1 ? "有效" : (Number(text) === 0 ? "无效" : ""))
        },
        {
          title: '是否默认D0渠道',
          dataIndex: 'isDefaultD0',
          align: 'center',
          customRender: ({text}) => (Number(text) === 1 ? "是" : (Number(text) === 0 ? "否" : ""))
        },
        {
          title: '是否默认T1渠道',
          dataIndex: 'isDefaultT1',
          align: 'center',
          customRender: ({text}) => (Number(text) === 1 ? "是" : (Number(text) === 0 ? "否" : ""))
        },
        {
          title: '是否支持D0',
          dataIndex: 'isSupportD0',
          width: 160,
          align: 'center',
          customRender: ({text}) => (Number(text) === 1 ? "是" : (Number(text) === 0 ? "否" : "")),
        },
        {
          title: '是否支持T1',
          dataIndex: 'isSupportT1',
          width: 160,
          align: 'center',
          customRender: ({text}) => (Number(text) === 1 ? "是" : (Number(text) === 0 ? "否" : "")),
        },
        {
          title: '是否支持批量代付',
          dataIndex: 'isSupportBatch',
          width: 160,
          align: 'center',
          customRender: ({ text }) => (Number(text) === 1 ? "是" : (Number(text) === 0 ? "否" : ""))
        },
        {
          title: '是否支持单笔代付',
          dataIndex: 'isSupportSingle',
          width: 160,
          align: 'center',
          customRender: ({ text }) => (Number(text) === 1 ? "是" : (Number(text) === 0 ? "否" : ""))
        },
        {
          title: 'D0最大单笔限额',
          dataIndex: 'maxSingleAmountD0',
          width: 160,
          align: 'center'
        },
        {
          title: 'D0最小单笔限额',
          dataIndex: 'minSingleAmountD0',
          width: 160,
          align: 'center'
        },
        {
          title: 'T1最大单笔限额',
          dataIndex: 'maxSingleAmountT1',
          width: 160,
          align: 'center',
        },
        {
          title: 'T1最小单笔限额',
          dataIndex: 'minSingleAmountT1',
          width: 160,
          align: 'center',
        },
        {
          title: '最大批次记录数量',
          dataIndex: 'maxBatchCount',
          width: 160,
          align: 'center'
        },
        {
          title: '清算日累计限制',
          dataIndex: 'remitDayLimit',
          width: 160,
          align: 'center',
        },
        {
          title: '优先级',
          dataIndex: 'priority',
          width: 160,
          align: 'center',
        },
        {
          title: '账户类型',
          dataIndex: 'bankAccountType',
          width: 160,
          align: 'center',
          customRender: ({text}) => (bankAccountTypeMap[Number(text)])
        },
        {
          title: '业务类型',
          dataIndex: 'businessType',
          customRender: ({ text }) => (businessTypeMap[Number(text)]),
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 200,
          align: 'center'
        },
      ],
      // 表格搜索条件
      where: {},
      selection: [],
      current: null,
      showDetail: false,
      showEdit: false,
      isUpdate: false,
    };
  },
  methods: {
    reload() {
      this.selection = [];
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.selection = [];
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleStatus(row) {
      //设置状态
      RemitChannelApi.updateStatus({"id": row.id, "status": 1- row.status});
      row.status = 1 - row.status;
    },

    handleEdit(row) {
      this.current = row;
      this.showEdit = true;
      this.isUpdate = true;
    },

    openEdit(row){
      this.current = row;
      this.showEdit = true;
    },

    datasource({ page, limit, where, orders }) {
      return RemitChannelApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};


export const bankAccountTypeMap = {
  0: "全部",
  1: "对公",
  2: "对私",
}

export const accountStatusMap = {
  0: "未激活",
  1: "正常",
  2: "冻结"
}

export const userTypeMap = {
  1: "平台运营",
  2: "大区",
  3: "运营中心",
  4: "代理商",
  5: "商户"
}

export const businessTypeMap = {
  0: "全部",
  100: "POS",
  200: "扫码",
  300: "营销费用",
  400: "EPOS",
  600: "分润",
  800: "代付",
  900: "聚合",
}
</script>
