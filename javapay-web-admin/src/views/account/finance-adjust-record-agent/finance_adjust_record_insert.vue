<template>
  <a-modal
    :width="600" :visible="visible" :confirm-loading="loading" :forceRender="true"
    :title="isUpdate ? '修改' : '新建'" :body-style="{ paddingBottom: '8px' }" :mask-closable="false"
    @update:visible="updateVisible" @ok="save">
    <a-form ref="form" :model="form" :rules="rules" :label-col="{ style: { width: '120px' } }">
      <a-form-item label=" 用户编号:" name="merchantNo">
        <a-input-search
          v-model:value="form.merchantNo" placeholder="请输入商户编号" allow-clear
          @search="showSelectAccount = true" />
      </a-form-item>
      <a-form-item label="账户UUID:" name="accountUuid">
        <a-input v-model:value="form.accountUuid" placeholder="请输入账户UUID" allow-clear autocomplete="off" />
      </a-form-item>
      <!--       <a-form-item label='源钱包UUID:' name='sourceWalletUuid'>
        <a-input v-model:value='form.sourceWalletUuid' placeholder='请输入钱包UUID' allow-clear autocomplete='off' />
      </a-form-item>
      <a-form-item label='目标钱包UUID:' name='targetWalletUuid'>
        <a-input v-model:value='form.targetWalletUuid' placeholder='请输入钱包UUID' allow-clear autocomplete='off' />
      </a-form-item> -->
      <a-form-item label="操作类型:" name="optType">
        <a-select v-model:value="form.optType" style="width: 250px" placeholder="请选择">
          <a-select-option :value="1">加款</a-select-option>
          <a-select-option :value="2">减款</a-select-option>
          <a-select-option :value="3">转账</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="原钱包类型" name="sourceWalletTypeCode" v-if="form.optType == 3">
        <a-select v-model:value="form.sourceWalletTypeCode" placeholder="请选择" allow-clear>
          <a-select-option :value="9999">请选择</a-select-option>
          <!-- <a-select-option :value="100">POS D0可提现钱包</a-select-option>
          <a-select-option :value="110">POS D0可提现业务冻结钱包</a-select-option>
          <a-select-option :value="120">POS D0可提现清算冻结钱包</a-select-option>
          <a-select-option :value="101">POS D0待入账钱包</a-select-option>
          <a-select-option :value="111">POS D0待入账业务冻结钱包</a-select-option>
          <a-select-option :value="102">POS T1钱包</a-select-option>
          <a-select-option :value="103">POS T1待入账钱包</a-select-option>
          <a-select-option :value="113">POS T1待入账业务冻结钱包</a-select-option>
          <a-select-option :value="112">POS T1业务冻结钱包</a-select-option>
          <a-select-option :value="122">POS T1清算冻结钱包</a-select-option>
          <a-select-option :value="130">POS钱包账户冻结钱包</a-select-option>

          <a-select-option :value="200">二维码D0可提现钱包</a-select-option>
          <a-select-option :value="210">二维码D0可提现业务冻结钱包</a-select-option>
          <a-select-option :value="220">二维码D0可提现清算冻结钱包</a-select-option>
          <a-select-option :value="201">二维码D0待入账钱包</a-select-option>
          <a-select-option :value="211">二维码D0待入账业务冻结钱包</a-select-option>
          <a-select-option :value="202">二维码T1钱包</a-select-option>
          <a-select-option :value="203">二维码T1待入账钱包</a-select-option>
          <a-select-option :value="213">二维码T1待入账业务冻结钱包</a-select-option>
          <a-select-option :value="212">二维码T1业务冻结钱包</a-select-option>
          <a-select-option :value="222">二维码T1清算冻结钱包</a-select-option>
          <a-select-option :value="230">二维码钱包账户冻结钱包</a-select-option> -->

          <!--           <a-select-option :value="400">快捷D0可提现钱包</a-select-option>
          <a-select-option :value="410">快捷D0可提现业务冻结钱包</a-select-option>
          <a-select-option :value="420">快捷D0可提现清算冻结钱包</a-select-option>
          <a-select-option :value="401">快捷D0待入账钱包</a-select-option>
          <a-select-option :value="411">快捷D0待入账业务冻结钱包</a-select-option>
          <a-select-option :value="402">快捷T1钱包</a-select-option>
          <a-select-option :value="403">快捷T1待入账钱包</a-select-option>
          <a-select-option :value="413">快捷T1待入账业务冻结钱包</a-select-option>
          <a-select-option :value="412">快捷T1业务冻结钱包</a-select-option>
          <a-select-option :value="422">快捷T1清算冻结钱包</a-select-option>
          <a-select-option :value="430">快捷钱包账户冻结钱包</a-select-option> -->

          <!--  <a-select-option :value="900">聚合D0可提现钱包</a-select-option>
          <a-select-option :value="910">聚合D0可提现业务冻结钱包</a-select-option>
          <a-select-option :value="920">聚合D0可提现清算冻结钱包</a-select-option>
          <a-select-option :value="901">聚合D0待入账钱包</a-select-option>
          <a-select-option :value="911">聚合D0待入账业务冻结钱包</a-select-option>
          <a-select-option :value="902">聚合T1钱包</a-select-option>
          <a-select-option :value="903">聚合T1待入账钱包</a-select-option>
          <a-select-option :value="913">聚合T1待入账业务冻结钱包</a-select-option>
          <a-select-option :value="912">聚合T1业务冻结钱包</a-select-option>
          <a-select-option :value="922">聚合T1清算冻结钱包</a-select-option>
          <a-select-option :value="930">聚合钱包账户冻结钱包</a-select-option> -->

          <a-select-option :value="300">营销奖励钱包</a-select-option>
          <a-select-option :value="310">营销奖励业务冻结钱包</a-select-option>
          <a-select-option :value="320">营销奖励清算冻结钱包</a-select-option>
          <a-select-option :value="330">营销奖励账户冻结钱包</a-select-option>

          <a-select-option :value="600">分润钱包</a-select-option>
          <a-select-option :value="610">分润可提现业务冻结钱包</a-select-option>
          <a-select-option :value="620">分润清算冻结钱包</a-select-option>
          <a-select-option :value="630">分润账户冻结钱包</a-select-option>

          <a-select-option :value="800">代付D0钱包</a-select-option>
          <a-select-option :value="810">代付D0业务冻结钱包</a-select-option>
          <a-select-option :value="820">代付D0清算冻结钱包</a-select-option>
          <a-select-option :value="802">代付T1钱包</a-select-option>
          <a-select-option :value="822">代付T1清算冻结钱包</a-select-option>
          <a-select-option :value="830">代付账户冻结钱包</a-select-option>

        </a-select>
      </a-form-item>
      <a-form-item label="目标钱包类型:" name="targetWalletTypeCode">
        <a-select v-model:value="form.targetWalletTypeCode" placeholder="请选择">
          <!-- <a-select-option :value="100">POS D0可提现钱包</a-select-option>
          <a-select-option :value="110">POS D0可提现业务冻结钱包</a-select-option>
          <a-select-option :value="120">POS D0可提现清算冻结钱包</a-select-option>
          <a-select-option :value="101">POS D0待入账钱包</a-select-option>
          <a-select-option :value="111">POS D0待入账业务冻结钱包</a-select-option>
          <a-select-option :value="102">POS T1钱包</a-select-option>
          <a-select-option :value="103">POS T1待入账钱包</a-select-option>
          <a-select-option :value="113">POS T1待入账业务冻结钱包</a-select-option>
          <a-select-option :value="112">POS T1业务冻结钱包</a-select-option>
          <a-select-option :value="122">POS T1清算冻结钱包</a-select-option>
          <a-select-option :value="130">POS钱包账户冻结钱包</a-select-option>

          <a-select-option :value="200">二维码D0可提现钱包</a-select-option>
          <a-select-option :value="210">二维码D0可提现业务冻结钱包</a-select-option>
          <a-select-option :value="220">二维码D0可提现清算冻结钱包</a-select-option>
          <a-select-option :value="201">二维码D0待入账钱包</a-select-option>
          <a-select-option :value="211">二维码D0待入账业务冻结钱包</a-select-option>
          <a-select-option :value="202">二维码T1钱包</a-select-option>
          <a-select-option :value="203">二维码T1待入账钱包</a-select-option>
          <a-select-option :value="213">二维码T1待入账业务冻结钱包</a-select-option>
          <a-select-option :value="212">二维码T1业务冻结钱包</a-select-option>
          <a-select-option :value="222">二维码T1清算冻结钱包</a-select-option>
          <a-select-option :value="230">二维码钱包账户冻结钱包</a-select-option> -->

          <!--           <a-select-option :value="400">快捷D0可提现钱包</a-select-option>
          <a-select-option :value="410">快捷D0可提现业务冻结钱包</a-select-option>
          <a-select-option :value="420">快捷D0可提现清算冻结钱包</a-select-option>
          <a-select-option :value="401">快捷D0待入账钱包</a-select-option>
          <a-select-option :value="411">快捷D0待入账业务冻结钱包</a-select-option>
          <a-select-option :value="402">快捷T1钱包</a-select-option>
          <a-select-option :value="403">快捷T1待入账钱包</a-select-option>
          <a-select-option :value="413">快捷T1待入账业务冻结钱包</a-select-option>
          <a-select-option :value="412">快捷T1业务冻结钱包</a-select-option>
          <a-select-option :value="422">快捷T1清算冻结钱包</a-select-option>
          <a-select-option :value="430">快捷钱包账户冻结钱包</a-select-option> -->

          <!-- <a-select-option :value="900">聚合D0可提现钱包</a-select-option>
          <a-select-option :value="910">聚合D0可提现业务冻结钱包</a-select-option>
          <a-select-option :value="920">聚合D0可提现清算冻结钱包</a-select-option>
          <a-select-option :value="901">聚合D0待入账钱包</a-select-option>
          <a-select-option :value="911">聚合D0待入账业务冻结钱包</a-select-option>
          <a-select-option :value="902">聚合T1钱包</a-select-option>
          <a-select-option :value="903">聚合T1待入账钱包</a-select-option>
          <a-select-option :value="913">聚合T1待入账业务冻结钱包</a-select-option>
          <a-select-option :value="912">聚合T1业务冻结钱包</a-select-option>
          <a-select-option :value="922">聚合T1清算冻结钱包</a-select-option>
          <a-select-option :value="930">聚合钱包账户冻结钱包</a-select-option> -->

          <a-select-option :value="300">营销奖励钱包</a-select-option>
          <a-select-option :value="310">营销奖励业务冻结钱包</a-select-option>
          <a-select-option :value="320">营销奖励清算冻结钱包</a-select-option>
          <a-select-option :value="330">营销奖励账户冻结钱包</a-select-option>

          <a-select-option :value="600">分润钱包</a-select-option>
          <a-select-option :value="610">分润可提现业务冻结钱包</a-select-option>
          <a-select-option :value="620">分润清算冻结钱包</a-select-option>
          <a-select-option :value="630">分润账户冻结钱包</a-select-option>

          <a-select-option :value="800">代付D0钱包</a-select-option>
          <a-select-option :value="810">代付D0业务冻结钱包</a-select-option>
          <a-select-option :value="820">代付D0清算冻结钱包</a-select-option>
          <a-select-option :value="802">代付T1钱包</a-select-option>
          <a-select-option :value="822">代付T1清算冻结钱包</a-select-option>
          <a-select-option :value="830">代付账户冻结钱包</a-select-option>

        </a-select>
      </a-form-item>
      <a-form-item label="金额:" name="amount">
        <a-input v-model:value="form.amount" placeholder="请输入金额" allow-clear autocomplete="off" style="width: 100%" />
      </a-form-item>
      <a-form-item label="备注:" name="remark">
        <a-input v-model:value="form.remark" placeholder="请输入备注" allow-clear autocomplete="off" />
      </a-form-item>
    </a-form>

    <AccountSelect v-if="showSelectAccount" v-model:visible="showSelectAccount" @done="onSelectAccountFinish" />
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { FinanceAdjustRecordApi } from '@/api/account/finance-adjust-record/FinanceAdjustRecordApi';
import AccountSelect from './account-select.vue'

export default {
  name: 'FinanceAdjustRecordInsert',
  components: {
    AccountSelect
  },
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {
        optType: 1,
        targetWalletTypeCode: 800
      },
      showSelectAccount: false,
      // 表单验证规则
      rules: {
        merchantNo: [{ required: true, message: '请输入用户编号', type: 'string', }],
        accountUuid: [{ required: true, message: '请输入账户UUID', type: 'string', }],
        amount: [{ required: true, message: '请输入金额', type: 'string', }],
        optType: [{ required: true, message: '请输入操作类型', type: 'number', trigger: 'change' }],
        targetWalletTypeCode: [{ required: true, message: '请输入目标钱包类型', type: 'number', trigger: 'change' }],
        sourceWalletTypeCode: [{ required: true, message: '请输入源钱包类型', type: 'number', trigger: 'change' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false
    };
  },
  mounted() {
    if (this.data) {
      this.form = Object.assign({}, this.data);
      this.isUpdate = true;
    } else {
      this.isUpdate = false;
    }

  },
  methods: {
    onSelectAccountFinish(info) {
      this.form.merchantNo = info?.merchantNo
      this.form.accountUuid = info?.accountUuid
    },
    /**
     * 保存和编辑
     *
     * <AUTHOR>
     * @date 2023/06/05 14:28
     */
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      // 执行编辑或修改
      if (this.isUpdate) {
        result = FinanceAdjustRecordApi.edit(this.form);
      } else {
        result = FinanceAdjustRecordApi.addAgentRecord(this.form);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    /**
     * 更新编辑界面的弹框是否显示
     *
     * @param value true-显示，false-隐藏
     * <AUTHOR>
     * @date 2023/06/05 14:28
     */
    updateVisible(value) {
      this.$emit('update:visible', value);
    },

  }
};
</script>
