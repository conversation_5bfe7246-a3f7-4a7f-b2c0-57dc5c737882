<template>
  <a-modal
    :width="1000" :visible="visible" title="账户选择" :mask-closable="false" :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible" @ok="save">
    <div class="ele-body">
      <!-- 搜索框内容 -->
      <div>
        <a-card :bordered="false">
          <a-form layout="inline" :model="where">
            <a-row :gutter="[0, 16]">
              <a-form-item label="机构号">
                <a-input v-model:value.trim="where.merchantNo" placeholder="请输入机构号" allow-clear />
              </a-form-item>
              <a-form-item label="账户UUID">
                <a-input v-model:value.trim="where.accountUuid" placeholder="请输入账户UUID" allow-clear />
              </a-form-item>
              <a-form-item label="企业名称">
                <a-input v-model:value="where.companyName" style="width: 200px" placeholder="请输入企业名称" allow-clear />
              </a-form-item>
              <a-form-item label="真实姓名">
                <a-input v-model:value="where.realName" style="width: 200px" placeholder="请输入真实姓名" allow-clear />
              </a-form-item>
              <a-form-item label="账户状态">
                <a-select v-model:value="where.status" style="width: 200px" placeholder="请选择" allow-clear>
                  <a-select-option :value="0">未激活</a-select-option>
                  <a-select-option :value="1">正常</a-select-option>
                  <a-select-option :value="2">冻结</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item class="ele-text-center">
                <a-space>
                  <a-button type="primary" @click="reload">查询</a-button>
                  <a-button @click="reset">重置</a-button>
                </a-space>
              </a-form-item>
            </a-row>
          </a-form>
        </a-card>
      </div>

      <!-- 表格内容 -->
      <div>
        <a-card :bordered="false">
          <ele-pro-table
            ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where"
            v-model:current="selectedRow" :scroll="{ x: 'max-content' }" />
        </a-card>
      </div>
    </div>
  </a-modal>
</template>

<script>
import { AgentAccountApi } from '@/api/account/agent-account/AgentAccountApi';

export default {
  props: {
    // 弹窗是否打开
    visible: Boolean
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      //表格查询条件
      where: {},
      //选择数据
      selectedRow: null,
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '账户UUID',
          dataIndex: 'accountUuid',
          key: 'accountUuid',
          align: 'center'
        },
        {
          title: '真实姓名',
          dataIndex: 'realName'
        },
        {
          title: '身份证号',
          dataIndex: 'idCardNoMask',
          align: 'center'
        },
        {
          title: '企业名称',
          dataIndex: 'companyName',
          align: 'center'
        },
        {
          title: '账户类型',
          dataIndex: 'accountType',
          align: 'center',
          customRender: ({ text }) => merchantTypeMap[Number(text)] || '未知'
        },
        {
          title: '账户状态',
          dataIndex: 'status',
          width: 160,
          align: 'center',
          customRender: ({ text }) => accountStatusMap[Number(text)] || '未知'
        },
        {
          title: '机构编号',
          dataIndex: 'merchantNo',
          key: 'merchantNo',
          width: 160,
          align: 'center'
        },
        {
          title: '上级机构号',
          dataIndex: 'superAgentNo',
          width: 160,
          align: 'center'
        },
        {
          title: '客户端编码',
          dataIndex: 'clientNo',
          key: 'clientNo',
          width: 160,
          align: 'center'
        },
        {
          title: '是否机构商户',
          dataIndex: 'isOrgMerchant',
          width: 160,
          align: 'center',
          customRender: ({ text }) => (Number(text) === 1 ? '是' : '否')
        },
        {
          title: '是否法人结算',
          dataIndex: 'isSelfCardSett',
          width: 160,
          align: 'center',
          customRender: ({ text }) => (Number(text) === 1 ? '是' : '否')
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
      ]
    };
  },
  methods: {
    save() {
      this.$emit('done', this.selectedRow);
      this.updateVisible(false);
    },

    reset() {
      this.where = {};
      this.selectedRow = null;
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    reload() {
      this.selectedRow = null;
      this.$refs.table.reload({ page: 1 });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    },

    datasource({ page, limit, where }) {
      return AgentAccountApi.findPage({ ...where, pageNo: page, pageSize: limit });

    }
  }
};

export const merchantTypeMap = {
  1: '小微',
  2: '企业',
  3: '个体工商户'
};

export const accountStatusMap = {
  0: '未激活',
  1: '正常',
  2: '冻结'
};

export const userTypeMap = {
  1: '平台运营',
  2: '大区',
  3: '运营中心',
  4: '代理商',
  5: '商户'
};
</script>
