<template>
  <a-modal
    :width='800'
    :visible='visible'
    :confirm-loading='loading'
    :forceRender="true"
    :title="isUpdate ? '审核' : '新建'"
    :body-style="{ paddingBottom: '8px' }"
    :mask-closable="false"
    @update:visible='updateVisible'
    @ok='save'
  >

    <a-form
      ref='form'
      :model='form'
      :rules='rules'
      :label-col='{ md: { span: 4 }, sm: { span: 12 } }'
      :wrapper-col='{ md: { span: 20 }, sm: { span: 12 } }'
    >
      <a-form-item label='用户编号:' name='merchantNo'>
        <a-input v-model:value='form.merchantNo' placeholder='请输入商户编号' allow-clear autocomplete='off' disabled/>
      </a-form-item>
      <a-form-item label='账户UUID:' name='accountUuid'>
        <a-input v-model:value='form.accountUuid' placeholder='请输入账户UUID' allow-clear autocomplete='off' disabled/>
      </a-form-item>
       <a-form-item label='源钱包UUID:' name='sourceWalletUuid'>
        <a-input v-model:value='form.sourceWalletUuid' placeholder='请输入钱包UUID' allow-clear autocomplete='off' disabled/>
      </a-form-item>
      <a-form-item label='目标钱包UUID:' name='targetWalletUuid'>
        <a-input v-model:value='form.targetWalletUuid' placeholder='请输入钱包UUID' allow-clear autocomplete='off' disabled/>
      </a-form-item> 
      <a-form-item label="原钱包类型" name='sourceWalletTypeCode'>
        <a-input v-model:value='walletTypeCodeMap[form.sourceWalletTypeCode]' allow-clear autocomplete='off' disabled/>
      </a-form-item>
      <a-form-item label='目标钱包类型:' name='targetWalletTypeCode'>
        <a-input v-model:value='walletTypeCodeMap[form.targetWalletTypeCode]' allow-clear autocomplete='off' disabled/>
      </a-form-item>
      <a-form-item label='金额:' name='amount'>
        <a-input v-model:value='form.amount' placeholder='请输入金额' allow-clear autocomplete='off' style="width: 100%" disabled />
      </a-form-item>
      <a-form-item label='操作类型:' name='optType'>
        <a-select v-model:value="form.optType" style="width: 250px" placeholder="请选择" disabled >
          <a-select-option :value="1">加款</a-select-option>
          <a-select-option :value="2">减款</a-select-option>
          <a-select-option :value="3">转账</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label='备注:' name='remark'>
        <a-input v-model:value='form.remark' readonly disabled allow-clear autocomplete='off' />
      </a-form-item>
      <a-form-item label="审核类型" name='checkStatus'>
        <a-select v-model:value="checkStatus"  placeholder="请选择">
            <a-select-option :value="1">通过</a-select-option>
            <a-select-option :value="2">驳回</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label='原因:' name='reason'>
        <a-input v-model:value='form.reason' placeholder='请输入原因' allow-clear autocomplete='off' style="width: 100%" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { FinanceAdjustRecordApi } from '@/api/account/finance-adjust-record/FinanceAdjustRecordApi';


export default {
  name: 'FinanceAdjustRecordCheck',
  components: {
  },
  emits: ['done', 'update:visible'],
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    data: Object
  },
  data() {
    return {
      // 表单数据
      form: Object.assign({}, this.data),
      // 表单验证规则
      rules: {
        merchantNo: [{ required: true, message: '请输入用户编号', type: 'string', trigger: 'blur' }],
        accountUuid: [{ required: true, message: '请输入账户UUID', type: 'string', trigger: 'blur' }],
        checkStatus: [{ required: true, message: '请选择审核状态', type: 'number', trigger: 'blur' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false,
      walletTypeCodeMap : {
          "300":"营销奖励钱包",
          "310":"营销奖励业务冻结钱包",
          "320":"营销奖励清算冻结钱包",
          "330":"营销奖励账户冻结钱包",
          "600":"分润钱包",
          "610":"分润可提现业务冻结钱包",
          "620":"分润清算冻结钱包",
          "630":"分润账户冻结钱包",
          "800":"代付D0钱包",
          "810":"代付D0业务冻结钱包",
          "820":"代付D0清算冻结钱包",
          "802":"代付T1钱包",
          "822":"代付T1清算冻结钱包",
          "830":"代付账户冻结钱包"
        }
    };
  },
  watch: {
    visible() {
      if (this.visible) {
        if (this.data) {
          this.form = Object.assign({}, this.data);
          this.isUpdate = true;
        } else {
          this.form = {};
          this.isUpdate = false;
        }
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
        }
      }
    }
  },
  methods: {
    /**
     * 保存和编辑
     *
     * <AUTHOR>
     * @date 2023/06/05 14:28
     */
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;
  
      this.form.checkStatus = this.checkStatus;
      // 执行编辑或修改
      result = FinanceAdjustRecordApi.check(this.form);
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 如果是新增，则form表单置空
          if (!this.isUpdate) {
            this.form = {};
          }

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    /**
     * 更新编辑界面的弹框是否显示
     *
     * @param value true-显示，false-隐藏
     * <AUTHOR>
     * @date 2023/06/05 14:28
     */
    updateVisible(value) {
      this.$emit('update:visible', value);
    },

  }
};
</script>
