<template>
  <div class="ele-body">
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="请求流水号">
              <a-input v-model:value.trim="where.reqFlowNo" placeholder="请求流水号" allow-clear />
            </a-form-item>
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="账户UUID">
              <a-input v-model:value.trim="where.accountUuid" placeholder="账户UUID" allow-clear />
            </a-form-item>
            <a-form-item label="开始日期">
              <a-date-picker v-model:value="where.searchBeginTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item label="结束日期">
              <a-date-picker v-model:value="where.searchEndTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table
          ref="table"
          row-key="id"
          :datasource="datasource"
          :columns="columns"
          :where="where"
          v-model:selection="selection"
          :scroll="{ x: 'max-content' }"
        >
          <!-- table上边工具栏 -->
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="openEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>

          <template #bodyCell="{ column, record }">
            <!-- table操作栏按钮 -->
            <template v-if="column.key === 'action'">
              <a-space>
                <a v-if="record.checkStatus === 0" @click="openCheck(record)">审核</a>
                <a-divider type="vertical" />
                <a-popconfirm title="确定要删除此记录吗？" @confirm="remove(record)">
                  <a v-if="record.checkStatus === 0" class="ele-text-danger">删除</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 编辑弹窗 -->
    <finance-adjust-record-insert v-if="showEdit" v-model:visible="showEdit" :data="current" @done="reload" />
    <finance-adjust-record-check v-model:visible="showCheck" :data="current" @done="reload" />
  </div>
</template>

<script>
import { message } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import { FinanceAdjustRecordApi } from '@/api/account/finance-adjust-record/FinanceAdjustRecordApi';
import FinanceAdjustRecordInsert from './finance_adjust_record_insert.vue';
import FinanceAdjustRecordCheck from './finance_adjust_record_check.vue';
import { toDateString } from 'ele-admin-pro';

export default {
  name: 'FinanceAdjustRecordAgent',
  components: {
    PlusOutlined,
    FinanceAdjustRecordInsert,
    FinanceAdjustRecordCheck
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id'
        },
        {
          title: '请求流水号',
          dataIndex: 'reqFlowNo'
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo'
        },
        /* {
          title: '账户UUID',
          dataIndex: 'accountUuid'
        }, */
/*         {
          title: '源钱包UUID',
          dataIndex: 'sourceWalletUuid'
        },
         {
          title: '目标钱包UUID',
          dataIndex: 'targetWalletUuid'
        }, */
        {
          title: '原钱包类型',
          dataIndex: 'sourceWalletTypeCode',
          customRender: ({ text }) => {
            const item = this.walletTypeCodeMap[text];
            return item || '--';
          }
        },
        {
          title: '目标钱包类型',
          dataIndex: 'targetWalletTypeCode',
          customRender: ({ text }) => {
            const item = this.walletTypeCodeMap[text];
            return item || '--';
          }
        },
        {
          title: '金额',
          dataIndex: 'amount'
        },
        {
          title: '操作类型',
          dataIndex: 'optType',
          customRender: ({ text }) => {
            const item = this.optTypeList.find(c => c.code === text);
            return item?.value || '--';
          }
        },
        {
          title: '调账类型',
          dataIndex: 'adjustType',
          customRender: ({ text }) => {
            const item = this.adjustTypeList.find(c => c.code === text);
            return item?.value || '--';
          }
        },
        {
          title: '审核状态',
          dataIndex: 'checkStatus',
          customRender: ({ text }) => {
            const item = this.checkStatusList.find(c => c.code === text);
            return item?.value || '--';
          }
        },
        {
          title: '创建人',
          dataIndex: 'createUserName'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          customRender : ({ text }) => toDateString(text, "yyyy-MM-dd HH:mm:ss")
        },
        {
          title: '审核人',
          dataIndex: 'checkUserName'
        },
        {
          title: '审核时间',
          dataIndex: 'checkTime',
          customRender : ({ text }) => toDateString(text, "yyyy-MM-dd HH:mm:ss")
        },
         {
          title: '备注',
          dataIndex: 'remark'
        },
        {
          title: '原因',
          dataIndex: 'reason'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 200,
          align: 'center'
        }
      ],
      // 表格搜索条件
      where: {},
      // 表格选中数据
      selection: [],
      checkStatusList: [{"code":0,"value":"未审核"} , {"code":1,"value":"审核通过"},{"code":2,"value":"审核不通过"} ],
      optTypeList : [ {"code":1,"value":"加款"} , {"code":2,"value":"减款"},{"code":3,"value":"转账"} ],
      adjustTypeList : [ {"code":1,"value":"人工调账"} , {"code":2,"value":"程序调账"}],
      walletTypeCodeMap : {
          "300":"营销奖励钱包",
          "310":"营销奖励业务冻结钱包",
          "320":"营销奖励清算冻结钱包",
          "330":"营销奖励账户冻结钱包",
          "600":"分润钱包",
          "610":"分润可提现业务冻结钱包",
          "620":"分润清算冻结钱包",
          "630":"分润账户冻结钱包",
          "800":"代付D0钱包",
          "810":"代付D0业务冻结钱包",
          "820":"代付D0清算冻结钱包",
          "802":"代付T1钱包",
          "822":"代付T1清算冻结钱包",
          "830":"代付账户冻结钱包"
        },
      // 当前编辑数据
      current: null,
      // 是否显示编辑弹窗
      showEdit: false,
      showCheck: false
    };
  },
  async mounted() {
  },
  methods: {
    /**
     * 搜索按钮
     *
     * <AUTHOR>
     * @date 2023/06/05 14:28
     */
    reload() {
      this.selection = [];
      this.$refs.table.reload({ page: 1 });
    },

    /**
     * 重置搜索
     *
     * <AUTHOR>
     * @date 2023/06/05 14:28
     */
    reset() {
      this.where = {};
      this.selectedRow = null;
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    /**
     * 删除
     *
     * <AUTHOR>
     * @date 2023/06/05 14:28
     */
    async remove(row) {
      const result = await FinanceAdjustRecordApi.delete({ id: row.id });
      message.success(result.message);
      this.reload();
    },

    /**
     * 打开新增或编辑弹窗
     *
     * <AUTHOR>
     * @date 2023/06/05 14:28
     */
    openEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    openCheck(row) {
      this.current = row;
      this.showCheck = true;
    },

    /**
     * 获取表格数据
     *
     * <AUTHOR>
     * @date 2023/06/05 14:28
     */
    datasource({ page, limit, where, orders }) {
      return FinanceAdjustRecordApi.findAgentPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
