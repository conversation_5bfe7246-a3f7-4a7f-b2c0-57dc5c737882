<template>
  <div class="ele-body">
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[8, 16]">
            <a-form-item label="开始日期">
              <a-date-picker v-model:value="where.searchBeginTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD 00:00:00" />
            </a-form-item>
            <a-form-item label="结束日期">
              <a-date-picker v-model:value="where.searchEndTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD 23:59:59" />
            </a-form-item>
            <a-form-item label="请求流水号">
              <a-input v-model:value.trim="where.reqFlowNo" placeholder="请求流水号" allow-clear />
            </a-form-item>
            <a-form-item label="钱包流水号">
              <a-input v-model:value.trim="where.walletFlowNo" placeholder="钱包流水号" allow-clear />
            </a-form-item>
            <a-form-item label="借贷标识">
              <a-select v-model:value="where.debitType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">加款</a-select-option>
                <a-select-option :value="2">减款</a-select-option>
                <a-select-option :value="3">不计</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="钱包类型">
              <a-select v-model:value="where.targetWalletTypeCode" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value='100'>POS D0可提现钱包</a-select-option>
                <a-select-option :value='110'>POS D0可提现业务冻结钱包</a-select-option>
                <a-select-option :value='120'>POS D0可提现清算冻结钱包</a-select-option>
                <a-select-option :value='101'>POS D0待入账钱包</a-select-option>
                <a-select-option :value='111'>POS D0待入账业务冻结钱包</a-select-option>
                <a-select-option :value='102'>POS T1钱包</a-select-option>
                <a-select-option :value='103'>POS T1待入账钱包</a-select-option>
                <a-select-option :value='113'>POS T1待入账业务冻结钱包</a-select-option>
                <a-select-option :value='112'>POS T1业务冻结钱包</a-select-option>
                <a-select-option :value='122'>POS T1清算冻结钱包</a-select-option>
                <a-select-option :value='130'>POS钱包账户冻结钱包</a-select-option>
                <a-select-option :value='200'>二维码D0可提现钱包</a-select-option>
                <a-select-option :value='210'>二维码D0可提现业务冻结钱包</a-select-option>
                <a-select-option :value='220'>二维码D0可提现清算冻结钱包</a-select-option>
                <a-select-option :value='201'>二维码D0待入账钱包</a-select-option>
                <a-select-option :value='211'>二维码D0待入账业务冻结钱包</a-select-option>
                <a-select-option :value='202'>二维码T1钱包</a-select-option>
                <a-select-option :value='203'>二维码T1待入账钱包</a-select-option>
                <a-select-option :value='213'>二维码T1待入账业务冻结钱包</a-select-option>
                <a-select-option :value='212'>二维码T1业务冻结钱包</a-select-option>
                <a-select-option :value='222'>二维码T1清算冻结钱包</a-select-option>
                <a-select-option :value='230'>二维码钱包账户冻结钱包</a-select-option>
                <a-select-option :value='400'>快捷支付D0可提现钱包</a-select-option>
                <a-select-option :value='410'>快捷支付D0可提现业务冻结钱包</a-select-option>
                <a-select-option :value='420'>快捷支付D0可提现清算冻结钱包</a-select-option>
                <a-select-option :value='401'>快捷支付D0待入账钱包</a-select-option>
                <a-select-option :value='411'>快捷支付D0待入账业务冻结钱包</a-select-option>
                <a-select-option :value='402'>快捷支付T1钱包</a-select-option>
                <a-select-option :value='403'>快捷支付T1待入账钱包</a-select-option>
                <a-select-option :value='413'>快捷支付T1待入账业务冻结钱包</a-select-option>
                <a-select-option :value='412'>快捷支付T1业务冻结钱包</a-select-option>
                <a-select-option :value='422'>快捷支付T1清算冻结钱包</a-select-option>
                <a-select-option :value='430'>快捷支付账户冻结钱包</a-select-option>
                <a-select-option :value='900'>聚合D0可提现钱包</a-select-option>
                <a-select-option :value='910'>聚合D0可提现业务冻结钱包</a-select-option>
                <a-select-option :value='920'>聚合D0可提现清算冻结钱包</a-select-option>
                <a-select-option :value='901'>聚合D0待入账钱包</a-select-option>
                <a-select-option :value='911'>聚合D0待入账业务冻结钱包</a-select-option>
                <a-select-option :value='902'>聚合T1钱包</a-select-option>
                <a-select-option :value='912'>聚合T1业务冻结钱包</a-select-option>
                <a-select-option :value='922'>聚合T1清算冻结钱包</a-select-option>
                <a-select-option :value='903'>聚合T1待入账钱包</a-select-option>
                <a-select-option :value='913'>聚合T1待入账业务冻结钱包</a-select-option>
                <a-select-option :value='930'>聚合钱包账户冻结钱包</a-select-option>
                <a-select-option :value='300'>营销奖励钱包</a-select-option>
                <a-select-option :value='310'>营销奖励业务冻结钱包</a-select-option>
                <a-select-option :value='320'>营销奖励清算冻结钱包</a-select-option>
                <a-select-option :value='330'>营销奖励账户冻结钱包</a-select-option>
                <a-select-option :value='600'>分润钱包</a-select-option>
                <a-select-option :value='610'>分润可提现业务冻结钱包</a-select-option>
                <a-select-option :value='620'>分润清算冻结钱包</a-select-option>
                <a-select-option :value='630'>分润账户冻结钱包</a-select-option>
                <a-select-option :value='800'>代付D0钱包</a-select-option>
                <a-select-option :value='810'>代付D0业务冻结钱包</a-select-option>
                <a-select-option :value='820'>代付D0清算冻结钱包</a-select-option>
                <a-select-option :value='802'>代付T1钱包</a-select-option>
                <a-select-option :value='822'>代付T1清算冻结钱包</a-select-option>
                <a-select-option :value='830'>代付账户冻结钱包</a-select-option>
                <a-select-option :value='840'>代付业务冻结钱包</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="入账方式">
              <a-select v-model:value="where.postType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value='1000'>POS消费</a-select-option>
                <a-select-option :value='1001'>POS消费撤销冲正</a-select-option>
                <a-select-option :value='1002'>POS消费退货冲正</a-select-option>
                <a-select-option :value='1010'>POS预授权完成入账</a-select-option>
                <a-select-option :value='1011'>POS预授权完成撤销冲正</a-select-option>
                <a-select-option :value='1020'>POS差错加款入账</a-select-option>
                <a-select-option :value='1100'>清算失败退回入账</a-select-option>
                <a-select-option :value='1101'>清算差错退回入账</a-select-option>
                <a-select-option :value='1102'>清算冻结入账</a-select-option>
                <a-select-option :value='1200'>二维码消费入账</a-select-option>
                <a-select-option :value='1210'>二维码差错款加款</a-select-option>
                <a-select-option :value='1300'>调账加款</a-select-option>
                <a-select-option :value='1500'>营销奖励加款</a-select-option>
                <a-select-option :value='1501'>代理分润加款</a-select-option>
                <a-select-option :value='1502'>代付充值加款</a-select-option>
                <a-select-option :value='1503'>代理转账加款</a-select-option>
                <a-select-option :value='1504'>代理分润差错加款</a-select-option>
                <a-select-option :value='1505'>代付充值差错加款</a-select-option>
                <a-select-option :value='1700'>EPOS支付消费</a-select-option>
                <a-select-option :value='1701'>EPOS支付差错加款</a-select-option>
                <a-select-option :value='2000'>POS撤销入账</a-select-option>
                <a-select-option :value='2001'>POS冲正入账</a-select-option>
                <a-select-option :value='2002'>POS退货入账</a-select-option>
                <a-select-option :value='2021'>POS差错款减款入账</a-select-option>
                <a-select-option :value='2010'>POS预授权完成撤销入账</a-select-option>
                <a-select-option :value='2011'>POS预授权完成冲正入账</a-select-option>
                <a-select-option :value='2100'>清算扣减</a-select-option>
                <a-select-option :value='2101'>清算差错扣减</a-select-option>
                <a-select-option :value='2102'>清算失败扣减</a-select-option>
                <a-select-option :value='2200'>二维码退货</a-select-option>
                <a-select-option :value='2210'>二维码差错款减款</a-select-option>
                <a-select-option :value='2300'>调账减款</a-select-option>
                <a-select-option :value='2400'>提现</a-select-option>
                <a-select-option :value='2401'>自动提现</a-select-option>
                <a-select-option :value='2402'>提现申请</a-select-option>
                <a-select-option :value='2403'>提现失败退回</a-select-option>
                <a-select-option :value='2404'>提现成功扣款</a-select-option>
                <a-select-option :value='2500'>营销奖励扣减</a-select-option>
                <a-select-option :value='2501'>代理分润扣减</a-select-option>
                <a-select-option :value='2502'>代付充值减款</a-select-option>
                <a-select-option :value='2503'>代理转账减款</a-select-option>
                <a-select-option :value='2504'>代理分润差错减款</a-select-option>
                <a-select-option :value='2505'>代付充值差错减款</a-select-option>
                <a-select-option :value='2600'>冻结单扣除</a-select-option>
                <a-select-option :value='2700'>EPOS支付撤销</a-select-option>
                <a-select-option :value='2701'>EPOS支付差错减款</a-select-option>
                <a-select-option :value='3000'>转账</a-select-option>
                <a-select-option :value='3100'>冻结</a-select-option>
                <a-select-option :value='3101'>解冻</a-select-option>
                <a-select-option :value='3102'>冻结(转账)</a-select-option>
                <a-select-option :value='3103'>解冻(转账)</a-select-option>
                <a-select-option :value='3031'>内部对账流水调平加款</a-select-option>
                <a-select-option :value='3032'>内部对账流水调平减款</a-select-option>
                <a-select-option :value='3201'>清算冻结</a-select-option>
                <a-select-option :value='3300'>调账转账</a-select-option>
                <a-select-option :value='1901'>转账加款</a-select-option>
                <a-select-option :value='2901'>转账减款</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="钱包流水UUID">
              <a-input v-model:value.trim="where.walletFlowUuid" placeholder="钱包流水UUID" allow-clear />
            </a-form-item>
            <a-form-item label="账户UUID">
              <a-input v-model:value.trim="where.accountUuid" placeholder="账户UUID" allow-clear />
            </a-form-item>
            <a-form-item label="钱包UUID">
              <a-input v-model:value.trim="where.walletUuid" placeholder="钱包UUID" allow-clear />
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table
          ref="table"
          row-key="id"
          :datasource="datasource"
          :columns="columns"
          :where="where"
          v-model:selection="selection"
          :scroll="{ x: 'max-content' }"
        >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key == 'debitType'">
            <a-tag v-if="record.debitType === 1" color="green">加款</a-tag>
            <a-tag v-if="record.debitType === 2" color="pink">减款</a-tag>
            <a-tag v-if="record.debitType === 3" color="yellow">不计</a-tag>
          </template>
          <template v-if="column.key == 'flowAmount'">
            <a-tag v-if="record.debitType === 1" color="green">+ {{record.flowAmount.toFixed(2)}}</a-tag>
            <a-tag v-if="record.debitType === 2" color="pink">- {{record.flowAmount.toFixed(2)}}</a-tag>
            <a-tag v-if="record.debitType === 3" color="yellow">  {{record.flowAmount.toFixed(2)}}</a-tag>
          </template>
          <template v-if="column.key == 'sourceWalletBalance'">
            <a-tag v-if="record.debitType === 1" color="green">{{record.sourceWalletBalance.toFixed(2)}}</a-tag>
            <a-tag v-if="record.debitType === 2" color="pink">{{record.sourceWalletBalance.toFixed(2)}}</a-tag>
            <a-tag v-if="record.debitType === 3" color="yellow">{{record.sourceWalletBalance.toFixed(2)}}</a-tag>
          </template>
          <template v-if="column.key == 'targetWalletBalance'">
            <a-tag v-if="record.debitType === 1" color="green">{{record.targetWalletBalance.toFixed(2)}}</a-tag>
            <a-tag v-if="record.debitType === 2" color="pink">{{record.targetWalletBalance.toFixed(2)}}</a-tag>
            <a-tag v-if="record.debitType === 3" color="yellow">{{record.targetWalletBalance.toFixed(2)}}</a-tag>
          </template>
        </template>
        </ele-pro-table>

          
      </a-card>

    </div>

  </div>



</template>


<script>
import { message } from 'ant-design-vue';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import { WalletFlowApi } from '@/api/account/wallet-flow/WalletFlowApi';

export default {
  name: 'WalletFlow',
  components: {
    PlusOutlined,
    DeleteOutlined
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '钱包流水UUID',
          dataIndex: 'walletFlowUuid'
        },
        {
          title: '账户UUID',
          dataIndex: 'accountUuid'
        },

        {
          title: '账户钱包类型',
          dataIndex: 'targetWalletTypeCode',
          customRender: ({text}) => (walletTypeMap[Number(text)])
        },
        {
          title: '流水金额',
          key : 'flowAmount',
          dataIndex: 'flowAmount',
          customRender: ({text}) => (Number(text).toFixed(2))
        },
        {
          title: '余额（交易前）',
          key: 'sourceWalletBalance',
          dataIndex: 'sourceWalletBalance',
          customRender: ({text}) => (Number(text).toFixed(2))
        },
        {
          title: '余额（交易后）',
          key: 'targetWalletBalance',
          dataIndex: 'targetWalletBalance',
          customRender: ({text}) => (Number(text).toFixed(2))
        },
        {
          title: '借贷标记',
          key : 'debitType',
          dataIndex: 'debitType'
          // customRender: ({text}) => (debitTypeMap[Number(text)])
        },
        {
          title: '入账方式',
          dataIndex: 'postType',
          customRender: ({text}) => (postTypeMap[text])
        },
        {
          title: '钱包UUID',
          dataIndex: 'walletUuid'
        },
        {
          title: '请求流水号',
          dataIndex: 'reqFlowNo'
        },
        {
          title: '钱包流水号',
          dataIndex: 'walletFlowNo'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
        }
      ],
      // 表格搜索条件
      where: {},
      // 表格选中数据
      selection: [],
      // 当前编辑数据
      current: null,
      // 是否显示编辑弹窗
      showEdit: false
    };
  },
  async mounted() {
  },
  methods: {
    /**
     * 搜索按钮
     *
     * <AUTHOR>
     * @date 2023/03/19 14:31
     */
    reload() {
      this.selection = [];
      this.$refs.table.reload({ page: 1 });
    },


    /**
     * 重置搜索
     *
     * <AUTHOR>
     * @date 2023/03/19 14:31
     */
    reset() {
      this.where = {};
      this.selection = [];
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    /**
     * 删除
     *
     * <AUTHOR>
     * @date 2023/03/19 14:31
     */
    async remove(row) {
      const result = await WalletFlowApi.delete({ id: row.id });
      message.success(result.message);
      this.reload();
    },

    /**
     * 打开新增或编辑弹窗
     *
     * <AUTHOR>
     * @date 2023/03/19 14:31
     */
    openEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    /**
     * 获取表格数据
     *
     * <AUTHOR>
     * @date 2023/03/19 14:31
     */
    datasource({ page, limit, where, orders }) {
      return WalletFlowApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};

export const debitTypeMap = {
  0: '全部',
  1: '加款',
  2: '减款',
  3: '不计'
};

export const postTypeMap = {
 '1000': 'POS消费',
 '1001': 'POS消费撤销冲正',
 '1002': 'POS消费退货冲正',
 '1010': 'POS预授权完成入账',
 '1011': 'POS预授权完成撤销冲正',
 '1020': 'POS差错加款入账',
 '1100': '清算失败退回入账',
 '1101': '清算差错退回入账',
 '1102': '清算冻结入账',
 '1200': '二维码消费入账',
 '1210': '二维码差错款加款',
 '1300': '调账加款',
 '1500': '营销奖励加款',
 '1501': '代理分润加款',
 '1502': '代付充值加款',
 '1503': '代理转账加款',
 '1504': '代理分润差错加款',
 '1505': '代付充值差错加款',
 '1700': 'EPOS支付消费',
 '1701': 'EPOS支付差错加款',
 '2000': 'POS撤销入账',
 '2001': 'POS冲正入账',
 '2002': 'POS退货入账',
 '2021': 'POS差错款减款入账',
 '2010': 'POS预授权完成撤销入账',
 '2011': 'POS预授权完成冲正入账',
 '2100': '清算扣减',
 '2101': '清算差错扣减',
 '2102': '清算失败扣减',
 '2200': '二维码退货',
 '2210': '二维码差错款减款',
 '2300': '调账减款',
 '2400': '提现',
 '2401': '自动提现',
 '2402': '提现申请',
 '2403': '提现失败退回',
 '2404': '提现成功扣款',
 '2500': '营销奖励扣减',
 '2501': '代理分润扣减',
 '2502': '代付充值减款',
 '2503': '代理转账减款',
 '2504': '代理分润差错减款',
 '2505': '代付充值差错减款',
 '2600': '冻结单扣除',
 '2700': 'EPOS支付撤销',
 '2701': 'EPOS支付差错减款',
 '3000': '转账',
 '3100': '冻结',
 '3101': '解冻',
 '3102': '冻结(转账)',
 '3103': '解冻(转账)',
 '3031': '内部对账流水调平加款',
 '3032': '内部对账流水调平减款',
 '3201': '清算冻结',
 '3300': '调账转账',
 '1901': '转账加款',
 '2901': '转账减款'
 };

export const walletTypeMap = {
  '0': '全部钱包',
  '100': 'POS D0可提现钱包',
  '110': 'POS D0可提现业务冻结钱包',
  '120': 'POS D0可提现清算冻结钱包',
  '101': 'POS D0待入账钱包',
  '111': 'POS D0待入账业务冻结钱包',
  '102': 'POS T1钱包',
  '103': 'POS T1待入账钱包',
  '113': 'POS T1待入账业务冻结钱包',
  '112': 'POS T1业务冻结钱包',
  '122': 'POS T1清算冻结钱包',
  '130': 'POS钱包账户冻结钱包',
  '200': '二维码D0可提现钱包',
  '210': '二维码D0可提现业务冻结钱包',
  '220': '二维码D0可提现清算冻结钱包',
  '201': '二维码D0待入账钱包',
  '211': '二维码D0待入账业务冻结钱包',
  '202': '二维码T1钱包',
  '203': '二维码T1待入账钱包',
  '213': '二维码T1待入账业务冻结钱包',
  '212': '二维码T1业务冻结钱包',
  '222': '二维码T1清算冻结钱包',
  '230': '二维码钱包账户冻结钱包',
  '400': '快捷支付D0可提现钱包',
  '410': '快捷支付D0可提现业务冻结钱包',
  '420': '快捷支付D0可提现清算冻结钱包',
  '401': '快捷支付D0待入账钱包',
  '411': '快捷支付D0待入账业务冻结钱包',
  '402': '快捷支付T1钱包',
  '403': '快捷支付T1待入账钱包',
  '413': '快捷支付T1待入账业务冻结钱包',
  '412': '快捷支付T1业务冻结钱包',
  '422': '快捷支付T1清算冻结钱包',
  '430': '快捷支付账户冻结钱包',
  '900': '聚合D0可提现钱包',
  '910': '聚合D0可提现业务冻结钱包',
  '920': '聚合D0可提现清算冻结钱包',
  '901': '聚合D0待入账钱包',
  '911': '聚合D0待入账业务冻结钱包',
  '902': '聚合T1钱包',
  '912': '聚合T1业务冻结钱包',
  '922': '聚合T1清算冻结钱包',
  '903': '聚合T1待入账钱包',
  '913': '聚合T1待入账业务冻结钱包',
  '930': '聚合钱包账户冻结钱包',
  '300': '营销奖励钱包',
  '310': '营销奖励业务冻结钱包',
  '320': '营销奖励清算冻结钱包',
  '330': '营销奖励账户冻结钱包',
  '600': '分润钱包',
  '610': '分润可提现业务冻结钱包',
  '620': '分润清算冻结钱包',
  '630': '分润账户冻结钱包',
  '800': '代付D0钱包',
  '810': '代付D0业务冻结钱包',
  '820': '代付D0清算冻结钱包',
  '802': '代付T1钱包',
  '822': '代付T1清算冻结钱包',
  '830': '代付账户冻结钱包'
};

</script>
