<template>
  <a-modal
    :width="800"
    :visible="visible"
    title="清算批次详情"
    :body-style="{ paddingBottom: '20px' }"
    :mask-closable="false"
    @update:visible="updateVisible"
    :footer="null"
    @close="close"
  >
    <a-divider orientation="left" :orientationMargin="0" dashed>清算批次信息</a-divider>
    <a-descriptions bordered :column="2">
      <a-descriptions-item label="ID" :span="2">{{ remitFlowInfo.id }}</a-descriptions-item>   
      <a-descriptions-item label="清算流水UUID">{{ remitFlowInfo.remitFlowUuid }}</a-descriptions-item>   
      <a-descriptions-item label="清算钱包流水UUID">{{ remitFlowInfo.walletFlowUuid }}</a-descriptions-item>   
      <a-descriptions-item label="清算渠道ID">{{ remitFlowInfo.remitChannelId }}</a-descriptions-item>   
      <a-descriptions-item label="清算渠道编号">{{ remitFlowInfo.remitChannelNo }}</a-descriptions-item>   
      <a-descriptions-item label="清算批次ID">{{ remitFlowInfo.remitBatchId }}</a-descriptions-item>   
      <a-descriptions-item label="批次号">{{ remitFlowInfo.remitBatchNo }}</a-descriptions-item>   
      <a-descriptions-item label="流水号">{{ remitFlowInfo.remitFlowNo }}</a-descriptions-item>   
      <a-descriptions-item label="机构批次号">{{ remitFlowInfo.remitChannelBatchNo }}</a-descriptions-item>   
      <a-descriptions-item label="机构流水号">{{ remitFlowInfo.remitChannelFlowNo }}</a-descriptions-item>   
      <a-descriptions-item label="清算金额">{{ remitFlowInfo.remitAmount }}</a-descriptions-item>   
      <a-descriptions-item label="清算状态">{{ remitStatus }}</a-descriptions-item>   
      <a-descriptions-item label="清算类型">{{ remitType }}</a-descriptions-item>
      <a-descriptions-item label="来源类型">{{ sourceType }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ remitFlowInfo.createTime }}</a-descriptions-item>
      <a-descriptions-item label="创建人UUID">{{ remitFlowInfo.createUserUuid }}</a-descriptions-item>
      <a-descriptions-item label="创建人姓名">{{ remitFlowInfo.createUserName }}</a-descriptions-item>
      <a-descriptions-item label="备注">{{ remitFlowInfo.remark }}</a-descriptions-item>
      <a-descriptions-item label="账户ID">{{ remitFlowInfo.accountId }}</a-descriptions-item>
      <a-descriptions-item label="账户UUID">{{ remitFlowInfo.accountUuid }}</a-descriptions-item>
      <a-descriptions-item label="钱包ID">{{ remitFlowInfo.walletId }}</a-descriptions-item>
      <a-descriptions-item label="钱包UUID">{{ remitFlowInfo.walletUuid }}</a-descriptions-item>
      <a-descriptions-item label="账户类型">{{ remitFlowInfo.accountType }}</a-descriptions-item>
      <a-descriptions-item label="银行卡号">{{ remitFlowInfo.accountNo }}</a-descriptions-item>
      <a-descriptions-item label="开户人姓名">{{ remitFlowInfo.accountName }}</a-descriptions-item>
      <a-descriptions-item label="银行编码">{{ remitFlowInfo.bankCode }}</a-descriptions-item>
      <a-descriptions-item label="银行名称">{{ remitFlowInfo.bankName }}</a-descriptions-item>
      <a-descriptions-item label="银行支行名称">{{ remitFlowInfo.bankSubName }}</a-descriptions-item>
      <a-descriptions-item label="银行行号">{{ remitFlowInfo.bankChannelNo }}</a-descriptions-item>
      <a-descriptions-item label="银行所属省">{{ remitFlowInfo.bankProvince }}</a-descriptions-item>
      <a-descriptions-item label="银行所属市">{{ remitFlowInfo.bankCity }}</a-descriptions-item>
      <a-descriptions-item label="银行所属区域">{{ remitFlowInfo.bankArea }}</a-descriptions-item>
      <a-descriptions-item label="清算费返回码">{{ remitFlowInfo.resCode }}</a-descriptions-item>
      <a-descriptions-item label="清算返回码描述">{{ remitFlowInfo.resCodeMemo }}</a-descriptions-item>
      <a-descriptions-item label="清算处理时间">{{ remitFlowInfo.resHandleTime }}</a-descriptions-item>
      <a-descriptions-item label="退款类型">{{ refundType }}</a-descriptions-item>
      <a-descriptions-item label="退款状态">{{ recStatus }}</a-descriptions-item>
      <a-descriptions-item label="退汇返回码">{{ remitFlowInfo.recCode }}</a-descriptions-item>
      <a-descriptions-item label="退汇返回码描述">{{ remitFlowInfo.recCodeMemo }}</a-descriptions-item>   
      <a-descriptions-item label="退汇处理时间">{{ remitFlowInfo.recHandleTime }}</a-descriptions-item>
      <a-descriptions-item label="交易状态">{{ transStatus }}</a-descriptions-item>
      <a-descriptions-item label="银行系统编码">{{ remitFlowInfo.bankResCode }}</a-descriptions-item>
      <a-descriptions-item label="银行响应码">{{ remitFlowInfo.bankResMessage }}</a-descriptions-item>
      <a-descriptions-item label="处理状态">{{ handleStatus }}</a-descriptions-item>
      <a-descriptions-item label="提现费率">{{ remitFlowInfo.withdrawDepositRate }}</a-descriptions-item>
      <a-descriptions-item label="提现单笔费用">{{ remitFlowInfo.withdrawDepositSingleFee }}</a-descriptions-item>
      <a-descriptions-item label="提现费用">{{ remitFlowInfo.withdrawDepositFee }}</a-descriptions-item>
      <a-descriptions-item label="收款到账金额">{{ remitFlowInfo.receiveAmount }}</a-descriptions-item>
      <a-descriptions-item label="风险预存期">{{ riskDay }}</a-descriptions-item>   
      <a-descriptions-item label="是否日结消费">{{ isDayConsume }}</a-descriptions-item>
      <a-descriptions-item label="业务类型">{{ businessType }}</a-descriptions-item>
      <a-descriptions-item label="原批次号">{{ remitFlowInfo.origBatchNo }}</a-descriptions-item>
      <a-descriptions-item label="原流水号">{{ remitFlowInfo.origFlowNo }}</a-descriptions-item>
      <a-descriptions-item label="卡号密文">{{ remitFlowInfo.accountNoCipher }}</a-descriptions-item>
      <a-descriptions-item label="卡号掩码">{{ remitFlowInfo.accountNoMask }}</a-descriptions-item>
      <a-descriptions-item label="是否密文">{{ isCipher }}</a-descriptions-item>
      <a-descriptions-item label="密钥ID">{{ remitFlowInfo.keyId }}</a-descriptions-item>
      <a-descriptions-item label="是否发送退汇邮件">{{ isSendReexchangeEmail }}</a-descriptions-item>
      <a-descriptions-item label="商户类型">{{ merchantType }}</a-descriptions-item>   
      <a-descriptions-item label="身份证号">{{ remitFlowInfo.idCardNo }}</a-descriptions-item>
      <a-descriptions-item label="系统跟踪号">{{ remitFlowInfo.traceNo }}</a-descriptions-item>
      <a-descriptions-item label="系统传输时间">{{ remitFlowInfo.traceTime }}</a-descriptions-item>
      <a-descriptions-item label="退汇原因">{{ remitFlowInfo.recReason }}</a-descriptions-item>
      <a-descriptions-item label="银行卡类型">{{ remitFlowInfo.bankCardType }}</a-descriptions-item>
      <a-descriptions-item label="清算请求时间">{{ remitFlowInfo.reqHandleTime }}</a-descriptions-item>
      <a-descriptions-item label="请求流水号">{{ remitFlowInfo.reqFlowNo }}</a-descriptions-item>
      <a-descriptions-item label="基础产品码">{{ remitFlowInfo.basicProductCode }}</a-descriptions-item>
      <a-descriptions-item label="营销产品码">{{ remitFlowInfo.marketingProductCode }}</a-descriptions-item>
      <a-descriptions-item label="提现钱包流水UUID">{{ remitFlowInfo.withdrawWalletFlowUuid }}</a-descriptions-item>   
      <a-descriptions-item label="扣减钱包流水UUID">{{ remitFlowInfo.minusWalletFlowUuid }}</a-descriptions-item>
      <a-descriptions-item label="渠道账户银行编码">{{ remitFlowInfo.secondBankCode }}</a-descriptions-item>
      <a-descriptions-item label="是否渠道账户清算">{{ isSecondRemit }}</a-descriptions-item>
      <a-descriptions-item label="渠道账户卡号">{{ remitFlowInfo.secondBankAccountNo }}</a-descriptions-item>
      <a-descriptions-item label="渠道账户卡户名（姓名）">{{ remitFlowInfo.secondBankAccountName }}</a-descriptions-item>
      <a-descriptions-item label="渠道账户卡号密文">{{ remitFlowInfo.secondBankAccountNoCipher }}</a-descriptions-item>
      <a-descriptions-item label="渠道账户卡号掩码">{{ remitFlowInfo.secondBankAccountNoMask }}</a-descriptions-item>
      <a-descriptions-item label="渠道账户卡银行行号">{{ remitFlowInfo.secondBankChannelNo }}</a-descriptions-item>
      <a-descriptions-item label="渠道账户卡银行名称">{{ remitFlowInfo.secondBankName }}</a-descriptions-item>
      <a-descriptions-item label="渠道账户卡银行支行名称">{{ remitFlowInfo.secondBankSubName }}</a-descriptions-item> 
      <a-descriptions-item label="渠道账户卡银行所属省">{{ remitFlowInfo.secondBankProvince }}</a-descriptions-item>
      <a-descriptions-item label="渠道账户卡银行所属市">{{ remitFlowInfo.secondBankCity }}</a-descriptions-item>
      <a-descriptions-item label="渠道账户卡银行所属区域">{{ remitFlowInfo.secondBankArea }}</a-descriptions-item>
      <a-descriptions-item label="对账状态">{{ checkFlag }}</a-descriptions-item>
      <a-descriptions-item label="商户名称">{{ remitFlowInfo.companyName }}</a-descriptions-item>
      <a-descriptions-item label="对私卡绑定的手机号">{{ remitFlowInfo.bankPhone }}</a-descriptions-item>
      <a-descriptions-item label="交易参考号">{{ remitFlowInfo.rrn }}</a-descriptions-item> 
      <a-descriptions-item label="清分成功交易流水号">{{ remitFlowInfo.succRrn }}</a-descriptions-item>
      <a-descriptions-item label="客户端编码">{{ remitFlowInfo.clientNo }}</a-descriptions-item>
      <a-descriptions-item label="钱包类型编码">{{ walletTypeCode }}</a-descriptions-item>
    </a-descriptions>
    <a-divider orientation="left" :orientationMargin="0" dashed>账户钱包信息</a-divider>
    <a-descriptions bordered :column="2">
      <a-descriptions-item v-for="(item, index) in accountWalletInfoParamsArr" :key="index" :label="item.parKey">{{ item.parValue }}</a-descriptions-item>
    </a-descriptions>

    <a-divider orientation="left" :orientationMargin="0" dashed>交易信息</a-divider>
    <a-descriptions bordered :column="2">
      <a-descriptions-item v-for="(item, index) in tranInfosParamsArr" :key="index" :label="item.parKey">{{ item.parValue }}</a-descriptions-item>
    </a-descriptions>

  </a-modal>
</template>

<script>

export default {
  name: 'RemitFlowDetail',
  props: {
    // 弹窗是否打开
    visible: Boolean,
    remitFlowInfo: Object,
  },
  emits: ['update:visible'],
  data(){
    return {
      accountWalletInfoparamsArr: [],
      tranInfosParamsArr: [],
    }
  },
  computed: {
    recStatus(){
      return recStatusMap[this.remitFlowInfo.recStatus];
    },
    handleStatus(){
      return handleStatusMap[this.remitFlowInfo.handleStatus]
    },
    isDayConsume(){
      return Number(this.remitFlowInfo.isDayConsume) === 1 ? "是" : "否";
    },
    isCipher(){
      return Number(this.remitFlowInfo.isCipher) === 1 ? "是" : "否";
    },
    isSendReexchangeEmail(){
      return Number(this.remitFlowInfo.isSendReexchangeEmail) === 1 ? "是" : "否"
    },
    isSecondRemit(){
      return Number(this.remitFlowInfo.isSecondRemit) === 1 ? "是" : "否"
    },
    remitType(){
      return Number(this.remitFlowInfo.remitType) === 1 ? "平台提现" : (Number(this.remitFlowInfo.remitType) === 2 ? "自行清算" : "");
    },
    sourceType(){
      return Number(this.remitFlowInfo.sourceType) === 1 ? "手工导入" : (Number(this.remitFlowInfo.sourceType) === 2 ? "平台导入" : "");
    },
    remitStatus(){
      return remitStatusMap[Number(this.remitFlowInfo.remitStatus)];
    },
    refundType(){
      return refundTypeMap[Number(this.remitFlowInfo.refundType)];
    },
    transStatus(){
      return transStatusMap[Number(this.remitFlowInfo.transStatus)];
    },
    riskDay(){
      return riskDayMap[Number(this.remitFlowInfo.riskDay)];
    },
    businessType(){
      return businessTypeMap[Number(this.remitFlowInfo.businessType)];
    },
    merchantType(){
      return (Number(this.remitFlowInfo.merchantType) === 1 ? "小微商户" : (Number(this.remitFlowInfo.merchantType) === 2 ? "机构商户" : ""));
    },
    checkFlagMap(){
      return checkFlagMap[this.remitFlowInfo.checkFlagMap];
    },
    walletTypeCode(){
      return walletTypeCodeMap[this.remitFlowInfo.walletTypeCode];
    }
  },
  watch: {
    "remitFlowInfo.accountWalletInfo"(){
      if(this.remitFlowInfo.accountWalletInfo){
        this.accountWalletInfoParamsArr = [];
        const paramdict = JSON.parse(this.remitFlowInfo.accountWalletInfo);
        //将json串转换成数组
        Object.keys(paramdict).forEach(key => this.accountWalletInfoParamsArr.push(
          {"parKey": key, "parValue": paramdict[key]}
        ))
      }
    },
    "remitFlowInfo.tranInfos"(){
      if(this.remitFlowInfo.tranInfos){
        this.tranInfosParamsArr = [];
        const paramdict = JSON.parse(this.remitFlowInfo.tranInfos);
        //将json串转换成数组
        Object.keys(paramdict).forEach(key => this.tranInfosParamsArr.push(
          {"parKey": key, "parValue": paramdict[key]}
        ))
      }
    },
  },
  methods: {
    updateVisible(value) {
      this.$emit('update:visible', value);
    },
    close(){
        this.updateVisible(false);
    },
  },
};


export const recStatusMap = {
    "0": "未退汇",
    "1": "已退汇",
    "2": "无需退汇",
}

export const handleStatusMap = {
    "0": "未处理",
    "1": "已处理",
    "2": "处理中",
    "3": "无需处理",
}

export const remitStatusMap = {
  0: "未清算",
  1: "已清算",
  2: "清算中",
  3: "清算失败",
}

export const refundTypeMap = {
  1: "原路退回",
  2: "重新付款",
}

export const transStatusMap = {
  1: "已创建",
  2: "交易成功",
  3: "交易失败",
  4: "支付中",
  5: "预下单成功（待支付）",
  6: "支付结果待查",
}

export const riskDayMap = {
  0: "D0",
  1: "T1",
}

export const businessTypeMap = {
    1: "POS",
    2: "二维码",
    0: "混合",
}

export const checkFlagMap = {
  0: "未对账",
  1: "平账",
  2: "长款",
  3: "短账",
  4: "金额不等",
}

export const walletTypeCodeMap = {
  100: "POS业务钱包",
  200: "扫码业务钱包",
  300: "营销费用钱包",
  400: "EPOS业务钱包",
  600: "分润钱包",
  800: "代付业务钱包",
  900: "聚合业务钱包",
}
</script>