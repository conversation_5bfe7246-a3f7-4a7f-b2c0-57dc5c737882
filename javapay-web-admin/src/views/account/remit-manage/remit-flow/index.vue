<template>
  <div class="ele-body">
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table
          ref="table"
          row-key="id"
          :datasource="datasource"
          :columns="columns"
          :where="where"
          v-model:selection="selection"
          :scroll="{ x: 'max-content' }"
        >
          <!-- table上边工具栏 -->
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="openEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>

          <template #bodyCell="{ column, record }">
            <!-- table操作栏按钮 -->
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情弹窗 -->
    <remitFlowDetail v-model:visible="showDetail" :remitFlowInfo="current" @done="reload" />
  </div>
</template>

<script>
import { RemitFlowApi } from '@/api/account/remit-flow/RemitFlowApi';
import RemitFlowDetail from './remit-flow-detail.vue';

export default {
  name: 'RemitFlow',
  components: {
    RemitFlowDetail,
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left',
        },
        {
          title: '清算流水UUID',
          dataIndex: 'remitFlowUuid',
        },
        {
          title: '钱包流水UUID',
          dataIndex: 'walletFlowUuid',
        },
        {
          title: '清算渠道编号',
          dataIndex: 'remitChannelNo',
        },
        {
          title: '批次号',
          dataIndex: 'remitBatchNo',
        },
        {
          title: '流水号',
          dataIndex: 'remitFlowNo',
        },
        {
          title: '机构批次号',
          dataIndex: 'remitChannelBatchNo',
        },
        {
          title: '机构流水号',
          dataIndex: 'remitChannelFlowNo',
        },
        {
          title: '清算金额',
          dataIndex: 'remitAmount',
        },
        {
          title: '清算状态',
          dataIndex: 'remitStatus',
          customRender: ({text}) => (remitStatusMap[Number(text)]),
        },
        {
          title: '清算类型',
          dataIndex: 'remitType',
          customRender: ({text}) => (Number(text) === 1 ? "平台提现" : (Number(text) === 2 ? "自行清算" : "")),
        },
        {
          title: '来源类型',
          dataIndex: 'sourceType',
          customRender: ({text}) => (Number(text) === 1 ? "手工导入" : (Number(text) === 2 ? "平台导入" : "")),
        },
        {
          title: '创建人UUID',
          dataIndex: 'createUserUuid',
        },
        {
          title: '创建人名称',
          dataIndex: 'createUserName',
        },
        {
          title: '备注',
          dataIndex: 'remark',
        },
        {
          title: '账户UUID',
          dataIndex: 'accountUuid',
        },
        {
          title: '钱包UUID',
          dataIndex: 'walletUuid',
        },
        {
          // TODO: 这个应该新样式展示吗？
          title: '账户钱包信息',
          dataIndex: 'accountWalletInfo',
        },
        {
          title: '账户类型',
          dataIndex: 'accountType',
        },
        {
          title: '银行卡号',
          dataIndex: 'accountNo',
        },
        {
          title: '开户人姓名',
          dataIndex: 'accountName',
        },
        {
          title: '银行编码',
          dataIndex: 'bankCode',
        },
        {
          title: '银行名称',
          dataIndex: 'bankName',
        },
        {
          title: '银行支行名称',
          dataIndex: 'bankSubName',
        },
        {
          title: '银行行号',
          dataIndex: 'bankChannelNo',
        },
        {
          title: '银行所属省',
          dataIndex: 'bankProvince',
        },
        {
          title: '银行所属市',
          dataIndex: 'bankCity',
        },
        {
          title: '银行所属区域',
          dataIndex: 'bankArea',
        },
        {
          title: '清算费返回码',
          dataIndex: 'resCode',
        },
        {
          title: '清算返回码描述',
          dataIndex: 'resCodeMemo',
        },
        {
          title: '清算处理时间',
          dataIndex: 'resHandleTime',
        },
        {
          title: '退款类型',
          dataIndex: 'refundType',
          customRender: ({text}) => (refundTypeMap[Number(text)]),
        },
        {
          title: '退汇状态',
          dataIndex: 'recStatus',
          customRender: ({text}) => (recStatusMap[Number(text)] || ""),
        },
        {
          title: '退汇返回码',
          dataIndex: 'recCode',
        },
        {
          title: '退汇返回码描述',
          dataIndex: 'recCodeMemo',
        },
        {
          title: '退汇处理时间',
          dataIndex: 'recHandleTime',
        },
        {
          title: '交易状态',
          dataIndex: 'transStatus',
          customRender: ({text}) => (transStatusMap[Number(text)]),
        },
        {
          title: '银行系统编码',
          dataIndex: 'bankResCode',
        },
        {
          title: '银行响应码',
          dataIndex: 'bankResMessage',
        },
        {
          title: '处理状态',
          dataIndex: 'handleStatus',
          customRender: ({text}) => (handleStatusMap[Number(text)] || ""),
        },
        {
          title: '提现费率',
          dataIndex: 'withdrawDepositRate',
        },
        {
          title: '提现单笔费用',
          dataIndex: 'withdrawDepositSingleFee',
        },
        {
          title: '提现费用',
          dataIndex: 'withdrawDepositFee'
        },
        {
          title: '收款到账金额',
          dataIndex: 'receiveAmount',
        },
        {
          title: '风险预存期',
          dataIndex: 'riskDay',
          customRender: ({text}) => (riskDayMap[Number(text)]),
        },
        {
          title: '是否日结消费',
          dataIndex: 'isDayConsume',
          customRender: ({text}) => (Number(text) === 1 ? "是" : (Number(text) === 0 ? "否" : ""))
        },
        {
          title: '业务类型',
          dataIndex: 'businessType',
          customRender: ({text}) => (businessTypeMap[Number(text)]),
        },
        {
          title: '原批次号',
          dataIndex: 'origBatchNo',
        },
        {
          title: '原流水号',
          dataIndex: 'origFlowNo',
        },
        {
          title: '卡号密文',
          dataIndex: 'accountNoCipher',
        },
        {
          title: '卡号掩码',
          dataIndex: 'accountNoMask',
        },
        {
          title: '是否密文',
          dataIndex: 'isCipher',
          customRender: ({text}) => (Number(text) === 1 ? "是" : (Number(text) === 0 ? "否" : "")),
        },
        {
          title: '是否发送退汇邮件',
          dataIndex: 'isSendReexchangeEmail',
          customRender: ({text}) => (Number(text) === 1 ? "是" : (Number(text) === 0 ? "否" : "")),
        },
        {
          title: '商户类型',
          dataIndex: 'merchantType',
          customRender: ({text}) => (Number(text) === 1 ? "小微商户" : (Number(text) === 2 ? "机构商户" : "")),
        },
        {
          title: '身份证号',
          dataIndex: 'idCardNo',
        },
        {
          title: '系统跟踪号',
          dataIndex: 'traceNo',
        },
        {
          title: '系统传输时间',
          dataIndex: 'traceTime'
        },
        {
          title: '退汇原因',
          dataIndex: 'recReason'
        },
        {
          title: '银行卡类型',
          dataIndex: 'bankCardType'
        },
        {
          title: '清算请求时间',
          dataIndex: 'reqHandleTime'
        },
        {
          title: '请求流水号',
          dataIndex: 'reqFlowNo'
        },
        {
          title: '基础产品码',
          dataIndex: 'basicProductCode'
        },
        {
          title: '营销产品码',
          dataIndex: 'marketingProductCode'
        },
        {
          title: '提现钱包流水UUID',
          dataIndex: 'withdrawWalletFlowUuid'
        },
        {
          title: '扣减钱包流水UUID',
          dataIndex: 'minusWalletFlowUuid'
        },
        {
          title: '渠道账户银行编码',
          dataIndex: 'secondBankCode'
        },
        {
          title: '是否渠道账户清算',
          dataIndex: 'isSecondRemit',
          customRender: ({text}) => (Number(text) === 1 ? "是" : (Number(text) === 0 ? "否" : "")),
        },
        {
          title: '渠道账户卡号',
          dataIndex: 'secondBankAccountNo'
        },
        {
          title: '渠道账户卡户名',
          dataIndex: 'secondBankAccountName'
        },
        {
          title: '渠道账户卡号密文',
          dataIndex: 'secondBankAccountNoCipher'
        },
        {
          title: '渠道账户卡号掩码',
          dataIndex: 'secondBankAccountNoMask'
        },
        {
          title: '渠道账户卡银行行号',
          dataIndex: 'secondBankChannelNo'
        },
        {
          title: '渠道账户卡银行名称',
          dataIndex: 'secondBankName'
        },
        {
          title: '渠道账户卡银行支行名称',
          dataIndex: 'secondBankSubName'
        },
        {
          title: '渠道账户卡银行所属省',
          dataIndex: 'secondBankProvince'
        },
        {
          title: '渠道账户卡银行所属市',
          dataIndex: 'secondBankCity'
        },
        {
          title: '渠道账户卡银行所属区域',
          dataIndex: 'secondBankArea'
        },
        {
          title: '对账状态',
          dataIndex: 'checkFlag',
          customRender: ({text}) => (checkFlagMap[Number(text)]),
        },
        {
          title: '商户名称',
          dataIndex: 'companyName'
        },
        {
          title: '对私卡绑定的手机号',
          dataIndex: 'bankPhone'
        },
        {
          title: '交易参考号',
          dataIndex: 'rrn'
        },
        {
          title: '清分成功交易流水号',
          dataIndex: 'succRrn'
        },
        {
          title: '交易信息',
          dataIndex: 'tranInfos'
        },
        {
          title: '客户端编码',
          dataIndex: 'clientNo'
        },
        {
          title: '删除状态',
          dataIndex: 'deleteStatus'
        },
        {
          title: '钱包类型编码',
          dataIndex: 'walletTypeCode',
          customRender: ({text}) => (walletTypeCodeMap[Number(text)]),
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 150,
          align: 'center'
        }
      ],
      // 表格搜索条件
      where: {},
      // 表格选中数据
      selection: [],
      // 当前编辑数据
      current: null,
      // 是否显示详情弹窗
      showDetail: false
    };
  },
  methods: {
    /**
     * 搜索按钮
     *
     * <AUTHOR>
     * @date 2023/07/13 10:29
     */
    reload() {
      this.selection = [];
      this.$refs.table.reload({ page: 1 });
    },

    /**
     * 重置搜索
     *
     * <AUTHOR>
     * @date 2023/07/13 10:29
     */
    reset() {
      this.reload();
    },

    /**
     * 查看详情
     *
     * <AUTHOR>
     * @date 2023/07/13 10:29
     */
    async handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    /**
     * 获取表格数据
     *
     * <AUTHOR>
     * @date 2023/07/13 10:29
     */
    datasource({ page, limit, where, orders }) {
      return RemitFlowApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};

export const recStatusMap = {
    "0": "未退汇",
    "1": "已退汇",
    "2": "无需退汇",
}

export const handleStatusMap = {
    "0": "未处理",
    "1": "已处理",
    "2": "处理中",
    "3": "无需处理",
}

export const remitStatusMap = {
  0: "未清算",
  1: "已清算",
  2: "清算中",
  3: "清算失败",
}

export const transStatusMap = {
  1: "已创建",
  2: "交易成功",
  3: "交易失败",
  4: "支付中",
  5: "预下单成功（待支付）",
  6: "支付结果待查",
}

export const riskDayMap = {
  0: "D0",
  1: "T1",
}

export const businessTypeMap = {
    1: "POS",
    2: "二维码",
    0: "混合",
}

export const checkFlagMap = {
  0: "未对账",
  1: "平账",
  2: "长款",
  3: "短账",
  4: "金额不等",
}

export const walletTypeCodeMap = {
  100: "POS业务钱包",
  200: "扫码业务钱包",
  300: "营销费用钱包",
  400: "EPOS业务钱包",
  600: "分润钱包",
  800: "代付业务钱包",
  900: "聚合业务钱包",
}

export const refundTypeMap = {
  1: "原路退回",
  2: "重新付款",
}
</script>
