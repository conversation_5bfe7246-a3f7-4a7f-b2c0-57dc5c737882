<template>
  <a-modal
    :width="800"
    :visible="visible"
    title="清算批次详情"
    :body-style="{ paddingBottom: '20px' }"
    :mask-closable="false"
    @update:visible="updateVisible"
    :footer="null"
    @close="close"
  >
    <a-divider orientation="left" :orientationMargin="0" dashed>清算笔数/金额</a-divider>
    <a-descriptions bordered :column="2">
      <a-descriptions-item label="批次号" :span="2">{{ remitBatchInfo.remitBatchNo }}</a-descriptions-item>
      <a-descriptions-item label="渠道批次号" :span="2">{{ remitBatchInfo.remitChannelBatchNo }}</a-descriptions-item>
      <a-descriptions-item label="清算渠道编号" :span="2">{{ remitBatchInfo.remitChannelNo }}</a-descriptions-item>
      <a-descriptions-item label="清算总笔数">{{ remitBatchInfo.remitTotalCount }}</a-descriptions-item>
      <a-descriptions-item label="清算总金额">{{ remitBatchInfo.remitTotalAmount }}</a-descriptions-item>
      <a-descriptions-item label="清算成功笔数">{{ remitBatchInfo.remitSuccessCount }}</a-descriptions-item>
      <a-descriptions-item label="清算成功金额">{{ remitBatchInfo.remitSuccessAmount }}</a-descriptions-item>
      <a-descriptions-item label="清算失败笔数">{{ remitBatchInfo.remitFailureCount }}</a-descriptions-item>
      <a-descriptions-item label="清算失败金额">{{ remitBatchInfo.remitFailureAmount }}</a-descriptions-item>
      <a-descriptions-item label="清算处理中笔数">{{ remitBatchInfo.remitProcessingCount }}</a-descriptions-item>
      <a-descriptions-item label="清算处理中金额">{{ remitBatchInfo.remitProcessingAmount }}</a-descriptions-item>      
    </a-descriptions>
    <a-divider orientation="left" :orientationMargin="0" dashed>清算状态</a-divider>
    <a-descriptions bordered :column="2">
      <a-descriptions-item label="清算类型">{{ remitType }}</a-descriptions-item>
      <a-descriptions-item label="清算状态">{{ remitBatchInfo.remitStatus }}</a-descriptions-item>
      <a-descriptions-item label="来源类型">{{ sourceType }}</a-descriptions-item>
      <a-descriptions-item label="批次锁定状态">{{ batchLockStatus }}</a-descriptions-item>
      <a-descriptions-item label="付款方式">{{ remitMethod }}</a-descriptions-item>
      <a-descriptions-item label="风险预存期">{{ riskDay }}</a-descriptions-item>
      <a-descriptions-item label="业务类型">{{ businessType }}</a-descriptions-item>
      <a-descriptions-item label="处理状态">{{ handleStatus }}</a-descriptions-item>
      <a-descriptions-item label="总行号">{{ remitBatchInfo.origBatchNo }}</a-descriptions-item>
      <a-descriptions-item label="创建人姓名">{{ remitBatchInfo.createUserName }}</a-descriptions-item>
      <a-descriptions-item label="创建人UUID">{{ remitBatchInfo.createUserUuid }}</a-descriptions-item>
      <a-descriptions-item label="商户类型">{{ merchantType }}</a-descriptions-item>
      <a-descriptions-item label="客户端编码">{{ remitBatchInfo.clientNo }}</a-descriptions-item>
      <a-descriptions-item label="请求流水号">{{ remitBatchInfo.reqFlowNo }}</a-descriptions-item>
      <a-descriptions-item label="是否渠道账户清算">{{ isSecondRemit }}</a-descriptions-item>
   
    </a-descriptions>
    <a-divider orientation="left" :orientationMargin="0" dashed>审核情况</a-divider>
    <a-descriptions bordered :column="2">
      <a-descriptions-item label="清算批次审核状态" :span="2">{{ remitBatchCheckStatus }}</a-descriptions-item>
      <a-descriptions-item label="审核类型">{{ checkType }}</a-descriptions-item>
      <a-descriptions-item label="审核状态">{{ checkStatus }}</a-descriptions-item>
      <a-descriptions-item label="审核人UUID">{{ remitBatchInfo.checkUserUuid }}</a-descriptions-item>
      <a-descriptions-item label="复核人UUID">{{ remitBatchInfo.recheckUserUuid }}</a-descriptions-item>
      <a-descriptions-item label="审核人姓名">{{ remitBatchInfo.checkUserName }}</a-descriptions-item>
      <a-descriptions-item label="复核人姓名">{{ remitBatchInfo.recheckUserName }}</a-descriptions-item>
      <a-descriptions-item label="审核时间">{{ remitBatchInfo.checkTime }}</a-descriptions-item>
      <a-descriptions-item label="复核时间">{{ remitBatchInfo.recheckTime }}</a-descriptions-item>
      <a-descriptions-item label="备注">{{ remitBatchInfo.remak }}</a-descriptions-item>
    </a-descriptions>
    <a-divider orientation="left" :orientationMargin="0" dashed>请求/回盘/退汇</a-divider>
    <a-descriptions bordered :column="2">
      <a-descriptions-item label="请求状态">{{ reqHandleStatus }}</a-descriptions-item>
      <a-descriptions-item label="请求时间">{{ remitBatchInfo.reqTime }}</a-descriptions-item>
      <a-descriptions-item label="请求文件路径" :span="2">{{ remitBatchInfo.reqFilePath }}</a-descriptions-item>
      <a-descriptions-item label="回盘状态">{{ resHandleStatus }}</a-descriptions-item>
      <a-descriptions-item label="回盘时间">{{ remitBatchInfo.resTime }}</a-descriptions-item>
      <a-descriptions-item label="回盘文件路径" :span="2">{{ remitBatchInfo.resFilePath }}</a-descriptions-item>
      <a-descriptions-item label="退汇状态">{{ recHandleStatus }}</a-descriptions-item>
      <a-descriptions-item label="退汇时间">{{ remitBatchInfo.recTime }}</a-descriptions-item>
      <a-descriptions-item label="退汇文件路径" :span="2">{{ remitBatchInfo.recFilePath }}</a-descriptions-item>
    </a-descriptions>
  </a-modal>
</template>

<script>

export default {
  name: 'RemitBatchDetail',
  props: {
    // 弹窗是否打开
    visible: Boolean,
    remitBatchInfo: Object,
  },
  emits: ['update:visible'],
  computed: {
    checkStatus(){
      return checkStatusMap[this.remitBatchInfo.checkStatus];
    },
    businessType(){
      return businessTypeMap[this.remitBatchInfo.businessType];
    },
    handleStatus(){
      return handleStatusMap[this.remitBatchInfo.handleStatus];
    },
    remitBatchCheckStatus(){
      return remitBatchCheckStatusMap[this.remitBatchInfo.remitBatchCheckStatus];
    },
    riskDay(){
      return this.remitBatchInfo.riskDay === 1 ? "T1" : this.remitBatchInfo.riskDay === 0 ? "D0" : "";
    },
    merchantType(){
      return this.remitBatchInfo.merchantType === 1 ? "小微商户" : this.remitBatchInfo.merchantType === 2 ? "机构商户" : "";
    },
    remitMethod(){
      return this.remitBatchInfo.remitMethod === 1 ? "自动付款" : this.remitBatchInfo.remitMethod === 2 ? "人工审核" : "";
    },
    batchLockStatus(){
      return this.remitBatchInfo.batchLockStatus  === 1 ? "已锁定" : this.remitBatchInfo.batchLockStatus === 0 ? "未锁定" : "";
    },
    checkType(){
      return this.remitBatchInfo.checkType === 1 ? "系统自动审核" : this.remitBatchInfo.checkType === 2 ? "人工审核" : "";
    },
    sourceType(){
      return this.remitBatchInfo.sourceType === 1 ? "手工创建" : this.remitBatchInfo.sourceType === 2 ? "平台导入" : "";
    },
    reqHandleStatus(){
      return reqHandleStatusMap[this.remitBatchInfo.reqHandleStatus];
    },
    resHandleStatus(){
      return resHandleStatusMap[this.remitBatchInfo.resHandleStatus];
    },
    recHandleStatus(){
      return recHandleStatusMap[this.remitBatchInfo.recHandleStatus];
    },
    isSecondRemit(){
      return this.remitBatchInfo.isSecondRemit ? "是" : "否";
    },
    remitType(){
      return this.remitBatchInfo.isSecondRemit === 1 ? "平台清算" : this.remitBatchInfo.isSecondRemit === 2 ? "自行提现" : "";
    },
    remitStatus(){
      return remitStatusMap[this.remitBatchInfo.remitStatus];
    }
  },
  methods: {
    updateVisible(value) {
      this.$emit('update:visible', value);
    },
    close(){
        this.updateVisible(false);
    }
  },
};

export const checkStatusMap = {
    0: "未审核",
    1: "初审通过",
    2: "复审通过",
    3: "审核不通过",
}

export const businessTypeMap = {
    1: "POS",
    2: "二维码",
    0: "混合",
}

export const handleStatusMap = {
    0: "未处理",
    1: "部分处理",
    2: "已处理",
    3: "无需处理",
}

export const remitBatchCheckStatusMap = {
    0: "未审核",
    1: "初审通过",
    2: "复审通过",
    3: "审核不通过",
}

export const reqHandleStatusMap = {
    0: "未发请求",
    1: "已生成文件",
    2: "已发请求",
}

export const resHandleStatusMap = {
    0: "未回盘",
    1: "已下载回盘文件",
    2: "已回盘",
}

export const recHandleStatusMap = {
    0: "未退汇",
    1: "已退汇",
    2: "无需退汇",
}

export const remitStatusMap = {
  0: "未清算",
  1: "已清算",
  2: "清算中",
  3: "清算失败",
}
</script>