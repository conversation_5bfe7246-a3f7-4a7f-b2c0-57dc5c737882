<template>
  <div class="ele-body">
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="清算批次号">
              <a-input v-model:value.trim="where.remitBatchNo" placeholder="请输入清算批次号" allow-clear />
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table
          ref="table"
          row-key="id"
          :datasource="datasource"
          :columns="columns"
          :where="where"
          v-model:selection="selection"
          :scroll="{ x: 'max-content' }"
        >
          <!-- table上边工具栏 -->

          <template #bodyCell="{ column, record }">
            <!-- table操作栏按钮 -->
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>                
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情弹窗 -->
    <RemitBatchDetail v-model:visible="showDetail" :remitBatchInfo="current" @done="reload" />
  </div>
</template>

<script>
import { RemitBatchApi } from '@/api/account/remit-batch/RemitBatchApi';
import RemitBatchDetail from './remit-batch-detail.vue';

export default {
  name: 'RemitBatch',
  components: {
    RemitBatchDetail,
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left',
        },
        {
          title: '批次号',
          dataIndex: 'remitBatchNo',
        },
        {
          title: '清算总笔数',
          dataIndex: 'remitTotalCount',
        },
        {
          title: '清算成功笔数',
          dataIndex: 'remitSuccessCount',
        },
        {
          title: '清算失败笔数',
          dataIndex: 'remitFailureCount',
        },
        {
          title: '清算处理中笔数',
          dataIndex: 'remitProcessingCount',
        },
        {
          title: '清算总金额',
          dataIndex: 'remitTotalAmount',
        },
        {
          title: '清算成功金额',
          dataIndex: 'remitSuccessAmount',
        },
        {
          title: '清算失败金额',
          dataIndex: 'remitFailureAmount',
        },
        {
          title: '清算处理中金额',
          dataIndex: 'remitProcessingAmount',
        },
        {
          title: '清算类型',
          dataIndex: 'remitType',
          customRender: ({text}) => (Number(text) === 1 ? "平台清算" : (Number(text) === 2 ? "自行提现" : "")),
        },
        {
          title: '清算状态',
          dataIndex: 'remitStatus',
          customRender: ({text}) => (remitStatusMap[Number(text)]),
        },
        {
          title: '来源类型',
          dataIndex: 'sourceType',
          customRender: ({text}) => (Number(text) === 1 ? "手工创建" : (Number(text) === 2 ? "平台导入" : "")),
        },
        {
          title: '审核类型',
          dataIndex: 'checkType',
          customRender: ({text}) => (Number(text) === 1 ? "系统自动审核" : (Number(text) === 2 ? "人工审核" : "")),
        },
        {
          title: '批次锁定状态',
          dataIndex: 'batchLockStatus',
          customRender: ({text}) => (Number(text) === 1 ? "已锁定" : (Number(text) === 0 ? "未锁定" : "")),
        },
        {
          title: '付款方式',
          dataIndex: 'remitMethod',
          customRender: ({text}) => (Number(text) === 1 ? "自动付款" : (Number(text) === 2 ? "人工审核" : "")),
        },
        {
          title: '审核人UUID',
          dataIndex: 'checkUserUuid'
        },
        {
          title: '审核人姓名',
          dataIndex: 'checkUserName'
        },
        {
          title: '审核时间',
          dataIndex: 'checkTime'
        },
        {
          title: '复核人UUID',
          dataIndex: 'recheckUserUuid'
        },
        {
          title: '复核人姓名',
          dataIndex: 'recheckUserName'
        },
        {
          title: '复核时间',
          dataIndex: 'recheckTime'
        },
        {
          title: '审核状态',
          dataIndex: 'checkStatus',
          customRender: ({ text }) => (checkStatusMap[Number(text)] || ""),
        },
        {
          title: '备注',
          dataIndex: 'remark',
        },
        {
          title: '渠道批次号',
          dataIndex: 'remitChannelBatchNo',
        },
        {
          title: '请求状态',
          dataIndex: 'reqHandleStatus',
          customRender: ({text}) => (reqHandleStatusMap[Number(text)]),
        },
        {
          title: '回盘状态',
          dataIndex: 'resHandleStatus',
          customRender: ({text}) => (resHandleStatusMap[Number(text)]),
        },
        {
          title: '退汇状态',
          dataIndex: 'recHandleStatus',
          customRender: ({text}) => (recHandleStatusMap[Number(text)]),
        },
        {
          title: '请求时间',
          dataIndex: 'reqTime',
        },
        {
          title: '回盘时间',
          dataIndex: 'resTime',
        },
        {
          title: '退汇时间',
          dataIndex: 'recTime',
        },
        {
          title: '请求文件路径',
          dataIndex: 'reqFilePath',
        },
        {
          title: '回盘文件路径',
          dataIndex: 'resFilePath',
        },
        {
          title: '退汇文件路径',
          dataIndex: 'recFilePath',
        },
        {
          title: '清算渠道编号',
          dataIndex: 'remitChannelNo',
        },
        {
          title: '风险预存期',
          dataIndex: 'riskDay',
          customRender: ({text}) => (Number(text) === 1 ? "T1" : (Number(text) === 0 ? "D0" : "")),
        },
        {
          title: '业务类型',
          dataIndex: 'businessType',
          customRender: ({text}) => (businessTypeMap[Number(text)] || ""),
        },
        {
          title: '处理状态',
          dataIndex: 'handleStatus',
          customRender: ({text}) => (handleStatusMap[Number(text)] || ""),
        },
        {
          title: '总行号',
          dataIndex: 'origBatchNo'
        },
        {
          title: '创建人姓名',
          dataIndex: 'createUserName'
        },
        {
          title: '创建人UUID',
          dataIndex: 'createUserUuid'
        },
        {
          title: '商户类型',
          dataIndex: 'merchantType',
          customRender: ({text}) => (Number(text) === 1 ? "小微商户" : (Number(text) === 2 ? "机构商户" : "")),
        },
        {
          title: '客户端编码',
          dataIndex: 'clientNo'
        },
        {
          title: '请求流水号',
          dataIndex: 'reqFlowNo'
        },
        {
          title: '是否渠道账户清算',
          dataIndex: 'isSecondRemit'
        },
        {
          title: '清算批次审核状态',
          dataIndex: 'remitBatchCheckStatus',
          customRender: ({text}) => (remitBatchCheckStatusMap[Number(text)] || ""),
        },
        {
          title: '操作',
          key: 'action',
          fixed: "right",
          width: 150,
          align: 'center',
        }
      ],
      // 表格搜索条件
      where: {},
      // 表格选中数据
      selection: [],
      // 当前编辑数据
      current: null,
      // 是否显示详情弹窗
      showDetail: false
    };
  },
  methods: {
    /**
     * 搜索按钮
     *
     * <AUTHOR>
     * @date 2023/07/13 10:29
     */
    reload() {
      this.selection = [];
      this.$refs.table.reload({ page: 1 });
    },

    /**
     * 重置搜索
     *
     * <AUTHOR>
     * @date 2023/07/13 10:29
     */
    reset() {
      this.reload();
    },

    /**
     * 详情
     *
     * <AUTHOR>
     * @date 2023/07/13 10:29
     */
    async handleDetail(row) {
      this.showDetail = true;
      this.current = row;
    },

    /**
     * 获取表格数据
     *
     * <AUTHOR>
     * @date 2023/07/13 10:29
     */
    datasource({ page, limit, where, orders }) {
      return RemitBatchApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};

export const checkStatusMap = {
    0: "未审核",
    1: "初审通过",
    2: "复审通过",
    3: "审核不通过",
}

export const businessTypeMap = {
    1: "POS",
    2: "二维码",
    0: "混合",
}

export const handleStatusMap = {
    0: "未处理",
    1: "部分处理",
    2: "已处理",
    3: "无需处理",
}

export const remitBatchCheckStatusMap = {
    0: "未审核",
    1: "初审通过",
    2: "复审通过",
    3: "审核不通过",
}

export const reqHandleStatusMap = {
    0: "未发请求",
    1: "已生成文件",
    2: "已发请求",
}

export const resHandleStatusMap = {
    0: "未回盘",
    1: "已下载回盘文件",
    2: "已回盘",
}

export const recHandleStatusMap = {
    0: "未退汇",
    1: "已退汇",
    2: "无需退汇",
}

export const remitStatusMap = {
  0: "未清算",
  1: "已清算",
  2: "清算中",
  3: "清算失败",
}
</script>
