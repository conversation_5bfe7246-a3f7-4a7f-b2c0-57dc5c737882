<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="路由编号">
              <a-input v-model:value.trim="where.routeNo" placeholder="请输入路由编号" allow-clear />
            </a-form-item>
            <a-form-item label="路由名称">
              <a-input v-model:value.trim="where.routeName" placeholder="请输入路由名称" allow-clear />
            </a-form-item>
            <a-form-item label="金额">
              <a-input v-model:value.trim="where.transAmount" placeholder="请输入金额" allow-clear />
            </a-form-item>
            <a-form-item label="渠道编号">
              <a-select v-model:value="where.channelId" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option v-for="item in channelData" :value="item.id" :label="item.channelName">{{ item.channelName }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="状态">
              <a-select v-model:value="where.status" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">无效</a-select-option>
                <a-select-option :value="1">有效</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="账户类型">
              <a-select v-model:value="where.bankAccountType" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">全部</a-select-option>
                <a-select-option :value="1">对公</a-select-option>
                <a-select-option :value="2">对私</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="借贷路由">
              <a-select v-model:value="where.bankCardType" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">全部</a-select-option>
                <a-select-option :value="1">贷记卡</a-select-option>
                <a-select-option :value="2">借记卡</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="节假日路由">
              <a-select v-model:value="where.routeDayType" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">全部</a-select-option>
                <a-select-option :value="1">工作日</a-select-option>
                <a-select-option :value="2">节假日</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="用户类型路由">
              <a-select v-model:value="where.routeUserType" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">平台运营</a-select-option>
                <a-select-option :value="2">大区</a-select-option>
                <a-select-option :value="3">运营中心</a-select-option>
                <a-select-option :value="4">代理商</a-select-option>
                <a-select-option :value="5">商户</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="业务类型">
              <a-select v-model:value="where.businessType" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">全部</a-select-option>
                <a-select-option :value="100">POS</a-select-option>
                <a-select-option :value="200">扫码</a-select-option>
                <a-select-option :value="300">营销费用</a-select-option>
                <a-select-option :value="400">EPOS</a-select-option>
                <a-select-option :value="600">分润</a-select-option>
                <a-select-option :value="800">代付</a-select-option>
                <a-select-option :value="900">聚合</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="清算方式">
              <a-select v-model:value="where.remitMode" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">全部</a-select-option>
                <a-select-option :value="1">提现</a-select-option>
                <a-select-option :value="2">代付</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="风险预存期">
              <a-select v-model:value="where.riskDayRule" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="100">全部</a-select-option>
                <a-select-option :value="0">D0</a-select-option>
                <a-select-option :value="1">T1</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="客户端编号">
              <a-input v-model:value.trim="where.clientNo" placeholder="请输入客户端编号" allow-clear />
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table
          ref="table"
          row-key="id"
          :datasource="datasource"
          :columns="columns"
          :where="where"
          v-model:selection="selection"
          :scroll="{ x: 'max-content' }"
        >
          <!-- table上边工具栏 -->
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="openEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-switch :checked="record.status === 1" @change="checked => editState(checked, record)" />
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleEdit(record)">修改</a>
                <a-divider type="vertical" />
                <a-popconfirm title="确定要更改状态吗？" @confirm="handleStatus(record)">
                  <a class="ele-text-danger">{{ record.status===1 ? "停用":"开启" }}</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 编辑弹窗 -->
    <remit-route-edit v-model:visible="showEdit" :showData="current" :channelData="channelData" @done="reload" />
  </div>
</template>

<script>
import { RemitRouteApi } from '@/api/account/remit-route/RemitRouteApi';
import { RemitChannelApi } from '@/api/account/remit-channel/RemitChannelApi';

import RemitRouteEdit from "./remit-route-edit.vue"
import { reactive, toRefs } from 'vue-demi';

export default {
  name: 'RemitRoute',
  components: {
    RemitRouteEdit,
  },
  setup(){
    const data = reactive({
      channelData: [],
    })

    init();
    async function init(){
      data.channelData = await getAllChannelData();
    }

    async function getAllChannelData(){
      return await RemitChannelApi.findAll();
    }
    return {
      ...toRefs(data),
    };
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '路由状态',
          dataIndex: 'status',
          align: 'center',
          customRender: ({text}) => (Number(text) === 1 ? "有效" : (Number(text) === 0 ? "无效" : ""))
        },
        {
          title: '路由编号',
          dataIndex: 'routeNo',
          key: "routeNo",
          align: 'center'
        },
        {
          title: '路由名称',
          dataIndex: 'routeName',
          align: 'center'
        },
        {
          title: '交易最小金额',
          dataIndex: 'transMinAmount',
          align: 'center',
        },
        {
          title: '交易最大金额',
          dataIndex: 'transMaxAmount',
          align: 'center',
        },
        {
          title: '优先级',
          dataIndex: 'priority',
          width: 160,
          align: 'center',
        },
        {
          title: '渠道编号',
          dataIndex: 'channelId',
          width: 160,
          align: 'center',
          customRender: ({text}) => (this.channelDataMap[Number(text)])
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          width: 160,
          align: 'center',
        },
        {
          title: '开始时间',
          dataIndex: 'startTime',
          width: 160,
          align: 'center',
        },
        {
          title: '结束时间',
          dataIndex: 'endTime',
          width: 160,
          align: 'center',
        },
        {
          title: '银行编码',
          dataIndex: 'bankCode',
          width: 160,
          align: 'center',
        },
        {
          title: '账户类型',
          dataIndex: 'bankAccountType',
          width: 160,
          align: 'center',
          customRender: ({text}) => (bankAccountTypeMap[Number(text)])
        },
        {
          title: "借贷路由",
          dataIndex: "bankCardType",
          width: 160,
          align: 'center',
          customRender: ({text}) => (bankCardTypeMap[Number(text)])
        },
        {
          title: '节假日路由',
          dataIndex: 'routeDayType',
          width: 160,
          align: 'center',
          customRender: ({text}) => (routeDayTypeMap[Number(text)])
        },
        {
          title: '路由用户类型',
          dataIndex: 'routeUserType',
          width: 160,
          align: 'center',
          customRender: ({text}) => (userTypeMap[Number(text)])
        },
        {
          title: '客户端编号',
          dataIndex: 'clientNo',
          width: 160,
          align: 'center'
        },
        {
          title: '清算方式',
          dataIndex: 'remitMode',
          width: 160,
          align: 'center',
          customRender: ({text}) => (remitModeMap[Number(text)])
        },
        {
          title: '风险预存期',
          dataIndex: 'riskDayRule',
          width: 160,
          align: 'center',
          customRender: ({text}) => (riskDayRuleMap[Number(text)])
        },
        {
          title: '业务类型',
          dataIndex: 'businessType',
          customRender: ({ text }) => (businessTypeMap[Number(text)]),
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 200,
          align: 'center'
        },
      ],
      // 表格搜索条件
      where: {},
      selection: [],
      current: null,
      showDetail: false,
      showEdit: false
    };
  },
  computed: {
    channelDataMap(){
      return this.channelData.reduce((obj,item) => (obj[item.id] = item.channelName, obj), {})
    },
    },
  methods: {
    reload() {
      this.selection = [];
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.selection = [];
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleStatus(row) {
      //设置状态
      RemitRouteApi.updateStatus({"id": row.id, "status": 1- row.status});
      row.status = 1 - row.status;
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    handleEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    openEdit(row){
      this.current = row;
      this.showEdit = true;
    },

    datasource({ page, limit, where, orders }) {
      return RemitRouteApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  },

};


export const bankAccountTypeMap = {
  0: "全部",
  1: "对公",
  2: "对私",
}

export const bankCardTypeMap = {
  0: "全部",
  1: "贷记卡",
  2: "借记卡"
}

export const routeDayTypeMap = {
  0: "全部",
  1: "工作日",
  2: "节假日",
}

export const remitModeMap = {
  0: "全部",
  1: "提现",
  2: "代付",
}

export const  riskDayRuleMap = {
  0: "D0",
  1: "T1",
  100: "全部"
}

export const businessTypeMap = {
  0: "全部",
  100: "POS",
  200: "扫码",
  300: "营销费用",
  400: "EPOS",
  600: "分润",
  800: "代付",
  900: "聚合",
}

export const userTypeMap = {
  100: "全部",
  3: "机构",
  4: "商户"
}

</script>
