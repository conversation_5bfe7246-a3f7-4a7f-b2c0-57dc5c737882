<template>
  <a-modal
    :width="800"
    :visible="visible"
    :confirm-loading="loading"
    :forceRender="true"
    :mask-closable="false"
    :title="isUpdate ? '修改' : '新建'"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @cancel="cancel"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules" :gutter="24" :layout="'vertical'">
      <a-row :gutter="24">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="路由编号" name="routeNo">
            <a-input v-model:value="form.routeNo" placeholder="请输入路由编号" allow-clear autocomplete="off" />
          </a-form-item>
          <a-form-item label="路由名称" name="routeName">
            <a-input v-model:value="form.routeName" placeholder="请输入路由名称" allow-clear autocomplete="off" />
          </a-form-item>
          <a-form-item label="最大金额" name="transMaxAmount">
            <a-input-number
              v-model:value="form.transMaxAmount"
              :min="0"
              placeholder="请输入最大金额"
              class="remit-route-input-number"
              allow-clear
              autocomplete="off"
            />
          </a-form-item>
          <a-form-item label="优先级" name="priority">
            <a-input-number
              v-model:value="form.priority"
              :min="0"
              placeholder="请输入优先级"
              class="remit-route-input-number"
              allow-clear
              autocomplete="off"
            />
          </a-form-item>
          <a-form-item label="结束时间" name="endTime">
            <a-time-picker v-model:value="form.endTime" format="HH:mm:ss" valueFormat="HH:mm:ss" />
          </a-form-item>
          <a-form-item label="账户类型" name="bankAccountType">
            <a-select v-model:value="form.bankAccountType" style="width: 250px" placeholder="请选择">
              <a-select-option :value="0">全部</a-select-option>
              <a-select-option :value="1">对公</a-select-option>
              <a-select-option :value="2">对私</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="借贷路由" name="bankCardType">
            <a-select v-model:value="form.bankCardType" style="width: 250px" placeholder="请选择">
              <a-select-option :value="0">全部</a-select-option>
              <a-select-option :value="1">贷记卡</a-select-option>
              <a-select-option :value="2">借记卡</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="用户类型" name="routeUserType">
            <a-select v-model:value="form.routeUserType" style="width: 250px" placeholder="请选择">
              <a-select-option :value="100">全部</a-select-option>
              <a-select-option :value="3">机构</a-select-option>
              <a-select-option :value="4">商户</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="风险预存期" name="riskDayRule">
            <a-select v-model:value="form.riskDayRule" style="width: 250px" placeholder="请选择">
              <a-select-option :value="100">全部</a-select-option>
              <a-select-option :value="0">D0</a-select-option>
              <a-select-option :value="1">T1</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="客户端编号" name="clientNo">
            <a-input v-model:value="form.clientNo" placeholder="请输入客户端编号" allow-clear autocomplete="off" />
          </a-form-item>
          <a-form-item label="最小金额" name="transMinAmount">
            <a-input-number
              v-model:value="form.transMinAmount"
              :min="0"
              placeholder="请输入最小金额"
              class="remit-route-input-number"
              allow-clear
              autocomplete="off"
            />
          </a-form-item>
          <a-form-item label="渠道编号" name="channelId">
            <a-select v-model:value="form.channelId" style="width: 200px" placeholder="请选择" allow-clear>
              <a-select-option v-for="(item, key) in channelData" :key="key" :value="item.id" :label="item.channelName">{{
                item.channelName
              }}</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="开始时间" name="startTime">
            <a-time-picker v-model:value="form.startTime" format="HH:mm:ss" valueFormat="HH:mm:ss" />
          </a-form-item>
          <a-form-item label="银行编码" name="bankCode">
            <a-input v-model:value="form.bankCode" placeholder="格式:请用英文逗号;隔开" allow-clear autocomplete="off" />
          </a-form-item>
          <a-form-item label="业务类型" name="businessType">
            <a-select v-model:value="form.businessType" style="width: 250px" placeholder="请选择">
              <a-select-option :value="0">全部</a-select-option>
              <a-select-option :value="100">POS</a-select-option>
              <a-select-option :value="200">扫码</a-select-option>
              <a-select-option :value="300">营销费用</a-select-option>
              <a-select-option :value="400">EPOS</a-select-option>
              <a-select-option :value="600">分润</a-select-option>
              <a-select-option :value="800">代付</a-select-option>
              <a-select-option :value="900">聚合</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="节假日路由" name="routeDayType">
            <a-select v-model:value="form.routeDayType" style="width: 250px" placeholder="请选择">
              <a-select-option :value="0">全部</a-select-option>
              <a-select-option :value="1">工作日</a-select-option>
              <a-select-option :value="2">节假日</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="清算方式" name="remitMode">
            <a-select v-model:value="form.remitMode" style="width: 250px" placeholder="请选择">
              <a-select-option :value="0">全部</a-select-option>
              <a-select-option :value="1">提现</a-select-option>
              <a-select-option :value="2">代付</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { RemitRouteApi } from '@/api/account/remit-route/RemitRouteApi';

export default {
  name: 'RemitRouteEdit',
  components: {},
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    showData: Object,
    channelData: Array
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: Object.assign({}, this.showData),
      // 表单验证规则
      rules: {
        routeNo: [{ required: true, message: '请输入路由编号', type: 'string', trigger: 'blur' }],
        clientNo: [{ required: true, message: '请输入客户端编号', type: 'string', trigger: 'blur' }],
        routeName: [{ required: true, message: '请输入路由名称', type: 'string', trigger: 'blur' }],
        channelId: [{ required: true, message: '请选择渠道', type: 'number', trigger: 'change' }],
        priority: [{ required: true, message: '请输入优先级', type: 'number', trigger: 'blur' }],
        bankAccountType: [{ required: true, message: '请输入账户类型', type: 'number', trigger: 'change' }],
        businessType: [{ required: true, message: '请选择业务类型', type: 'number', trigger: 'change' }],
        bankCardType: [{ required: true, message: '请选择借贷路由', type: 'number', trigger: 'change' }],
        routeDayType: [{ required: true, message: '请选择节假日路由', type: 'number', trigger: 'change' }],
        routeUserType: [{ required: true, message: '请选择路由用户类型', type: 'number', trigger: 'change' }],
        remitMode: [{ required: true, message: '请选择清算方式', type: 'number', trigger: 'change' }],
        riskDayRule: [{ required: true, message: '请选择风险预存期', type: 'number', trigger: 'change' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false
    };
  },
  watch: {
    visible() {
      if (this.visible) {
        if (this.showData) {
          this.form = Object.assign({}, this.showData);
          this.isUpdate = true;
        } else {
          this.form = {
            bankAccountType: 0,
            businessType: 0,
            bankCardType: 0,
            routeDayType: 0,
            routeUserType: 100,
            remitMode: 0,
            riskDayRule: 100
          };
          this.isUpdate = false;
        }
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
        }
      }
    }
  },
  methods: {
    /**
     * 保存和编辑
     *
     * <AUTHOR>
     * @date 2023/06/05 14:28
     */
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      // 执行编辑或修改
      if (this.isUpdate) {
        result = RemitRouteApi.edit(this.form);
      } else {
        result = RemitRouteApi.add(this.form);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 如果是新增，则form表单置空
          if (!this.isUpdate) {
            this.form = {};
          }

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    /**
     * 更新编辑界面的弹框是否显示
     *
     * @param value true-显示，false-隐藏
     * <AUTHOR>
     * @date 2023/06/29 09:39
     */
    updateVisible(value) {
      this.$emit('update:visible', value);
    },
    //取消事件
    cancel() {
      this.form = Object.assign({}, this.showData);
      this.$refs.form.clearValidate();
    }
  }
};
</script>
<style scoped>
.remit-route-input-number {
  width: 250px;
}
</style>