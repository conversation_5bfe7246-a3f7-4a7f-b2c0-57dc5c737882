<template>
  <div class="account-wallet-detail">
    <a-row :gutter="24">
      <a-col :md="24" :sm="24" :xs="24">
        <a-form-item label="钱包UUID" name="walletUuid">
          <a-input v-model:value="wallet.walletUuid" placeholder="请输入账户UUID" allow-clear :disabled="isDisabled" />
        </a-form-item>
      </a-col>
      <a-col :md="12" :sm="24" :xs="24">
        <a-form-item label="D0钱包状态" name="unionD0WalletStatus">
          <a-input v-model:value="unionD0WalletStatus" placeholder="请输入D0钱包状态" allow-clear :disabled="isDisabled" />
        </a-form-item>
        <a-form-item label="D0提现限额" name="withdrawAmountLimit">
          <a-input v-model:value="wallet.walletLimit.withdrawAmountLimit" placeholder="请输入D0提现限额" allow-clear :disabled="isDisabled" />
        </a-form-item>
        <a-form-item label="D0钱包费率" name="unionD0WithdrawDepositRate">
          <a-input v-model:value="wallet.userRate.unionD0WithdrawDepositRate" placeholder="请输入D0钱包费率" allow-clear :disabled="isDisabled" />
        </a-form-item>
        <a-form-item label="D1钱包费率" name="unionD1WithdrawDepositRate">
          <a-input v-model:value="wallet.userRate.unionD1WithdrawDepositRate" placeholder="请输入D1钱包费率" allow-clear :disabled="isDisabled" />
        </a-form-item>
        <a-form-item label="T1钱包费率" name="unionT1WithdrawDepositRate">
          <a-input v-model:value="wallet.userRate.unionT1WithdrawDepositRate" placeholder="请输入T1钱包费率" allow-clear :disabled="isDisabled" />
        </a-form-item>
        <a-form-item label="D0钱包余额" name="unionD0Wallet">
          <a-input v-model:value="wallet.unionD0Wallet" placeholder="请输入D0钱包余额" allow-clear :disabled="isDisabled" />
        </a-form-item>
        <a-form-item label="D0清算冻结钱包余额" name="unionD0RemitFrozenWallet">
          <a-input v-model:value="wallet.unionD0RemitFrozenWallet" placeholder="请输入D0清算冻结钱包余额" allow-clear :disabled="isDisabled" />
        </a-form-item>
      </a-col>
      <a-col :md="12" :sm="24" :xs="24">
        <a-form-item label="T1钱包状态" name="unionT1WalletStatus">
          <a-input v-model:value="unionT1WalletStatus" placeholder="请输入T1钱包状态" allow-clear :disabled="isDisabled" />
        </a-form-item>
        <a-form-item label="D0提现累计" name="unionD0WithdrawCumulate">
          <a-input v-model:value="wallet.walletCumulate.unionD0WithdrawCumulate" placeholder="请输入D0提现累计" allow-clear :disabled="isDisabled" />
        </a-form-item>
        <a-form-item label="D0钱包单笔费用" name="unionD0WithdrawDepositSingleFee">
          <a-input v-model:value="wallet.userRate.unionD0WithdrawDepositSingleFee" placeholder="请输入D0钱包单笔费用" allow-clear :disabled="isDisabled" />
        </a-form-item>
        <a-form-item label="D1钱包单笔费用" name="unionD1WithdrawDepositSingleFee">
          <a-input v-model:value="wallet.userRate.unionD1WithdrawDepositSingleFee" placeholder="请输入D1钱包单笔费用" allow-clear :disabled="isDisabled" />
        </a-form-item>
        <a-form-item label="T1钱包单笔费用" name="unionT1WithdrawDepositSingleFee">
          <a-input v-model:value="wallet.userRate.unionT1WithdrawDepositSingleFee" placeholder="请输入T1钱包单笔费用" allow-clear :disabled="isDisabled" />
        </a-form-item>
        <a-form-item label="D0业务冻结钱包余额" name="unionD0BusinessFrozenWallet">
          <a-input v-model:value="wallet.unionD0BusinessFrozenWallet" placeholder="请输入D0业务冻结钱包余额" allow-clear :disabled="isDisabled" />
        </a-form-item>
      </a-col>
      <a-col :md="12" :sm="24" :xs="24">
        <a-form-item label="D0待入账钱包余额" name="unionD0PreWallet">
          <a-input v-model:value="wallet.unionD0PreWallet" placeholder="请输入D0待入账钱包余额" allow-clear :disabled="isDisabled" />
        </a-form-item>
        <a-form-item label="T1钱包余额" name="unionT1Wallet">
          <a-input v-model:value="wallet.unionT1Wallet" placeholder="请输入T1钱包余额" allow-clear :disabled="isDisabled" />
        </a-form-item>
        <a-form-item label="T1清算冻结钱包余额" name="unionT1RemitFrozenWallet">
          <a-input v-model:value="wallet.unionT1RemitFrozenWallet" placeholder="请输入T1清算冻结钱包余额" allow-clear :disabled="isDisabled" />
        </a-form-item>
      </a-col>
      <a-col :md="12" :sm="24" :xs="24">
        <a-form-item label="D0待入账业务冻结钱包余额" name="unionD0BusinessFrozenPreWallet">
          <a-input v-model:value="wallet.unionD0BusinessFrozenPreWallet" placeholder="请输入D0待入账钱包余额" allow-clear :disabled="isDisabled" />
        </a-form-item>
        <a-form-item label="T1业务冻结钱包余额" name="unionT1BusinessFrozenWallet">
          <a-input v-model:value="wallet.unionT1BusinessFrozenWallet" placeholder="请输入T1业务冻结钱包余额" allow-clear :disabled="isDisabled" />
        </a-form-item>
      </a-col>
      <a-col :md="12" :sm="24" :xs="24">
        <a-form-item label="T1待入账钱包余额" name="unionT1PreWallet">
          <a-input v-model:value="wallet.unionT1PreWallet" placeholder="请输入T1待入账钱包余额" allow-clear :disabled="isDisabled" />
        </a-form-item>
        <a-form-item label="冻结钱包余额" name="unionWalletAccountFrozenWallet">
          <a-input v-model:value="wallet.unionWalletAccountFrozenWallet" placeholder="请输入冻结钱包余额" allow-clear :disabled="isDisabled" />
        </a-form-item>
      </a-col>
      <a-col :md="12" :sm="24" :xs="24">
        <a-form-item label="T1待入账业务冻结钱包余额" name="unionT1BusinessFrozenPreWallet">
          <a-input v-model:value="wallet.unionT1BusinessFrozenPreWallet" placeholder="请输入T1待入账业务冻结钱包余额" allow-clear :disabled="isDisabled" />
        </a-form-item>
      </a-col>
    </a-row>
  </div>
</template>
<script>
export default {
  name: "WalletDetail",
  props: {
    wallet: Object,
    isDisabled: Boolean,
    walletType: Number,
    },
  setup(props){
    if(!props.wallet.walletCumulate){
      props.wallet.walletCumulate = {}
    }
    if(!props.wallet.accountLimit){
      props.wallet.wallettLimit = {}
    }
    if(!props.wallet.userRate){
      props.wallet.userRate = {}
    }
    if(!props.wallet.walletStatus){
      props.wallet.walletStatus = {}
    }
  },
  computed: {
    unionD0WalletStatus(){
      return StatusMap[this.wallet.walletStatus.unionD0WalletStatus] || "未知"
    },
    unionT1WalletStatus(){
      return StatusMap[this.wallet.walletStatus.unionT1WalletStatus] || "未知"
    }
  }
  
}
export const StatusMap = {
  1: "正常",
  2: "冻结(只进不出)",
  3: "冻结(无法交易)"
}
</script>
