<template>
  <a-modal
    :width="800"
    :visible="visible"
    title="账户详情"
    :body-style="{ paddingBottom: '20px' }"
    :mask-closable="false"
    @update:visible="updateVisible"
    :footer="null"
  >
    <a-form ref="form" :model="form" :layout="activeTabKey === '1' ? 'horizontal' : 'vertical'">
      <a-tabs v-model:activeKey="activeTabKey" type="card">
        <!-- 基本信息 -->
        <a-tab-pane key="1" tab="基本信息">
          <!-- 基本信息 -->
          <a-row :gutter="24">
            <a-col :md="24" :sm="24" :xs="24">
              <a-form-item label="账户UUID" name="accountUuid">
                <a-input v-model:value="form.accountUuid" placeholder="请输入账户UUID" allow-clear :disabled="isDisabled" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="机构编号" name="orgMerchantNo">
                <a-input v-model:value="form.orgMerchantNo" placeholder="请输入机构编号" allow-clear :disabled="isDisabled" />
              </a-form-item>
              <a-form-item label="法人姓名" name="realName">
                <a-input v-model:value="form.realName" placeholder="请输入法人姓名" allow-clear :disabled="isDisabled" />
              </a-form-item>
              <a-form-item label="用户类型" name="userType">
                <a-input v-model:value="userType" placeholder="请输入用户类型" allow-clear :disabled="isDisabled" />
              </a-form-item>
              <a-form-item label="是否机构商户" name="isOrgMerchant">
                <a-input v-model:value="isOrgMerchant" placeholder="请输入是否机构商户" allow-clear :disabled="isDisabled" />
              </a-form-item>
              <a-form-item label="创建时间" name="createTime">
                <a-input v-model:value="form.createTime" placeholder="请输入创建时间" allow-clear :disabled="isDisabled" />
              </a-form-item>
              <a-form-item label="账户状态" name="status">
                <a-input v-model:value="accountStatus" placeholder="请输入商户状态" allow-clear :disabled="isDisabled" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="上级机构编号" name="superAgentNo">
                <a-input v-model:value="form.superAgentNo" placeholder="请输入上级机构编号" allow-clear :disabled="isDisabled" />
              </a-form-item>
              <a-form-item label="企业名称" name="companyName">
                <a-input v-model:value="form.companyName" placeholder="请输入企业名称" allow-clear :disabled="isDisabled" />
              </a-form-item>
              <a-form-item label="账户类型" name="accountType">
                <a-input v-model:value="accountType" placeholder="请输入账户类型" allow-clear :disabled="isDisabled" />
              </a-form-item>
              <a-form-item label="是否结算卡结算" name="isSelfCardSett">
                <a-input v-model:value="isSelfCardSett" placeholder="请输入是否结算卡结算" allow-clear :disabled="isDisabled" />
              </a-form-item>
              <a-form-item label="请求流水号" name="reqFlowNo">
                <a-input v-model:value="form.reqFlowNo" placeholder="请输入请求流水号" allow-clear :disabled="isDisabled" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-tab-pane>

        <!-- 分润钱包 -->
        <a-tab-pane key="2" tab="分润钱包">
          <WalletDetail :isDisabled="true" :wallet="form.profitWallet" :walletType="1" />
        </a-tab-pane>

        <!-- 营销费用钱包 -->
        <a-tab-pane key="3" tab="营销费用钱包">
          <WalletDetail :isDisabled="true" :wallet="form.rewardWallet" :walletType="2" />
        </a-tab-pane>

        <!-- 代付钱包 -->
        <a-tab-pane key="4" tab="代付钱包">
          <WalletDetail :isDisabled="true" :wallet="form.payForAnotherWallet" :walletType="3" />
        </a-tab-pane>

      </a-tabs>
    </a-form>
  </a-modal>
</template>


<script>
import { reactive, toRefs } from 'vue-demi';
import WalletDetail from "../_components/WalletDetail.vue"

export default {
  name: 'AgentWalletManageDetail',
  components: { WalletDetail },
  props: {
    // 弹窗是否打开
    visible: Boolean,
    accountDetail: Object,
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      isDisabled: true,
      optType: 3,
      activeTabKey: '1',
    });

    if(!props.accountDetail.profitWallet){
      props.accountDetail.profitWallet = {}
    }
    if(!props.accountDetail.rewardWallet){
      props.accountDetail.rewardWallet = {}
    }
    if(!props.accountDetail.payForAnotherWallet){
      props.accountDetail.payForAnotherWallet = {}
    }
   
    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      updateVisible
    };
  },
  data() {
    return {
      form: Object.assign({}, this.accountDetail),
    }
  },
  computed: {
    isOrgMerchant(){
      return this.form.isOrgMerchant === 1 ? "是" : "否"
    },
    isSelfCardSett(){
      return this.form.isSelfCardSett === 1 ? "是" : "否"
    },
    accountType(){
      return merchantTypeMap[this.form.accountType] || "未知"
    },
    userType(){
      return userTypeMap[this.form.userType] || "未知"
    },
    accountStatus(){
      return accountStatusMap[this.form.status] || "未知"
    }
  }
};

export const merchantTypeMap = {
  1: "小微",
  2: "企业",
  3: "个体工商户",
}
export const userTypeMap = {
  0: "平台运营",
  1: "大区",
  2: "运营中心",
  3: "代理商",
  4: "商户"
}
export const accountStatusMap = {
  0: "未激活",
  1: "正常",
  2: "冻结"
}
</script>