<template>
  <a-modal
    :width="500"
    :visible="visible"
    :confirm-loading="loading"
    :title="`修改钱包状态 (${form.accountId})`"
    :body-style="{ paddingBottom: '8px' }"
    :mask-closable="false"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 5 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 19 }, sm: { span: 24 } }"
    >
      <a-form-item label="钱包类型" name="walletTypeCode">
        <a-select v-model:value="form.walletTypeCode" style="width: 100%" placeholder="请选择" allow-clear>
          <a-select-option :value="300">营销奖励钱包</a-select-option>
          <a-select-option :value="600">分润钱包</a-select-option>
          <a-select-option :value="800">代付D0钱包</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="钱包状态 " name="unionWalletStatus">
        <a-select v-model:value="form.unionWalletStatus" style="width: 100%" placeholder="请选择" allow-clear>
          <a-select-option :value="1">正常</a-select-option>
          <a-select-option :value="2">限制出(只进不出)</a-select-option>
          <a-select-option :value="3">冻结(无法交易)</a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { AgentAccountApi } from '@/api/account/agent-account/AgentAccountApi';

export default {
  props: {
    data: Object,
    visible: Boolean
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {},
      // 表单验证规则
      rules: {
        walletTypeCode: [{ required: true, message: '请选择钱包类型' }],
        unionWalletStatus: [{ required: true, message: '请选择钱包状态' }]
      },
      // 提交状态
      loading: false
    };
  },
  mounted() {
    if (this.data) {
      this.form = Object.assign({}, this.data);
    }
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      AgentAccountApi.editWalletStatus(this.form)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
