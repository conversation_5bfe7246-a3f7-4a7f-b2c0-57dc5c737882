<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="账户UUID">
              <a-input v-model:value.trim="where.accountUuid" placeholder="请输入账户UUID" allow-clear />
            </a-form-item>
            <a-form-item label="钱包UUID">
              <a-input v-model:value="where.walletUuid" style="width: 200px" placeholder="请输入钱包UUID" allow-clear />
            </a-form-item>
            <a-form-item label="支付通道">
              <a-select v-model:value="where.channelCode" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="item.channelCode" v-for="item in channelCodes" :key="item.id">{{ item.channelName }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="机构商户号">
              <a-input v-model:value="where.orgMerchantNo" style="width: 200px" placeholder="请输入机构商户号" allow-clear />
            </a-form-item>
            <a-form-item label="钱包类型">
              <a-select v-model:value="where.walletTypeCode" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="100">POS业务钱包</a-select-option>
                <a-select-option :value="200">扫码业务钱包</a-select-option>
                <a-select-option :value="300">营销费用钱包</a-select-option>
                <a-select-option :value="400">EPOS业务钱包</a-select-option>
                <a-select-option :value="600">分润钱包</a-select-option>
                <a-select-option :value="700">B2B代付钱包</a-select-option>
                <a-select-option :value="800">代付业务钱包</a-select-option>
                <a-select-option :value="900">聚合业务钱包</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table
          ref="table"
          row-key="id"
          :datasource="datasource"
          :columns="columns"
          :where="where"
          v-model:selection="selection"
          :scroll="{ x: 'max-content' }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-switch :checked="record.status === 1" @change="checked => editState(checked, record)" />
            </template>
            
            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <MerchantWalletManageDetail v-if="showDetail" v-model:visible="showDetail" :merchantWalletInfo="current" :channelCodesMap="channelCodesMap" />
  </div>
</template>

<script>
import MerchantWalletManageDetail from './merchant-wallet-manage-detail.vue'
import {MerchantWalletManageApi} from '@/api/account/merchant-account/MerchantWalletManageApi';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';

export default {
  name: 'MerchantWalletManage',
  components: {
    MerchantWalletManageDetail,
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '账户ID',
          dataIndex: 'accountId',
          align: 'center',
          fixed: 'center'
        },
        {
          title: '账户UUID',
          dataIndex: 'accountUuid',
          align: 'center'
        },
        {
          title: '支付通道',
          dataIndex: 'channelCode',
          align: 'center',
          customRender:  ({text}) => (this.channelCodesMap[text] ? this.channelCodesMap[text] : text)
        },
        {
          title: '机构商户编号',
          dataIndex: 'orgMerchantNo',
          align: 'center',
        },
        {
          title: '钱包类型',
          dataIndex: 'walletTypeCode',
          align: 'center',
          customRender: ({text}) => (WalletTypeEnum[text] ? WalletTypeEnum[text] : text)
        },
        {
          title: '钱包UUID',
          dataIndex: 'walletUuid',
          align: 'center'
        },
        {
          title: 'D0钱包状态',
          ataIndex: 'walletStatus.unionD0WalletStatus',
          align: 'center',
          customRender: ({record}) => ( record.walletStatus ? WalletStatusMap[record.walletStatus.unionD0WalletStatus] : "")
        },
        {
          title: 'T1钱包状态',
          dataIndex: 'walletStatus.unionT1WalletStatus',
          align: 'center',
          customRender: ({record}) => ( record.walletStatus ? WalletStatusMap[record.walletStatus.unionT1WalletStatus] : "")
        },
        {
          title: 'D0钱包余额',
          dataIndex: 'unionD0Wallet',
          width: 160,
          align: 'center',
          customRender: ({text}) => (Number(text).toFixed(2))
        },
        {
          title: 'D0业务冻结钱包余额',
          dataIndex: 'unionD0BusinessFrozenWallet',
          width: 160,
          align: 'center',
          customRender: ({text}) => (Number(text).toFixed(2))
        },
        {
          title: 'D0清算冻结钱包余额',
          dataIndex: 'unionD0RemitFrozenWallet',
          width: 160,
          align: 'center',
          customRender: ({text}) => (Number(text).toFixed(2))
        },
        {
          title: 'D0待入账钱包余额',
          dataIndex: 'unionD0PreWallet',
          width: 160,
          align: 'center',
          customRender: ({text}) => (Number(text).toFixed(2))
        },
        {
          title: 'D0待入账业务冻结钱包余额',
          dataIndex: 'unionD0BusinessFrozenPreWallet',
          width: 160,
          align: 'center',
          customRender: ({text}) => (Number(text).toFixed(2))
        },
        {
          title: 'T1钱包余额',
          dataIndex: 'unionT1Wallet',
          width: 160,
          align: 'center',
          customRender: ({text}) => (Number(text).toFixed(2))
        },
        {
          title: 'T1业务冻结钱包余额',
          dataIndex: 'unionT1BusinessFrozenWallet',
          width: 160,
          align: 'center',
          customRender: ({text}) => (Number(text).toFixed(2))
        },
        {
          title: 'T1清算冻结钱包余额',
          dataIndex: 'unionT1RemitFrozenWallet',
          width: 160,
          align: 'center',
          customRender: ({text}) => (Number(text).toFixed(2))
        },
        {
          title: 'T1待入账钱包余额',
          dataIndex: 'unionT1PreWallet',
          width: 160,
          align: 'center',
          customRender: ({text}) => (Number(text).toFixed(2))
        },
        {
          title: 'T1待入账业务冻结钱包余额',
          dataIndex: 'unionT1BusinessFrozenPreWallet',
          width: 160,
          align: 'center',
          customRender: ({text}) => (Number(text).toFixed(2))
        },
        {
          title: '上日T1结余',
          dataIndex: 'unionBeforeRemitSurplusAmount',
          width: 160,
          align: 'center',
          customRender: ({text}) => (Number(text).toFixed(2))
        },
        {
          title: ' 账户冻结钱包',
          dataIndex: 'unionWalletAccountFrozenWallet',
          align: 'center',
          customRender: ({text}) => (Number(text).toFixed(2))
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 150,
          align: 'center'
        }
      ],
      // 表格搜索条件
      where: {},
      selection: [],
      current: null,
      channelCodes: [],
      channelCodesMap: {},
      showDetail: false,
      showEdit: false,
    };
  },
  watch: {
    channelCodes(){
      if(this.channelCodes.length > 1){
        this.channelCodes.map(item => this.channelCodesMap[item.channelCode] = item.channelName);
      }
    }
  },
  mounted() {
    this.getChannelList();
  },
  methods: {
    async getChannelList() {
      const data = await ChannelManageApi.list({ validStatus: 1 });
      this.channelCodes = data || [];
    },
    
    reload() {
      this.selection = [];
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.selection = [];
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    handleEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    datasource({ page, limit, where, orders }) {      
      return MerchantWalletManageApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  },
  };

export const WalletTypeEnum = {
  "100": "POS业务钱包",
  "200": "扫码业务钱包",
  "300": "营销费用钱包",
  "400": "EPOS业务钱包",
  "600": "分润钱包",
  "800": "代付业务钱包",
  "900": "聚合业务钱包",
}
export const WalletStatusMap = {
  1: "正常",
  2: "冻结(只进不出)",
  3: "冻结(无法交易)"
}
</script>
