<template>
  <a-modal
    :width="800"
    :visible="visible"
    title="钱包详情"
    :body-style="{ paddingBottom: '20px' }"
    :mask-closable="false"
    @update:visible="updateVisible"
    :footer="null"
  >
    <a-descriptions bordered :column="2">
      <a-descriptions-item label="钱包UUID" :span="2">{{ merchantWalletInfo.walletUuid }}</a-descriptions-item>
      <a-descriptions-item label="支付通道">{{ transformChannelCode(merchantWalletInfo.channelCode) }}</a-descriptions-item>
      <a-descriptions-item label="机构商户号">{{ merchantWalletInfo.orgMerchantNo }}</a-descriptions-item>
      <a-descriptions-item label="钱包类型" :span="2">{{ transformWalletType(merchantWalletInfo.walletTypeCode) }}</a-descriptions-item>
      <a-descriptions-item label="D0钱包状态">
        <a-tag v-if="form.walletStatus.unionD0WalletStatus === 1" color="cyan">正常</a-tag>
        <a-tag v-else-if="form.walletStatus.unionD0WalletStatus === 2" color="blue">冻结(只进不出)</a-tag>
        <a-tag v-else-if="form.walletStatus.unionD0WalletStatus === 3" color="purple">冻结(无法交易)</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="T1钱包状态">
        <a-tag v-if="form.walletStatus.unionT1WalletStatus === 1" color="cyan">正常</a-tag>
        <a-tag v-else-if="form.walletStatus.unionT1WalletStatus === 2" color="blue">冻结(只进不出)</a-tag>
        <a-tag v-else-if="form.walletStatus.unionT1WalletStatus === 3" color="purple">冻结(无法交易)</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="D0提现限额">{{ form.walletLimit.withdrawAmountLimit }}</a-descriptions-item>
      <a-descriptions-item label="D0提现累计">{{ form.walletCumulate.unionD0WithdrawCumulate }}</a-descriptions-item>
      <a-descriptions-item label="D0钱包费率">{{ form.userRate.unionD0WithdrawDepositRate }}</a-descriptions-item>
      <a-descriptions-item label="D0钱包单笔费用">{{ form.userRate.unionD0WithdrawDepositSingleFee }}</a-descriptions-item>
      <a-descriptions-item label="D1钱包费率">{{ form.userRate.unionD1WithdrawDepositRate }}</a-descriptions-item>
      <a-descriptions-item label="D1钱包单笔费用">{{ form.userRate.unionD1WithdrawDepositSingleFee}}</a-descriptions-item>
      <a-descriptions-item label="T1钱包费率">{{ form.userRate.unionT1WithdrawDepositRate }}</a-descriptions-item>
      <a-descriptions-item label="T1钱包单笔费用">{{ form.userRate.unionT1WithdrawDepositSingleFee }}</a-descriptions-item>
      <a-descriptions-item label="D0钱包余额">{{ merchantWalletInfo.unionD0Wallet }}</a-descriptions-item>
      <a-descriptions-item label="D0业务钱包冻结余额">{{ merchantWalletInfo.unionD0BusinessFrozenWallet }}</a-descriptions-item>
      <a-descriptions-item label="D0清算冻结钱包余额" :span="2">{{ merchantWalletInfo.unionD0RemitFrozenWallet }}</a-descriptions-item>
      <a-descriptions-item label="D0待入账钱包余额">{{ merchantWalletInfo.unionD0PreWallet }}</a-descriptions-item>
      <a-descriptions-item label="D0待入账业务冻结钱包余额">{{ merchantWalletInfo.unionD0BusinessFrozenPreWallet }}</a-descriptions-item>
      <a-descriptions-item label="T1钱包余额">{{ merchantWalletInfo.unionT1Wallet }}</a-descriptions-item>
      <a-descriptions-item label="T1业务冻结钱包余额">{{ merchantWalletInfo.unionT1BusinessFrozenWallet }}</a-descriptions-item>
      <a-descriptions-item label="T1清算冻结钱包余额" :span="2">{{ merchantWalletInfo.unionT1RemitFrozenWallet }}</a-descriptions-item>
      <a-descriptions-item label="T1待入账钱包余额">{{ merchantWalletInfo.unionT1PreWallet }}</a-descriptions-item>
      <a-descriptions-item label="T1待入账业务冻结钱包余额">{{ merchantWalletInfo.unionT1BusinessFrozenPreWallet }}</a-descriptions-item>
      <a-descriptions-item label="冻结钱包余额" :span="2">{{ merchantWalletInfo.unionWalletAccountFrozenWallet }}</a-descriptions-item>
    </a-descriptions>
  </a-modal>
</template>

<script>
import { reactive, toRefs } from 'vue';
import { MerchantWalletManageApi } from '@/api/account/merchant-account/MerchantWalletManageApi';

export default {
  name: 'MerchantWalletManageDetail',
  props: {
    // 弹窗是否打开
    visible: Boolean,
    merchantWalletInfo: Object,
    channelCodesMap: Object,
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {
        walletStatus: {},
        walletCumulate: {},
        walletLimit: {},
        userRate: {},
      },
    });

    init()
    async function init() {
      await getDetail();
    }

    async function getDetail() {
      const info = await MerchantWalletManageApi.detail({ id: props.merchantWalletInfo.id });
      data.form = Object.assign({}, info);
      if(!data.form.walletLimit){
        data.form.walletLimit = {}
      }
      if(!data.form.walletCumulate){
        data.form.walletCumulate = {}
      }
      if(!data.form.userRate){
        data.form.userRate = {}
      }
      if(!data.form.walletStatus){
        data.form.walletStatus = {}
      }
    }

    const updateVisible = value => {
      if(data.form){
        context.emit('update:visible', value);
      }
    };

    return {
      ...toRefs(data),
      updateVisible,
    };
  },
  methods: {
    transformChannelCode(i){
      return this.channelCodesMap[i] ? this.channelCodesMap[i] : i;
    },
    transformWalletType(i){
      return WalletTypeEnum[i] ? WalletTypeEnum[i] : i;
    },
  }
};

export const WalletTypeEnum = {
  "100": "POS业务钱包",
  "200": "扫码业务钱包",
  "300": "营销费用钱包",
  "400": "EPOS业务钱包",
  "600": "分润钱包",
  "800": "代付业务钱包",
  "900": "聚合业务钱包",
}
</script>