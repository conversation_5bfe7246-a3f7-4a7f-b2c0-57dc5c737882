<template>
  <div class="payment-success-example">
    <a-card title="支付成功处理示例">
      <div class="demo-section">
        <h3>模拟支付成功</h3>
        <p>点击下面的按钮模拟不同的支付成功场景：</p>
        
        <a-space wrap>
          <a-button type="primary" @click="simulateWhopPayment">
            模拟 Whop 支付成功
          </a-button>
          <a-button @click="simulateCustomAddressPayment">
            自定义地址支付
          </a-button>
          <a-button @click="simulateBatchPayment">
            批量支付处理
          </a-button>
          <a-button @click="toggleChatBox">
            {{ showChatBox ? '隐藏' : '显示' }}聊天框
          </a-button>
        </a-space>
      </div>

      <a-divider />

      <div class="demo-section">
        <h3>支付数据配置</h3>
        <a-form :model="paymentForm" layout="vertical">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="用户ID">
                <a-input v-model:value="paymentForm.userId" placeholder="请输入用户ID" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="支付金额">
                <a-input-number 
                  v-model:value="paymentForm.amount" 
                  :min="0.01" 
                  :step="0.01"
                  placeholder="请输入支付金额"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="产品名称">
                <a-input v-model:value="paymentForm.productName" placeholder="请输入产品名称" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="自定义地址（可选）">
                <a-input v-model:value="paymentForm.customAddress" placeholder="留空则自动生成" />
              </a-form-item>
            </a-col>
          </a-row>

          <a-form-item label="自定义消息内容（可选）">
            <a-textarea 
              v-model:value="paymentForm.customMessage" 
              :rows="3"
              placeholder="留空则使用默认消息模板"
            />
          </a-form-item>
        </a-form>
      </div>

      <a-divider />

      <div class="demo-section">
        <h3>处理结果</h3>
        <div v-if="lastResult" class="result-display">
          <a-alert
            :type="lastResult.success ? 'success' : 'error'"
            :message="lastResult.success ? '处理成功' : '处理失败'"
            :description="lastResult.message"
            show-icon
          />
          
          <div v-if="lastResult.success && lastResult.address" class="address-result">
            <h4>生成的地址：</h4>
            <a-input 
              :value="lastResult.address" 
              readonly
              style="margin-bottom: 8px"
            >
              <template #suffix>
                <copy-outlined @click="copyToClipboard(lastResult.address)" />
              </template>
            </a-input>
            <a-button type="link" @click="openAddress(lastResult.address)">
              访问地址
            </a-button>
          </div>
        </div>
        
        <div v-else class="no-result">
          <a-empty description="暂无处理结果" />
        </div>
      </div>
    </a-card>

    <!-- 聊天框组件 -->
    <PaymentChatBox 
      v-model:visible="showChatBox"
      :auto-show="true"
      @message-received="onMessageReceived"
    />

    <!-- 消息接收日志 -->
    <a-drawer
      v-model:visible="showMessageLog"
      title="消息接收日志"
      placement="right"
      width="400"
    >
      <div class="message-log">
        <div v-for="(log, index) in messageLogs" :key="index" class="log-item">
          <div class="log-time">{{ formatTime(log.timestamp) }}</div>
          <div class="log-content">{{ log.content }}</div>
          <div v-if="log.address" class="log-address">
            地址: {{ log.address }}
          </div>
        </div>
        
        <div v-if="messageLogs.length === 0" class="no-logs">
          <a-empty description="暂无消息日志" />
        </div>
      </div>
    </a-drawer>

    <!-- 底部操作栏 -->
    <div class="bottom-actions">
      <a-button @click="showMessageLog = true">
        查看消息日志 ({{ messageLogs.length }})
      </a-button>
      <a-button @click="clearLogs">清空日志</a-button>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue';
import { message } from 'ant-design-vue';
import { CopyOutlined } from '@ant-design/icons-vue';
import PaymentChatBox from '@/components/PaymentChatBox/index.vue';
import { handleWhopPaymentSuccess, PaymentSuccessHandler } from '@/utils/payment-success-handler';

export default {
  name: 'PaymentSuccessExample',
  components: {
    PaymentChatBox,
    CopyOutlined
  },
  setup() {
    const showChatBox = ref(false);
    const showMessageLog = ref(false);
    const lastResult = ref(null);
    const messageLogs = reactive([]);

    // 支付表单数据
    const paymentForm = reactive({
      userId: 'user_12345',
      amount: 99.99,
      productName: 'VIP会员',
      customAddress: '',
      customMessage: ''
    });

    // 生成随机支付ID
    const generatePaymentId = () => {
      return 'pay_' + Date.now() + '_' + Math.random().toString(36).substring(2, 8);
    };

    // 模拟 Whop 支付成功
    const simulateWhopPayment = async () => {
      const paymentData = {
        userId: paymentForm.userId,
        paymentId: generatePaymentId(),
        amount: paymentForm.amount,
        productName: paymentForm.productName,
        userAddress: paymentForm.customAddress || null
      };

      const options = {};
      if (paymentForm.customMessage) {
        options.customMessage = paymentForm.customMessage;
      }

      try {
        const result = await handleWhopPaymentSuccess(paymentData, paymentForm.customAddress);
        lastResult.value = result;
        
        if (result.success) {
          showChatBox.value = true;
        }
      } catch (error) {
        console.error('支付处理失败:', error);
        lastResult.value = {
          success: false,
          message: error.message
        };
      }
    };

    // 模拟自定义地址支付
    const simulateCustomAddressPayment = async () => {
      const customAddress = 'https://custom-app.com/special-access/' + Date.now();
      
      const paymentData = {
        userId: paymentForm.userId,
        paymentId: generatePaymentId(),
        amount: paymentForm.amount,
        productName: paymentForm.productName + ' (自定义地址)',
        userAddress: customAddress
      };

      try {
        const result = await PaymentSuccessHandler.handlePaymentSuccess(paymentData, {
          sendNotification: true,
          generateAddress: false // 使用提供的地址，不生成新地址
        });
        
        lastResult.value = result;
        
        if (result.success) {
          showChatBox.value = true;
          
          // 手动发送聊天消息
          PaymentSuccessHandler.sendChatMessage(
            paymentData.userId,
            '🎉 支付成功！您的自定义地址已准备就绪。',
            customAddress
          );
        }
      } catch (error) {
        console.error('自定义地址支付处理失败:', error);
        lastResult.value = {
          success: false,
          message: error.message
        };
      }
    };

    // 模拟批量支付处理
    const simulateBatchPayment = async () => {
      const paymentList = [
        {
          userId: 'user_001',
          paymentId: generatePaymentId(),
          amount: 29.99,
          productName: '基础套餐'
        },
        {
          userId: 'user_002', 
          paymentId: generatePaymentId(),
          amount: 59.99,
          productName: '高级套餐'
        },
        {
          userId: 'user_003',
          paymentId: generatePaymentId(),
          amount: 99.99,
          productName: 'VIP套餐'
        }
      ];

      try {
        const results = await PaymentSuccessHandler.batchHandlePaymentSuccess(paymentList);
        
        const successCount = results.filter(r => r.success).length;
        const failCount = results.length - successCount;
        
        lastResult.value = {
          success: failCount === 0,
          message: `批量处理完成：成功 ${successCount} 个，失败 ${failCount} 个`,
          batchResults: results
        };

        message.info(`批量支付处理完成：${successCount}/${results.length} 成功`);
        
        if (successCount > 0) {
          showChatBox.value = true;
        }
      } catch (error) {
        console.error('批量支付处理失败:', error);
        lastResult.value = {
          success: false,
          message: error.message
        };
      }
    };

    // 切换聊天框显示
    const toggleChatBox = () => {
      showChatBox.value = !showChatBox.value;
    };

    // 复制到剪贴板
    const copyToClipboard = async (text) => {
      try {
        await navigator.clipboard.writeText(text);
        message.success('已复制到剪贴板');
      } catch (error) {
        message.error('复制失败');
      }
    };

    // 打开地址
    const openAddress = (address) => {
      if (address && address.startsWith('http')) {
        window.open(address, '_blank');
      } else {
        message.info('地址格式不正确');
      }
    };

    // 消息接收处理
    const onMessageReceived = (messageData) => {
      messageLogs.push({
        timestamp: Date.now(),
        content: messageData.content,
        address: messageData.address,
        userId: messageData.userId
      });
    };

    // 格式化时间
    const formatTime = (timestamp) => {
      return new Date(timestamp).toLocaleString('zh-CN');
    };

    // 清空日志
    const clearLogs = () => {
      messageLogs.splice(0, messageLogs.length);
      message.success('日志已清空');
    };

    return {
      showChatBox,
      showMessageLog,
      lastResult,
      messageLogs,
      paymentForm,
      simulateWhopPayment,
      simulateCustomAddressPayment,
      simulateBatchPayment,
      toggleChatBox,
      copyToClipboard,
      openAddress,
      onMessageReceived,
      formatTime,
      clearLogs
    };
  }
};
</script>

<style lang="less" scoped>
.payment-success-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .demo-section {
    margin-bottom: 24px;

    h3 {
      margin-bottom: 12px;
      color: #333;
    }

    p {
      color: #666;
      margin-bottom: 16px;
    }
  }

  .result-display {
    .address-result {
      margin-top: 16px;
      padding: 16px;
      background: #f9f9f9;
      border-radius: 6px;

      h4 {
        margin-bottom: 8px;
        color: #333;
      }
    }
  }

  .no-result {
    padding: 20px;
    text-align: center;
  }

  .message-log {
    .log-item {
      padding: 12px;
      border-bottom: 1px solid #f0f0f0;
      margin-bottom: 8px;

      .log-time {
        font-size: 12px;
        color: #999;
        margin-bottom: 4px;
      }

      .log-content {
        font-size: 14px;
        color: #333;
        margin-bottom: 4px;
      }

      .log-address {
        font-size: 12px;
        color: #1890ff;
        word-break: break-all;
      }
    }

    .no-logs {
      padding: 40px 20px;
      text-align: center;
    }
  }

  .bottom-actions {
    position: fixed;
    bottom: 20px;
    left: 20px;
    z-index: 999;
    display: flex;
    gap: 8px;
  }
}
</style>
