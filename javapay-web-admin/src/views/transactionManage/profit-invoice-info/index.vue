<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <!-- <a-form-item label="核销月份">
              <a-date-picker picker="month" v-model:value="where.verifyMonth" format="YYYY-MM" valueFormat="YYYY-MM" />
            </a-form-item> -->
            <a-form-item label="开票状态">
              <a-select v-model:value="where.invoiceStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">未开票</a-select-option>
                <a-select-option :value="1">待审核</a-select-option>
                <a-select-option :value="2">已开票</a-select-option>
                <a-select-option :value="3">审核不通过</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="用户编号" v-purview="'0'">
              <a-input v-model:value.trim="where.userNo" placeholder="请输入用户编号" allow-clear />
            </a-form-item>
            <a-form-item label="用户类型" v-purview="'0'">
              <a-select v-model:value="where.userType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">大区</a-select-option>
                <a-select-option :value="2">运营中心</a-select-option>
                <a-select-option :value="3">代理商</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="核销开始日期">
              <a-date-picker v-model:value="where.verifyBeginTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item label="核销结束日期">
              <a-date-picker v-model:value="where.verifyEndTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table
          ref="table"
          row-key="id"
          :datasource="datasource"
          :columns="columns"
          :where="where"
          v-model:selection="selection"
          :scroll="{ x: 'max-content' }"
        >
          <!-- table上边工具栏 -->
          <template #toolbar>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'invoiceStatus'">
              <a-tag v-if="record.invoiceStatus === 0">未开票</a-tag>
              <a-tag v-else-if="record.invoiceStatus === 1" color="blue">待审核</a-tag>
              <a-tag v-else-if="record.invoiceStatus === 2" color="green">已开票</a-tag>
              <a-tag v-else-if="record.invoiceStatus === 3" color="red">审核不通过</a-tag>
            </template>
            <template v-else-if="column.key === 'userType'">
              <a-badge v-if="record.userType === 1" color="pink" text="大区" />
              <a-badge v-else-if="record.userType === 2" color="blue" text="运营中心" />
              <a-badge v-else-if="record.userType === 3" color="cyan" text="代理商" />
            </template>
            <template v-else-if="column.key === 'verifyMonths'">
              <span>{{ record.verifyMonthsList.map(item => item.verifyMonth).join(',') }}</span>
            </template>
            <template v-else-if="column.key === 'organName'">
              <span v-if="record.userType === 1">{{record.regionName}}</span>
              <span v-else-if="record.userType === 2">{{record.branchName}}</span>
              <span v-else-if="record.userType === 3">{{record.agentName}}</span>
            </template>

            <!-- 操作栏 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
                <template v-if="hasPurview(['0']) && record.invoiceStatus === 1">
                  <a-divider type="vertical" />
                  <a @click="handleCheck(record)">审核</a>
                </template>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 审核 -->
    <CheckProfitVerify v-if="showCheckProfitVerify" v-model:visible="showCheckProfitVerify" :data="current" @done="reload" />

    <!-- 详情 -->
    <ProfitInvoiceInfoDetail v-if="showDetail" v-model:visible="showDetail" :data="current" />
  </div>
</template>

<script>
import { hasPurview } from '@/utils/permission';
import { ProfitInvoiceInfoApi } from '@/api/transactionManage/ProfitInvoiceInfoApi';
import CheckProfitVerify from '../profit-verify/modules/CheckProfitVerify.vue';
import ProfitInvoiceInfoDetail from './modules/ProfitInvoiceInfoDetail.vue'

export default {
  name: 'ProfitVerify',
  components: {
    CheckProfitVerify,
    ProfitInvoiceInfoDetail
  },
  data() {
    return {
      hasPurview,
      // 表格搜索条件
      where: {},
      showCheckProfitVerify: false,
      showDetail: false,
      current: null,

      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          hideCol: !hasPurview(['0']),
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '机构编号',
          dataIndex: 'userNo',
          align: 'center'
        },
        {
          title: '机构类型',
          dataIndex: 'userType',
          key: 'userType',
          align: 'center'
        },
        {
          title: '机构名称',
          dataIndex: 'organName',
          key: 'organName',
          align: 'center'
        },
        {
          title: '税点',
          dataIndex: 'taxPoint',
          align: 'center'
        },
        {
          title: '核销月份',
          dataIndex: 'verifyMonths',
          key: 'verifyMonths',
          align: 'center'
        },
        {
          title: '核销人',
          dataIndex: 'verifyPerson',
          align: 'center'
        },
        {
          title: '核销时间',
          dataIndex: 'verifyDate',
          align: 'center'
        },
        {
          title: '开票状态',
          dataIndex: 'invoiceStatus',
          key: 'invoiceStatus',
          align: 'center'
        },
        {
          title: '审核信息',
          dataIndex: 'checkMessage',
          align: 'center'
        },
        {
          title: '实开票金额',
          dataIndex: 'actualInvoiceAmount',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right',
          width: 200
        }
      ].filter(i => !i.hideCol),
    }
  },
  methods: {
    //详情
    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    //审核
    handleCheck(row) {
      this.current = row;
      this.showCheckProfitVerify = true;
    },


    reload() {
      this.selection = [];
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.selection = [];
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    datasource({ page, limit, where, orders }) {
      return ProfitInvoiceInfoApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
