<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="提现流水单号">
              <a-input v-model:value.trim="where.flowNo" placeholder="请输入提现流水单号" allow-clear />
            </a-form-item>
            <a-form-item label="通道订单号">
              <a-input v-model:value.trim="where.chnOrderNo" placeholder="请输入通道订单号" allow-clear />
            </a-form-item>
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="商户名称">
              <a-input v-model:value.trim="where.merchantName" placeholder="请输入商户名称" allow-clear />
            </a-form-item>
            <a-form-item label="通道商户编号">
              <a-input v-model:value.trim="where.chnMerchNo" placeholder="请输入通道商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="通道商户名称">
              <a-input v-model:value.trim="where.chnMerchName" placeholder="请输入通道商户名称" allow-clear />
            </a-form-item>
            <a-form-item label="银行户名">
              <a-input v-model:value.trim="where.bankAccountName" placeholder="请输入银行户名" allow-clear />
            </a-form-item>
            <a-form-item label="结算账户类型">
              <a-select v-model:value="where.settleAccountType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option value="G">对公</a-select-option>
                <a-select-option value="S">对私</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="结算周期">
              <a-select v-model:value="where.settlePeriod" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">D0提现</a-select-option>
                <a-select-option :value="2">T1出款</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="提现状态">
              <a-select v-model:value="where.withdrawStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">已创建</a-select-option>
                <a-select-option :value="1">出款处理中</a-select-option>
                <a-select-option :value="2">已到账</a-select-option>
                <a-select-option :value="3">出款失败</a-select-option>
                <a-select-option :value="4">请求提交中</a-select-option>
                <a-select-option :value="5">人工审核</a-select-option>
                <a-select-option :value="6">重新出款</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="开始日期">
              <a-date-picker v-model:value="where.searchBeginTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item label="结束日期">
              <a-date-picker v-model:value="where.searchEndTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #toolbar>
            <div style="margin-bottom: 10px">
              <a-space>
                <span>
                  <span style="margin-right: 20px">
                    <span style="color: red">提现交易总额:</span>
                    <span style="color: black">{{ transFeeSummary.sumAmount || '0.00' }}</span>
                  </span>
                  <span style="margin-right: 20px">
                    <span style="color: red">提现交易手续费总额:</span>
                    <span style="color: black">{{ transFeeSummary.sumFee || '0.00' }}</span>
                  </span>
                  <span style="margin-right: 20px">
                    <span style="color: red">提现交易总笔数:</span>
                    <span style="color: black">{{ transFeeSummary.withdrawCount }}</span>
                  </span>
                </span>
              </a-space>
            </div>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'accountApplyStatus'">
              <a-tag v-if="record.accountApplyStatus === 0">否</a-tag>
              <a-tag v-else-if="record.accountApplyStatus === 1" color="success">是</a-tag>
            </template>
            <template v-else-if="column.key === 'accountDeductStatus'">
              <a-tag v-if="record.accountDeductStatus === 0">否</a-tag>
              <a-tag v-else-if="record.accountDeductStatus === 1" color="success">是</a-tag>
            </template>
            <template v-else-if="column.key === 'chnMerchWalletType'">
              <a-tag v-if="record.chnMerchWalletType === '100'" color="red">POS D0可提现钱包</a-tag>
              <a-tag v-else-if="record.chnMerchWalletType === '102'" color="orange">POS T1钱包</a-tag>
              <a-tag v-else-if="record.chnMerchWalletType === '200'" color="cyan">二维码D0可提现钱包</a-tag>
              <a-tag v-else-if="record.chnMerchWalletType === '202'" color="blue">二维码T1钱包</a-tag>
              <a-tag v-else-if="record.chnMerchWalletType === '400'" color="green">快捷支付D0可提现钱包</a-tag>
              <a-tag v-else-if="record.chnMerchWalletType === '402'" color="yellow">快捷支付T1钱包</a-tag>
              <a-tag v-else-if="record.chnMerchWalletType === '900'" color="pink">聚合D0可提现钱包</a-tag>
              <a-tag v-else-if="record.chnMerchWalletType === '902'" color="purple">聚合T1钱包</a-tag>
            </template>
            <template v-else-if="column.key === 'isChnCumulate'">
              <a-tag v-if="record.isChnCumulate === 0">否</a-tag>
              <a-tag v-else-if="record.isChnCumulate === 1" color="success">是</a-tag>
            </template>
            <template v-else-if="column.key === 'isCumulate'">
              <a-tag v-if="record.isCumulate === 0">否</a-tag>
              <a-tag v-else-if="record.isCumulate === 1" color="success">是</a-tag>
            </template>
            <template v-else-if="column.key === 'isPushNotify'">
              <a-tag v-if="record.isPushNotify === 0">否</a-tag>
              <a-tag v-else-if="record.isPushNotify === 1" color="success">是</a-tag>
            </template>
            <template v-else-if="column.key === 'settleAccountType'">
              <a-tag v-if="record.settleAccountType === 'G'" color="orange">对公</a-tag>
              <a-tag v-else-if="record.settleAccountType === 'S'" color="cyan">对私</a-tag>
            </template>
            <template v-else-if="column.key === 'isTriggerRisk'">
              <a-tag v-if="record.isTriggerRisk === 0">否</a-tag>
              <a-tag v-else-if="record.isTriggerRisk === 1" color="error">是</a-tag>
            </template>
            <template v-else-if="column.key === 'transCode'">
              <a-tag v-if="record.transCode === '03W100'" color="cyan">商户提现</a-tag>
              <a-tag v-else-if="record.transCode === '03W101'" color="processing">通道商户提现</a-tag>
              <a-tag v-else-if="record.transCode === '03W102'" color="orange">提现重新出款</a-tag>
              <a-tag v-else-if="record.transCode === '03W103'" color="purple">T1出款</a-tag>
            </template>
            <template v-else-if="column.key === 'settleStatus'">
              <a-tag v-if="record.settleStatus === 2" color="cyan">已记账</a-tag>
              <a-tag v-else-if="record.settleStatus === 3" color="processing">结算中</a-tag>
              <a-tag v-else-if="record.settleStatus === 4" color="error">结算失败</a-tag>
              <a-tag v-else-if="record.settleStatus === 5" color="success">结算成功</a-tag>
            </template>
            <template v-else-if="column.key === 'failHandleMode'">
              <a-tag v-if="record.failHandleMode === -1" color="success">未指定</a-tag>
              <a-tag v-else-if="record.failHandleMode === 0" color="success">付款成功</a-tag>
              <a-tag v-else-if="record.failHandleMode === 1" color="processing">重新付款</a-tag>
              <a-tag v-else-if="record.failHandleMode === 2" color="orange">退回钱包</a-tag>
            </template>
            <template v-else-if="column.key === 'failHandleStatus'">
              <a-tag v-if="record.failHandleStatus === 0">无需处理</a-tag>
              <a-tag v-else-if="record.failHandleStatus === 1" color="processing">等待处理</a-tag>
              <a-tag v-else-if="record.failHandleStatus === 2" color="success">处理完成</a-tag>
            </template>
            <template v-else-if="column.key === 'withdrawStatus'">
              <a-tag v-if="record.withdrawStatus === 0">已创建</a-tag>
              <a-tag v-else-if="record.withdrawStatus === 1" color="cyan">出款处理中</a-tag>
              <a-tag v-else-if="record.withdrawStatus === 2" color="success">已到账</a-tag>
              <a-tag v-else-if="record.withdrawStatus === 3" color="error">出款失败</a-tag>
              <a-tag v-else-if="record.withdrawStatus === 4" color="processing">请求提交中</a-tag>
              <a-tag v-else-if="record.withdrawStatus === 5" color="orange">人工审核</a-tag>
              <a-tag v-else-if="record.withdrawStatus === 6" color="purple">重新出款</a-tag>
            </template>
            <template v-if="column.key === 'settlePeriod'">
              <a-tag v-if="record.settlePeriod === 1" color="pink">D0提现</a-tag>
              <a-tag v-else-if="record.settlePeriod === 2" color="cyan">T1出款</a-tag>
            </template>
            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
                <template
                  v-if="
                    (record.withdrawStatus === 3 || record.withdrawStatus === 5) &&
                      record.failHandleStatus === 1 &&
                      record.failHandleMode === -1
                  "
                >
                  <a-divider type="vertical" />
                  <a @click="handleCheck(record)">人工处理</a>
                </template>
                <!-- <template v-if="record.withdrawStatus === 2 && record.failHandleStatus !== 2">
                  <a-divider type="vertical" />
                  <a @click="handleReexchange(record)">退汇</a>
                </template> -->
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <WithdrawalRecordDetail v-model:visible="showDetail" :detail="current" :channelCodes="channelCodes" />

    <!-- 审核 -->
    <WithdrawalRecordCheck v-model:visible="showCheck" v-if="showCheck" @done="reload" :detail="current" />

    <!-- 退汇 -->
    <WithdrawalRecordReexchange v-model:visible="showReexchange" v-if="showReexchange" @done="reload" :detail="current" />
  </div>
</template>

<script>
import { WithdrawalRecordApi } from '@/api/transactionManage/merch-withdrawal/WithdrawalRecordApi';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import WithdrawalRecordDetail from './withdrawal-record-detail.vue';
import WithdrawalRecordCheck from './withdrawal-record-check.vue';
import WithdrawalRecordReexchange from './withdrawal-record-reexchange.vue';

export default {
  name: 'MerchWithdrawalRecord',
  components: { WithdrawalRecordDetail, WithdrawalRecordCheck, WithdrawalRecordReexchange },
  data() {
    return {
      channelCodes: [],
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '商户名称',
          align: 'center',
          dataIndex: 'merchantName'
        },
        {
          title: '商户编号',
          align: 'center',
          dataIndex: 'merchantNo'
        },
        {
          title: '通道商户编号',
          align: 'center',
          dataIndex: 'chnMerchNo'
        },
        {
          title: '通道商户名称',
          align: 'center',
          dataIndex: 'chnMerchName'
        },
        {
          title: '提现金额(元)',
          align: 'center',
          dataIndex: 'withdrawAmount'
        },
        {
          title: '到账金额(元)',
          align: 'center',
          dataIndex: 'receivedAmount'
        },
        {
          title: '提现手续费金额(元)',
          align: 'center',
          dataIndex: 'feeAmount'
        },
        {
          title: '提现费率(%)',
          align: 'center',
          dataIndex: 'withdrawRate'
        },
        {
          title: '提现单笔费用(元)',
          align: 'center',
          dataIndex: 'withdrawSingleFee'
        },
        {
          title: '提现状态',
          dataIndex: 'withdrawStatus',
          key: 'withdrawStatus',
          align: 'center'
        },
        {
          title: '提现返回描述',
          dataIndex: 'resDesc',
          width: 200
        },
        {
          title: '提现完成时间',
          align: 'center',
          dataIndex: 'withdrawTime'
        },
        {
          title: '银行户名',
          align: 'center',
          dataIndex: 'bankAccountName'
        },
        {
          title: '银行账号',
          align: 'center',
          dataIndex: 'bankAccountNoMask'
        },
        {
          title: '银行支行名称',
          align: 'center',
          dataIndex: 'bankBranchName'
        },
        {
          title: '银行名称',
          align: 'center',
          dataIndex: 'bankName'
        },
        {
          title: '结算账户类型',
          dataIndex: 'settleAccountType',
          key: 'settleAccountType',
          align: 'center'
        },
        {
          title: '结算周期',
          dataIndex: 'settlePeriod',
          key: 'settlePeriod',
          align: 'center'
        },
        {
          title: '提现流水单号',
          align: 'center',
          dataIndex: 'flowNo'
        },
        {
          title: '通道订单号',
          align: 'center',
          dataIndex: 'chnOrderNo'
        },
        {
          title: '通道返回编码',
          align: 'center',
          dataIndex: 'chnResCode'
        },
        {
          title: '通道返回描述',
          dataIndex: 'chnResDesc',
          width: 180
        },

        {
          title: '创建时间',
          align: 'center',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改时间',
          align: 'center',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right',
          width: 180
        }
      ],
      // 表格搜索条件
      where: {
        searchBeginTime: this.getToday(),
        searchEndTime: this.getToday()
      },
      showDetail: false,
      showCheck: false,
      showReexchange: false,
      transFeeSummary: {}
    };
  },
  async mounted() {
    const data = await ChannelManageApi.list({ validStatus: 1 });
    this.channelCodes = data || [];

    this.getTransFeeSummaryData();
  },
  methods: {
    //获取今日日期 YYYY-MM-DD
    getToday() {
      let date = new Date();
      let year = date.getFullYear();
      let month = date.getMonth() + 1; // 注意，JS 中月份是从 0 开始计数的
      let day = date.getDate();

      // 如果月份或日期是个位数，我们需要在其前面补零
      month = month < 10 ? '0' + month : month;
      day = day < 10 ? '0' + day : day;

      return `${year}-${month}-${day}`;
    },

    handleCheck(row) {
      this.current = row;
      this.showCheck = true;
    },

    handleReexchange(row) {
      this.current = row;
      this.showReexchange = true;
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    reload() {
      this.$refs.table.reload({ page: 1 });
      this.getTransFeeSummaryData();
    },

    reset() {
      this.where = {
        searchBeginTime: this.getToday(),
        searchEndTime: this.getToday()
      };
      this.$refs.table.reload({ page: 1, where: this.where });
      this.getTransFeeSummaryData();
    },

    async getTransFeeSummaryData() {
      this.transFeeSummary = (await WithdrawalRecordApi.sum(this.where)) || {};
    },

    datasource({ page, limit, where, orders }) {
      return WithdrawalRecordApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
