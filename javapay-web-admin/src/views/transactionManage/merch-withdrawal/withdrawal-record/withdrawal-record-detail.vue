<template>
  <a-modal
    :width="1000"
    :visible="visible"
    :maskClosable="false"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '30px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="商户名称">{{ form.merchantName }}</a-descriptions-item>
      <a-descriptions-item label="商户编号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="通道商户名称">{{ form.chnMerchName }}</a-descriptions-item>
      <a-descriptions-item label="通道商户编号">{{ form.chnMerchNo }}</a-descriptions-item>
      <a-descriptions-item label="提现金额(元)">{{ form.withdrawAmount }}</a-descriptions-item>
      <a-descriptions-item label="到账金额(元)">{{ form.receivedAmount }}</a-descriptions-item>
      <a-descriptions-item label="提现手续费金额(元)">{{ form.feeAmount }}</a-descriptions-item>
      <a-descriptions-item label="提现费率(%)">{{ form.withdrawRate }}</a-descriptions-item>
      <a-descriptions-item label="提现单笔费用(元)">{{ form.withdrawSingleFee }}</a-descriptions-item>
      <a-descriptions-item label="提现状态">
        <a-tag v-if="form.withdrawStatus === 0">已创建</a-tag>
        <a-tag v-else-if="form.withdrawStatus === 1" color="cyan">出款处理中</a-tag>
        <a-tag v-else-if="form.withdrawStatus === 2" color="success">已到账</a-tag>
        <a-tag v-else-if="form.withdrawStatus === 3" color="error">出款失败</a-tag>
        <a-tag v-else-if="form.withdrawStatus === 4" color="processing">请求提交中</a-tag>
        <a-tag v-else-if="form.withdrawStatus === 5" color="orange">人工审核</a-tag>
        <a-tag v-else-if="form.withdrawStatus === 6" color="purple">重新出款</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="提现返回描述">{{ form.resDesc }}</a-descriptions-item>
      <a-descriptions-item label="提现完成时间">{{ form.withdrawTime }}</a-descriptions-item>
      <a-descriptions-item label="提现流水单号">{{ form.flowNo }}</a-descriptions-item>
      <a-descriptions-item label="提现订单号">{{ form.orderNo }}</a-descriptions-item>
      <a-descriptions-item label="银行户名">{{ form.bankAccountName }}</a-descriptions-item>
      <a-descriptions-item label="银行账号">{{ form.bankAccountNoMask }}</a-descriptions-item>
      <a-descriptions-item label="银行支行名称">{{ form.bankBranchName }}</a-descriptions-item>
      <a-descriptions-item label="银行名称">{{ form.bankName }}</a-descriptions-item>
      <a-descriptions-item label="结算账户类型">
        <a-tag v-if="form.settleAccountType === 'G'" color="orange">对公</a-tag>
        <a-tag v-else-if="form.settleAccountType === 'S'" color="cyan">对私</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="结算周期">
        <a-tag v-if="form.settlePeriod === 1" color="pink">D0提现</a-tag>
        <a-tag v-else-if="form.settlePeriod === 2" color="cyan">T1出款</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="通道订单号">{{ form.chnOrderNo }}</a-descriptions-item>
      <a-descriptions-item label="通道返回编码">{{ form.chnResCode }}</a-descriptions-item>
      <a-descriptions-item label="通道返回描述">{{ form.chnResDesc }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
export default {
  props: {
    visible: Boolean,
    detail: Object,
    channelCodes: Array
  },
  emits: ['update:visible'],
  data() {
    return {
      form: {}
    };
  },
  watch: {
    detail() {
      this.form = Object.assign({}, this.detail);
    }
  },
  methods: {
    filterChannelCode(channelCode) {
      const item = this.channelCodes.find(i => i.channelCode === channelCode);
      return item?.channelName || channelCode;
    },
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
