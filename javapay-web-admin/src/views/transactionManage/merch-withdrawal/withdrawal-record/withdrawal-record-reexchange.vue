<template>
  <a-modal
    :width="500"
    :visible="visible"
    :confirm-loading="loading"
    title="审核"
    :body-style="{ paddingBottom: '8px' }"
    :mask-closable="false"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 6 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 18 }, sm: { span: 24 } }"
    >
      <a-form-item label="退汇处理方式" name="failHandleMode">
        <a-select v-model:value="form.failHandleMode" style="width: 100%" placeholder="请选择" allow-clear>
          <a-select-option :value="1">重新出款</a-select-option>
          <a-select-option :value="2">退回钱包</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="退汇处理备注" name="failHandleRemark">
        <a-textarea v-model:value="form.failHandleRemark" placeholder="请输入退汇处理备注" :auto-size="{ minRows: 1 }" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { WithdrawalRecordApi } from '@/api/transactionManage/merch-withdrawal/WithdrawalRecordApi';

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {},
      // 表单验证规则
      rules: {
        failHandleMode: [{ required: true, message: '请选择退汇处理方式' }],
        failHandleRemark: [{ required: true, message: '请输入退汇处理备注' }]
      },
      // 提交状态
      loading: false
    };
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      this.form.id = this.detail.id;
      WithdrawalRecordApi.reexchange(this.form)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
