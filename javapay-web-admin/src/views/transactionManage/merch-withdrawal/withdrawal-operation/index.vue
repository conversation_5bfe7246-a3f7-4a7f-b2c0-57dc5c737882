<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="商户账户UUID">
              <a-input v-model:value.trim="where.accountUuid" placeholder="请输入商户账户UUID" allow-clear />
            </a-form-item>
            <a-form-item label="商户姓名">
              <a-input v-model:value.trim="where.legalName" placeholder="请输入商户姓名" allow-clear />
            </a-form-item>
            <a-form-item label="商户名称">
              <a-input v-model:value.trim="where.merchantName" placeholder="请输入商户名称" allow-clear />
            </a-form-item>
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="提现订单号">
              <a-input v-model:value.trim="where.orderNo" placeholder="请输入提现订单号" allow-clear />
            </a-form-item>
            <a-form-item label="开始日期">
              <a-date-picker v-model:value="where.searchBeginTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item label="结束日期">
              <a-date-picker v-model:value="where.searchEndTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'isTriggerRisk'">
              <a-tag v-if="record.isTriggerRisk === 0">否</a-tag>
              <a-tag v-else-if="record.isTriggerRisk === 1" color="error">是</a-tag>
            </template>

            <template v-else-if="column.key === 'merchGrade'">
              <a-tag v-if="record.merchGrade === 'A'" color="pink">企业商户</a-tag>
              <a-tag v-else-if="record.merchGrade === 'B'" color="cyan">个体工商户</a-tag>
              <a-tag v-else-if="record.merchGrade === 'C'" color="blue">小微商户</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <WithdrawalOperationDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import WithdrawalOperationDetail from './withdrawal-operation-detail.vue';
import { WithdrawalOperationApi } from '@/api/transactionManage/merch-withdrawal/WithdrawalOperationApi';

export default {
  name: 'WithdrawalOperation',
  components: { WithdrawalOperationDetail },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '商户账户UUID',
          dataIndex: 'accountUuid',
          align: 'center'
        },
        {
          title: '触发风控标识',
          dataIndex: 'isTriggerRisk',
          key: 'isTriggerRisk',
          align: 'center'
        },
        {
          title: '商户姓名',
          dataIndex: 'legalName',
          align: 'center'
        },
        {
          title: '商户等级',
          dataIndex: 'merchGrade',
          key: 'merchGrade',
          align: 'center'
        },
        {
          title: '商户ID',
          dataIndex: 'merchId',
          align: 'center'
        },
        {
          title: '商户名称',
          dataIndex: 'merchantName',
          align: 'center'
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '提现订单号',
          dataIndex: 'orderNo',
          align: 'center'
        },
        {
          title: '提现返回编码',
          dataIndex: 'resCode',
          align: 'center'
        },
        {
          title: '提现返回描述',
          dataIndex: 'resDesc',
          width: 180
        },
        {
          title: '风控事件ID',
          dataIndex: 'riskEventId',
          align: 'center'
        },
        {
          title: '风险等级',
          dataIndex: 'riskGrade',
          align: 'center'
        },
        {
          title: '交易总笔数',
          dataIndex: 'transCount',
          align: 'center'
        },
        {
          title: '交易ID列表',
          dataIndex: 'transIds',
          align: 'center'
        },
        {
          title: '提现总金额(元)',
          dataIndex: 'withdrawAmount',
          align: 'center'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right',
          width: 100
        }
      ],
      // 表格搜索条件
      where: {
        searchBeginTime: this.getToday(),
        searchEndTime: this.getToday()},
      showDetail: false
    };
  },
  methods: {
    //获取今日日期 YYYY-MM-DD
    getToday() {
      let date = new Date();
      let year = date.getFullYear();
      let month = date.getMonth() + 1;  // 注意，JS 中月份是从 0 开始计数的
      let day = date.getDate();

      // 如果月份或日期是个位数，我们需要在其前面补零
      month = month < 10 ? '0' + month : month;
      day = day < 10 ? '0' + day : day;

      return `${year}-${month}-${day}`;
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    datasource({ page, limit, where, orders }) {
      return WithdrawalOperationApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    },
  }
};
</script>
