<template>
  <a-modal
    :width="1000"
    :visible="visible"
    :maskClosable="false"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '30px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="商户账户UUID">{{ form.accountUuid }}</a-descriptions-item>
      <a-descriptions-item label="触发风控标识">
        <a-tag v-if="form.isTriggerRisk === 0">否</a-tag>
        <a-tag v-else-if="form.isTriggerRisk === 1" color="error">是</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="商户姓名">{{ form.legalName }}</a-descriptions-item>
      <a-descriptions-item label="商户等级">
        <a-tag v-if="form.merchGrade === 'A'" color="pink">企业商户</a-tag>
        <a-tag v-else-if="form.merchGrade === 'B'" color="cyan">个体工商户</a-tag>
        <a-tag v-else-if="form.merchGrade === 'C'" color="blue">小微商户</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="商户ID">{{ form.merchId }}</a-descriptions-item>
      <a-descriptions-item label="商户名称">{{ form.merchantName }}</a-descriptions-item>
      <a-descriptions-item label="商户编号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="提现订单号">{{ form.orderNo }}</a-descriptions-item>
      <a-descriptions-item label="提现返回编码">{{ form.resCode }}</a-descriptions-item>
      <a-descriptions-item label="提现返回描述">{{ form.resDesc }}</a-descriptions-item>
      <a-descriptions-item label="风控事件ID">{{ form.riskEventId }}</a-descriptions-item>
      <a-descriptions-item label="风险等级">{{ form.riskGrade }}</a-descriptions-item>
      <a-descriptions-item label="交易总笔数">{{ form.transCount }}</a-descriptions-item>
      <a-descriptions-item label="交易ID列表">{{ form.transIds }}</a-descriptions-item>
      <a-descriptions-item label="提现总金额(元)">{{ form.withdrawAmount }}</a-descriptions-item>
      <a-descriptions-item label="创建人">{{ form.createUserId }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
export default {
  props: {
    visible: Boolean,
    detail: Object,
  },
  emits: ['update:visible'],
  data() {
    return {
      form: {}
    };
  },
  watch: {
    detail() {
      this.form = Object.assign({}, this.detail);
    }
  },
  methods: {
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
