<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="通道商户编号">
              <a-input v-model:value.trim="where.chnMerchNo" placeholder="请输入通道商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="退汇流水号">
              <a-input v-model:value.trim="where.reexchangeFlowNo" placeholder="请输入退汇流水号" allow-clear />
            </a-form-item>
            <a-form-item label="通道名称">
              <a-select v-model:value="where.channelCode" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
                  {{ channelName }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="退汇处理状态">
              <a-select v-model:value="where.handleStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">无需处理</a-select-option>
                <a-select-option :value="1">待处理</a-select-option>
                <a-select-option :value="2">已处理</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'handleStatus'">
              <a-tag v-if="record.handleStatus === 0">无需处理</a-tag>
              <a-tag v-else-if="record.handleStatus === 1" color="orange">待处理</a-tag>
              <a-tag v-else-if="record.handleStatus === 2" color="success">已处理</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <WithdrawalReexchangeNotifyDetail v-model:visible="showDetail" :detail="current" :channel-codes="channelCodes" />
  </div>
</template>

<script>
import WithdrawalReexchangeNotifyDetail from './withdrawal-reexchange-notify-detail.vue';
import { WithdrawalReexchangeNotifyApi } from '@/api/transactionManage/merch-withdrawal/WithdrawalReexchangeNotifyApi';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';

export default {
  name: 'WithdrawalReexchangeNotify',
  components: { WithdrawalReexchangeNotifyDetail },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '银行户名',
          dataIndex: 'bankAccountName'
        },
        {
          title: '银行账号',
          dataIndex: 'bankAccountNoMask'
        },
        {
          title: '银行支行名称',
          dataIndex: 'bankBranchName'
        },
        {
          title: '银行联行号',
          dataIndex: 'bankChannelNo'
        },
        {
          title: '银行名称',
          dataIndex: 'bankName'
        },
        {
          title: '通道名称',
          dataIndex: 'channelCode',
          align: 'center',
          customRender: ({ text }) => {
            const item = this.channelCodes.find(c => c.channelCode === text);
            return item?.channelName || '--';
          }
        },
        {
          title: '通道商户编号',
          dataIndex: 'chnMerchNo'
        },
        {
          title: '退汇处理状态',
          dataIndex: 'handleStatus',
          key: 'handleStatus',
          align: 'center'
        },
        {
          title: '退汇流水号',
          dataIndex: 'reexchangeFlowNo'
        },
        {
          title: '退汇原因',
          dataIndex: 'reexchangeReason',
          width: 180
        },
        {
          title: '退汇时间',
          dataIndex: 'reexchangeTime'
        },
        {
          title: '交易通道订单号',
          dataIndex: 'transChnOrderNo'
        },
        {
          title: '交易流水单号',
          dataIndex: 'transFlowNo'
        },
        {
          title: '交易完成时间',
          dataIndex: 'transTime'
        },
        {
          title: '提现通道订单号',
          dataIndex: 'withdrawChnOrderNo'
        },
        {
          title: '提现流水单号',
          dataIndex: 'withdrawFlowNo'
        },
        {
          title: '提现完成时间',
          dataIndex: 'withdrawTime'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right',
          width: 100
        }
      ],
      // 表格搜索条件
      where: {},
      channelCodes: [],
      showDetail: false
    };
  },
  async mounted() {
    const data = await ChannelManageApi.list({ validStatus: 1 });
    this.channelCodes = data || [];
  },
  methods: {
    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    datasource({ page, limit, where, orders }) {
      return WithdrawalReexchangeNotifyApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
