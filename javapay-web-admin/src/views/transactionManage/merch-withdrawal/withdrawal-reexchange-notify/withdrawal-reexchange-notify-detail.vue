<template>
  <a-modal
    :width="1000"
    :visible="visible"
    :maskClosable="false"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '30px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="银行户名">{{ form.bankAccountName }}</a-descriptions-item>
      <a-descriptions-item label="银行账号">{{ form.bankAccountNoMask }}</a-descriptions-item>
      <a-descriptions-item label="银行支行名称">{{ form.bankBranchName }}</a-descriptions-item>
      <a-descriptions-item label="银行联行号">{{ form.bankChannelNo }}</a-descriptions-item>
      <a-descriptions-item label="银行名称">{{ form.bankName }}</a-descriptions-item>
      <a-descriptions-item label="通道名称">
        <template v-for="({ channelCode, channelName }, key) in channelCodes" :key="key">
          <a-badge v-if="form.channelCode === channelCode" color="purple" :text="channelName" />
        </template>
      </a-descriptions-item>
      <a-descriptions-item label="通道商户编号">{{ form.chnMerchNo }}</a-descriptions-item>
      <a-descriptions-item label="退汇处理状态">
        <a-tag v-if="form.handleStatus === 0">无需处理</a-tag>
        <a-tag v-else-if="form.handleStatus === 1" color="orange">待处理</a-tag>
        <a-tag v-else-if="form.handleStatus === 2" color="success">已处理</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="退汇流水号">{{ form.reexchangeFlowNo }}</a-descriptions-item>
      <a-descriptions-item label="退汇原因">{{ form.reexchangeReason }}</a-descriptions-item>
      <a-descriptions-item label="退汇时间">{{ form.reexchangeTime }}</a-descriptions-item>
      <a-descriptions-item label="交易通道订单号">{{ form.transChnOrderNo }}</a-descriptions-item>
      <a-descriptions-item label="交易流水单号">{{ form.transFlowNo }}</a-descriptions-item>
      <a-descriptions-item label="交易完成时间">{{ form.transTime }}</a-descriptions-item>
      <a-descriptions-item label="提现通道订单号">{{ form.withdrawChnOrderNo }}</a-descriptions-item>
      <a-descriptions-item label="提现流水单号">{{ form.withdrawFlowNo }}</a-descriptions-item>
      <a-descriptions-item label="提现完成时间">{{ form.withdrawTime }}</a-descriptions-item>
      <a-descriptions-item label="创建人">{{ form.createUserId }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
export default {
  props: {
    visible: Boolean,
    detail: Object,
    channelCodes: Array
  },
  emits: ['update:visible'],
  data() {
    return {
      form: {}
    };
  },
  watch: {
    detail() {
      this.form = Object.assign({}, this.detail);
    }
  },
  methods: {
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
