<template>
  <a-modal
    :width="500"
    :visible="visible"
    :confirm-loading="loading"
    title="手动结算"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules">
      <a-form-item label="结算周期" name="settlePeriod">
        <a-select v-model:value="form.settlePeriod" placeholder="请选择结算周期" style="width: 100%">
          <a-select-option :value="1">日结</a-select-option>
          <a-select-option :value="2">月结</a-select-option>
        </a-select>
      </a-form-item>
      <!-- 结算周期为1的日结显示YYYYMMDD 为2月结的显示YYYYMM -->
      <a-form-item :label="form.settlePeriod === 2 ? '结算月份' : '结算日期'" name="profitSettleDate">
        <a-date-picker
          v-model:value="form.profitSettleDate"
          :format="form.settlePeriod === 1 ? 'YYYYMMDD' : 'YYYYMM'"
          :valueFormat="form.settlePeriod === 1 ? 'YYYYMMDD' : 'YYYYMM'"
          :picker="form.settlePeriod === 1 ? '' : 'month'"
          :disabled="!form.settlePeriod"
        />
      </a-form-item>
      <a-form-item label="分润类型" prop="profitType">
        <a-select v-model:value="form.profitType" style="width: 100%" placeholder="请选择" allow-clear>
          <a-select-option :value="1">交易分润</a-select-option>
          <a-select-option :value="2">营销活动分润</a-select-option>
          <a-select-option :value="3">机构提现分润</a-select-option>
          <a-select-option :value="4">机构代付分润</a-select-option>
          <a-select-option :value="5">终端激活奖励分润</a-select-option>
          <a-select-option :value="6">终端达标奖励分润</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="用户类型" v-purview="'0'" prop="userType">
        <a-select v-model:value="form.userType" placeholder="请选择">
          <a-select-option :value="1">大区</a-select-option>
          <a-select-option :value="2">运营中心</a-select-option>
          <a-select-option :value="3">代理商</a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { SettleBillApi } from '@/api/transactionManage/SettleBillApi';
import { message } from 'ant-design-vue';

export default {
  name: 'SettleBillManualStatisticsHandle',
  props: {
    visible: Boolean
  },
  emits: ['update:visible', 'done'],
  data() {
    return {
      //表单数据
      form: {},
      //提交状态
      loading: false,
      //表单规则
      rules: {
        settlePeriod: [{ required: true, message: '请选择结算周期' }],
        profitSettleDate: [{ required: true, message: '请选择结算日期' }]
        // profitType: [{ required: true, message: '请选择分润类型' }],
      }
    };
  },
  watch: {
    'form.settlePeriod'() {
      this.form.profitSettleDate = '';
    }
  },
  methods: {
    //传值更新父组件的值
    updateVisible(value) {
      this.$emit('update:visible', value);
    },

    //提交
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let result = SettleBillApi.manualStatisticsHandle(this.form);
      result
        .then(res => {
          // 移除加载框
          this.loading = false;
          //成功之后清除数据
          this.form = {};
          // 提示添加成功
          message.success(res.message);
          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);
        })
        .catch(() => {
          this.loading = false;
        });
    }
  }
};
</script>
