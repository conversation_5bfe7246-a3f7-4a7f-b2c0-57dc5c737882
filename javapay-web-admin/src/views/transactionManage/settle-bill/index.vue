<template>
  <div class="ele-body">
    <a-spin :spinning="spinning" tip="下载中, 请稍候...">
      <!-- 搜索表单 -->
      <div class="block-interval">
        <a-card :bordered="false">
          <a-form layout="inline" :model="where">
            <a-row :gutter="[0, 16]">
              <a-form-item label="结算单号">
                <a-input v-model:value.trim="where.settleFlowNo" placeholder="请输入结算单号" allow-clear />
              </a-form-item>
              <a-form-item label="分润类型">
                <a-select v-model:value="where.profitType" style="width: 200px" placeholder="请选择" allow-clear>
                  <a-select-option :value="1">交易分润</a-select-option>
                  <a-select-option :value="2">营销活动分润</a-select-option>
                  <a-select-option :value="3">机构提现分润</a-select-option>
                  <a-select-option :value="4">机构代付分润</a-select-option>
                  <a-select-option :value="5">终端激活奖励分润</a-select-option>
                  <a-select-option :value="6">终端达标奖励分润</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="分润出款方式">
                <a-select v-model:value="where.settleChannel" style="width: 200px" placeholder="请选择" allow-clear>
                  <a-select-option :value="0">交易通道</a-select-option>
                  <a-select-option :value="1">平台税筹账户</a-select-option>
                  <a-select-option :value="2">一代税筹账户</a-select-option>
                  <a-select-option :value="3">展业平台</a-select-option>
                  <a-select-option :value="4">活动方出款</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="结算周期">
                <a-select v-model:value="where.settlePeriod" style="width: 200px" placeholder="请选择" allow-clear>
                  <a-select-option :value="1">日结</a-select-option>
                  <a-select-option :value="2">月结</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="是否差错标记">
                <a-select v-model:value="where.isErrorMark" style="width: 200px" placeholder="请选择" allow-clear>
                  <a-select-option :value="1">是</a-select-option>
                  <a-select-option :value="0">否</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="入账状态">
                <a-select v-model:value="where.postStatus" style="width: 200px" placeholder="请选择" allow-clear>
                  <a-select-option :value="0">无需入账</a-select-option>
                  <a-select-option :value="1">未入账</a-select-option>
                  <a-select-option :value="2">已入账</a-select-option>
                  <a-select-option :value="3">重复入账</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="入账时间">
                <a-date-picker v-model:value="where.postTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
              </a-form-item>
              <a-form-item label="用户账户" v-if="hasPurview('0')">
                <a-input v-model:value.trim="where.accountUuid" placeholder="请输入用户账户" allow-clear />
              </a-form-item>
              <a-form-item label="用户钱包" v-if="hasPurview('0')">
                <a-input v-model:value.trim="where.walletUuid" placeholder="请输入用户钱包" allow-clear />
              </a-form-item>
              <a-form-item label="用户编号">
                <a-input v-model:value.trim="where.userNo" placeholder="请输入用户编号" allow-clear />
              </a-form-item>
              <a-form-item label="用户类型" v-if="hasPurview(['0', '3'])">
                <a-select v-model:value="where.userType" style="width: 200px" placeholder="请选择" allow-clear>
                  <template v-if="hasPurview('0')">
                    <a-select-option :value="1">大区</a-select-option>
                    <a-select-option :value="2">运营中心</a-select-option>
                  </template>
                  <a-select-option :value="3">代理商</a-select-option>
                  <a-select-option :value="5">代理商(子代)</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="用户级别" v-if="hasPurview(['0', '3'])">
                <a-input v-model:value.trim="where.userLevel" placeholder="请输入用户等级" allow-clear />
              </a-form-item>
              <a-form-item label="分润日期">
                <a-radio-group v-model:value="dateType" @change="where.profitSettleDate = null">
                  <a-radio-button value="date">日</a-radio-button>
                  <a-radio-button value="month">月</a-radio-button>
                  <a-date-picker
                    :picker="dateType"
                    v-model:value="where.profitSettleDate"
                    :valueFormat="dateType === 'date' ? 'YYYY-MM-DD' : 'YYYY-MM'"
                  />
                </a-radio-group>
              </a-form-item>
              <a-form-item label="开始日期">
                <a-date-picker v-model:value="where.searchBeginTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
              </a-form-item>
              <a-form-item label="结束日期">
                <a-date-picker v-model:value="where.searchEndTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
              </a-form-item>
              <a-form-item class="ele-text-center">
                <a-space>
                  <a-button type="primary" @click="reload">查询</a-button>
                  <a-button @click="reset">重置</a-button>
                </a-space>
              </a-form-item>
            </a-row>
          </a-form>
        </a-card>
      </div>

      <!-- 表格 -->
      <div>
        <a-card :bordered="false" class="table-height">
          <ele-pro-table
            ref="table"
            row-key="id"
            :datasource="datasource"
            :columns="columns"
            :where="where"
            v-model:selection="selection"
            :scroll="{ x: 'max-content' }"
          >
            <!-- table上边工具栏 -->
            <template #toolbar>
              <a-descriptions title="统计信息" :column="4" size="small" style="margin-bottom: 15px">
                <a-descriptions-item label="入账总金额">{{ summary.postTotalAmount }}</a-descriptions-item>
                <a-descriptions-item label="分润总金额">{{ summary.sumProfitAmount }}</a-descriptions-item>
                <a-descriptions-item label="结算总金额">{{ summary.sumSettleAmount }}</a-descriptions-item>
                <a-descriptions-item label="税差总金额">{{ summary.sumAddTaxAmount }}</a-descriptions-item>
                <a-descriptions-item label="结算账单总笔数">{{ summary.billCount }}</a-descriptions-item>
                <template v-if="hasPurview(['0', '3'])">
                  <a-descriptions-item label="子代分润总金额">{{ summary.sumSubProfitTotalAmount }}</a-descriptions-item>
                  <a-descriptions-item label="子代结算总金额">{{ summary.sumSubSettleTotalAmount }}</a-descriptions-item>
                  <a-descriptions-item label="子代税差总金额">{{ summary.sumSubAddTotalTaxAmount }}</a-descriptions-item>
                </template>
              </a-descriptions>
              <a-space>
                <a-button @click="manualStatisticsClick" v-if="hasPurview(['0'])">
                  <template #icon>
                    <node-expand-outlined />
                  </template>
                  <span>手动结算</span>
                </a-button>
                <a-button @click="showAddErrorProfitBill = true" v-if="hasPurview(['0'])">
                  <template #icon>
                    <plus-outlined />
                  </template>
                  <span>添加差错记录</span>
                </a-button>
                <a-button @click="handleExportExcel">
                  <template #icon>
                    <download-outlined />
                  </template>
                  <span>导出excel</span>
                </a-button>
              </a-space>
            </template>

            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'postStatus'">
                <a-tag v-if="record.postStatus === 0">无需入账</a-tag>
                <a-tag v-else-if="record.postStatus === 1" color="orange">未入账</a-tag>
                <a-tag v-else-if="record.postStatus === 2" color="green">已入账</a-tag>
                <a-tag v-else-if="record.postStatus === 3" color="red">重复入账</a-tag>
              </template>

              <template v-if="column.key === 'settleDateRegion'">
                {{
                  record.settleStartDate
                    ? `${dayjs(record.settleStartDate).format('YYYY-MM-DD')}-${dayjs(record.settleEndDate).format('YYYY-MM-DD')}`
                    : '--'
                }}
              </template>

              <template v-if="column.key === 'profitType'">
                <a-tag v-if="record.profitType === 1" color="pink">交易分润</a-tag>
                <a-tag v-else-if="record.profitType === 2" color="cyan">营销活动分润</a-tag>
                <a-tag v-else-if="record.profitType === 3" color="blue">机构提现分润</a-tag>
                <a-tag v-else-if="record.profitType === 4" color="purple">机构代付分润</a-tag>
                <a-tag v-else-if="record.profitType === 5" color="pink">终端激活奖励分润</a-tag>
                <a-tag v-else-if="record.profitType === 6" color="cyan">终端达标奖励分润</a-tag>
              </template>

              <template v-else-if="column.key === 'isErrorMark'">
                <a-tag v-if="record.isErrorMark === 0">否</a-tag>
                <a-tag v-else-if="record.isErrorMark === 1" color="orange">是</a-tag>
              </template>

              <template v-else-if="column.key === 'userType'">
                <a-badge v-if="record.userType === 1" color="pink" text="大区" />
                <a-badge v-else-if="record.userType === 2" color="blue" text="运营中心" />
                <a-badge v-else-if="record.userType === 3" color="cyan" text="代理商" />
                <a-badge v-else-if="record.userType === 5" color="purple" text="代理商(子代)" />
              </template>

              <template v-else-if="column.key === 'subWithdrawStatus'">
                <a-badge v-if="record.subWithdrawStatus === 0" color="yellow" text="待结算" />
                <a-badge v-else-if="record.subWithdrawStatus === 1" color="blue" text="出款处理中" />
                <a-badge v-else-if="record.subWithdrawStatus === 2" color="green" text="出款成功" />
                <a-badge v-else-if="record.subWithdrawStatus === 3" color="red" text="出款失败" />
              </template>

              <template v-else-if="column.key === 'settleChannel'">
                <a-badge v-if="record.settleChannel === 1" color="pink" text="平台税筹账户" />
                <a-badge v-else-if="record.settleChannel === 0" color="purple" text="交易通道" />
                <a-badge v-else-if="record.settleChannel === 2" color="blue" text="一代税筹账户" />
                <a-badge v-else-if="record.settleChannel === 3" color="cyan" text="展业平台" />
                <a-badge v-else-if="record.settleChannel === 4" color="orange" text="活动方出款" />
              </template>

              <template v-else-if="column.key === 'action'">
                <a-space>
                  <a @click="handleDetail(record)">详情</a>
                </a-space>
              </template>
            </template>
          </ele-pro-table>
        </a-card>
      </div>
    </a-spin>

    <!-- 详情 -->
    <SettleBillDetail v-model:visible="showDetail" :detail="current" />

    <!-- 手动统计 -->
    <SettleBillManualStatisticsHandle v-model:visible="showManualStatistics" />

    <!-- 添加差错记录 -->
    <ErrorProfitBillAdd v-if="showAddErrorProfitBill" v-model:visible="showAddErrorProfitBill" @done="reload" />
  </div>
</template>

<script>
import { SettleBillApi } from '@/api/transactionManage/SettleBillApi';
import SettleBillDetail from './SettleBillDetail.vue';
import ErrorProfitBillAdd from './ErrorProfitBillAdd.vue';
import { hasPurview } from '@/utils/permission';
import SettleBillManualStatisticsHandle from './SettleBillManualStatisticsHandle.vue';
import dayjs from 'dayjs';
import { message } from 'ant-design-vue';

export default {
  name: 'SettleBill',
  components: {
    SettleBillDetail,
    ErrorProfitBillAdd,
    SettleBillManualStatisticsHandle
  },
  data() {
    const isHideCol = !hasPurview('0');
    return {
      dateType: 'date',
      summary: {},
      hasPurview,
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left',
          hideCol: isHideCol
        },
        {
          title: '用户编号',
          dataIndex: 'userNo',
          align: 'center'
        },
        {
          title: '用户类型',
          dataIndex: 'userType',
          key: 'userType',
          align: 'center',
          hideCol: hasPurview('5')
        },
        {
          title: '用户级别',
          dataIndex: 'userLevel',
          align: 'center',
          hideCol: !hasPurview(['0', '3'])
        },
        {
          title: '结算单号',
          dataIndex: 'settleFlowNo'
        },
        {
          title: '分润类型',
          dataIndex: 'profitType',
          key: 'profitType',
          align: 'center'
        },
        {
          title: '分润出款方式',
          dataIndex: 'settleChannel',
          key: 'settleChannel',
          align: 'center'
        },
        {
          title: '分润数据日期区间',
          key: 'settleDateRegion',
          align: 'center'
        },
        {
          title: '分润金额',
          dataIndex: 'profitAmount',
          align: 'center'
        },
        {
          title: '结算金额',
          dataIndex: 'settleAmount',
          align: 'center'
        },
        {
          title: '税差金额',
          dataIndex: 'addTaxAmount',
          align: 'center'
        },
        {
          title: '子代分润金额',
          dataIndex: 'subProfitTotalAmount',
          align: 'center'
        },
        {
          title: '子代税差总额',
          dataIndex: 'subAddTotalTax',
          align: 'center'
        },
        {
          title: '子代结算总额',
          dataIndex: 'subSettleTotalAmount',
          align: 'center'
        },
        {
          title: '入账金额',
          dataIndex: 'postAmount',
          align: 'center'
        },
        {
          title: '入账状态',
          dataIndex: 'postStatus',
          key: 'postStatus',
          align: 'center'
        },
        {
          title: '入账时间',
          dataIndex: 'postTime',
          align: 'center'
        },
        {
          title: '结算周期',
          dataIndex: 'settlePeriod',
          align: 'center',
          customRender: ({ text }) => {
            if (text === 1) return '日结';
            else if (text === 2) return '月结';
            return '--';
          }
        },
        {
          title: '子代提现单号',
          dataIndex: 'subWithdrawFlowNo',
          align: 'center'
        },
        {
          title: '子代结算状态',
          dataIndex: 'subWithdrawStatus',
          key: 'subWithdrawStatus',
          align: 'center'
        },
        {
          title: '子代结算时间',
          dataIndex: 'subWithdrawTime',
          align: 'center'
        },
        {
          title: '用户账户',
          dataIndex: 'accountUuid'
        },
        {
          title: '用户钱包',
          dataIndex: 'walletUuid'
        },

        {
          title: '是否差错标记',
          dataIndex: 'isErrorMark',
          key: 'isErrorMark',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right',
          width: 150
        }
      ].filter(i => !i.hideCol),
      // 表格搜索条件
      where: {
        searchBeginTime: dayjs().format('YYYY-MM-DD'),
        searchEndTime: dayjs().format('YYYY-MM-DD')
      },
      selection: [],
      current: null,
      showDetail: false,
      showEdit: false,
      showManualStatistics: false,
      showAddErrorProfitBill: false,
      spinning: false
    };
  },
  mounted() {
    this.getSummaryData();
  },
  methods: {
    async handleExportExcel() {
      if (!(this.where.searchBeginTime || this.where.searchEndTime)) {
        message.warning('请选择开始、结束日期');
        return;
      }
      this.spinning = true;
      const res = await SettleBillApi.downloadProfitSettleBill(this.where).catch(() => {
        this.spinning = false;
      });
      this.spinning = false;
      const fileReader = new FileReader();
      fileReader.onload = function () {
        try {
          // 说明是普通对象数据，后台转换失败
          const jsonData = JSON.parse(fileReader.result);
          message.error(jsonData.message);
        } catch (err) {
          let fileName = '结算账单-下载.xlsx';
          // 解析对象失败，说明是正常的文件流
          let blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.style.display = 'none';
          a.href = url;
          a.download = fileName;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }
      };
      fileReader.readAsText(res?.data);
    },

    async getSummaryData() {
      this.summary = (await SettleBillApi.sum(this.where)) || {};
    },

    reload() {
      this.selection = [];
      this.$refs.table.reload({ page: 1 });
      this.getSummaryData();
    },

    reset() {
      this.where = {
        searchBeginTime: dayjs().format('YYYY-MM-DD'),
        searchEndTime: dayjs().format('YYYY-MM-DD')
      };
      this.selection = [];
      this.$refs.table.reload({ page: 1, where: this.where });
      this.getSummaryData();
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    handleEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    dayjs,

    manualStatisticsClick() {
      this.showManualStatistics = true;
    },

    datasource({ page, limit, where, orders }) {
      return SettleBillApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
