<template>
  <a-modal
    :width="800"
    :visible="visible"
    :confirm-loading="loading"
    title="添加差错账单记录"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules" :labelCol="{ style: { width: '80px' } }">
      <a-row :gutter="24">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="用户类型" name="userType">
            <a-select v-model:value="form.userType" style="width: 100%" placeholder="请选择">
              <a-select-option :value="1">大区</a-select-option>
              <a-select-option :value="2">运营中心</a-select-option>
              <a-select-option :value="3">代理商</a-select-option>
              <a-select-option :value="5">代理商(子代)</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="分润类型" name="profitType">
            <a-select v-model:value="form.profitType" style="width: 100%" placeholder="请选择">
              <a-select-option :value="1">交易分润</a-select-option>
              <a-select-option :value="2">营销活动分润</a-select-option>
              <a-select-option :value="3">机构提现分润</a-select-option>
              <a-select-option :value="4">机构代付分润</a-select-option>
              <a-select-option :value="5">终端激活奖励分润</a-select-option>
              <a-select-option :value="6">终端达标奖励分润</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="借贷标识" name="debitType">
            <a-select v-model:value="form.debitType" style="width: 100%" placeholder="请选择">
              <a-select-option :value="1">加款+</a-select-option>
              <a-select-option :value="2">减款-</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="结算周期" name="settlePeriod">
            <a-radio-group v-model:value="form.settlePeriod" @change="form.profitSettleDate = null">
              <a-radio-button :value="1">日结</a-radio-button>
              <a-radio-button :value="2">月结</a-radio-button>
            </a-radio-group>
          </a-form-item>
          <a-form-item :label="form.settlePeriod === 1 ? '结算日期' : '结算月份'" name="profitSettleDate">
            <a-date-picker
              style="width: 100%"
              :picker="form.settlePeriod === 1 ? 'date' : 'month'"
              v-model:value="form.profitSettleDate"
              :valueFormat="form.settlePeriod === 1 ? 'YYYY-MM-DD' : 'YYYY-MM'"
            />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="用户编号" name="userNo">
            <a-input v-model:value="form.userNo" placeholder="请输入用户编号" allow-clear />
          </a-form-item>
          <a-form-item v-if="form.profitType === 3" label="出款通道" name="subWdSettleChannelTag" help="机构提现分润出款通道">
            <a-select v-model:value="form.subWdSettleChannelTag" style="width: 100%" placeholder="请选择">
              <a-select-option :value="0">支付通道</a-select-option>
              <a-select-option :value="1">平台税筹通道</a-select-option>
              <a-select-option :value="2">一代税筹通道</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item v-else />
          <a-form-item label="结算金额" name="settleAmount">
            <a-input v-model:value="form.settleAmount" placeholder="请输入结算金额" allow-clear />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { SettleBillApi } from '@/api/transactionManage/SettleBillApi';

export default {
  props: {
    visible: Boolean,
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {
        settlePeriod: 1
      },
      // 表单验证规则
      rules: {
        userType: [{ required: true, message: '请选择' }],
        profitType: [{ required: true, message: '请选择' }],
        debitType: [{ required: true, message: '请选择' }],
        settlePeriod: [{ required: true, message: '请选择' }],
        subWdSettleChannelTag: [{ required: true, message: '请选择' }],
        profitSettleDate: [{ required: true, message: '请选择' }],
        userNo: [{ required: true, message: '请输入用户编号' }],
        settleAmount: [{ required: true, message: '请输入结算金额' }]
      },
      loading: false
    };
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      SettleBillApi.addErrorProfitBills(this.form)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
