<template>
  <a-modal
    :width="1000"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2" bordered size="middle">
      <a-descriptions-item label="结算单号">{{ form.settleFlowNo }}</a-descriptions-item>
      <a-descriptions-item label="分润类型">
        <a-tag v-if="form.profitType === 1" color="pink">交易分润</a-tag>
        <a-tag v-else-if="form.profitType === 2" color="cyan">营销活动分润</a-tag>
        <a-tag v-else-if="form.profitType === 3" color="blue">机构提现分润</a-tag>
        <a-tag v-else-if="form.profitType === 4" color="purple">机构代付分润</a-tag>
        <a-tag v-else-if="form.profitType === 5" color="pink">终端激活奖励分润</a-tag>
        <a-tag v-else-if="form.profitType === 6" color="cyan">终端达标奖励分润</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="分润出款方式">
        <a-badge v-if="form.settleChannel === 1" color="pink" text="平台税筹账户" />
        <a-badge v-else-if="form.settleChannel === 0" color="purple" text="交易通道" />
        <a-badge v-else-if="form.settleChannel === 2" color="blue" text="一代税筹账户" />
        <a-badge v-else-if="form.settleChannel === 3" color="cyan" text="展业平台" />
        <a-badge v-else-if="form.settleChannel === 4" color="orange" text="活动方出款" />
      </a-descriptions-item>
      <a-descriptions-item label="分润数据日期区间">{{
        form.settleStartDate
          ? `${dayjs(form.settleStartDate).format('YYYY-MM-DD')}-${dayjs(form.settleEndDate).format('YYYY-MM-DD')}`
          : '--'
      }}</a-descriptions-item>
      <a-descriptions-item label="分润金额">{{ form.profitAmount }}</a-descriptions-item>
      <a-descriptions-item label="结算周期">
        {{ form.settlePeriod === 1 ? '日结' : form.settlePeriod === 2 ? '月结' : '--' }}
      </a-descriptions-item>
      <a-descriptions-item label="结算金额">{{ form.settleAmount }}</a-descriptions-item>
      <a-descriptions-item label="税差金额">{{ form.addTaxAmount }}</a-descriptions-item>
      <a-descriptions-item label="子代分润金额">{{ form.subProfitTotalAmount }}</a-descriptions-item>
      <a-descriptions-item label="子代税差总额">{{ form.subAddTotalTax }}</a-descriptions-item>
      <a-descriptions-item label="子代结算总额">{{ form.subSettleTotalAmount }}</a-descriptions-item>
      <a-descriptions-item label="入账金额">{{ form.postAmount }}</a-descriptions-item>
      <a-descriptions-item label="入账状态">
        <a-tag v-if="form.postStatus === 0">无需入账</a-tag>
        <a-tag v-else-if="form.postStatus === 1" color="orange">未入账</a-tag>
        <a-tag v-else-if="form.postStatus === 2" color="green">已入账</a-tag>
        <a-tag v-else-if="form.postStatus === 3" color="red">重复入账</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="入账时间">{{ form.postTime }}</a-descriptions-item>
      <a-descriptions-item label="子代提现单号">{{ form.subWithdrawFlowNo }}</a-descriptions-item>
      <a-descriptions-item label="子代结算状态">
        <a-badge v-if="form.subWithdrawStatus === 0" color="yellow" text="待结算" />
        <a-badge v-else-if="form.subWithdrawStatus === 1" color="blue" text="出款处理中" />
        <a-badge v-else-if="form.subWithdrawStatus === 2" color="green" text="出款成功" />
        <a-badge v-else-if="form.subWithdrawStatus === 3" color="red" text="出款失败" />
      </a-descriptions-item>
      <a-descriptions-item label="子代结算时间">{{ form.subWithdrawTime }}</a-descriptions-item>
      <a-descriptions-item label="用户账户">{{ form.accountUuid }}</a-descriptions-item>
      <a-descriptions-item label="用户钱包">{{ form.walletUuid }}</a-descriptions-item>
      <a-descriptions-item label="用户编号">{{ form.userNo }}</a-descriptions-item>
      <a-descriptions-item label="用户类型">
        <a-badge v-if="form.userType === 1" color="pink" text="大区" />
        <a-badge v-else-if="form.userType === 2" color="blue" text="运营中心" />
        <a-badge v-else-if="form.userType === 3" color="cyan" text="代理商" />
        <a-badge v-else-if="form.userType === 5" color="purple" text="代理商(子代)" />
      </a-descriptions-item>
      <a-descriptions-item v-if="hasPurview('0')" label="用户级别">{{ form.userLevel }}</a-descriptions-item>
      <a-descriptions-item label="是否差错标记">
        <a-tag v-if="form.isErrorMark === 0">否</a-tag>
        <a-tag v-else-if="form.isErrorMark === 1" color="orange">是</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';
import { hasPurview } from '@/utils/permission';
import dayjs from 'dayjs';

export default {
  name: 'SettleBillDetail',
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible,
      hasPurview,
      dayjs
    };
  }
};
</script>
