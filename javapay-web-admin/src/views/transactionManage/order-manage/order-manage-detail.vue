<template>
  <a-modal
    :width="800"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
    :footer="null"
  >
    <a-descriptions bordered :column="2">
      <a-descriptions-item label="大区编号" v-if="hasPurview('0')">{{ form.regionNo }}</a-descriptions-item>
      <a-descriptions-item label="运营中心编号" v-if="hasPurview(['0', '1'])">{{ form.branchNo }}</a-descriptions-item>
      <a-descriptions-item label="代理商编号" v-if="!hasPurview(['3'])">{{ form.agentNo }}</a-descriptions-item>
      <a-descriptions-item label="商户编号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="订单编号">{{ form.orderNo }}</a-descriptions-item>
      <a-descriptions-item label="商品名称">{{ form.goodsName }}</a-descriptions-item>
      <a-descriptions-item label="商品详细">{{ form.goodsDetail }}</a-descriptions-item>
      <a-descriptions-item label="支付方式">
        <a-tag v-if="form.payMethod === 1" color="pink">云闪付</a-tag>
        <a-tag v-else-if="form.payMethod === 2" color="cyan">微信支付</a-tag>
        <a-tag v-else-if="form.payMethod === 3" color="blue">支付宝支付</a-tag>
        <a-tag v-else-if="form.payMethod === 4" color="purple">EPOS支付</a-tag>
        <a-tag v-else-if="form.payMethod === 5" color="pink">POS刷卡</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="支付金额">{{ form.payAmount }}</a-descriptions-item>
      <a-descriptions-item label="支付时间">{{ form.payTime }}</a-descriptions-item>
      <a-descriptions-item label="支付渠道">
        <template v-for="({ channelCode, channelName }, key) in payChannelList" :key="key">
          <a-tag v-if="form.payChannel === channelCode" color="pink">{{ channelName }}</a-tag>
        </template>
      </a-descriptions-item>

      <a-descriptions-item label="订单来源">
        <a-tag v-if="form.orderSource === 1" color="pink">收银台</a-tag>
        <a-tag v-else-if="form.orderSource === 2" color="cyan">APP收款</a-tag>
        <a-tag v-else-if="form.orderSource === 3" color="blue">POS终端</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="订单金额">{{ form.orderAmount }}</a-descriptions-item>
      <a-descriptions-item label="订单状态">
        <a-tag v-if="form.orderStatus === 1" color="pink">待支付</a-tag>
        <a-tag v-else-if="form.orderStatus === 2" color="cyan">已完成</a-tag>
        <a-tag v-else-if="form.orderStatus === 3" color="blue">已取消</a-tag>
        <a-tag v-else-if="form.orderStatus === 4" color="purple">支付失败</a-tag>
        <a-tag v-else-if="form.orderStatus === 5" color="blue">已退款</a-tag>
        <a-tag v-else-if="form.orderStatus === 6" color="purple">已部分退款</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="下单时间">{{ form.orderTime }}</a-descriptions-item>
      <a-descriptions-item label="失效时间">{{ form.timeExpire }}</a-descriptions-item>
      <a-descriptions-item label="取消时间">{{ form.cancelTime }}</a-descriptions-item>
      <a-descriptions-item label="退款金额">{{ form.refundAmount }}</a-descriptions-item>
      <a-descriptions-item label="退款时间">{{ form.refundTime }}</a-descriptions-item>
      <a-descriptions-item label="退款原因">{{ form.refundReason }}</a-descriptions-item>
    </a-descriptions>
  </a-modal>
</template>

<script>
import { hasPurview } from '@/utils/permission';

export default {
  name: 'OrderManageDetail',
  props: {
    visible: Boolean,
    detail: Object,
    payChannelList: Array
  },
  emits: ['update:visible'],

  data() {
    return {
      //表单数据
      form: {},
      hasPurview
    };
  },
  watch: {
    //父组件改变，则重新赋值
    detail() {
      this.form = Object.assign({}, this.detail);
    }
  },
  methods: {
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>

<style></style>
