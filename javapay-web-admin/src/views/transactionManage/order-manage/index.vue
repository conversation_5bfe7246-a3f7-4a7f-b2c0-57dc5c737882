<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
            </a-form-item>

            <a-form-item label="运营中心编号" v-purview="['0', '1']">
              <a-input v-model:value.trim="where.branchNo" placeholder="请输入运营中心编号" allow-clear />
            </a-form-item>

            <a-form-item label="大区编号" v-purview="'0'">
              <a-input v-model:value.trim="where.regionNo" placeholder="请输入大区编号" allow-clear />
            </a-form-item>

            <a-form-item label="代理商编号" v-if="!hasPurview(['3'])">
              <a-input v-model:value.trim="where.agentNo" placeholder="请输入代理商编号" allow-clear />
            </a-form-item>

            <a-form-item label="订单编号">
              <a-input v-model:value.trim="where.orderNo" placeholder="请输入订单编号" allow-clear />
            </a-form-item>

            <a-form-item label="支付方式">
              <a-select v-model:value="where.payMethod" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">云闪付</a-select-option>
                <a-select-option :value="2">微信支付</a-select-option>
                <a-select-option :value="3">支付宝支付</a-select-option>
                <a-select-option :value="4">EPOS支付</a-select-option>
                <a-select-option :value="5">POS刷卡</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="订单状态">
              <a-select v-model:value="where.orderStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">待支付</a-select-option>
                <a-select-option :value="2">已完成</a-select-option>
                <a-select-option :value="3">已取消</a-select-option>
                <a-select-option :value="4">支付失败</a-select-option>
                <a-select-option :value="5">已退款</a-select-option>
                <a-select-option :value="6">已部分退款</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="订单来源">
              <a-select v-model:value="where.orderSource" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">收银台</a-select-option>
                <a-select-option :value="2">APP收款</a-select-option>
                <a-select-option :value="3">POS终端</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="支付渠道">
              <a-select v-model:value="where.payChannel" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option v-for="({ channelCode, channelName }, key) in payChannelList" :key="key" :value="channelCode">{{
                  channelName
                }}</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="开始日期">
              <a-date-picker v-model:value="where.searchBeginTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>

            <a-form-item label="结束日期">
              <a-date-picker v-model:value="where.searchEndTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'payMethod'">
              <a-tag v-if="record.payMethod === 1" color="pink">云闪付</a-tag>
              <a-tag v-else-if="record.payMethod === 2" color="cyan">微信支付</a-tag>
              <a-tag v-else-if="record.payMethod === 3" color="blue">支付宝支付</a-tag>
              <a-tag v-else-if="record.payMethod === 4" color="purple">EPOS支付</a-tag>
              <a-tag v-else-if="record.payMethod === 5" color="cyan">POS刷卡</a-tag>
            </template>

            <template v-if="column.key === 'orderSource'">
              <a-tag v-if="record.orderSource === 1" color="pink">收银台</a-tag>
              <a-tag v-else-if="record.orderSource === 2" color="cyan">APP收款</a-tag>
              <a-tag v-else-if="record.orderSource === 3" color="blue">POS终端</a-tag>
            </template>

            <template v-if="column.key === 'orderStatus'">
              <a-tag v-if="record.orderStatus === 1" color="pink">待支付</a-tag>
              <a-tag v-else-if="record.orderStatus === 2" color="cyan">已完成</a-tag>
              <a-tag v-else-if="record.orderStatus === 3" color="blue">已取消</a-tag>
              <a-tag v-else-if="record.orderStatus === 4" color="purple">支付失败</a-tag>
              <a-tag v-else-if="record.orderStatus === 5" color="blue">已退款</a-tag>
              <a-tag v-else-if="record.orderStatus === 6" color="purple">已部分退款</a-tag>
            </template>

            <template v-if="column.key === 'payChannel'">
              <template v-for="({ channelCode, channelName }, key) in payChannelList" :key="key">
                <a-tag v-if="record.payChannel === channelCode" color="pink">{{ channelName }}</a-tag>
              </template>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <OrderManageDetail v-model:visible="showDetail" :detail="current" :payChannelList="payChannelList" />
  </div>
</template>

<script>
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import { OrderManageApi } from '@/api/transactionManage/order-manage/OrderManageApi';
import OrderManageDetail from './order-manage-detail.vue';
import { toDateString } from 'ele-admin-pro';
import { hasPurview } from '@/utils/permission';

export default {
  name: 'OrderManage',
  components: {
    OrderManageDetail
  },
  data() {
    return {
      //表格搜索条件
      where: {
        searchBeginTime: this.getToday(),
        searchEndTime: this.getToday()
      },
      //支付渠道列表
      payChannelList: [],
      //当前编辑数据
      current: null,
      //是否显示详情弹窗
      showDetail: false,
      hasPurview,

      //表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left',
          hideCol: !hasPurview('0')
        },
        {
          title: '大区编号',
          dataIndex: 'regionNo',
          align: 'center',
          hideCol: !hasPurview('0')
        },
        {
          title: '运营中心编号',
          dataIndex: 'branchNo',
          align: 'center',
          hideCol: !hasPurview(['0', '1'])
        },
        {
          title: '代理商编号',
          dataIndex: 'agentNo',
          align: 'center',
          hideCol: hasPurview(['3'])
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '订单编号',
          dataIndex: 'orderNo',
          align: 'center'
        },
        {
          title: '商品名称',
          dataIndex: 'goodsName',
          align: 'center'
        },
        {
          title: '商品详细',
          dataIndex: 'goodsDetail',
          align: 'center'
        },
        {
          title: '支付方式',
          dataIndex: 'payMethod',
          key: 'payMethod',
          align: 'center'
        },
        {
          title: '支付金额',
          dataIndex: 'payAmount',
          align: 'center'
        },
        {
          title: '支付时间',
          dataIndex: 'payTime',
          align: 'center'
        },
        {
          title: '支付渠道',
          dataIndex: 'payChannel',
          key: 'payChannel',
          align: 'center'
        },
        {
          title: '订单来源',
          dataIndex: 'orderSource',
          key: 'orderSource',
          align: 'center'
        },
        {
          title: '订单金额',
          dataIndex: 'orderAmount',
          align: 'center'
        },
        {
          title: '订单状态',
          dataIndex: 'orderStatus',
          key: 'orderStatus',
          align: 'center'
        },
        {
          title: '下单时间',
          dataIndex: 'orderTime',
          customRender: ({ text }) => toDateString(text),
          align: 'center'
        },
        {
          title: '失效时间',
          dataIndex: 'timeExpire',
          customRender: ({ text }) => toDateString(text),
          align: 'center'
        },
        {
          title: '取消时间',
          dataIndex: 'cancelTime',
          customRender: ({ text }) => toDateString(text),
          align: 'center'
        },
        {
          title: '退款金额',
          dataIndex: 'refundAmount',
          align: 'center'
        },
        {
          title: '退款时间',
          dataIndex: 'refundTime',
          customRender: ({ text }) => toDateString(text),
          align: 'center'
        },
        {
          title: '退款原因',
          dataIndex: 'refundReason',
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right',
          width: 150
        }
      ].filter(i => !i.hideCol)
    };
  },
  async mounted() {
    this.payChannelList = await ChannelManageApi.list({ validStatus: 1 });
  },
  methods: {
    //获取今日日期 YYYY-MM-DD
    getToday() {
      let date = new Date();
      let year = date.getFullYear();
      let month = date.getMonth() + 1;  // 注意，JS 中月份是从 0 开始计数的
      let day = date.getDate();

      // 如果月份或日期是个位数，我们需要在其前面补零
      month = month < 10 ? '0' + month : month;
      day = day < 10 ? '0' + day : day;

      return `${year}-${month}-${day}`;
    },

    /**
     * 搜索按钮
     */
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    /**
     * 重置搜索
     */
    reset() {
      this.where = {
        searchBeginTime: this.getToday(),
        searchEndTime: this.getToday()
      };
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    /**
     * 查看详情
     */
    handleDetail(row) {
      console.log('查看详情');
      this.current = row;
      this.showDetail = true;
    },

    /**
     * 获取表格数据
     */
    datasource({ page, limit, where, orders }) {
      return OrderManageApi.getOrderInfoPages({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>

<style></style>
