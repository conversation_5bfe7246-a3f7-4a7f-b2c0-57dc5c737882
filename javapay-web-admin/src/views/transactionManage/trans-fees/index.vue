<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
            </a-form-item>

            <a-form-item label="通道编号">
              <a-select v-model:value="where.channelNo" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option v-for="item in channelList" :key="item.id" :value="item.channelCode">{{ item.channelName }}</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="大区编号">
              <a-input v-model:value.trim="where.regionNo" placeholder="请输入大区编号" allow-clear />
            </a-form-item>
            <a-form-item label="运营中心编号">
              <a-input v-model:value.trim="where.branchNo" placeholder="请输入运营中心编号" allow-clear />
            </a-form-item>
            <a-form-item label="代理商编号">
              <a-input v-model:value.trim="where.agentNo" placeholder="请输入代理商编号" allow-clear />
            </a-form-item>
            <a-form-item label="交易流水单号">
              <a-input v-model:value.trim="where.flowNo" placeholder="请输入交易流水单号" allow-clear />
            </a-form-item>
            <a-form-item label="费率编号">
              <a-input v-model:value.trim="where.templateNo" placeholder="请输入费率编号" allow-clear />
            </a-form-item>
            <a-form-item label="代理商费率版本号">
              <a-input v-model:value.trim="where.agentRateVersion" placeholder="请输入代理商费率版本号" allow-clear />
            </a-form-item>
            <a-form-item label="运营中心费率版本号">
              <a-input v-model:value.trim="where.branchRateVersion" placeholder="请输入运营中心费率版本号" allow-clear />
            </a-form-item>
            <a-form-item label="商户费率版本号">
              <a-input v-model:value.trim="where.merchRateVersion" placeholder="请输入商户费率版本号" allow-clear />
            </a-form-item>
            <a-form-item label="大区费率版本号">
              <a-input v-model:value.trim="where.regionRateVersion" placeholder="请输入大区费率版本号" allow-clear />
            </a-form-item>
            <a-form-item label="交易通道日期">
              <a-date-picker v-model:value="where.chnTransDate" format="YYYYMMDD" valueFormat="YYYYMMDD" />
            </a-form-item>
            <a-form-item label="开始时间:">
              <a-date-picker v-model:value="where.searchBeginTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item label="结束时间:">
              <a-date-picker v-model:value="where.searchEndTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table
          ref="table"
          row-key="id"
          :datasource="datasource"
          :columns="columns"
          :where="where"
          v-model:selection="selection"
          :scroll="{ x: 'max-content' }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'debitType'">
              <a-tag v-if="record.debitType === 1" color="pink">加款</a-tag>
              <a-tag v-else-if="record.debitType === 2" color="cyan">减款</a-tag>
              <a-tag v-else-if="record.debitType === 3" color="purple">不记账</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <TransFeesDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { TransFeesApi } from '@/api/transactionManage/TransFeesApi.js';
import TransFeesDetail from './trans-fees-detail.vue';
import { hasPurview } from '@/utils/permission';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';

export default {
  name: 'TransFees',
  components: {
    TransFeesDetail
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '交易ID',
          dataIndex: 'transId',
          align: 'center'
        },
        {
          title: '交易流水单号',
          dataIndex: 'flowNo'
        },
        {
          title: '交易通道',
          dataIndex: 'transChn',
          customRender: ({ text }) => {
            const item = this.channelList.find(c => c.channelCode === text);
            return item?.channelName || '--';
          },
          align: 'center'
        },
        {
          title: '费率编号',
          dataIndex: 'templateNo',
          align: 'center'
        },
        {
          title: '交易金额(元)',
          dataIndex: 'transAmount',
          align: 'center'
        },
        {
          title: '通道商户名称',
          dataIndex: 'chnMerchName'
        },
        {
          title: '通道商户编号',
          dataIndex: 'chnMerchNo',
          align: 'center'
        },
        {
          title: '借贷标识',
          dataIndex: 'debitType',
          key: 'debitType',
          align: 'center'
        },
        {
          title: '平台信息',
          hideCol: hasPurview(['1', '2', '3']),
          children: [
            {
              title: '通道费率(%)',
              dataIndex: 'chnRate',
              align: 'center'
            },
            {
              title: '通道D0服务费费率(%)',
              dataIndex: 'chnD0Rate',
              align: 'center'
            },
            {
              title: '通道D0单笔服务费(元)',
              dataIndex: 'chnD0SingleFee',
              align: 'center'
            },
            {
              title: '通道交易手续费(元)',
              dataIndex: 'chnFee',
              align: 'center',
              hideCol: hasPurview(['1', '2', '3','5'])
            },
            {
              title: '通道D0手续费(元)',
              dataIndex: 'chnD0Fee',
              align: 'center'
            },
            {
              title: '平台交易总分润(元)',
              dataIndex: 'plfTransProfit',
              align: 'center',
              hideCol: !hasPurview('0')
            },
            {
              title: '平台D0分润(元)',
              dataIndex: 'plfD0Profit',
              align: 'center'
            }
          ].filter(i => !i.hideCol)
        },
        {
          title: '商户费用信息',
          children: [
            {
              title: '商户编号',
              dataIndex: 'merchantNo',
              align: 'center'
            },
            {
              title: '商户费率版本号',
              dataIndex: 'merchRateVersion',
              align: 'center'
            },
            {
              title: '商户费率(%)',
              dataIndex: 'merchRate',
              align: 'center'
            },
            {
              title: '商户D0服务费费率(%)',
              dataIndex: 'merchD0Rate',
              align: 'center'
            },
            {
              title: '商户D0单笔服务费(元)',
              dataIndex: 'merchD0SingleFee',
              align: 'center'
            },
            {
              title: '商户交易手续费(元)',
              dataIndex: 'merchFee',
              align: 'center'
            },
            {
              title: '商户D0手续费(元)',
              dataIndex: 'merchD0Fee',
              align: 'center'
            },
            {
              title: '商户结算金额(元)',
              dataIndex: 'merchSettleAmount',
              align: 'center'
            }
          ]
        },

        {
          title: '大区中心',
          hideCol: hasPurview(['2', '3','5']),
          children: [
            {
              title: '大区编号',
              dataIndex: 'regionNo',
              align: 'center',
              hideCol: hasPurview(['1', '2', '3','5'])
            },
            {
              title: '大区费率版本号',
              dataIndex: 'regionRateVersion',
              align: 'center'
            },
            {
              title: '大区返佣比例',
              dataIndex: 'regionRateRatio',
              align: 'center'
            },
            {
              title: '大区费率(%)',
              dataIndex: 'regionRate',
              align: 'center'
            },
            {
              title: '大区D0服务费费率',
              dataIndex: 'regionD0Rate',
              align: 'center'
            },
            {
              title: '大区D0单笔服务费(元)',
              dataIndex: 'regionD0SingleFee',
              align: 'center'
            },
            {
              title: '大区费率成本金额(元)',
              dataIndex: 'regionFee',
              align: 'center'
            },
            {
              title: '大区D0成本金额(元)',
              dataIndex: 'regionD0Fee',
              align: 'center'
            },
            {
              title: '大区费率分润金额(元)',
              dataIndex: 'regionProfit',
              align: 'center',
              hideCol: !hasPurview(['0', '1'])
            }
          ].filter(i => !i.hideCol)
        },
        {
          title: '运营中心',
          hideCol: hasPurview(['3','5']),
          children: [
            {
              title: '运营中心编号',
              dataIndex: 'branchNo',
              align: 'center',
              hideCol: hasPurview(['2', '3' ,'5'])
            },
            {
              title: '运营中心费率版本号',
              dataIndex: 'branchRateVersion',
              align: 'center'
            },
            {
              title: '运营中心返佣比例',
              dataIndex: 'branchRateRatio',
              align: 'center'
            },
            {
              title: '运营中心费率(%)',
              dataIndex: 'branchRate',
              align: 'center'
            },
            {
              title: '运营中心D0服务费费率(%)',
              dataIndex: 'branchD0Rate',
              align: 'center'
            },
            {
              title: '运营中心D0服务单笔费用(元)',
              dataIndex: 'branchD0SingleFee',
              align: 'center'
            },
            {
              title: '运营中心费率成本金额(元)',
              dataIndex: 'branchFee',
              align: 'center'
            },
            {
              title: '运营中心D0成本金额(元)',
              dataIndex: 'branchD0Fee',
              align: 'center'
            },
            {
              title: '运营中心费率分润金额(元)',
              dataIndex: 'branchProfit',
              align: 'center',
              hideCol: !hasPurview(['0', '2','5'])
            }
          ].filter(i => !i.hideCol)
        },
        {
          title: '代理商',
          children: [
            {
              title: '代理商编号',
              dataIndex: 'agentNo',
              align: 'center',
              hideCol: hasPurview(['3','5'])
            },
            {
              title: '代理商费率版本号',
              dataIndex: 'agentRateVersion',
              align: 'center'
            },
            {
              title: '代理商返佣比例',
              dataIndex: 'agentRateRatio',
              align: 'center'
            },
            {
              title: '代理商费率(%)',
              dataIndex: 'agentRate',
              align: 'center'
            },
            {
              title: '代理商D0服务费费率(%)',
              dataIndex: 'agentD0Rate',
              align: 'center'
            },
            {
              title: '代理商D0单笔服务费(元)',
              dataIndex: 'agentD0SingleFee',
              align: 'center'
            },
            {
              title: '代理商费率成本金额(元)',
              dataIndex: 'agentFee',
              align: 'center'
            },
            {
              title: '代理商D0成本金额(元)',
              dataIndex: 'agentD0Fee',
              align: 'center'
            },
            {
              title: '代理商费率分润金额(元)',
              dataIndex: 'agentProfit',
              align: 'center',
              hideCol: !hasPurview(['0', '2', '3','5'])
            }
          ].filter(i => !i.hideCol)
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 100,
          align: 'center'
        }
      ].filter(i => !i.hideCol),
      // 表格搜索条件
      where: {
        searchBeginTime: this.getToday(),
        searchEndTime: this.getToday()
      },
      selection: [],
      current: null,
      showDetail: false,
      channelList: [],
      hasPurview
    };
  },
  mounted() {
    this.getChannelList();
  },
  methods: {
    //获取今日日期 YYYY-MM-DD
    getToday() {
      let date = new Date();
      let year = date.getFullYear();
      let month = date.getMonth() + 1; // 注意，JS 中月份是从 0 开始计数的
      let day = date.getDate();

      // 如果月份或日期是个位数，我们需要在其前面补零
      month = month < 10 ? '0' + month : month;
      day = day < 10 ? '0' + day : day;

      return `${year}-${month}-${day}`;
    },

    reload() {
      this.selection = [];
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {
        searchBeginTime: this.getToday(),
        searchEndTime: this.getToday()
      };
      this.selection = [];
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where, orders }) {
      return TransFeesApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    },

    async getChannelList() {
      const data = await ChannelManageApi.list({ validStatus: 1 });
      this.channelList = data || [];
    },
  }
};
</script>
