<template>
  <div class="ele-body">
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="通道商户编号">
              <a-input v-model:value.trim="where.chnMerchNo" placeholder="通道商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="交易订单号">
              <a-input v-model:value.trim="where.transOrderNo" placeholder="交易订单号" allow-clear />
            </a-form-item>
            <a-form-item label="通知唯一订单号">
              <a-input v-model:value.trim="where.notifyOrderNo" placeholder="通知唯一订单号" allow-clear />
            </a-form-item>
            <a-form-item label="支付通道">
              <a-select v-model:value="where.channelCode" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
                  {{ channelName }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="处理状态">
              <a-select v-model:value="where.handleStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">未处理</a-select-option>
                <a-select-option :value="1">处理中</a-select-option>
                <a-select-option :value="2">处理完成</a-select-option>
                <a-select-option :value="3">处理拒绝</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="通知类型">
              <a-select v-model:value="where.notifyType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">银联交易风险案例通知</a-select-option>
                <a-select-option :value="2">交易调单通知</a-select-option>
                <a-select-option :value="3">交易退单通知</a-select-option>
                <a-select-option :value="4">A/T扫码风险案例通知</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="开始时间">
              <a-date-picker v-model:value="where.searchBeginTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item label="结束时间">
              <a-date-picker v-model:value="where.searchEndTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: '1000' }">
          <!-- table上边工具栏 -->
          <template #toolbar>
            <a-space>
              <a-button v-if="hasPurview(['0'])" @click="showRiskyCaseSync = true">
                <template #icon>
                  <RedoOutlined />
                </template>
                <span>风险案例同步</span>
              </a-button>
            </a-space>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'handleStatus'">
              <a-tag v-if="record.handleStatus === 0">未处理</a-tag>
              <a-tag v-else-if="record.handleStatus === 1" color="blue">处理中</a-tag>
              <a-tag v-else-if="record.handleStatus === 2" color="success">处理完成</a-tag>
              <a-tag v-else-if="record.handleStatus === 3" color="red">处理拒绝</a-tag>
            </template>

            <template v-else-if="column.key === 'notifyType'">
              <a-tag v-if="record.notifyType === 1">银联交易风险案例通知</a-tag>
              <a-tag v-else-if="record.notifyType === 2">交易调单通知</a-tag>
              <a-tag v-else-if="record.notifyType === 3">交易退单通知</a-tag>
              <a-tag v-else-if="record.notifyType === 4">A/T扫码风险案例通知</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
                <a-divider type="vertical" />
                <a @click="handleCaseProcessing(record)">案例处理</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <RiskyTransNoticeDetail v-model:visible="showDetail" :detail="current" />

    <RiskyTransCaseProcessingType1
      v-if="showProcessingType1"
      v-model:visible="showProcessingType1"
      :data="current"
      :readonly="isCaseProcessingReadonly"
      @done="reload"
    />
    <RiskyTransCaseProcessingType2
      v-if="showProcessingType2"
      v-model:visible="showProcessingType2"
      :data="current"
      :readonly="isCaseProcessingReadonly"
      @done="reload"
    />
    <RiskyTransCaseProcessingType3
      v-if="showProcessingType3"
      v-model:visible="showProcessingType3"
      :data="current"
      :readonly="isCaseProcessingReadonly"
      @done="reload"
    />
    <RiskyTransCaseProcessingType4
      v-if="showProcessingType4"
      v-model:visible="showProcessingType4"
      :data="current"
      :readonly="isCaseProcessingReadonly"
      @done="reload"
    />

    <RiskyCaseSync v-if="showRiskyCaseSync" v-model:visible="showRiskyCaseSync" @done="reload" />
  </div>
</template>

<script>
import { hasPurview } from '@/utils/permission';
import { RiskyTransNoticeApi } from '@/api/transactionManage/RiskyTransNoticeApi';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import RiskyTransNoticeDetail from './modules/risky-trans-notice-detail.vue';
import RiskyTransCaseProcessingType1 from './modules/risky-trans-case-processing.vue';
import RiskyTransCaseProcessingType2 from './modules/dispatch-order-trans-processing.vue';
import RiskyTransCaseProcessingType3 from './modules/retreat-order-trans-processing.vue';
import RiskyTransCaseProcessingType4 from './modules/atcode-risky-trans-processing.vue';
import RiskyCaseSync from '../risky-case-sync/index.vue';

export default {
  name: 'RiskyTransNotice',
  components: {
    RiskyTransNoticeDetail,
    RiskyTransCaseProcessingType1,
    RiskyTransCaseProcessingType2,
    RiskyTransCaseProcessingType3,
    RiskyTransCaseProcessingType4,
    RiskyCaseSync
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          align: 'center',
          width: 80,
          fixed: 'left'
        },
        {
          title: '支付通道',
          dataIndex: 'channelCode',
          align: 'center',
          customRender: ({ text, record }) => {
            const item = this.channelCodes.find(c => c.channelCode === text);
            record.channelName = item?.channelName || '--';
            return record.channelName;
          },
          width: 180
        },
        {
          title: '通道商户编号',
          dataIndex: 'chnMerchNo',
          align: 'center',
          width: 180
        },
        {
          title: '通道商户名称',
          dataIndex: 'chnMerchName',
          align: 'center',
          width: 180
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo',
          align: 'center',
          width: 180
        },
        {
          title: '交易订单号',
          dataIndex: 'transOrderNo',
          align: 'center',
          width: 300,
          ellipsis: true
        },
        {
          title: '通知唯一订单号',
          dataIndex: 'notifyOrderNo',
          align: 'center',
          width: 180
        },
        {
          title: '处理状态',
          dataIndex: 'handleStatus',
          key: 'handleStatus',
          align: 'center',
          width: 180
        },
        {
          title: '通知类型',
          dataIndex: 'notifyType',
          key: 'notifyType',
          align: 'center',
          width: 180
        },
        {
          title: '案例描述',
          dataIndex: 'orderResc',
          width: 250,
          ellipsis: true
        },
        {
          title: '处理结果描述',
          dataIndex: 'handleRespMsg',
          width: 250,
          ellipsis: true
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          width: 180
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 120,
          align: 'center'
        }
      ],
      // 表格搜索条件
      where: {},
      // 当前编辑数据
      current: null,
      // 是否显示编辑弹窗
      showDetail: false,
      isCaseProcessingReadonly: false,
      showProcessingType1: false,
      showProcessingType2: false,
      showProcessingType3: false,
      showProcessingType4: false,
      showRiskyCaseSync: false,
      channelCodes: []
    };
  },
  mounted() {
    this.getChannelCodes();
  },
  methods: {
    async getChannelCodes() {
      const data = await ChannelManageApi.list({ validStatus: 1 });
      this.channelCodes = data || [];
    },

    handleCaseProcessing(row) {
      this.current = row;
      this.isCaseProcessingReadonly = [1, 2].includes(row.handleStatus);
      this[`showProcessingType${row.notifyType}`] = true;
    },

    hasPurview,

    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where, orders }) {
      return RiskyTransNoticeApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
