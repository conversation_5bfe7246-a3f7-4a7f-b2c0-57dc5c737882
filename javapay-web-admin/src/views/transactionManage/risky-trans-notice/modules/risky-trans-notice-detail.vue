<template>
  <a-modal :width="800" :visible="visible" title="详情" :body-style="{ paddingBottom: '8px' }" @update:visible="updateVisible">
    <a-descriptions :column="2" title="基本信息">
      <a-descriptions-item label="支付通道">{{ form.channelName }}</a-descriptions-item>
      <a-descriptions-item label="通道商户编号">{{ form.chnMerchNo }}</a-descriptions-item>
      <a-descriptions-item label="通道商户名称">{{ form.chnMerchName }}</a-descriptions-item>
      <a-descriptions-item label="商户编号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="交易订单号">{{ form.transOrderNo }}</a-descriptions-item>
      <a-descriptions-item label="通知唯一订单号">{{ form.notifyOrderNo }}</a-descriptions-item>
      <a-descriptions-item label="处理状态">
        <a-tag v-if="form.handleStatus === 0">未处理</a-tag>
        <a-tag v-else-if="form.handleStatus === 1" color="blue">处理中</a-tag>
        <a-tag v-else-if="form.handleStatus === 2" color="success">处理完成</a-tag>
        <a-tag v-else-if="form.handleStatus === 3" color="red">处理拒绝</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="通知类型">
        <a-tag v-if="form.notifyType === 1">银联交易风险案例通知</a-tag>
        <a-tag v-else-if="form.notifyType === 2">交易调单通知</a-tag>
        <a-tag v-else-if="form.notifyType === 3">交易退单通知</a-tag>
        <a-tag v-else-if="form.notifyType === 4">A/T扫码风险案例通知</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="案例描述">{{ form.orderResc }}</a-descriptions-item>
      <a-descriptions-item label="处理结果描述">{{ form.handleRespMsg }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
    </a-descriptions>

    <div style="margin: 20px 0">
      <a-divider orientation="left" :orientationMargin="0" dashed>通知信息</a-divider>
    </div>

    <a-collapse v-model:activeKey="activePannelKey" accordion>
      <a-collapse-panel
        v-for="(item, key) in notifyDtoList"
        :key="key"
        :header="`交易订单号: ${item[TradeNoMap.get(form.notifyType)] || '--'}`"
      >
        <a-descriptions :column="2">
          <!-- A/T扫码风险案例通知类型对象 -->
          <template v-if="NotifyType2DtoMap.get(form.notifyType) === 'atqrcodeRiskNotifyDto'">
            <a-descriptions-item label="交易金额">{{ item.amount }}</a-descriptions-item>
            <a-descriptions-item label="交易订单号">{{ item.businessTradeNo }}</a-descriptions-item>
            <a-descriptions-item label="投诉内容">{{ item.complainMsg }}</a-descriptions-item>
            <a-descriptions-item label="投诉人（昵称）">{{ item.complainantName }}</a-descriptions-item>
            <a-descriptions-item label="投诉人手机号">{{ item.contact }}</a-descriptions-item>
            <a-descriptions-item label="内部商户处理结果(人工处理措施)">{{ item.finalMeasure }}</a-descriptions-item>
            <a-descriptions-item label="案例编号">{{ item.flowNo }}</a-descriptions-item>
            <a-descriptions-item label="审核结果(流程状态)">{{ item.flowStatus }}</a-descriptions-item>
            <a-descriptions-item label="所需资质说明">{{ item.materialRemark }}</a-descriptions-item>
            <a-descriptions-item label="材料说明">{{ item.materialRemark1 }}</a-descriptions-item>
            <a-descriptions-item label="结算商户号">{{ item.mercNum }}</a-descriptions-item>
            <a-descriptions-item label="投诉日期">{{ item.processPreRiskTime }}</a-descriptions-item>
            <a-descriptions-item label="产品类型">{{ item.productType }}</a-descriptions-item>
            <a-descriptions-item label="风险类型描述">{{ item.riskDesc }}</a-descriptions-item>
            <a-descriptions-item label="类别名称">{{ item.riskType }}</a-descriptions-item>
            <a-descriptions-item label="交易时间">{{ item.startTrxTime }}</a-descriptions-item>
            <a-descriptions-item label="交易状态">{{ item.status }}</a-descriptions-item>
            <a-descriptions-item label="支付宝反馈结果(上有处理措施)">{{ item.upperProcessMethod }}</a-descriptions-item>
          </template>

          <!-- 交易退单通知类型对象 -->
          <template v-if="NotifyType2DtoMap.get(form.notifyType) === 'transRefundOrderNotifyDto'">
            <a-descriptions-item label="银行商户号">{{ item.bankMercNo }}</a-descriptions-item>
            <a-descriptions-item label="发卡行">{{ item.bankName }}</a-descriptions-item>
            <a-descriptions-item label="银行参考号">{{ item.bankOrderNo }}</a-descriptions-item>
            <a-descriptions-item label="卡类型">{{ item.cardType }}</a-descriptions-item>
            <a-descriptions-item label="通道交易状态">{{ item.channelTradeStatus }}</a-descriptions-item>
            <a-descriptions-item label="差错原因">{{ item.errorReason }}</a-descriptions-item>
            <a-descriptions-item label="商家订单号">{{ item.mercOrderNo }}</a-descriptions-item>
            <a-descriptions-item label="回退原因">{{ item.operateDesc }}</a-descriptions-item>
            <a-descriptions-item label="订单类型">{{ item.orderType }}</a-descriptions-item>
            <a-descriptions-item label="退单编号">{{ item.retreatNum }}</a-descriptions-item>
            <a-descriptions-item label="结算金额">{{ item.settleAmount }}</a-descriptions-item>
            <a-descriptions-item label="结算商户号">{{ item.settleMercNo }}</a-descriptions-item>
            <a-descriptions-item label="结算类型">{{ SettleTypeMap.get(item.settleType) }}</a-descriptions-item>
            <a-descriptions-item label="系统交易日期">{{ item.systemTradeDate }}</a-descriptions-item>
            <a-descriptions-item label="系统交易时间">{{ item.systemTradeTime }}</a-descriptions-item>
            <a-descriptions-item label="终端号">{{ item.terminalNo }}</a-descriptions-item>
            <a-descriptions-item label="交易金额">{{ item.tradeAmount }}</a-descriptions-item>
            <a-descriptions-item label="交易卡号">{{ item.tradeCardNo }}</a-descriptions-item>
            <a-descriptions-item label="手续费">{{ item.tradeFee }}</a-descriptions-item>
            <a-descriptions-item label="交易完成时间">{{ item.tradeFinishTime }}</a-descriptions-item>
            <a-descriptions-item label="交易商户名称">{{ item.tradeMercName }}</a-descriptions-item>
            <a-descriptions-item label="交易商户编号">{{ item.tradeMercNo }}</a-descriptions-item>
            <a-descriptions-item label="交易订单号">{{ item.tradeOrderNo }}</a-descriptions-item>
            <a-descriptions-item label="交易来源">{{ item.tradeSource }}</a-descriptions-item>
            <a-descriptions-item label="交易状态">{{ item.tradeStatus }}</a-descriptions-item>
            <a-descriptions-item label="交易类型">{{ item.tradeType }}</a-descriptions-item>
            <a-descriptions-item label="银联流水号">{{ item.unionFlowNo }}</a-descriptions-item>
          </template>

          <!-- 交易调单通知类型对象 -->
          <template v-if="NotifyType2DtoMap.get(form.notifyType) === 'transTuningOrderDto'">
            <a-descriptions-item label="卡类型">{{ item.bankCardType }}</a-descriptions-item>
            <a-descriptions-item label="银行商户号">{{ item.bankMercNo }}</a-descriptions-item>
            <a-descriptions-item label="发卡行">{{ item.bankName }}</a-descriptions-item>
            <a-descriptions-item label="银行参考号">{{ item.bankOrderNo }}</a-descriptions-item>
            <a-descriptions-item label="通道交易状态">{{ item.channelTradeStatus }}</a-descriptions-item>
            <a-descriptions-item label="是否活动交易">{{ item.isActivityTrade }}</a-descriptions-item>
            <a-descriptions-item label="是否为押金交易">{{ item.isDepositTrade }}</a-descriptions-item>
            <a-descriptions-item label="最后期限">{{ item.lastDate }}</a-descriptions-item>
            <a-descriptions-item label="商家订单号">{{ item.mercOrderNo }}</a-descriptions-item>
            <a-descriptions-item label="交易订单号">{{ item.orderNo }}</a-descriptions-item>
            <a-descriptions-item label="订单类型">{{ item.orderType }}</a-descriptions-item>
            <a-descriptions-item label="原因码">{{ item.reasonCode }}</a-descriptions-item>
            <a-descriptions-item label="回退原因">{{ item.rejectReason }}</a-descriptions-item>
            <a-descriptions-item label="备注">{{ item.remark }}</a-descriptions-item>
            <a-descriptions-item label="应答码释义">{{ item.rspCodeNote }}</a-descriptions-item>
            <a-descriptions-item label="结算金额">{{ item.settleAmount }}</a-descriptions-item>
            <a-descriptions-item label="结算商户号">{{ item.settleMercNo }}</a-descriptions-item>
            <a-descriptions-item label="结算类型">{{ SettleTypeMap.get(item.settleType) }}</a-descriptions-item>
            <a-descriptions-item label="发起日期">{{ item.startDate }}</a-descriptions-item>
            <a-descriptions-item label="系统交易日期">{{ item.systemTradeDate }}</a-descriptions-item>
            <a-descriptions-item label="系统交易时间">{{ item.systemTradeTime }}</a-descriptions-item>
            <a-descriptions-item label="终端号">{{ item.terminalNo }}</a-descriptions-item>
            <a-descriptions-item label="交易金额">{{ item.tradeAmount }}</a-descriptions-item>
            <a-descriptions-item label="交易卡号">{{ item.tradeCardNo }}</a-descriptions-item>
            <a-descriptions-item label="手续费">{{ item.tradeFee }}</a-descriptions-item>
            <a-descriptions-item label="交易完成时间">{{ item.tradeFinishTime }}</a-descriptions-item>
            <a-descriptions-item label="交易商户名称">{{ item.tradeMercName }}</a-descriptions-item>
            <a-descriptions-item label="交易商户号">{{ item.tradeMercNo }}</a-descriptions-item>
            <a-descriptions-item label="交易应答码">{{ item.tradeRspCode }}</a-descriptions-item>
            <a-descriptions-item label="交易来源">{{ item.tradeSource }}</a-descriptions-item>
            <a-descriptions-item label="交易状态">{{ item.tradeStatus }}</a-descriptions-item>
            <a-descriptions-item label="交易类型">{{ item.tradeType }}</a-descriptions-item>
            <a-descriptions-item label="调单编号">{{ item.tuningNo }}</a-descriptions-item>
            <a-descriptions-item label="调单注释">{{ item.tuningNote }}</a-descriptions-item>
            <a-descriptions-item label="调单类型">{{ item.tuningType }}</a-descriptions-item>
          </template>

          <!-- 银联交易风险案例类型通知对象 -->
          <template v-if="NotifyType2DtoMap.get(form.notifyType) === 'unionTransRiskNotifyDto'">
            <a-descriptions-item label="审核状态">{{ AuditStatusMap.get(item.auditStatus) }}</a-descriptions-item>
            <a-descriptions-item label="银行卡号">{{ item.bankCardNo }}</a-descriptions-item>
            <a-descriptions-item label="卡类型">{{ item.bankCardType }}</a-descriptions-item>
            <a-descriptions-item label="发卡行">{{ item.bankName }}</a-descriptions-item>
            <a-descriptions-item label="案例处理方式">{{ CaseDealWayMap.get(item.caseDealWay) }}</a-descriptions-item>
            <a-descriptions-item label="案例编号">{{ item.caseNo }}</a-descriptions-item>
            <a-descriptions-item label="案例所需资质说明" :span="2">{{ item.caseQualificationDesc }}</a-descriptions-item>
            <a-descriptions-item label="失败详情" :span="2">{{ item.failDetail }}</a-descriptions-item>
            <a-descriptions-item label="失败原因" :span="2">{{ item.failReason }}</a-descriptions-item>
            <a-descriptions-item label="商家订单号（流水号）">{{ item.mercFlowNo }}</a-descriptions-item>
            <a-descriptions-item label="交易订单号">{{ item.orderNo }}</a-descriptions-item>
            <a-descriptions-item label="订单类型">{{ item.orderType }}</a-descriptions-item>
            <a-descriptions-item label="审核结果">{{ item.remark }}</a-descriptions-item>
            <a-descriptions-item label="风险类别名称">{{ item.riskClassName }}</a-descriptions-item>
            <a-descriptions-item label="结算商户号">{{ item.settleMercNo }}</a-descriptions-item>
            <a-descriptions-item label="系统处理措施">{{ item.systemTreatmentMeasures }}</a-descriptions-item>
            <a-descriptions-item label="交易金额">{{ item.tradeAmount }}</a-descriptions-item>
            <a-descriptions-item label="交易时间">{{ item.tradeDate }}</a-descriptions-item>
            <a-descriptions-item label="交易商户编号">{{ item.tradeMercNo }}</a-descriptions-item>
            <a-descriptions-item label="交易状态">{{ item.tradeStatus }}</a-descriptions-item>
            <a-descriptions-item label="交易类型">{{ item.tradeType }}</a-descriptions-item>
          </template>
        </a-descriptions>
      </a-collapse-panel>
    </a-collapse>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';
import { hasPurview } from '@/utils/permission';

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {},
      activePannelKey: 0,
      notifyDtoList: []
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);

        const dto_key = NotifyType2DtoMap.get(props.detail.notifyType);
        data.notifyDtoList = props['detail'][dto_key] || [];

        data.activePannelKey = 0;
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible,
      hasPurview,
      TradeStatusMap,
      OrderTypeMap,
      TradeTypeMap,
      SettleTypeMap,
      BankCardTypeMap,
      AuditStatusMap,
      CaseDealWayMap,
      NotifyType2DtoMap,
      TradeNoMap
    };
  }
};

// 交易状态
const TradeStatusMap = new Map([
  ['10A', '交易创建'],
  ['10B', '交易进行中'],
  ['10E', '交易已撤销'],
  ['10I', '交易成功已完成'],
  ['10L', '交易已关闭'],
  ['10M', '交易同步异常'],
  ['10N', '预授权已撤销'],
  ['10G', '冲正失败'],
  ['10J', '等待交易查询确认'],
  ['10O', '预授权完成已撤销'],
  ['10F', '交易已冲正'],
  ['10C', '交易成功'],
  ['10D', '交易失败'],
  ['10H', '退货失败'],
  ['10K', '撤销中/冲正中/退货中'],
  ['10P', '交易预处理']
]);

// 订单类型
const OrderTypeMap = new Map([
  [1048576, '部分退款'],
  [2, '撤销'],
  [8, '查余'],
  [32, '冲正'],
  [4, '退货'],
  [2048, '预授权撤销'],
  [1024, '预授权'],
  [1, '消费'],
  [4096, '预授权完成'],
  [8192, '预授权完成撤销']
]);

// 交易类型
const TradeTypeMap = new Map([
  ['active_scan', '主扫支付'],
  ['passive_scan', '被扫支付'],
  ['card_pay', '刷卡支付'],
  ['cloud_pay', '云闪付'],
  ['exemption_pay', '双免支付'],
  ['non_pay', '非接支付'],
  ['public_scan', '公众号支付'],
  ['plug_card_pay', '插卡支付'],
  ['wallet_scan', '钱包支付'],
  ['nfc_scan', '标签支付']
]);

// 结算类型
const SettleTypeMap = new Map([
  ['TS', '主动结算'],
  ['T1', '被动结算']
]);

// 卡类型
const BankCardTypeMap = new Map([
  ['94B9B2CD07054AD8966908C1EB25863E', '银联贷记卡'],
  ['9CE762E65EFD4E70962680F60DF928B2', '银联借记卡'],
  ['A7802D2362324FFA9C2AE53F210DED91', '运通借记卡'],
  ['8C248C50AF154B71AE26817D86975493', '运通贷记卡'],
  ['65D3BF87EE18471AB28E92A3987F180B', '运通外卡'],
  ['D31482DD7F6C4A428F1D7167B7088A6B', '万事达贷记卡'],
  ['41D223929B074E98B5BD6CB9E12B2E7B', '万事达借记卡']
]);

// 审核状态
const AuditStatusMap = new Map([
  ['DDSH', '待审核'],
  ['SHZ', '审核中'],
  ['TH', '回退'],
  ['SHTG', '审核通过']
]);

// 案例处理方式
const CaseDealWayMap = new Map([
  ['investigate', '风控调查'],
  ['warn', '预警']
]);

const NotifyType2DtoMap = new Map([
  [1, 'unionTransRiskNotifyDto'],
  [2, 'transTuningOrderDto'],
  [3, 'transRefundOrderNotifyDto'],
  [4, 'atqrcodeRiskNotifyDto']
]);

const TradeNoMap = new Map([
  [1, 'orderNo'],
  [2, 'orderNo'],
  [3, 'tradeOrderNo'],
  [4, 'businessTradeNo']
]);
</script>
