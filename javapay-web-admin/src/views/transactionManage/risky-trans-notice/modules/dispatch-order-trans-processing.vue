<template>
  <a-modal
    :width="1000"
    :visible="visible"
    :confirm-loading="loading"
    title="银联交易调单处理"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-descriptions :column="1">
      <a-descriptions-item label="通知唯一订单号">{{ form.notifyOrderNo }}</a-descriptions-item>
      <a-descriptions-item label="处理状态">
        <a-tag v-if="form.handleStatus === 0">未处理</a-tag>
        <a-tag v-else-if="form.handleStatus === 1" color="blue">处理中</a-tag>
        <a-tag v-else-if="form.handleStatus === 2" color="success">处理完成</a-tag>
        <a-tag v-else-if="form.handleStatus === 3" color="red">处理拒绝</a-tag>
      </a-descriptions-item>
    </a-descriptions>

    <div style="margin: 20px 0">
      <a-divider dashed />
    </div>

    <a-form ref="form" :model="form" :rules="rules" layout="vertical">
      <a-divider dashed orientation="left" orientationMargin="0">基本信息</a-divider>
      <a-row :gutter="[16, 16]">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="联系电话" name="linkPhone">
            <a-input v-model:value="form.linkPhone" placeholder="请输入联系电话" allow-clear :disabled="readonly" />
          </a-form-item>
          <a-form-item label="补充说明" name="replenish">
            <a-textarea
              v-model:value="form.replenish"
              :auto-size="{ minRows: 1, maxRows: 4 }"
              placeholder="补充说明要商户填调单注释要求的内容 商户名称、地址、电话、法人姓名"
              allow-clear
              :disabled="readonly"
            />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="详细地址" name="addressDetail">
            <a-textarea
              v-model:value="form.addressDetail"
              :auto-size="{ minRows: 1, maxRows: 4 }"
              placeholder="请输入详细地址"
              allow-clear
              :disabled="readonly"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 文件信息 -->
      <a-divider dashed orientation="left" orientationMargin="0">文件信息</a-divider>
      <a-row :gutter="[16, 16]">
        <template v-for="(item, key) in fileList" :key="key">
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item :label="`${item.label}`" :required="item.required">
              <a-upload
                v-model:file-list="item.fileData"
                accept=".png, .jpg, .jpeg"
                :max-count="item.maxCount"
                list-type="picture-card"
                :before-upload="file => handleSelectFile(file, item)"
                @preview="handlePreviewFile"
                :disabled="readonly"
              >
                <div v-if="item.maxCount && item.fileData?.length < item.maxCount">
                  <plus-outlined />
                  <div style="margin-top: 8px">Upload</div>
                </div>
              </a-upload>
            </a-form-item>
          </a-col>
        </template>
      </a-row>
      <!-- 预览图片 -->
      <a-image
        :style="{ display: 'none' }"
        :src="previewImage"
        :preview="{ visible: previewVisible, onVisibleChange: setPreviewVisible }"
      />
    </a-form>

    <template #footer v-if="readonly">
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { Upload, message } from 'ant-design-vue';
import { compressorImageSpecifySize } from '@/utils/image-compressor-util';
import { RiskyTransNoticeApi } from '@/api/transactionManage/RiskyTransNoticeApi';
import { FIXED_CHANNEL_CODE } from '@/config/setting';

export default {
  props: {
    visible: Boolean,
    readonly: Boolean,
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      form: {},
      rules: {
        linkPhone: [{ required: true, message: '请输入联系电话' }],
        replenish: [{ required: true, message: '请输入补充说明' }],
        addressDetail: [{ required: true, message: '请输入详细地址' }]
      },
      // 预览图片
      previewImage: '',
      previewVisible: false,
      // 提交loading
      loading: false,
      // 文件列表
      fileList: [
        {
          label: '签购单文件',
          fileType: 34,
          fileData: [],
          maxCount: 4
        },
        {
          label: '交易证明',
          fileType: 35,
          fileData: [],
          maxCount: 4
        },
        {
          label: '交易卡正面照片',
          fileType: 36,
          fileData: [],
          maxCount: 4
        },
        {
          label: '交易卡反面照',
          fileType: 37,
          fileData: [],
          maxCount: 4
        },
        {
          label: '身份证正面照片',
          fileType: 38,
          fileData: [],
          maxCount: 4
        },
        {
          label: '身份证反面照片',
          fileType: 39,
          fileData: [],
          maxCount: 4
        }
      ]
    };
  },
  mounted() {
    this.form = Object.assign({}, this.data);

    if (this.form.submitData) {
      const submitJson2Obj = JSON.parse(this.form.submitData);

      this.form = Object.assign(this.form, submitJson2Obj);

      // 处理图片
      const fileListMap = this.form.imageJsonList || [];
      fileListMap.forEach(fileItem=>{
        const findItem = this.fileList.find(item => item.fileType === fileItem.imageType);
        if (findItem) {
          findItem.fileData = fileItem.imagePath ? [...findItem.fileData, { url: fileItem.imagePath  ,id:fileItem.id}] : findItem.fileData;
        }
      })
    }
  },
  methods: {
    /**
     * 提交表单
     */
    async save() {
      // 校验表单
      await this.$refs.form.validate();
      // 校验文件
      await this.validateFileList();

      // 修改加载框为正在加载
      this.loading = true;

      // 上传图片
      await this.uploadImages().catch(() => {
        this.loading = false;
        return Promise.reject('Error: 图片上传失败');
      });

      const params = {
        notifyOrderNo: this.form.notifyOrderNo,
        linkPhone: this.form.linkPhone,
        replenish: this.form.replenish,
        addressDetail: this.form.addressDetail,
        imageJsonList: this.form.imageJsonList
      };
      RiskyTransNoticeApi.ylTransRiskDispatchOrderOperate(params)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    /**
     * 上传图片
     */
    async uploadImages() {
      const fileListHasVal = this.fileList.filter(i => i.fileData?.length);
      const noChangeFiles = [];
      const changeedFiles = [];
      fileListHasVal.forEach(i => {
        i.fileData.forEach(j => {
          if (/^(https?:)/.test(j.url)) {
            noChangeFiles.push({
              id: j.id,
              imageType: i.fileType,
              imagePath: j.url
            });
          } else {
            changeedFiles.push({
              fileType: i.fileType,
              suffixType: j.suffixType,
              fileData: j.url
            });
          }
        });
      });

      let imageJsonList = [];
      if (changeedFiles.length) {
        const data = await RiskyTransNoticeApi.uploadImages({
          channelCode: FIXED_CHANNEL_CODE,
          fileDTOList: changeedFiles,
          merchantNo: this.form.merchantNo
        });
        imageJsonList = data.imageJsonList;
      }

      this.form.imageJsonList = [...noChangeFiles, ...imageJsonList];
    },

    /**
     * 选中文件
     * @param {*} file
     * @param {*} item 当前图片项
     */
    handleSelectFile(file, item) {
      compressorImageSpecifySize(file).then(({ url }) => {
        item.fileData = [...item.fileData, { url, suffixType: 'png' }];
      });
      return Upload.LIST_IGNORE;
    },

    /**
     * 校验图片是否上传
     */
    validateFileList() {
      return new Promise((resolve, reject) => {
        if (this.fileList.every(f => !f.required || !!f.fileData?.length)) {
          resolve();
        } else {
          message.warn('请完整上传图片信息');
          reject();
        }
      });
    },

    handlePreviewFile(file) {
      this.previewImage = file?.url;
      this.setPreviewVisible(true);
    },

    setPreviewVisible(visible) {
      this.previewVisible = visible;
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
