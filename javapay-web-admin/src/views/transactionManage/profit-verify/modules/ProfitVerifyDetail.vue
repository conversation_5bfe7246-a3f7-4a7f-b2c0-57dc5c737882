<template>
  <a-modal
    :width="1000"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2" bordered>
      <a-descriptions-item label="机构编号">{{ form.userNo }}</a-descriptions-item>
      <a-descriptions-item label="机构类型">
        <a-badge v-if="form.userType === 1" color="pink" text="大区" />
        <a-badge v-else-if="form.userType === 2" color="blue" text="运营中心" />
        <a-badge v-else-if="form.userType === 3" color="cyan" text="代理商" />
      </a-descriptions-item>

      <a-descriptions-item label="核销月份">{{ form.verifyMonth }}</a-descriptions-item>
      <a-descriptions-item label="核销人">{{ form.verifyPerson }}</a-descriptions-item>
      <a-descriptions-item label="核销时间">{{ form.verifyDate }}</a-descriptions-item>


      <a-descriptions-item label="开票状态">
        <a-tag v-if="form.invoiceStatus === 0">未开票</a-tag>
        <a-tag v-else-if="form.invoiceStatus === 1" color="blue">待审核</a-tag>
        <a-tag v-else-if="form.invoiceStatus === 2" color="green">已开票</a-tag>
        <a-tag v-else-if="form.invoiceStatus === 3" color="red">审核不通过</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="审核信息">{{ form.checkMessage }}</a-descriptions-item>
      <a-descriptions-item label="应开票金额">{{ form.billableAmount }}</a-descriptions-item>
      <a-descriptions-item label="实开票金额">{{ form.actualInvoiceAmount }}</a-descriptions-item>

      <a-descriptions-item label="分润总金额">{{ form.profitTotalAmount }}</a-descriptions-item>
      <a-descriptions-item label="分润税差总金额">{{ form.profitTaxTotalDiff }}</a-descriptions-item>
      <a-descriptions-item label="交易分润总金额">{{ form.transTotalProfitAmount }}</a-descriptions-item>
      <a-descriptions-item label="交易分润税差总金额">{{ form.transTotalProfitAmountDiff }}</a-descriptions-item>
      <a-descriptions-item label="机构提现分润总金额">{{ form.orgWithdrawTotalProfitAmount }}</a-descriptions-item>
      <a-descriptions-item label="机构提现分润税差总金额">{{ form.orgWithdrawTotalProfitTaxDiff }}</a-descriptions-item>
      <a-descriptions-item label="机构代付分润总金额">{{ form.orgDaifuTotalProfitAmount }}</a-descriptions-item>
      <a-descriptions-item label="机构代付分润税差总金额">{{ form.orgDaifuTotalProfitTaxDiff }}</a-descriptions-item>

      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>

    <a-collapse :active-key="['1']" v-if="form.invoiceInfoList?.length && form.imageJsonList == null">
      <a-collapse-panel key="1" header="发票信息">
        <a-form>
          <template v-for="(item, idx) in form.invoiceInfoList || []" :key="idx">
            <a-row :gutter="18">
              <a-col :span="7">
                <a-form-item label="发票号码">
                  <a-input v-model:value="item.invoiceNo" placeholder="请输入发票号码" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="7">
                <a-form-item label="发票张数">
                  <a-input-number v-model:value="item.invoiceCount" placeholder="请输入发票张数" :min="1" style="width: 100%" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="7">
                <a-form-item label="发票金额">
                  <a-input-number
                    v-model:value="item.invoiceAmount"
                    :min="0"
                    prefix="￥"
                    style="width: 100%"
                    placeholder="请输入发票金额"
                    @change="() => autoCalculate(item, true)"
                    disabled
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="18">
              <a-col :span="7">
                <a-form-item label="税率(%)">
                  <a-input-number
                    v-model:value="item.taxRate"
                    :min="0"
                    style="width: 100%"
                    placeholder="请输入发票税率"
                    @change="() => autoCalculate(item)"
                    disabled
                  />
                </a-form-item>
              </a-col>
              <a-col :span="7">
                <a-form-item label="税额">
                  <a-input-number v-model:value="item.taxAmount" :min="0" prefix="￥" style="width: 100%" placeholder="请输入发票税额" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="7">
                <a-form-item label="不含税金额">
                  <a-input-number v-model:value="item.amount" :min="0" prefix="￥" style="width: 100%" placeholder="请输入不含税金额" disabled />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="18">
              <a-col :span="7">
                <a-form-item label="发票图片">
                  <a-upload
                    v-model:file-list="item.imageJsonList"
                    accept=".png, .jpg, .jpeg"
                    :max-count="1"
                    list-type="picture-card"
                    :before-upload="file => handleSelectFile(file, item)"
                    @preview="() => handlePreview(item)"
                    disabled
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </template>
        </a-form>
      </a-collapse-panel>
    </a-collapse>

    <a-collapse :active-key="['1']" v-if="form.imageJsonList?.length">
      <a-collapse-panel key="1" header="图片信息">
        <a-form :layout="'vertical'">
          <a-row :gutter="[16, 16]">
            <template v-for="(item, key) in fileList" :key="key">
              <a-col :md="12" :sm="24" :xs="24">
                <a-form-item :label="`${item.label}`">
                  <a-upload
                    v-model:file-list="item.fileData"
                    accept=".png, .jpg, .jpeg"
                    :max-count="item.maxCount"
                    list-type="picture-card"
                    @preview="() => handlePreviewForOld(item)"
                    disabled
                  >
                    <div v-if="!item.fileData?.length">
                      <plus-outlined />
                      <div style="margin-top: 8px">Upload</div>
                    </div>
                  </a-upload>
                </a-form-item>
              </a-col>
            </template>
          </a-row>
        </a-form>
      </a-collapse-panel>
    </a-collapse>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>

    <!-- 预览图片 -->
    <a-image
      :style="{ display: 'none' }"
      :src="previewImage"
      :preview="{ visible: previewVisible, onVisibleChange: setPreviewVisible }"
    />
  </a-modal>
</template>

<script>
import { onMounted, reactive, toRefs } from 'vue';
import { hasPurview } from '@/utils/permission';

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {},
      hasPurview,
      // 文件列表
      fileList: [
        {
          label: '发票图片',
          fileType: 43,
          fileData: [],
          maxCount: 5
        }
      ],
      // 预览图片
      previewImage: '',
      previewVisible: false
    });

    onMounted(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);

        // 处理invoiceInfoList中的图片
        data.form.invoiceInfoList?.forEach(item => {
          item.imageJsonList = [{url: item.imageJsonDTO?.imagePath, id: item.imageJsonDTO?.id}]; // 将 imageJsonDTO 放入 imageJsonList
        })

        // 处理图片
        const fileListMap = data.form.imageJsonList || [];
        fileListMap.forEach(fileItem => {
          const findItem = data.fileList.find(item => item.fileType === fileItem.imageType);
          if (findItem) {
            findItem.fileData = fileItem.imagePath
              ? [...findItem.fileData, { url: fileItem.imagePath, id: fileItem.id }]
              : findItem.fileData;
          }
        });
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    const handlePreview = ( item ) => {
      data.previewImage = item.imageJsonList[0].url;
      setPreviewVisible(true);
    };

    //处理老数据的
    const handlePreviewForOld = ( item ) => {
      console.log(item.fileData[0].url)
      data.previewImage = item.fileData[0].url;
      setPreviewVisible(true);
    };

    const setPreviewVisible = visible => {
      data.previewVisible = visible;
    };

    return {
      ...toRefs(data),
      updateVisible,
      handlePreview,
      handlePreviewForOld,
      setPreviewVisible
    };
  }
};
</script>
