<template>
  <a-modal
    :width="1000"
    :visible="visible"
    :confirm-loading="loading"
    title="开票"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <!-- 失败描述 -->
    <div style="margin-bottom: 15px" v-if="form.invoiceStatus === 3 && !!form.checkMessage">
      <a-typography-text type="danger">
        审核未通过原因: <a-typography-text type="danger" underline>{{ form.checkMessage }}</a-typography-text>
      </a-typography-text>
    </div>

    <!-- 主体 -->
    <a-form ref="form" :model="form" :rules="rules" :label-col="{ style: { width: '100px' } }">
      <a-tabs v-model:activeKey="activeKey" type="card">
        <a-tab-pane key="0" tab="基本信息">
          <a-row :gutter="18">
            <a-col :span="8">
              <a-form-item label="企业名称">
                <a-input-number :value="form.organName" style="width: 100%" disabled />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="开票税点">
                <a-input-number :value="form.taxPoint" style="width: 100%" disabled />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="18" v-for="info in form.data" :key="info.id">
            <a-col :span="8">
              <a-form-item label="核销月份" name="verifyMonth">
                <a-date-picker
                  style="width: 100%"
                  picker="month"
                  v-model:value="info.verifyMonth"
                  format="YYYY-MM"
                  valueFormat="YYYY-MM"
                  disabled
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="应开票金额" name="billableAmount">
                <a-input-number v-model:value="info.billableAmount" :min="0" prefix="￥" style="width: 100%" disabled />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="18">
            <a-col :span="8">
              <a-form-item label="应开票总金额" name="billableTotalAmount">
                <a-input-number :value="billableTotalAmount" :min="0" prefix="￥" style="width: 100%" disabled />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="实开票总金额" name="actualInvoiceTotalAmount">
                <a-input-number :value="actualInvoiceTotalAmount" :min="0" prefix="￥" style="width: 100%" disabled />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="待开票金额">
                <a-input-number :value="billableTotalAmount - actualInvoiceTotalAmount" :min="0" prefix="￥" :precision="2" style="width: 100%" disabled />
              </a-form-item>
            </a-col>
          </a-row>



          <a-card title="开票列表">
            <template v-for="(item, idx) in form.invoiceListMap || []" :key="idx">
              <a-row :gutter="18">
                <a-col :span="7">
                  <a-form-item label="发票号码" :name="['invoiceListMap', idx, 'invoiceNo']" :rules="rules.invoiceNo">
                    <a-input v-model:value="item.invoiceNo" placeholder="请输入发票号码" />
                  </a-form-item>
                </a-col>
                <a-col :span="7">
                  <a-form-item label="发票张数" :name="['invoiceListMap', idx, 'invoiceCount']" :rules="rules.invoiceCount">
                    <a-input-number v-model:value="item.invoiceCount" placeholder="请输入发票张数" :min="1" style="width: 100%" />
                  </a-form-item>
                </a-col>
                <a-col :span="7">
                  <a-form-item label="发票金额" :name="['invoiceListMap', idx, 'invoiceAmount']" :rules="rules.invoiceAmount">
                    <a-input-number
                      v-model:value="item.invoiceAmount"
                      :min="0"
                      prefix="￥"
                      style="width: 100%"
                      placeholder="请输入发票金额"
                      @change="() => autoCalculate(item, true)"
                    />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-row :gutter="18">
                <a-col :span="7">
                  <a-form-item label="税率(%)" :name="['invoiceListMap', idx, 'taxRate']" :rules="rules.taxRate">
                    <a-input-number
                      v-model:value="item.taxRate"
                      :min="0"
                      style="width: 100%"
                      placeholder="请输入发票税率"
                      @change="() => autoCalculate(item)"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="7">
                  <a-form-item label="税额" :name="['invoiceListMap', idx, 'taxAmount']" :rules="rules.taxAmount">
                    <a-input-number v-model:value="item.taxAmount" :min="0" prefix="￥" style="width: 100%" placeholder="请输入发票税额" />
                  </a-form-item>
                </a-col>
                <a-col :span="7">
                  <a-form-item label="不含税金额" :name="['invoiceListMap', idx, 'amount']" :rules="rules.amount">
                    <a-input-number v-model:value="item.amount" :min="0" prefix="￥" style="width: 100%" placeholder="请输入不含税金额" />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-row :gutter="18">
                <a-col :span="7">
                  <a-form-item label="发票图片" :name="['invoiceListMap', idx, 'imageJsonDTO']" :rules="rules.imageJsonDTO">
                    <a-upload
                      v-model:file-list="item.fileData"
                      accept=".png, .jpg, .jpeg"
                      :max-count="1"
                      list-type="picture-card"
                      :before-upload="file => handleSelectFile(file, item)"
                      @remove="() => handleRemoveImg(item)"
                      @preview="() => handlePreview(item)"
                    >
                      <div v-if="item.fileData == null || item.fileData?.length < 1">
                        <plus-outlined />
                        <div style="margin-top: 8px">Upload</div>
                      </div>
                    </a-upload>
                  </a-form-item>
                </a-col>

                <a-col :span="14" />

                <!-- 列表操作 -->
                <a-col :span="3">
                  <a-form-item>
                    <a-space>
                      <!-- + -->
                      <a-button v-if="idx === 0" @click="addItem">
                        <template #icon> <plus-outlined /> </template>
                      </a-button>
                      <!-- - -->
                      <a-button v-else @click="deleteItem(idx)">
                        <template #icon> <minus-outlined /> </template>
                      </a-button>
                      <a-button @click="copyItem(item)"> 复制 </a-button>
                    </a-space>
                  </a-form-item>
                </a-col>
              </a-row>

              <div v-if="idx !== form.invoiceListMap.length - 1" style="margin-bottom: 20px">
                <a-divider dashed />
              </div>
            </template>
          </a-card>
        </a-tab-pane>
      </a-tabs>
    </a-form>

    <!-- 预览图片 -->
    <a-image :style="{ display: 'none' }" :src="previewImage" :preview="{ visible: previewVisible, onVisibleChange: setPreviewVisible }" />
  </a-modal>
</template>

<script>
import { ProfitVerifyApi } from '@/api/transactionManage/ProfitVerifyApi';
import { ProfitInvoiceInfoApi } from '@/api/transactionManage/ProfitInvoiceInfoApi';
import { compressorImageSpecifySize } from '@/utils/image-compressor-util';
import { message, Upload } from 'ant-design-vue';
import {  isNumber } from 'ele-admin-pro';
import { cloneDeep } from 'lodash-es';
import { omit } from 'lodash-es';
import NP from 'number-precision';

export default {
  props: {
    visible: Boolean,
    data: Object
  },
  emits: ['update:visible', 'done'],

  data() {
    return {
      //表单数据
      form: {
        data: [],
        invoiceListMap: []
      },
      item: {},
      // 表单验证规则
      rules: {
        actualInvoiceAmount: [{ required: true, message: '请输入实开票金额' }],
        invoiceNo: [{ required: true, message: '必填' }],
        invoiceCount: [{ required: true, message: '必填' }],
        invoiceAmount: [{ required: true, message: '必填' }],
        taxRate: [{ required: true, message: '必填' }],
        taxAmount: [{ required: true, message: '必填' }],
        amount: [{ required: true, message: '必填' }],
        imageJsonDTO: [{ required: true, message: '必填' }]
      },
      //提交状态
      loading: false,
      activeKey: '0',
      // 文件列表
      fileList: [
        {
          label: '发票图片',
          fileType: 43,
          fileData: [],
          required: true,
          maxCount: 5
        }
      ],
      // 预览图片
      previewImage: '',
      previewVisible: false,

      billableTotalAmount: 0, //应开票总金额
      actualInvoiceTotalAmount: 0, //实开票总金额
    };
  },
  mounted() {
    if (this.data) {
      //赋值
      this.form.data = Object.assign([], this.data);

      //计算应开票总金额
      this.billableTotalAmount = this.form.data.reduce((sum, item) => sum + item.billableAmount, 0).toFixed(2);

      const { userNo, userType, taxPoint, regionName, branchName, agentName } = this.data[0];
      this.form.userNo = userNo;
      this.form.userType = userType;
      this.form.taxPoint = taxPoint;
      this.form.organName = userType === 1 ? regionName : userType === 2 ? branchName : agentName;

      // 处理发票列表
      this.form.invoiceListMap = this.data.invoiceInfoList || [];
      if (!this.form.invoiceListMap.length) {
        this.addItem();
      }
    }
  },
  methods: {
    /**
     * 提交表单
     */
    async save() {
      // 校验表单
      await this.$refs.form.validate();
      if (this.actualInvoiceTotalAmount !== this.billableTotalAmount) {
        message.error("实开票金额必须等于应开票金额");
        return;
      }

      //检查税点是否一致
      for (let i = 0; i < this.form.invoiceListMap.length; i++) {
        const invoiceItem = this.form.invoiceListMap[i];
        if (this.form.taxPoint !== invoiceItem.taxRate) {
          message.error("发票税点与报备的开票税点不一致，请先联系运营处理!");
          return;
        }
      }

      // 修改加载框为正在加载
      this.loading = true;

      let params = cloneDeep(this.form);
      let verifyMonthsList = this.form.data.map(item => {
        return {
          'verifyMonth': item.verifyMonth,
          'billableAmount': item.billableAmount,
        }
      })
      let idsList = this.form.data.map(item => {
        return item.id
      })

      params.verifyMonthsList = verifyMonthsList;
      params.idsList = idsList;
      params.actualInvoiceAmount = this.actualInvoiceTotalAmount;
      params.invoiceInfoList = params.invoiceListMap;

      //移除多余字段
      params = omit(params, ['data']);
      params = omit(params, ['invoiceListMap']);
      params.invoiceInfoList.forEach(item => {
        delete item.fileData;
      });

      ProfitInvoiceInfoApi.submitInvoiceReview(params)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    /**
     * 上传单个照片
     */
     async uploadSingleImage(item) {

      const uploadFile = [{
        fileType: 43,
        suffixType: 'png',
        fileData: item.fileData[0].url
      }]

      const data = await ProfitVerifyApi.uploadOrgImages({
          fileDTOList: uploadFile,
          orgNo: this.form.userNo,
          orgType: this.form.userType
        });

        //得到结果之后 进行赋值
        item.imageJsonDTO = data.imageJsonList[0];
    },

    /**
     * 上传图片
     */
    async uploadImages() {
      const fileListHasVal = this.fileList.filter(i => i.fileData?.length);
      const noChangeFiles = [];
      const changeedFiles = [];
      fileListHasVal.forEach(i => {
        i.fileData.forEach(j => {
          if (/^(https?:)/.test(j.url)) {
            noChangeFiles.push({
              id: j.id,
              imageType: i.fileType,
              imagePath: j.url
            });
          } else {
            changeedFiles.push({
              fileType: i.fileType,
              suffixType: j.suffixType,
              fileData: j.url
            });
          }
        });
      });

      let imageJsonList = [];
      if (changeedFiles.length) {
        const data = await ProfitVerifyApi.uploadOrgImages({
          fileDTOList: changeedFiles,
          orgNo: this.form.userNo,
          orgType: this.form.userType
        });
        imageJsonList = data.imageJsonList;
      }

      this.form.imageJsonList = [...noChangeFiles, ...imageJsonList];
    },

    /**
     * 选中文件
     * @param {*} file
     * @param {*} item 当前图片项
     */
    handleSelectFile(file, item) {
      compressorImageSpecifySize(file).then(({ url }) => {
        item.fileData = [{ url, suffixType: 'png' }];
        //压缩完进行上传
        this.uploadSingleImage(item);
      });
      return Upload.LIST_IGNORE;
    },

    /**
     * 校验图片是否上传
     */
    validateFileList() {
      return new Promise((resolve, reject) => {
        if (this.fileList.every(f => !f.required || !!f.fileData?.length)) {
          resolve();
        } else {
          message.warn('请上传图片信息');
          reject();
        }
      });
    },

    autoCalculate(item, totalFlag = false) {
      // 计算税额&金额
      if (item) {
        const { invoiceAmount, taxRate } = item;
        if ([invoiceAmount, taxRate].every(value => isNumber(value))) {
          item.amount = NP.round(NP.divide(invoiceAmount, NP.plus(1, NP.divide(taxRate, 100))), 2);
          item.taxAmount = NP.round(NP.minus(invoiceAmount, item.amount), 2);
        }
      }

      // 计算实开票金额
      if (totalFlag) {
        const sumAmount = this.form.invoiceListMap.reduce((cur, pre) => {
          return NP.plus(cur, isNumber(pre.invoiceAmount) ? pre.invoiceAmount : 0);
        }, 0);
        this.form.actualInvoiceAmount = sumAmount || '';
      }

      //计算实际开票总金额
      this.actualInvoiceTotalAmount = this.form.invoiceListMap.reduce((sum, item) => sum + item.invoiceAmount, 0).toFixed(2);
    },

    handleRemoveImg(item) {
      item.fileData = null;
    },

    handlePreview({ fileData }) {
      this.previewImage = fileData[0]?.url;
      this.setPreviewVisible(true);
    },

    setPreviewVisible(visible) {
      this.previewVisible = visible;
    },

    addItem() {
      const row = {
        invoiceNo: '',
        invoiceCount: 1,
        invoiceAmount: '',
        taxRate: '',
        taxAmount: '',
        amount: ''
      };
      this.form.invoiceListMap.push(row);
    },

    deleteItem(index) {
      this.form.invoiceListMap = this.form.invoiceListMap.filter((i, key) => index !== key);
      this.autoCalculate(null, true);
    },

    copyItem(item) {
      this.form.invoiceListMap.push(cloneDeep(item));
      this.autoCalculate(null, true);
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
