<template>
  <a-modal
    :width="1000"
    :visible="visible"
    :confirm-loading="loading"
    title="审核"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form :label-col="{ style: { width: '100px' } }">
      <a-tabs v-model:activeKey="activeKey" type="card">
        <a-tab-pane key="0" tab="基本信息">
          <a-row :gutter="18">
            <a-col :span="8">
              <a-form-item label="企业名称">
                <a-input-number :value="form.organName" style="width: 100%" disabled />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="开票税点">
                <a-input-number :value="form.taxPoint" style="width: 100%" disabled />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="18" v-for="(monthItem, idx) in form.verifyMonthsList" :key="idx">
            <a-col :span="8">
              <a-form-item label="核销月份" name="verifyMonth">
                <a-date-picker
                  style="width: 100%"
                  picker="month"
                  v-model:value="monthItem.verifyMonth"
                  format="YYYY-MM"
                  valueFormat="YYYY-MM"
                  disabled
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="应开票金额" name="billableAmount">
                <a-input-number v-model:value="monthItem.billableAmount" :min="0" prefix="￥" style="width: 100%" disabled />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="18">
            <a-col :span="8">
              <a-form-item label="应开票总金额" name="billableTotalAmount">
                <a-input-number :value="billableTotalAmount" :min="0" prefix="￥" style="width: 100%" disabled />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="实开票总金额" name="actualInvoiceAmount">
                <a-input-number :value="form.actualInvoiceAmount" :min="0" prefix="￥" style="width: 100%" disabled />
              </a-form-item>
            </a-col>
          </a-row>

          <a-card title="开票列表">
            <template v-for="(item, idx) in form.invoiceListMap || []" :key="idx">
              <a-row :gutter="18">
                <a-col :span="7">
                  <a-form-item label="发票号码" :name="['invoiceListMap', idx, 'invoiceNo']" :rules="rules.invoiceNo">
                    <a-input v-model:value="item.invoiceNo" placeholder="请输入发票号码" disabled />
                  </a-form-item>
                </a-col>
                <a-col :span="7">
                  <a-form-item label="发票张数" :name="['invoiceListMap', idx, 'invoiceCount']" :rules="rules.invoiceCount">
                    <a-input-number v-model:value="item.invoiceCount" placeholder="请输入发票张数" :min="1" style="width: 100%" disabled />
                  </a-form-item>
                </a-col>
                <a-col :span="7">
                  <a-form-item label="发票金额" :name="['invoiceListMap', idx, 'invoiceAmount']" :rules="rules.invoiceAmount">
                    <a-input-number
                      v-model:value="item.invoiceAmount"
                      :min="0"
                      prefix="￥"
                      style="width: 100%"
                      placeholder="请输入发票金额"
                      disabled
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="18">
                <a-col :span="7">
                  <a-form-item label="税率(%)" :name="['invoiceListMap', idx, 'taxRate']" :rules="rules.taxRate">
                    <a-input-number v-model:value="item.taxRate" :min="0" style="width: 100%" placeholder="请输入发票税率" disabled />
                  </a-form-item>
                </a-col>
                <a-col :span="7">
                  <a-form-item label="税额" :name="['invoiceListMap', idx, 'taxAmount']" :rules="rules.taxAmount">
                    <a-input-number
                      v-model:value="item.taxAmount"
                      :min="0"
                      prefix="￥"
                      style="width: 100%"
                      placeholder="请输入发票税额"
                      disabled
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="7">
                  <a-form-item label="不含税金额" :name="['invoiceListMap', idx, 'amount']" :rules="rules.amount">
                    <a-input-number
                      v-model:value="item.amount"
                      :min="0"
                      prefix="￥"
                      style="width: 100%"
                      placeholder="请输入不含税金额"
                      disabled
                    />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-row :gutter="18">
                <a-col :span="7">
                  <a-form-item label="发票图片">
                    <a-upload
                      v-model:file-list="item.imageJsonList"
                      accept=".png, .jpg, .jpeg"
                      :max-count="1"
                      list-type="picture-card"
                      :before-upload="file => handleSelectFile(file, item)"
                      @preview="() => handlePreview(item)"
                      disabled
                    />
                  </a-form-item>
                </a-col>
              </a-row>

              <div v-if="idx !== form.invoiceListMap.length - 1" style="margin-bottom: 20px">
                <a-divider dashed />
              </div>
            </template>
          </a-card>
        </a-tab-pane>
      </a-tabs>
    </a-form>

    <div style="margin: 20px 0 10px">
      <a-typography-text mark>请填写审核信息</a-typography-text>
    </div>

    <a-form ref="form" :model="checkForm" :rules="rules" :label-col="{ style: { width: '100px' } }">
      <a-row :gutter="18">
        <a-col :span="14">
          <a-form-item label="审核状态" name="invoiceStatus">
            <a-select v-model:value="checkForm.invoiceStatus" class="ele-fluid" placeholder="请选择">
              <a-select-option :value="2">通过</a-select-option>
              <a-select-option :value="3">不通过</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="14">
          <a-form-item label="审核信息" name="checkMessage">
            <a-textarea v-model:value="checkForm.checkMessage" placeholder="审核信息" :auto-size="{ minRows: 2, maxRows: 5 }" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>

    <!-- 预览图片 -->
    <a-image :style="{ display: 'none' }" :src="previewImage" :preview="{ visible: previewVisible, onVisibleChange: setPreviewVisible }" />
  </a-modal>
</template>

<script>
import { ProfitInvoiceInfoApi } from '@/api/transactionManage/ProfitInvoiceInfoApi';
import { message } from 'ant-design-vue';

export default {
  props: {
    visible: Boolean,
    data: Object
  },
  emits: ['update:visible', 'done'],

  data() {
    return {
      //表单数据
      form: {
        invoiceListMap: []
      },
      checkForm: {},
      item: {},
      // 表单验证规则
      rules: {
        invoiceStatus: [{ required: true, message: '请选择' }],
        checkMessage: [{ required: true, message: '请输入审核信息' }]
      },
      //提交状态
      loading: false,
      activeKey: '0',
      // 文件列表
      fileList: [
        {
          label: '发票图片',
          fileType: 43,
          fileData: [],
          maxCount: 5
        }
      ],
      // 预览图片
      previewImage: '',
      previewVisible: false,

      billableTotalAmount: 0, //应开票总金额
    };
  },
  mounted() {
    if (this.data) {
      this.form = Object.assign({}, this.data);

      //计算应开票总金额
      this.billableTotalAmount = this.form.verifyMonthsList.reduce((sum, item) => sum + item.billableAmount, 0).toFixed(2);

      const { userType, regionName, branchName, agentName } = this.data;
      //企业名称
      this.form.organName = userType === 1 ? regionName : userType === 2 ? branchName : agentName;

      // 处理invoiceInfoList中的图片
      this.form.invoiceInfoList.forEach(item => {
        item.imageJsonList = [{url: item.imageJsonDTO?.imagePath, id: item.imageJsonDTO?.id}]; // 将 imageJsonDTO 放入 imageJsonList
      })

      // 处理发票列表
      this.form.invoiceListMap = this.form.invoiceInfoList || [];

      // 处理图片
      const fileListMap = this.form.imageJsonList || [];
      fileListMap.forEach(fileItem => {
        const findItem = this.fileList.find(item => item.fileType === fileItem.imageType);
        if (findItem) {
          findItem.fileData = fileItem.imagePath ? [...findItem.fileData, { url: fileItem.imagePath, id: fileItem.id }] : findItem.fileData;
        }
      });
    }
  },
  methods: {
    /**
     * 提交表单
     */
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      const params = {
        id: this.form.id,
        verifyMonth: this.form.verifyMonth,
        userNo: this.form.userNo,
        actualInvoiceAmount: this.form.actualInvoiceAmount,
        ...this.checkForm
      };

      ProfitInvoiceInfoApi.checkReview(params)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示修改成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    handlePreview({ imageJsonList }) {
      this.previewImage = imageJsonList[0]?.url;
      this.setPreviewVisible(true);
    },

    setPreviewVisible(visible) {
      this.previewVisible = visible;
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
