<template>
  <a-modal
    :width="500"
    :visible="visible"
    :confirm-loading="loading"
    title="手动插入核销记录"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules" :layout="'vertical'">
      <a-form-item label="核销月份" name="verifyMonth">
        <a-date-picker picker="month" style="width: 100%" v-model:value="form.verifyMonth" format="YYYY-MM" valueFormat="YYYY-MM" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { ProfitVerifyApi } from '@/api/transactionManage/ProfitVerifyApi';
import { message } from 'ant-design-vue';

export default {
  props: {
    visible: Boolean
  },
  emits: ['update:visible', 'done'],
  data() {
    return {
      form: {},
      loading: false,
      rules: {
        verifyMonth: [{ required: true, message: '请选择核销月份' }]
      }
    };
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      ProfitVerifyApi.manualBatchAdd(this.form)
        .then(res => {
          this.loading = false;

          message.success(res.message);

          this.updateVisible(false);

          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
