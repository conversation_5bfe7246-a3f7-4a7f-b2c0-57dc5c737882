<template>
  <div class="ele-body">
    <a-spin :spinning="spinning" tip="下载中, 请稍候...">
      <!-- 搜索表单 -->
      <div class="block-interval">
        <a-card :bordered="false">
          <a-form layout="inline" :model="where">
            <a-row :gutter="[0, 16]">
              <a-form-item label="核销月份">
                <a-date-picker picker="month" v-model:value="where.verifyMonth" format="YYYY-MM" valueFormat="YYYY-MM" />
              </a-form-item>
              <a-form-item label="开票状态">
                <a-select v-model:value="where.invoiceStatus" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option :value="0">未开票</a-select-option>
                  <a-select-option :value="1">待审核</a-select-option>
                  <a-select-option :value="2">已开票</a-select-option>
                  <a-select-option :value="3">审核不通过</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="用户编号" v-purview="'0'">
                <a-input v-model:value.trim="where.userNo" placeholder="请输入用户编号" allow-clear />
              </a-form-item>
              <a-form-item label="用户类型" v-purview="'0'">
                <a-select v-model:value="where.userType" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option :value="1">大区</a-select-option>
                  <a-select-option :value="2">运营中心</a-select-option>
                  <a-select-option :value="3">代理商</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="核销开始日期">
                <a-date-picker v-model:value="where.verifyBeginTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
              </a-form-item>
              <a-form-item label="核销结束日期">
                <a-date-picker v-model:value="where.verifyEndTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
              </a-form-item>

              <a-form-item class="ele-text-center">
                <a-space>
                  <a-button type="primary" @click="reload">查询</a-button>
                  <a-button @click="reset">重置</a-button>
                </a-space>
              </a-form-item>
            </a-row>
          </a-form>
        </a-card>
      </div>

      <!-- 表格 -->
      <div>
        <a-card :bordered="false" class="table-height">
          <ele-pro-table
            ref="table"
            row-key="id"
            :datasource="datasource"
            :columns="columns"
            :where="where"
            v-model:selection="selection"
            :scroll="{ x: 'max-content' }"
          >
            <!-- table上边工具栏 -->
            <template #toolbar>
              <a-descriptions title="统计信息" :column="2" size="small" style="margin-bottom: 15px">
                <a-descriptions-item label="应开票总金额">{{ summary.billableAmountSummary }}</a-descriptions-item>
                <a-descriptions-item label="交易分润应开票总金额">{{ summary.transTotalProfitAmount }}</a-descriptions-item>
                <a-descriptions-item label="机构提现分润应开票总金额">{{ summary.orgWithdrawTotalProfitAmoSummary }}</a-descriptions-item>
                <a-descriptions-item label="机构代付分润应开票总金额">{{ summary.orgDaifuTotalProfitAmountSummary }}</a-descriptions-item>
              </a-descriptions>
              <a-space>
                <a-button @click="handleInsert" v-if="hasPurview(['0'])">
                  <template #icon>
                    <node-expand-outlined />
                  </template>
                  <span>手动插入记录</span>
                </a-button>
                <a-button @click="handleExportExcel">
                  <template #icon>
                    <download-outlined />
                  </template>
                  <span>导出excel</span>
                </a-button>

                <a-button @click="handleProfitInvoiceExportExcel" v-if="hasPurview(['0'])">
                  <template #icon>
                    <download-outlined />
                  </template>
                  <span>分润发票导出excel</span>
                </a-button>

                <a-button @click="handleBatchApplyInvoice" v-if="hasPurview(['1', '2', '3'])">
                  <template #icon>
                    <download-outlined />
                  </template>
                  <span>批量开票</span>
                </a-button>
              </a-space>
            </template>

            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'verifyStatus'">
                <a-tag v-if="record.verifyStatus === 0">待核销</a-tag>
                <a-tag v-else-if="record.verifyStatus === 1" color="pink">核销中</a-tag>
                <a-tag v-else-if="record.verifyStatus === 2" color="green">核销通过</a-tag>
                <a-tag v-else-if="record.verifyStatus === 3" color="red">核销不通过</a-tag>
              </template>
              <template v-else-if="column.key === 'invoiceStatus'">
                <a-tag v-if="record.invoiceStatus === 0">未开票</a-tag>
                <a-tag v-else-if="record.invoiceStatus === 1" color="blue">待审核</a-tag>
                <a-tag v-else-if="record.invoiceStatus === 2" color="green">已开票</a-tag>
                <a-tag v-else-if="record.invoiceStatus === 3" color="red">审核不通过</a-tag>
              </template>
              <template v-else-if="column.key === 'userType'">
                <a-badge v-if="record.userType === 1" color="pink" text="大区" />
                <a-badge v-else-if="record.userType === 2" color="blue" text="运营中心" />
                <a-badge v-else-if="record.userType === 3" color="cyan" text="代理商" />
              </template>
              <template v-else-if="column.key === 'organName'">
                <span v-if="record.userType === 1">{{record.regionName}}</span>
                <span v-else-if="record.userType === 2">{{record.branchName}}</span>
                <span v-else-if="record.userType === 3">{{record.agentName}}</span>
              </template>

              <!-- 操作栏 -->
              <template v-else-if="column.key === 'action'">
                <a-space>
                  <a @click="handleDetail(record)">详情</a>
                  <!-- <template v-if="hasPurview(['0']) && record.invoiceStatus === 1">
                    <a-divider type="vertical" />
                    <a @click="handleCheck(record)">审核</a>
                  </template>
                  <template v-if="hasPurview(['1', '2', '3']) && [0, 3].includes(record.invoiceStatus)">
                    <a-divider type="vertical" />
                    <a @click="handleApply(record)">申请开票</a>
                  </template> -->
                </a-space>
              </template>
            </template>
          </ele-pro-table>
        </a-card>
      </div>
    </a-spin>

    <!-- 详情 -->
    <ProfitVerifyDetail v-if="showDetail" v-model:visible="showDetail" :detail="current" />

    <!-- 申请开票 -->
    <ApplyInvoice v-if="showApplyInvoice" v-model:visible="showApplyInvoice" :data="selection" @done="reload" />

    <!-- 审核 -->
    <CheckProfitVerify v-if="showCheckProfitVerify" v-model:visible="showCheckProfitVerify" :data="current" @done="reload" />

    <!-- 手动插入 -->
    <ManualInsert v-if="showManualInsert" v-model:visible="showManualInsert" @done="reload" />
  </div>
</template>

<script>
import { ProfitVerifyApi } from '@/api/transactionManage/ProfitVerifyApi';
import { hasPurview } from '@/utils/permission';
import ProfitVerifyDetail from './modules/ProfitVerifyDetail.vue';
import ApplyInvoice from './modules/ApplyInvoice.vue';
import CheckProfitVerify from './modules/CheckProfitVerify.vue';
import ManualInsert from './modules/ManualInsert.vue';
import { message } from 'ant-design-vue';

export default {
  name: 'ProfitVerify',
  components: {
    ProfitVerifyDetail,
    ApplyInvoice,
    CheckProfitVerify,
    ManualInsert
  },
  data() {
    return {
      hasPurview,
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          hideCol: !hasPurview(['0']),
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '机构编号',
          dataIndex: 'userNo',
          align: 'center'
        },
        {
          title: '机构类型',
          dataIndex: 'userType',
          key: 'userType',
          align: 'center'
        },
        {
          title: '机构名称',
          dataIndex: 'organName',
          key: 'organName',
          align: 'center'
        },
        {
          title: '税点',
          dataIndex: 'taxPoint',
          align: 'center'
        },
        {
          title: '核销月份',
          dataIndex: 'verifyMonth',
          align: 'center'
        },
        {
          title: '核销人',
          dataIndex: 'verifyPerson',
          align: 'center'
        },
        {
          title: '核销时间',
          dataIndex: 'verifyDate',
          align: 'center'
        },
        {
          title: '开票状态',
          dataIndex: 'invoiceStatus',
          key: 'invoiceStatus',
          align: 'center'
        },
        {
          title: '审核信息',
          dataIndex: 'checkMessage',
          align: 'center'
        },
        {
          title: '应开票金额',
          dataIndex: 'billableAmount',
          align: 'center'
        },
        {
          title: '实开票金额',
          dataIndex: 'actualInvoiceAmount',
          align: 'center'
        },
        {
          title: '分润总金额',
          dataIndex: 'profitTotalAmount',
          align: 'center'
        },
        {
          title: '分润税差总金额',
          dataIndex: 'profitTaxTotalDiff',
          align: 'center'
        },
        {
          title: '交易分润总金额',
          dataIndex: 'transTotalProfitAmount',
          align: 'center'
        },
        {
          title: '交易分润税差总金额',
          dataIndex: 'transTotalProfitAmountDiff',
          align: 'center'
        },
        {
          title: '机构提现分润总金额',
          dataIndex: 'orgWithdrawTotalProfitAmount',
          align: 'center'
        },
        {
          title: '机构提现分润税差总金额',
          dataIndex: 'orgWithdrawTotalProfitTaxDiff',
          align: 'center'
        },
        {
          title: '机构代付分润总金额',
          dataIndex: 'orgDaifuTotalProfitAmount',
          align: 'center'
        },
        {
          title: '机构代付分润税差总金额',
          dataIndex: 'orgDaifuTotalProfitTaxDiff',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right',
          width: 200
        }
      ].filter(i => !i.hideCol),
      // 表格搜索条件
      where: {},

      selection: [],
      current: null,
      summary: {},
      showDetail: false,
      showEdit: false,
      showApplyInvoice: false,
      showCheckProfitVerify: false,
      showManualInsert: false,
      spinning: false
    };
  },
  mounted() {
    this.getSummaryData();
  },
  methods: {
    //导出excel
    async handleExportExcel() {
      this.spinning = true;
      const res = await ProfitVerifyApi.downloadProfitVerify(this.where).catch(() => {
        this.spinning = false;
      });
      this.spinning = false;
      const fileReader = new FileReader();
      fileReader.onload = function () {
        try {
          // 说明是普通对象数据，后台转换失败
          const jsonData = JSON.parse(fileReader.result);
          message.error(jsonData.message);
        } catch (err) {
          const contentDisposition = res.headers['content-disposition'];
          let fileName = decodeURIComponent(contentDisposition.substring(contentDisposition.indexOf('=') + 1));
          fileName = '分润核销-下载.xlsx';
          // 解析对象失败，说明是正常的文件流
          let blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.style.display = 'none';
          a.href = url;
          a.download = fileName;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }
      };
      fileReader.readAsText(res?.data);
    },

    //分润发票导出excel
    async handleProfitInvoiceExportExcel() {
      this.spinning = true;
      const res = await ProfitVerifyApi.downloadProfitInvoice(this.where).catch(() => {
        this.spinning = false;
      });
      this.spinning = false;
      const fileReader = new FileReader();
      fileReader.onload = function () {
        try {
          // 说明是普通对象数据，后台转换失败
          const jsonData = JSON.parse(fileReader.result);
          message.error(jsonData.message);
        } catch (err) {
          const contentDisposition = res.headers['content-disposition'];
          let fileName = decodeURIComponent(contentDisposition.substring(contentDisposition.indexOf('=') + 1));
          fileName = '分润发票-下载.xlsx';
          // 解析对象失败，说明是正常的文件流
          let blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.style.display = 'none';
          a.href = url;
          a.download = fileName;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }
      };
      fileReader.readAsText(res?.data);
    },

    //批量开票
    handleBatchApplyInvoice() {
      //校验,只能选择未开票或者审核不通过的进行开票
      for (let item of this.selection) {
        if (![0, 3].includes(item.invoiceStatus)) {
          message.error("数据错误,只能选择未开票或者审核不通过的进行开票!");
          return;
        }
      }
      if(this.selection.length == 0) {
        message.error("请先勾选要开票核销的月份!");
        return;
      }

      //校验通过的,进入开票页面
      // this.current = row;
      this.showApplyInvoice = true;
    },

    async getSummaryData() {
      this.summary = (await ProfitVerifyApi.sum(this.where)) || {};
    },

    reload() {
      this.selection = [];
      this.$refs.table.reload({ page: 1 });
      this.getSummaryData();
    },

    reset() {
      this.where = {};
      this.selection = [];
      this.$refs.table.reload({ page: 1, where: this.where });
      this.getSummaryData();
    },

    handleApply(row) {
      this.current = row;
      this.showApplyInvoice = true;
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    handleEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    handleCheck(row) {
      this.current = row;
      this.showCheckProfitVerify = true;
    },

    handleInsert(row) {
      this.current = row;
      this.showManualInsert = true;
    },

    datasource({ page, limit, where, orders }) {
      return ProfitVerifyApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
