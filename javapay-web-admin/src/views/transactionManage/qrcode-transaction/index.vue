<template>
  <div class="ele-body">
    <a-spin :spinning="spinning" tip="下载中, 请稍候...">
      <!-- 搜索表单 -->
      <div class="block-interval">
        <a-card :bordered="false">
          <a-form layout="inline" :model="where">
            <a-row :gutter="[0, 16]">
              <a-form-item label="商户编号">
                <a-input v-model:value.trim="where.merchNo" placeholder="请输入商户编号" allow-clear />
              </a-form-item>

              <a-form-item label="运营中心编号" v-purview="['0', '1']">
                <a-input v-model:value.trim="where.branchNo" placeholder="请输入运营中心编号" allow-clear />
              </a-form-item>

              <a-form-item label="大区编号" v-purview="'0'">
                <a-input v-model:value.trim="where.regionNo" placeholder="请输入大区编号" allow-clear />
              </a-form-item>

              <a-form-item label="代理商编号" v-if="!hasPurview('3')">
                <a-input v-model:value.trim="where.agentNo" placeholder="请输入代理商编号" allow-clear />
              </a-form-item>

              <a-form-item label="订单编号">
                <a-input v-model:value.trim="where.orderNo" placeholder="请输入订单编号" allow-clear />
              </a-form-item>

              <a-form-item label="通道订单号">
                <a-input v-model:value.trim="where.chnOrderNo" placeholder="请输入通道订单号" allow-clear />
              </a-form-item>

              <a-form-item label="通道对账单号">
                <a-input v-model:value.trim="where.chnBillNo" placeholder="请输入通道对账单号" allow-clear />
              </a-form-item>

              <a-form-item label="通道商户编号">
                <a-input v-model:value.trim="where.chnMerchNo" placeholder="请输入通道商户编号" allow-clear />
              </a-form-item>

              <a-form-item label="通道内部追踪号">
                <a-input v-model:value.trim="where.chnTraceNo" placeholder="请输入通道内部追踪号" allow-clear />
              </a-form-item>

              <a-form-item label="通道流水号">
                <a-input v-model:value.trim="where.chnFlowNo" placeholder="请输入通道流水号" allow-clear />
              </a-form-item>

              <a-form-item label="交易流水单号">
                <a-input v-model:value.trim="where.flowNo" placeholder="请输入交易流水单号" allow-clear />
              </a-form-item>

              <a-form-item label="付款方式">
                <a-select v-model:value="where.payMethod" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option :value="1">云闪付</a-select-option>
                  <a-select-option :value="2">微信支付</a-select-option>
                  <a-select-option :value="3">支付宝支付</a-select-option>
                  <a-select-option :value="4">EPOS支付</a-select-option>
                  <a-select-option :value="5">POS刷卡</a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item label="交易通道">
                <a-select v-model:value="where.transChn" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option v-for="({ channelCode, channelName }, key) in transChnList" :key="key" :value="channelCode">{{
                    channelName
                  }}</a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item label="交易类型">
                <a-select v-model:value="where.transCode" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option value="02Q100">扫码交易查询</a-select-option>
                  <a-select-option value="02Q101">扫码交易补救</a-select-option>
                  <a-select-option value="02S100">扫码支付通知</a-select-option>
                  <a-select-option value="02S101">扫码退款通知</a-select-option>
                  <a-select-option value="02W100">微信被扫</a-select-option>
                  <a-select-option value="02W200">微信主扫</a-select-option>
                  <a-select-option value="02W300">微信公众号支付</a-select-option>
                  <a-select-option value="02W310">微信JS支付退款</a-select-option>
                  <a-select-option value="02B100">支付宝被扫</a-select-option>
                  <a-select-option value="02B200">支付宝主扫</a-select-option>
                  <a-select-option value="02B300">支付宝生活号支付</a-select-option>
                  <a-select-option value="02B310">支付宝JS支付退款</a-select-option>
                  <a-select-option value="02Y100">银联被扫</a-select-option>
                  <a-select-option value="02Y200">银联主扫</a-select-option>
                  <a-select-option value="02Y300">银联JS支付</a-select-option>
                  <a-select-option value="02Y310">银联JS支付退款</a-select-option>
                  <a-select-option value="02E300">EPOS短信</a-select-option>
                  <a-select-option value="02E301">EPOS支付</a-select-option>
                  <a-select-option value="02E310">EPOS退款</a-select-option>
                  <a-select-option value="02E100">签约短信</a-select-option>
                  <a-select-option value="02E101">协议签约</a-select-option>
                  <a-select-option value="02E102">支付短信</a-select-option>
                  <a-select-option value="02E103">支付确认</a-select-option>
                  <a-select-option value="02E104">签约并支付</a-select-option>
                  <a-select-option value="02E105">协议解约</a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item label="借贷标识">
                <a-select v-model:value="where.debitType" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option :value="1">加款</a-select-option>
                  <a-select-option :value="2">减款</a-select-option>
                  <a-select-option :value="3">不记账</a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item label="结算周期">
                <a-select v-model:value="where.settlePeriod" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option :value="1">D0结算</a-select-option>
                  <a-select-option :value="2">T1结算</a-select-option>
                  <a-select-option :value="3">D1结算</a-select-option>
                  <a-select-option :value="4">定时结算</a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item label="结算方式">
                <a-select v-model:value="where.settleMethod" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option :value="1">无需结算</a-select-option>
                  <a-select-option :value="2">自动结算</a-select-option>
                  <a-select-option :value="3">自主结算</a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item label="交易状态">
                <a-select v-model:value="where.transStatus" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option :value="1">已创建</a-select-option>
                  <a-select-option :value="2">交易成功</a-select-option>
                  <a-select-option :value="3">交易失败</a-select-option>
                  <a-select-option :value="4">交易进行中</a-select-option>
                  <a-select-option :value="5">请求已受理</a-select-option>
                  <a-select-option :value="6">支付结果待查</a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item label="退款状态">
                <a-select v-model:value="where.refundStatus" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option :value="1">未退款</a-select-option>
                  <a-select-option :value="2">已退款</a-select-option>
                  <a-select-option :value="3">已部分退款</a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item label="记账状态">
                <a-select v-model:value="where.settleStatus" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option :value="0">无需记账</a-select-option>
                  <a-select-option :value="1">未记账</a-select-option>
                  <a-select-option :value="2">已记账</a-select-option>
                  <a-select-option :value="3">结算中</a-select-option>
                  <a-select-option :value="4">结算失败</a-select-option>
                  <a-select-option :value="5">结算成功</a-select-option>
                  <a-select-option :value="6">已转D1</a-select-option>
                  <a-select-option :value="7">已转T1</a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item label="开始日期">
                <a-date-picker v-model:value="where.createStartTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
              </a-form-item>

              <a-form-item label="结束日期">
                <a-date-picker v-model:value="where.createEndTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
              </a-form-item>

              <a-form-item label="交易通道日期">
                <a-date-picker v-model:value="where.chnTransDate" format="YYYYMMDD" valueFormat="YYYYMMDD" />
              </a-form-item>

              <a-form-item class="ele-text-center">
                <a-space>
                  <a-button type="primary" @click="reload">查询</a-button>
                  <a-button @click="reset">重置</a-button>
                </a-space>
              </a-form-item>
            </a-row>
          </a-form>
        </a-card>
      </div>

      <!-- 表格 -->
      <div>
        <a-card :bordered="false" class="table-height">
          <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
            <!-- table上边工具栏 -->
            <template #toolbar>
              <!-- 列表查询统计 -->
              <div style="margin-bottom: 10px">
                <a-space>
                  <span>
                    <span style="margin-right: 20px">
                      <span style="color: red">交易总额:</span>
                      <span style="color: black">{{ transFeeSummary.totalTransAmount }}</span>
                    </span>

                    <span style="margin-right: 20px">
                      <span style="color: red">商户交易手续费总额:</span>
                      <span style="color: black">{{ transFeeSummary.merchTotalFee }}</span>
                    </span>

                    <span style="margin-right: 20px">
                      <span style="color: red">商户D0服务费总额:</span>
                      <span style="color: black">{{ transFeeSummary.merchTotalD0Fee }}</span>
                    </span>

                    <span style="margin-right: 20px" v-if="!hasPurview(['1', '2', '3'])">
                      <span style="color: red">平台交易分润总额:</span>
                      <span style="color: black">{{ transFeeSummary.platformTransProfitAmount }}</span>
                    </span>

                    <span style="margin-right: 20px" v-if="!hasPurview(['1', '2', '3'])">
                      <span style="color: red">平台D0分润金额:</span>
                      <span style="color: black">{{ transFeeSummary.platformD0ProfitAmount }}</span>
                    </span>

                    <span style="margin-right: 20px" v-if="!hasPurview(['2', '3'])">
                      <span style="color: red">大区分润总金额:</span>
                      <span style="color: black">{{ transFeeSummary.regionProfitAmount }}</span>
                    </span>

                    <span style="margin-right: 20px" v-if="!hasPurview('3')">
                      <span style="color: red">运营中心分润总金额:</span>
                      <span style="color: black">{{ transFeeSummary.branchProfitAmount }}</span>
                    </span>

                    <span style="margin-right: 20px">
                      <span style="color: red">代理商分润总金额:</span>
                      <span style="color: black">{{ transFeeSummary.agentProfitAmount }}</span>
                    </span>
                  </span>
                </a-space>
              </div>

              <div>
                <a-space>
                  <a-button @click="handleDownloadQRCodeTransactionExcel">
                    <template #icon>
                      <download-outlined />
                    </template>
                    <span>下载交易记录.excel</span>
                  </a-button>

                  <a-button @click="handleDownloadProfitStatisticsExcel">
                    <template #icon>
                      <download-outlined />
                    </template>
                    <span>下载分润统计.excel</span>
                  </a-button>
                </a-space>
              </div>
            </template>

            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'isChnCumulate'">
                <a-tag v-if="record.isChnCumulate === 0" color="pink">否</a-tag>
                <a-tag v-else-if="record.isChnCumulate === 1" color="cyan">是</a-tag>
              </template>

              <template v-if="column.key === 'transCode'">
                <a-tag v-if="record.transCode === '02Q100'" color="pink">扫码交易查询</a-tag>
                <a-tag v-else-if="record.transCode === '02Q101'" color="cyan">扫码交易补救</a-tag>
                <a-tag v-else-if="record.transCode === '02S100'" color="blue">扫码支付通知</a-tag>
                <a-tag v-else-if="record.transCode === '02S101'" color="purple">扫码退款通知</a-tag>
                <a-tag v-else-if="record.transCode === '02W100'" color="pink">微信被扫</a-tag>
                <a-tag v-else-if="record.transCode === '02W200'" color="cyan">微信主扫</a-tag>
                <a-tag v-else-if="record.transCode === '02W300'" color="blue">微信公众号支付</a-tag>
                <a-tag v-else-if="record.transCode === '02W310'" color="purple">微信JS支付退款</a-tag>
                <a-tag v-else-if="record.transCode === '02B100'" color="pink">支付宝被扫</a-tag>
                <a-tag v-else-if="record.transCode === '02B200'" color="blue">支付宝主扫</a-tag>
                <a-tag v-else-if="record.transCode === '02B300'" color="purple">支付宝生活号支付</a-tag>
                <a-tag v-else-if="record.transCode === '02B310'" color="pink">支付宝JS支付退款</a-tag>
                <a-tag v-else-if="record.transCode === '02Y100'" color="cyan">银联被扫</a-tag>
                <a-tag v-else-if="record.transCode === '02Y200'" color="blue">银联主扫</a-tag>
                <a-tag v-else-if="record.transCode === '02Y300'" color="purple">银联JS支付</a-tag>
                <a-tag v-else-if="record.transCode === '02Y310'" color="pink">银联JS支付退款</a-tag>
                <a-tag v-else-if="record.transCode === '02E300'" color="cyan">EPOS短信</a-tag>
                <a-tag v-else-if="record.transCode === '02E301'" color="blue">EPOS支付</a-tag>
                <a-tag v-else-if="record.transCode === '02E310'" color="purple">EPOS退款</a-tag>
                <a-tag v-else-if="record.transCode === '02E100'" color="pink">签约短信</a-tag>
                <a-tag v-else-if="record.transCode === '02E101'" color="cyan">协议签约</a-tag>
                <a-tag v-else-if="record.transCode === '02E102'" color="blue">支付短信</a-tag>
                <a-tag v-else-if="record.transCode === '02E103'" color="purple">支付确认</a-tag>
                <a-tag v-else-if="record.transCode === '02E104'" color="pink">签约并支付</a-tag>
                <a-tag v-else-if="record.transCode === '02E105'" color="cyan">协议解约</a-tag>
              </template>

              <template v-if="column.key === 'payCardType'">
                <a-tag v-if="record.payCardType === 1" color="pink">借记卡</a-tag>
                <a-tag v-else-if="record.payCardType === 2" color="cyan">贷记卡</a-tag>
                <a-tag v-else-if="record.payCardType === 3" color="blue">准贷记卡</a-tag>
                <a-tag v-else-if="record.payCardType === 4" color="purple">预付费卡</a-tag>
              </template>

              <template v-if="column.key === 'payMethod'">
                <a-tag v-if="record.payMethod === 1" color="pink">云闪付</a-tag>
                <a-tag v-else-if="record.payMethod === 2" color="cyan">微信支付</a-tag>
                <a-tag v-else-if="record.payMethod === 3" color="blue">支付宝支付</a-tag>
                <a-tag v-else-if="record.payMethod === 4" color="purple">EPOS支付</a-tag>
                <a-tag v-else-if="record.payMethod === 5" color="pink">POS刷卡</a-tag>
              </template>

              <template v-if="column.key === 'postStatus'">
                <a-tag v-if="record.postStatus === 0" color="pink">无需入账</a-tag>
                <a-tag v-else-if="record.postStatus === 1" color="cyan">未入账</a-tag>
                <a-tag v-else-if="record.postStatus === 2" color="blue">已入账</a-tag>
              </template>

              <template v-if="column.key === 'isCalculateFee'">
                <a-tag v-if="record.isCalculateFee === 0" color="pink">否</a-tag>
                <a-tag v-else-if="record.isCalculateFee === 1" color="cyan">是</a-tag>
              </template>

              <template v-if="column.key === 'debitType'">
                <a-tag v-if="record.debitType === 1" color="pink">加款</a-tag>
                <a-tag v-else-if="record.debitType === 2" color="pink">减款</a-tag>
                <a-tag v-else-if="record.debitType === 3" color="cyan">不记账</a-tag>
              </template>

              <template v-if="column.key === 'settlePeriod'">
                <a-tag v-if="record.settlePeriod === 1" color="pink">D0结算</a-tag>
                <a-tag v-else-if="record.settlePeriod === 2" color="cyan">T1结算</a-tag>
                <a-tag v-else-if="record.settlePeriod === 3" color="blue">D1结算</a-tag>
                <a-tag v-else-if="record.settlePeriod === 4" color="purple">定时结算</a-tag>
              </template>

              <template v-if="column.key === 'settleMethod'">
                <a-tag v-if="record.settleMethod === 1" color="pink">无需结算</a-tag>
                <a-tag v-else-if="record.settleMethod === 2" color="cyan">自动结算</a-tag>
                <a-tag v-else-if="record.settleMethod === 3" color="blue">自主结算</a-tag>
              </template>

              <template v-if="column.key === 'transStatus'">
                <a-tag v-if="record.transStatus === 1" color="pink">已创建</a-tag>
                <a-tag v-else-if="record.transStatus === 2" color="cyan">交易成功</a-tag>
                <a-tag v-else-if="record.transStatus === 3" color="blue">交易失败</a-tag>
                <a-tag v-else-if="record.transStatus === 4" color="purple">交易进行中</a-tag>
                <a-tag v-else-if="record.transStatus === 5" color="pink">请求已受理</a-tag>
                <a-tag v-else-if="record.transStatus === 6" color="cyan">支付结果待查</a-tag>
              </template>

              <template v-if="column.key === 'refundStatus'">
                <a-tag v-if="record.refundStatus === 1" color="pink">未退款</a-tag>
                <a-tag v-else-if="record.refundStatus === 2" color="cyan">已退款</a-tag>
                <a-tag v-else-if="record.refundStatus === 3" color="blue">已部分退款</a-tag>
              </template>

              <template v-if="column.key === 'settleStatus'">
                <a-tag v-if="record.settleStatus === 0" color="pink">无需记账</a-tag>
                <a-tag v-else-if="record.settleStatus === 1" color="cyan">未记账</a-tag>
                <a-tag v-else-if="record.settleStatus === 2" color="blue">已记账</a-tag>
                <a-tag v-else-if="record.settleStatus === 3" color="purple">结算中</a-tag>
                <a-tag v-else-if="record.settleStatus === 4" color="pink">结算失败</a-tag>
                <a-tag v-else-if="record.settleStatus === 5" color="cyan">结算成功</a-tag>
                <a-tag v-else-if="record.settleStatus === 6" color="blue">已转D1</a-tag>
                <a-tag v-else-if="record.settleStatus === 7" color="purple">已转T1</a-tag>
              </template>

              <template v-if="column.key === 'billStatus'">
                <a-tag v-if="record.billStatus === 1" color="pink">未对帐</a-tag>
                <a-tag v-else-if="record.billStatus === 2" color="cyan">已对帐</a-tag>
              </template>

              <template v-if="column.key === 'isCumulate'">
                <a-tag v-if="record.isCumulate === 0" color="pink">否</a-tag>
                <a-tag v-else-if="record.isCumulate === 1" color="cyan">是</a-tag>
              </template>

              <template v-if="column.key === 'isTriggerRisk'">
                <a-tag v-if="record.isTriggerRisk === 0" color="pink">否</a-tag>
                <a-tag v-else-if="record.isTriggerRisk === 1" color="cyan">是</a-tag>
              </template>

              <template v-if="column.key === 'isPushProfit'">
                <a-tag v-if="record.isPushProfit === 0" color="pink">否</a-tag>
                <a-tag v-else-if="record.isPushProfit === 1" color="cyan">是</a-tag>
              </template>

              <template v-if="column.key === 'isSign'">
                <a-tag v-if="record.isSign === 0" color="pink">未签约</a-tag>
                <a-tag v-else-if="record.isSign === 1" color="cyan">已签约</a-tag>
              </template>

              <template v-if="column.key === 'payuserType'">
                <a-tag v-if="record.payuserType === 1" color="pink">平台付款用户</a-tag>
                <a-tag v-else-if="record.payuserType === 2" color="cyan">商家会员用户</a-tag>
              </template>

              <template v-if="column.key === 'merchGrade'">
                <a-tag v-if="record.merchGrade === 'A'" color="pink">企业商户</a-tag>
                <a-tag v-else-if="record.merchGrade === 'B'" color="cyan">个体工商户</a-tag>
                <a-tag v-else-if="record.merchGrade === 'C'" color="blue">小微商户</a-tag>
              </template>

              <template v-if="column.key === 'isChnSettle'">
                <a-tag v-if="record.isChnSettle === -1">无需处理</a-tag>
                <a-tag v-else-if="record.isChnSettle === 0" color="orange">未清分</a-tag>
                <a-tag v-else-if="record.isChnSettle === 1" color="success">已清分</a-tag>
              </template>

              <template v-if="column.key === 'chnCostMethod'">
                <a-tag v-if="record.chnCostMethod === 1" color="pink">平台计算</a-tag>
                <a-tag v-else-if="record.chnCostMethod === 2" color="cyan">对账导入</a-tag>
              </template>

              <template v-if="column.key === 'transChn'">
                <template v-for="({ channelCode, channelName }, key) in transChnList" :key="key">
                  <a-tag v-if="record.transChn === channelCode" color="pink">{{ channelName }}</a-tag>
                </template>
              </template>

              <!-- table操作栏按钮 -->
              <template v-else-if="column.key === 'action'">
                <a-space>
                  <a @click="handleDetail(record)">详情</a>
                  <a-divider type="vertical" v-if="record.isCalculateFee === 1" />
                  <a @click="handleTransFeeDetail(record)" v-if="record.isCalculateFee === 1">手续费详情</a>
                </a-space>
              </template>
            </template>
          </ele-pro-table>
        </a-card>
      </div>
    </a-spin>
    <!-- 详情 -->
    <QRcodeTransactionDetail v-model:visible="showDetail" :detail="current" :transChnList="transChnList" />

    <TransFeesDetail v-model:visible="showTransFeeDetail" :detail="current" />
  </div>
</template>

<script>
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import { QRcodeTransactionApi } from '@/api/transactionManage/qrcode-transaction/QRcodeTransactionApi';
import QRcodeTransactionDetail from './qrcode-transaction-detail.vue';
import TransFeesDetail from '../trans-fees/trans-fees-detail.vue';

import { hasPurview } from '@/utils/permission';
import { message } from 'ant-design-vue';

export default {
  name: 'QRCodeTransaction',
  components: {
    QRcodeTransactionDetail,
    TransFeesDetail
  },
  data() {
    return {
      //表格搜索条件
      where: {
        createStartTime: this.getToday(),
        createEndTime: this.getToday()
      },
      //交易通道列表
      transChnList: [],
      transFeeSummary: {},
      current: null,
      showDetail: false,
      spinning: false,
      showTransFeeDetail: false,
      hasPurview,

      //表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left',
          hideCol: !hasPurview('0')
        },
        {
          title: '商户编号',
          dataIndex: 'merchNo',
          fixed: 'left',
          align: 'center'
        },
        {
          title: '商户名称',
          dataIndex: 'merchName',
          fixed: 'left',
          align: 'center',
          width: 180
        },
        {
          title: '大区编号',
          dataIndex: 'regionNo',
          align: 'center',
          hideCol: !hasPurview('0')
        },
        {
          title: '运营中心编号',
          dataIndex: 'branchNo',
          align: 'center',
          hideCol: !hasPurview(['0', '1'])
        },
        {
          title: '代理商编号',
          dataIndex: 'agentNo',
          align: 'center',
          hideCol: hasPurview('3')
        },
        {
          title: '通道商户编号',
          dataIndex: 'chnMerchNo',
          align: 'center'
        },
        {
          title: '通道商户名称',
          dataIndex: 'chnMerchName',
          align: 'center'
        },
        {
          title: '设备编号',
          dataIndex: 'termSn',
          align: 'center'
        },
        {
          title: '交易创建时间',
          dataIndex: 'createTime',
          align: 'center'
        },
        {
          title: '交易完成时间',
          dataIndex: 'transTime',
          align: 'center'
        },
        {
          title: '订单编号',
          dataIndex: 'orderNo',
          align: 'center'
        },
        {
          title: '交易流水单号',
          dataIndex: 'flowNo',
          align: 'center'
        },
        {
          title: '商户等级',
          dataIndex: 'merchGrade',
          key: 'merchGrade',
          align: 'center'
        },
        {
          title: '风险等级',
          dataIndex: 'riskGrade',
          align: 'center'
        },
        {
          title: '付款卡号',
          dataIndex: 'payCardNoMask',
          align: 'center'
        },
        {
          title: '付款银行卡银行名称',
          dataIndex: 'payCardBankName',
          align: 'center'
        },
        {
          title: '通道成本计算方式',
          dataIndex: 'chnCostMethod',
          key: 'chnCostMethod',
          align: 'center'
        },
        {
          title: '交易通道',
          dataIndex: 'transChn',
          key: 'transChn',
          align: 'center'
        },
        {
          title: '通道交易商户编号',
          dataIndex: 'chnTransMerchNo',
          align: 'center'
        },
        {
          title: '通道交易终端编号',
          dataIndex: 'chnTransTermNo',
          align: 'center'
        },
        {
          title: '通道订单号',
          dataIndex: 'chnOrderNo',
          align: 'center'
        },
        {
          title: '通道内部追踪号',
          dataIndex: 'chnTraceNo',
          align: 'center'
        },
        {
          title: '通道对账单号',
          dataIndex: 'chnBillNo',
          align: 'center'
        },
        {
          title: '交易类型',
          dataIndex: 'transCode',
          key: 'transCode',
          align: 'center'
        },
        {
          title: '付款方式',
          dataIndex: 'payMethod',
          key: 'payMethod',
          align: 'center'
        },
        {
          title: '交易金额',
          dataIndex: 'transAmount',
          align: 'center'
        },
        {
          title: '交易手续费',
          dataIndex: ['transFee', 'merchFee'],
          align: 'center'
        },
        {
          title: '交易结算金额',
          dataIndex: ['transFee', 'merchSettleAmount'],
          align: 'center'
        },
        {
          title: '交易状态',
          dataIndex: 'transStatus',
          key: 'transStatus',
          align: 'center'
        },
        {
          title: '交易返回编码',
          dataIndex: 'resCode',
          align: 'center'
        },
        {
          title: '交易返回描述',
          dataIndex: 'resDesc',
          align: 'center'
        },
        {
          title: '通道返回编码',
          dataIndex: 'chnResCode',
          align: 'center'
        },
        {
          title: '通道返回描述',
          dataIndex: 'chnResDesc',
          align: 'center'
        },
        {
          title: '付款卡类型',
          dataIndex: 'payCardType',
          key: 'payCardType',
          align: 'center'
        },
        {
          title: '借贷标识',
          dataIndex: 'debitType',
          key: 'debitType',
          align: 'center'
        },
        {
          title: '结算周期',
          dataIndex: 'settlePeriod',
          key: 'settlePeriod',
          align: 'center'
        },
        {
          title: '结算方式',
          dataIndex: 'settleMethod',
          key: 'settleMethod',
          align: 'center'
        },
        {
          title: '通道是否清分(海科)',
          dataIndex: 'isChnSettle',
          key: 'isChnSettle',
          align: 'center'
        },
        {
          title: '记账状态',
          dataIndex: 'settleStatus',
          key: 'settleStatus',
          align: 'center'
        },
        {
          title: '记账时间',
          dataIndex: 'postTime',
          align: 'center'
        },
        {
          title: '通道交易日期',
          dataIndex: 'chnTransDate',
          align: 'center'
        },
        {
          title: '结算日期',
          dataIndex: 'settleDate',
          align: 'center'
        },
        {
          title: '触发风控标识',
          dataIndex: 'isTriggerRisk',
          key: 'isTriggerRisk',
          align: 'center'
        },
        {
          title: '风控事件ID',
          dataIndex: 'riskEventId',
          align: 'center'
        },
        {
          title: '是否计算手续费',
          dataIndex: 'isCalculateFee',
          key: 'isCalculateFee',
          align: 'center'
        },
        {
          title: '是否累计',
          dataIndex: 'isCumulate',
          key: 'isCumulate',
          align: 'center'
        },
        {
          title: '通道是否累计',
          dataIndex: 'isChnCumulate',
          key: 'isChnCumulate',
          align: 'center'
        },
        {
          title: '对账状态',
          dataIndex: 'billStatus',
          key: 'billStatus',
          align: 'center'
        },
        {
          title: '退款状态',
          dataIndex: 'refundStatus',
          key: 'refundStatus',
          align: 'center'
        },
        {
          title: '付款卡ID',
          dataIndex: 'payCardId',
          align: 'center'
        },
        {
          title: '付款用户ID',
          dataIndex: 'payuserId',
          align: 'center'
        },
        {
          title: '付款卡是否签约',
          dataIndex: 'isSign',
          key: 'isSign',
          align: 'center'
        },
        {
          title: '付款用户类型',
          dataIndex: 'payuserType',
          key: 'payuserType',
          align: 'center'
        },
        {
          title: '数据推送状态',
          dataIndex: 'isPushProfit',
          key: 'isPushProfit',
          align: 'center'
        },
        {
          title: '实时交易终端IP',
          dataIndex: 'termIp',
          align: 'center'
        },
        {
          title: '实时交易终端经度',
          dataIndex: 'termLon',
          align: 'center'
        },
        {
          title: '实时交易终端纬度',
          dataIndex: 'termLat',
          align: 'center'
        },
        {
          title: '坐标所在省名称',
          dataIndex: 'province',
          align: 'center'
        },
        {
          title: '坐标所在城市名称',
          dataIndex: 'city',
          align: 'center'
        },
        {
          title: '坐标所在区/县名称',
          dataIndex: 'district',
          align: 'center'
        },
        {
          title: '坐标所在乡镇/街道名称',
          dataIndex: 'township',
          align: 'center'
        },
        {
          title: '乡镇街道编码',
          dataIndex: 'townCode',
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right',
          width: 150
        }
      ].filter(i => !i.hideCol)
    };
  },
  async mounted() {
    this.transChnList = await ChannelManageApi.list({ validStatus: 1 });
    //获取列表查询统计
    this.getTransFeeSummaryData(this.where);
  },
  methods: {
    //获取今日日期 YYYY-MM-DD
    getToday() {
      let date = new Date();
      let year = date.getFullYear();
      let month = date.getMonth() + 1; // 注意，JS 中月份是从 0 开始计数的
      let day = date.getDate();

      // 如果月份或日期是个位数，我们需要在其前面补零
      month = month < 10 ? '0' + month : month;
      day = day < 10 ? '0' + day : day;

      return `${year}-${month}-${day}`;
    },

    reload() {
      this.selection = [];
      this.$refs.table.reload({ page: 1 });
      //获取列表查询统计
      this.getTransFeeSummaryData(this.where);
    },

    reset() {
      this.where = {
        createStartTime: this.getToday(),
        createEndTime: this.getToday()
      };
      this.selection = [];
      this.$refs.table.reload({ page: 1, where: this.where });
      //获取列表查询统计
      this.getTransFeeSummaryData(this.where);
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    async handleTransFeeDetail(row) {
      const data = await QRcodeTransactionApi.transFeeDetail({ id: row.id });
      this.current = data;
      this.showTransFeeDetail = true;
    },

    async handleDownloadQRCodeTransactionExcel() {
      console.log('下载二维码交易');
      this.spinning = true;
      const data = await QRcodeTransactionApi.downloadQRCodeTransactionExcel(this.where).catch(() => {
        this.spinning = false;
      });
      this.spinning = false;
      const fileReader = new FileReader();
      fileReader.onload = function () {
        try {
          // 说明是普通对象数据，后台转换失败
          const jsonData = JSON.parse(fileReader.result);
          message.error(jsonData.message);
        } catch (err) {
          // 解析对象失败，说明是正常的文件流
          let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.style.display = 'none';
          a.href = url;
          a.download = `二维码交易记录-下载.xlsx`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }
      };
      fileReader.readAsText(data);
    },

    async handleDownloadProfitStatisticsExcel() {
      console.log('下载分润');
      this.spinning = true;
      const data = await QRcodeTransactionApi.downloadProfitStatisticsExcel(this.where).catch(() => {
        this.spinning = false;
      });
      this.spinning = false;
      const fileReader = new FileReader();
      fileReader.onload = function () {
        try {
          // 说明是普通对象数据，后台转换失败
          const jsonData = JSON.parse(fileReader.result);
          message.error(jsonData.message);
        } catch (err) {
          // 解析对象失败，说明是正常的文件流
          let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.style.display = 'none';
          a.href = url;
          a.download = `分润统计记录-下载.xlsx`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }
      };
      fileReader.readAsText(data);
    },

    //列表查询统计
    async getTransFeeSummaryData(params) {
      this.transFeeSummary = await QRcodeTransactionApi.transFeeSummary(params);
    },

    datasource({ page, limit, where, orders }) {
      return QRcodeTransactionApi.getQRcodeTransInfoPages({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>

<style></style>
