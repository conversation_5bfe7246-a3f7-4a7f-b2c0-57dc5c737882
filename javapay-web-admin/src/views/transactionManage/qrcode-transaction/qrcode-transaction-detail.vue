<template>
  <a-modal
    :width="800"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
    :footer="null"
  >
    <a-descriptions bordered :column="2">
      <a-descriptions-item label="大区编号" v-if="hasPurview('0')">{{ form.regionNo }}</a-descriptions-item>
      <a-descriptions-item label="运营中心编号" v-if="hasPurview(['0', '1'])">{{ form.branchNo }}</a-descriptions-item>
      <a-descriptions-item label="代理商编号" v-if="!hasPurview('3')">{{ form.agentNo }}</a-descriptions-item>
      <a-descriptions-item label="商户编号">{{ form.merchNo }}</a-descriptions-item>
      <a-descriptions-item label="商户名称">{{ form.merchName }}</a-descriptions-item>
      <a-descriptions-item label="商户姓名">{{ form.legalName }}</a-descriptions-item>
      <a-descriptions-item label="通道商户编号">{{ form.chnMerchNo }}</a-descriptions-item>

      <a-descriptions-item label="通道商户名称">{{ form.chnMerchName }}</a-descriptions-item>
      <a-descriptions-item label="设备编号">{{ form.termSn }}</a-descriptions-item>
      <a-descriptions-item label="交易创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="交易完成时间">{{ form.transTime }}</a-descriptions-item>
      <a-descriptions-item label="订单编号">{{ form.orderNo }}</a-descriptions-item>
      <a-descriptions-item label="交易流水单号">{{ form.flowNo }}</a-descriptions-item>
      <a-descriptions-item label="交易通道">
        <template v-for="({ channelCode, channelName }, key) in transChnList" :key="key">
          <a-tag v-if="form.transChn === channelCode" color="pink">{{ channelName }}</a-tag>
        </template>
      </a-descriptions-item>

      <a-descriptions-item label="通道交易商户编号">{{ form.chnTransMerchNo }}</a-descriptions-item>
      <a-descriptions-item label="通道交易终端编号">{{ form.chnTransTermNo }}</a-descriptions-item>

      <a-descriptions-item label="交易类型">
        <a-tag v-if="form.transCode === '02Q100'" color="pink">扫码交易查询</a-tag>
        <a-tag v-else-if="form.transCode === '02Q101'" color="cyan">扫码交易补救</a-tag>
        <a-tag v-else-if="form.transCode === '02S100'" color="blue">扫码支付通知</a-tag>
        <a-tag v-else-if="form.transCode === '02S101'" color="purple">扫码退款通知</a-tag>
        <a-tag v-else-if="form.transCode === '02W100'" color="pink">微信被扫</a-tag>
        <a-tag v-else-if="form.transCode === '02W200'" color="cyan">微信被扫</a-tag>
        <a-tag v-else-if="form.transCode === '02W300'" color="blue">微信公众号支付</a-tag>
        <a-tag v-else-if="form.transCode === '02W310'" color="cyan">微信JS支付退款</a-tag>
        <a-tag v-else-if="form.transCode === '02B100'" color="purple">支付宝被扫</a-tag>
        <a-tag v-else-if="form.transCode === '02B200'" color="pink">支付宝主扫</a-tag>
        <a-tag v-else-if="form.transCode === '02B300'" color="cyan">支付宝生活号支付</a-tag>
        <a-tag v-else-if="form.transCode === '02B310'" color="blue">支付宝JS支付退款</a-tag>
        <a-tag v-else-if="form.transCode === '02Y100'" color="purple">银联被扫</a-tag>
        <a-tag v-else-if="form.transCode === '02Y200'" color="pink">银联主扫</a-tag>
        <a-tag v-else-if="form.transCode === '02Y300'" color="cyan">银联JS支付</a-tag>
        <a-tag v-else-if="form.transCode === '02Y310'" color="blue">银联JS支付退款</a-tag>
        <a-tag v-else-if="form.transCode === '02E300'" color="purple">EPOS短信</a-tag>
        <a-tag v-else-if="form.transCode === '02E301'" color="pink">EPOS支付</a-tag>
        <a-tag v-else-if="form.transCode === '02E310'" color="cyan">EPOS退款</a-tag>
        <a-tag v-else-if="form.transCode === '02E100'" color="blue">签约短信</a-tag>
        <a-tag v-else-if="form.transCode === '02E101'" color="purple">协议签约</a-tag>
        <a-tag v-else-if="form.transCode === '02E102'" color="pink">支付短信</a-tag>
        <a-tag v-else-if="form.transCode === '02E103'" color="cyan">支付确认</a-tag>
        <a-tag v-else-if="form.transCode === '02E104'" color="blue">签约并支付</a-tag>
        <a-tag v-else-if="form.transCode === '02E105'" color="purple">协议解约</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="付款方式">
        <a-tag v-if="form.payMethod === 1" color="pink">云闪付</a-tag>
        <a-tag v-else-if="form.payMethod === 2" color="cyan">微信支付</a-tag>
        <a-tag v-else-if="form.payMethod === 3" color="blue">支付宝支付</a-tag>
        <a-tag v-else-if="form.payMethod === 4" color="purple">EPOS支付</a-tag>
        <a-tag v-else-if="form.payMethod === 5" color="pink">POS刷卡</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="交易金额">{{ form.transAmount }}</a-descriptions-item>
      <a-descriptions-item label="交易手续费" v-if="form.transFee && form.transFee.merchFee">{{
        form.transFee.merchFee
      }}</a-descriptions-item>
      <a-descriptions-item label="交易结算金额" v-if="form.transFee && form.transFee.merchSettleAmount">{{
        form.transFee.merchSettleAmount
      }}</a-descriptions-item>

      <a-descriptions-item label="交易状态">
        <a-tag v-if="form.transStatus === 1" color="pink">已创建</a-tag>
        <a-tag v-else-if="form.transStatus === 2" color="cyan">交易成功</a-tag>
        <a-tag v-else-if="form.transStatus === 3" color="blue">交易失败</a-tag>
        <a-tag v-else-if="form.transStatus === 4" color="purple">交易进行中</a-tag>
        <a-tag v-else-if="form.transStatus === 5" color="pink">请求已受理</a-tag>
        <a-tag v-else-if="form.transStatus === 6" color="cyan">支付结果待查</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="交易返回编码">{{ form.resCode }}</a-descriptions-item>
      <a-descriptions-item label="交易返回描述">{{ form.resDesc }}</a-descriptions-item>
      <a-descriptions-item label="通道返回编码">{{ form.chnResCode }}</a-descriptions-item>
      <a-descriptions-item label="通道返回描述">{{ form.chnResDesc }}</a-descriptions-item>

      <a-descriptions-item label="付款卡类型">
        <a-tag v-if="form.payCardType === 1" color="pink">借记卡</a-tag>
        <a-tag v-else-if="form.payCardType === 2" color="cyan">贷记卡</a-tag>
        <a-tag v-else-if="form.payCardType === 3" color="blue">准贷记卡</a-tag>
        <a-tag v-else-if="form.payCardType === 4" color="purple">预付费卡</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="借贷标识">
        <a-tag v-if="form.debitType === 1" color="pink">加款</a-tag>
        <a-tag v-else-if="form.debitType === 2" color="cyan">减款</a-tag>
        <a-tag v-else-if="form.debitType === 3" color="purple">不记账</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="结算周期">
        <a-tag v-if="form.settlePeriod === 1" color="pink">D0结算</a-tag>
        <a-tag v-else-if="form.settlePeriod === 2" color="cyan">T1结算</a-tag>
        <a-tag v-else-if="form.settlePeriod === 3" color="blue">D1结算</a-tag>
        <a-tag v-else-if="form.settlePeriod === 4" color="purple">定时结算</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="结算方式">
        <a-tag v-if="form.settleMethod === 1" color="pink">无需结算</a-tag>
        <a-tag v-else-if="form.settleMethod === 2" color="cyan">自动结算</a-tag>
        <a-tag v-else-if="form.settleMethod === 3" color="blue">自主结算</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="通道是否清分(海科)">
        <a-tag v-if="form.isChnSettle === -1">无需处理</a-tag>
        <a-tag v-else-if="form.isChnSettle === 0" color="orange">未清分</a-tag>
        <a-tag v-else-if="form.isChnSettle === 1" color="success">已清分</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="记账状态">
        <a-tag v-if="form.settleStatus === 0" color="pink">无需记账</a-tag>
        <a-tag v-else-if="form.settleStatus === 1" color="cyan">未记账</a-tag>
        <a-tag v-else-if="form.settleStatus === 2" color="blue">已记账</a-tag>
        <a-tag v-else-if="form.settleStatus === 3" color="purple">结算中</a-tag>
        <a-tag v-else-if="form.settleStatus === 4" color="blue">结算失败</a-tag>
        <a-tag v-else-if="form.settleStatus === 5" color="purple">结算成功</a-tag>
        <a-tag v-else-if="form.settleStatus === 6" color="purple">已转D1</a-tag>
        <a-tag v-else-if="form.settleStatus === 7" color="purple">已转T1</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="记账时间">{{ form.postTime }}</a-descriptions-item>
      <a-descriptions-item label="通道交易日期">{{ form.chnTransDate }}</a-descriptions-item>
      <a-descriptions-item label="结算日期">{{ form.settleDate }}</a-descriptions-item>

      <a-descriptions-item label="触发风控标识">
        <a-tag v-if="form.isTriggerRisk === 0" color="pink">否</a-tag>
        <a-tag v-else-if="form.isTriggerRisk === 1" color="cyan">是</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="风控事件ID">{{ form.riskEventId }}</a-descriptions-item>

      <a-descriptions-item label="是否计算手续费">
        <a-tag v-if="form.isCalculateFee === 0" color="pink">否</a-tag>
        <a-tag v-else-if="form.isCalculateFee === 1" color="cyan">是</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="是否累计">
        <a-tag v-if="form.isCumulate === 0" color="pink">否</a-tag>
        <a-tag v-else-if="form.isCumulate === 1" color="cyan">是</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="通道是否累计">
        <a-tag v-if="form.isChnCumulate === 0" color="pink">否</a-tag>
        <a-tag v-else-if="form.isChnCumulate === 1" color="cyan">是</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="对账状态">
        <a-tag v-if="form.billStatus === 1" color="pink">未对帐</a-tag>
        <a-tag v-else-if="form.billStatus === 2" color="cyan">已对帐</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="退款状态">
        <a-tag v-if="form.refundStatus === 1" color="pink">未退款</a-tag>
        <a-tag v-else-if="form.refundStatus === 2" color="cyan">已退款</a-tag>
        <a-tag v-else-if="form.refundStatus === 3" color="blue">已部分退款</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="付款卡ID">{{ form.payCardId }}</a-descriptions-item>
      <a-descriptions-item label="付款用户ID">{{ form.payuserId }}</a-descriptions-item>
      <a-descriptions-item label="付款人姓名">{{ form.payName }}</a-descriptions-item>
      <a-descriptions-item label="付款卡是否签约">
        <a-tag v-if="form.isSign === 0" color="pink">未签约</a-tag>
        <a-tag v-else-if="form.isSign === 1" color="cyan">已签约</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="付款用户类型">
        <a-tag v-if="form.payuserType === 1" color="pink">平台付款用户</a-tag>
        <a-tag v-else-if="form.payuserType === 2" color="cyan">商家会员用户</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="数据推送状态">
        <a-tag v-if="form.isPushProfit === 0" color="pink">否</a-tag>
        <a-tag v-else-if="form.isPushProfit === 1" color="cyan">是</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="实时交易终端IP">{{ form.termIp }}</a-descriptions-item>
      <a-descriptions-item label="实时交易终端经度">{{ form.termLon }}</a-descriptions-item>
      <a-descriptions-item label="实时交易终端纬度">{{ form.termLat }}</a-descriptions-item>
      <a-descriptions-item label="坐标所在省名称">{{ form.province }}</a-descriptions-item>
      <a-descriptions-item label="坐标所在城市名称">{{ form.city }}</a-descriptions-item>
      <a-descriptions-item label="坐标所在区/县名称">{{ form.district }}</a-descriptions-item>
      <a-descriptions-item label="坐标所在乡镇/街道名称">{{ form.township }}</a-descriptions-item>
      <a-descriptions-item label="乡镇街道编码">{{ form.townCode }}</a-descriptions-item>

      <a-descriptions-item label="商户账户UUID">{{ form.accountUuid }}</a-descriptions-item>
      <a-descriptions-item label="商户等级">
        <a-tag v-if="form.merchGrade === 'A'" color="pink">企业商户</a-tag>
        <a-tag v-else-if="form.merchGrade === 'B'" color="cyan">个体工商户</a-tag>
        <a-tag v-else-if="form.merchGrade === 'C'" color="blue">小微商户</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="订单ID">{{ form.orderId }}</a-descriptions-item>
      <a-descriptions-item label="终端SN">{{ form.termSn }}</a-descriptions-item>
      <a-descriptions-item label="交易MCC">{{ form.transMcc }}</a-descriptions-item>
      <a-descriptions-item label="付款人姓名">{{ form.payName }}</a-descriptions-item>
      <a-descriptions-item label="风险等级">{{ form.riskGrade }}</a-descriptions-item>
      <a-descriptions-item label="付款卡号">{{ form.payCardNoMask }}</a-descriptions-item>
      <a-descriptions-item label="付款银行卡银行名称">{{ form.payCardBankName }}</a-descriptions-item>
      <a-descriptions-item label="付款卡签约协议号">{{ form.signAgreementNo }}</a-descriptions-item>
      <a-descriptions-item label="付款用户唯一标识">{{ form.payuserOpenid }}</a-descriptions-item>

      <a-descriptions-item label="通道商户钱包类型">{{ form.chnMerchWalletType }}</a-descriptions-item>
      <a-descriptions-item label="通道商户钱包UUID">{{ form.chnMerchWalletUuid }}</a-descriptions-item>
      <a-descriptions-item label="通道订单号">{{ form.chnOrderNo }}</a-descriptions-item>
      <a-descriptions-item label="通道流水号">{{ form.chnFlowNo }}</a-descriptions-item>
      <a-descriptions-item label="通道内部追踪号">{{ form.chnTraceNo }}</a-descriptions-item>
      <a-descriptions-item label="通道对账单号">{{ form.chnBillNo }}</a-descriptions-item>
      <a-descriptions-item label="通道支持自主发起结算">
        <a-tag v-if="form.chnSupportSendSettle === 1" color="pink">是</a-tag>
        <a-tag v-else-if="form.chnSupportSendSettle === 0" color="cyan">否</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="支付机构商户编号">{{ form.payOrgMerchNo }}</a-descriptions-item>
      <a-descriptions-item label="支付机构终端号">{{ form.payOrgTermNo }}</a-descriptions-item>
      <a-descriptions-item label="支付机构订单号">{{ form.payOrgOrderNo }}</a-descriptions-item>

      <a-descriptions-item label="是否特惠类费率">
        <a-tag v-if="form.isTriggerRisk === 1" color="pink">是</a-tag>
        <a-tag v-else-if="form.isTriggerRisk === 0" color="cyan">否</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="通道成本计算方式">
        <a-tag v-if="form.chnCostMethod === 1" color="pink">平台计算</a-tag>
        <a-tag v-else-if="form.chnCostMethod === 2" color="cyan">对账导入</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="是否交易分账">
        <a-tag v-if="form.settleSplit === 1" color="pink">是</a-tag>
        <a-tag v-else-if="form.settleSplit === 0" color="cyan">否</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="记账标识">
        <a-tag v-if="form.debitType === 1" color="pink">加款(+)</a-tag>
        <a-tag v-else-if="form.debitType === 2" color="cyan">减款(-)</a-tag>
        <a-tag v-else-if="form.debitType === 3" color="blue">不记账 </a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="结算时间">{{ form.settleTime }}</a-descriptions-item>
      <a-descriptions-item label="结算描述">{{ form.settleDesc }}</a-descriptions-item>
      <a-descriptions-item label="对账时间">{{ form.billTime }}</a-descriptions-item>
      <a-descriptions-item label="原订单编号">{{ form.origOrderNo }}</a-descriptions-item>
      <a-descriptions-item label="原交易ID">{{ form.origTransId }}</a-descriptions-item>
      <a-descriptions-item label="原交易流水单号">{{ form.origFlowNo }}</a-descriptions-item>
    </a-descriptions>
  </a-modal>
</template>

<script>
import { hasPurview } from '@/utils/permission';
export default {
  name: 'QRcodeTransactionDetail',
  props: {
    visible: Boolean,
    detail: Object,
    transChnList: Array
  },
  emits: ['update:visible'],
  data() {
    return {
      form: {},
      hasPurview
    };
  },
  watch: {
    //父组件值改变则重新赋值
    detail() {
      this.form = Object.assign({}, this.detail);
    }
  },
  methods: {
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>

<style></style>
