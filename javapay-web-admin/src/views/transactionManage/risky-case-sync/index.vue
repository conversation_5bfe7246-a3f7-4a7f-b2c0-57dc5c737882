<template>
  <a-modal
    :width="760"
    :visible="visible"
    :confirm-loading="loading"
    title="风险案例同步"
    :mask-closable="false"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-radio-group v-model:value="caseModule" :options="caseModuleMap" option-type="button" button-style="solid" />

    <div style="margin: 20px 0">
      <a-divider dashed />
    </div>

    <a-card :bordered="false" hoverable>
      <component ref="case_module" :is="caseModule" />
    </a-card>
  </a-modal>
</template>

<script>
import { defineComponent } from 'vue';
import UnionTransCaseSync from './modules/UnionTransCaseSync.vue';
import AtCodeTransCaseSync from './modules/AtCodeTransCaseSync.vue';
import RetreatOrderCaseSync from './modules/RetreatOrderCaseSync.vue';
import DispatchOrderCaseSync from './modules/DispatchOrderCaseSync.vue';

const caseModuleMap = [
  {
    label: '银联交易风险案例同步',
    value: UnionTransCaseSync
  },
  {
    label: '银联调单案例同步',
    value: DispatchOrderCaseSync
  },
  {
    label: '银联退单案例同步',
    value: RetreatOrderCaseSync
  },
  {
    label: 'A/T交易风险案例同步',
    value: AtCodeTransCaseSync
  }
];

export default defineComponent({
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      caseModule: UnionTransCaseSync,
      caseModuleMap,
      // 提交状态
      loading: false
    };
  },
  mounted() {},
  methods: {
    async save() {
      // 修改加载框为正在加载
      this.loading = true;

      try {
        await this.$refs.case_module.onSubmit();
        this.loading = false;
        this.updateVisible(false);
      } catch (error) {
        this.loading = false;
      }
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
});
</script>
