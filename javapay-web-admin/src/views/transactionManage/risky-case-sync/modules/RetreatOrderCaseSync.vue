<template>
  <a-form ref="form" :model="form" :rules="rules" layout="vertical">
    <a-row :gutter="24">
      <a-col :md="12" :sm="24" :xs="24">
        <a-form-item label="结算商户名称" name="mercNameLike">
          <a-input v-model:value="form.mercNameLike" placeholder="请输入结算商户名称" allow-clear />
        </a-form-item>
        <a-form-item label="银行流水号" name="sysRefNum">
          <a-input v-model:value="form.sysRefNum" placeholder="请输入银行流水号" allow-clear />
        </a-form-item>
        <a-form-item label="页码" name="currentPage">
          <a-input-number v-model:value="form.currentPage" :min="1" style="width: 100%" placeholder="请输入页码" allow-clear />
        </a-form-item>
        <a-form-item label="处理状态" name="auditStatus">
          <a-select v-model:value="form.auditStatus" style="width: 100%" placeholder="请选择">
            <a-select-option :value="0">未处理</a-select-option>
            <a-select-option :value="1">处理中</a-select-option>
            <a-select-option :value="2">处理完成</a-select-option>
            <a-select-option :value="3">处理拒绝</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="创建时间(开始)" name="createTimeBegin">
          <a-date-picker v-model:value="form.createTimeBegin" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" style="width: 100%" />
        </a-form-item>
        <a-form-item label="交易时间(开始)" name="transTimeStart">
          <a-date-picker v-model:value="form.transTimeStart" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" style="width: 100%" />
        </a-form-item>
      </a-col>
      <a-col :md="12" :sm="24" :xs="24">
        <a-form-item label="结算商户编号" name="mercNum">
          <a-input v-model:value="form.mercNum" placeholder="请输入结算商户编号" allow-clear />
        </a-form-item>
        <a-form-item label="交易卡号" name="cardNo">
          <a-input v-model:value="form.cardNo" placeholder="请输入交易卡号" allow-clear />
        </a-form-item>
        <a-form-item label="注销状态" name="isDisabled">
          <a-select v-model:value="form.isDisabled" style="width: 100%" placeholder="请选择">
            <a-select-option value="0">正常</a-select-option>
            <a-select-option value="1">已注销</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="请款状态" name="requestFund">
          <a-select v-model:value="form.requestFund" style="width: 100%" placeholder="请选择">
            <a-select-option value="request_1">待调款</a-select-option>
            <a-select-option value="request_2">请款中</a-select-option>
            <a-select-option value="request_3">请款成功</a-select-option>
            <a-select-option value="request_4">请款失败</a-select-option>
            <a-select-option value="request_5">例外协商中</a-select-option>
            <a-select-option value="request_6">例外协商成功</a-select-option>
            <a-select-option value="request_7">例外协商失败</a-select-option>
            <a-select-option value="request_8">待扣分润</a-select-option>
            <a-select-option value="request_9">已扣分润</a-select-option>
            <a-select-option value="request_10">已扣商户分款</a-select-option>
            <a-select-option value="request_11">其他处理中</a-select-option>
            <a-select-option value="request_12">其他处理成功</a-select-option>
            <a-select-option value="request_13">其他处理失败</a-select-option>
            <a-select-option value="request_14">待处理</a-select-option>
            <a-select-option value="request_15">拦截成功</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="创建时间(结束)" name="createTimeEnd">
          <a-date-picker v-model:value="form.createTimeEnd" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" style="width: 100%" />
        </a-form-item>
        <a-form-item label="交易时间(结束)" name="transTimeEnd">
          <a-date-picker v-model:value="form.transTimeEnd" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" style="width: 100%" />
        </a-form-item>
      </a-col>
    </a-row>
  </a-form>
</template>

<script>
import { message } from 'ant-design-vue';
import { RiskyCaseSyncApi } from '@/api/transactionManage/RiskyCaseSyncApi';

export default {
  emits: ['done'],
  data() {
    return {
      // 表单数据
      form: {
        currentPage: 1
      },
      // 表单验证规则
      rules: {
        currentPage: [{ required: true, message: '请输入页码' }]
      }
    };
  },
  methods: {
    async onSubmit() {
      // 校验表单
      await this.$refs.form.validate();

      const result = await RiskyCaseSyncApi.riskRetreatOrderQuery(this.form);
      message.success(result.message);
    }
  }
};
</script>
