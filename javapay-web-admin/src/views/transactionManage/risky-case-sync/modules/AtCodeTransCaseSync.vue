<template>
  <a-form ref="form" :model="form" :rules="rules" layout="vertical">
    <a-row :gutter="24">
      <a-col :md="12" :sm="24" :xs="24">
        <a-form-item label="流程编号" name="flowNo" help="流程编号和查询时间必须填写一个">
          <a-input v-model:value="form.flowNo" placeholder="多个编号以英文逗号隔开" allow-clear />
        </a-form-item>
        <a-form-item label="通道商户编号" name="mercNum">
          <a-input v-model:value="form.mercNum" placeholder="请输入通道商户编号" allow-clear />
        </a-form-item>
        <a-form-item label="分页起始数据" name="beginLine">
          <a-input-number v-model:value="form.beginLine" :min="0" style="width: 100%" placeholder="请输入分页起始数据" allow-clear />
        </a-form-item>
        <a-form-item label="创建时间(开始)" name="createTimeBegin">
          <a-date-picker v-model:value="form.createTimeBegin" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" style="width: 100%" />
        </a-form-item>
      </a-col>
      <a-col :md="12" :sm="24" :xs="24">
        <a-form-item label="处理状态" name="flowStatus">
          <a-select v-model:value="form.flowStatus" style="width: 100%" placeholder="请选择">
            <a-select-option :value="0">未处理</a-select-option>
            <a-select-option :value="1">处理中</a-select-option>
            <a-select-option :value="2">处理完成</a-select-option>
            <a-select-option :value="3">处理拒绝</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label=" " />
        <a-form-item label="分页大小" name="pageSize">
          <a-input-number v-model:value="form.pageSize" :min="1" :max="100" style="width: 100%" placeholder="1~100" allow-clear />
        </a-form-item>
        <a-form-item label="创建时间(结束)" name="createTimeEnd">
          <a-date-picker v-model:value="form.createTimeEnd" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" style="width: 100%" />
        </a-form-item>
      </a-col>
    </a-row>
  </a-form>
</template>

<script>
import { message } from 'ant-design-vue';
import { RiskyCaseSyncApi } from '@/api/transactionManage/RiskyCaseSyncApi';

export default {
  emits: ['done'],
  data() {
    return {
      // 表单数据
      form: {
        beginLine: 0,
        pageSize: 10
      },
      // 表单验证规则
      rules: {
        currentPage: [{ required: true, message: '请输入页码' }]
      }
    };
  },
  methods: {
    async onSubmit() {
      // 校验表单
      await this.$refs.form.validate();

      const result = await RiskyCaseSyncApi.riskCodeTransQuery(this.form);
      message.success(result.message);
    }
  }
};
</script>
