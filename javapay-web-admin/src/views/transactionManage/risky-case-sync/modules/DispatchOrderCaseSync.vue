<template>
  <a-form ref="form" :model="form" :rules="rules" layout="vertical">
    <a-row :gutter="24">
      <a-col :md="12" :sm="24" :xs="24">
        <a-form-item label="结算商户编号" name="mercNum">
          <a-input v-model:value="form.mercNum" placeholder="请输入结算商户编号" allow-clear />
        </a-form-item>
        <a-form-item label="案例编号" name="dispatchNum">
          <a-input v-model:value="form.dispatchNum" placeholder="请输入案例编号" allow-clear />
        </a-form-item>
        <a-form-item label="处理状态" name="dispatchStatus">
          <a-select v-model:value="form.dispatchStatus" style="width: 100%" placeholder="请选择">
            <a-select-option :value="0">未处理</a-select-option>
            <a-select-option :value="1">处理中</a-select-option>
            <a-select-option :value="2">处理完成</a-select-option>
            <a-select-option :value="3">处理拒绝</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="创建时间(开始)" name="createTimeBegin">
          <a-date-picker v-model:value="form.createTimeBegin" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" style="width: 100%" />
        </a-form-item>
        <a-form-item label="银行首发日(开始)" name="bankFirstDateBegin">
          <a-date-picker v-model:value="form.bankFirstDateBegin" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" style="width: 100%" />
        </a-form-item>
        <a-form-item label="最后期限日(开始)" name="bankLastDateBegin">
          <a-date-picker v-model:value="form.bankLastDateBegin" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" style="width: 100%" />
        </a-form-item>
      </a-col>
      <a-col :md="12" :sm="24" :xs="24">
        <a-form-item label="结算商户名称" name="mercNameLike">
          <a-input v-model:value="form.mercNameLike" placeholder="请输入结算商户名称" allow-clear />
        </a-form-item>
        <a-form-item label="页码" name="currentPage">
          <a-input-number v-model:value="form.currentPage" :min="1" style="width: 100%" placeholder="请输入页码" allow-clear />
        </a-form-item>
        <a-form-item label="注销状态" name="disabledState">
          <a-select v-model:value="form.disabledState" style="width: 100%" placeholder="请选择">
            <a-select-option value="normal">正常</a-select-option>
            <a-select-option value="loggedOut">已注销</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="创建时间(结束)" name="createTimeEnd">
          <a-date-picker v-model:value="form.createTimeEnd" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" style="width: 100%" />
        </a-form-item>
        <a-form-item label="银行首发日(结束)" name="bankFirstDateEnd">
          <a-date-picker v-model:value="form.bankFirstDateEnd" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" style="width: 100%" />
        </a-form-item>
        <a-form-item label="最后期限日(结束)" name="bankLastDateEnd">
          <a-date-picker v-model:value="form.bankLastDateEnd" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" style="width: 100%" />
        </a-form-item>
      </a-col>
    </a-row>
  </a-form>
</template>

<script>
import { message } from 'ant-design-vue';
import { RiskyCaseSyncApi } from '@/api/transactionManage/RiskyCaseSyncApi';

export default {
  emits: ['done'],
  data() {
    return {
      // 表单数据
      form: { currentPage: 1 },

      // 表单验证规则
      rules: {
        currentPage: [{ required: true, message: '请输入页码' }]
      }
    };
  },
  methods: {
    async onSubmit() {
      // 校验表单
      await this.$refs.form.validate();

      const result = await RiskyCaseSyncApi.riskDispatchOrderQuery(this.form);
      message.success(result.message);
    }
  }
};
</script>
