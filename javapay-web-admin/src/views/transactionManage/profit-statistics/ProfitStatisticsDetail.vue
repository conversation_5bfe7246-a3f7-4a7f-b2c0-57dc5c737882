<template>
  <a-modal
    :width="1000"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2" bordered size="middle">
      <a-descriptions-item label="结算单号" :span="2">{{ form.settleFlowNo }}</a-descriptions-item>
      <a-descriptions-item label="交易通道">{{ filterTransChnCode(form.transChnCode) }}</a-descriptions-item>
      <a-descriptions-item label="交易通道日期">{{ form.transChnDate }}</a-descriptions-item>
      <a-descriptions-item label="分润类型">
        <a-tag v-if="form.profitType === 1" color="pink">交易分润</a-tag>
        <a-tag v-else-if="form.profitType === 2" color="cyan">营销活动分润</a-tag>
        <a-tag v-else-if="form.profitType === 3" color="blue">机构提现分润</a-tag>
        <a-tag v-else-if="form.profitType === 4" color="purple">机构代付分润</a-tag>
        <a-tag v-else-if="form.profitType === 5" color="pink">终端激活奖励分润</a-tag>
        <a-tag v-else-if="form.profitType === 6" color="cyan">终端达标奖励分润</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="分润出款方式">
        <a-badge v-if="form.settleChannel === 1" color="pink" text="平台税筹账户" />
        <a-badge v-else-if="form.settleChannel === 0" color="purple" text="交易通道" />
        <a-badge v-else-if="form.settleChannel === 2" color="blue" text="一代税筹账户" />
        <a-badge v-else-if="form.settleChannel === 3" color="cyan" text="展业平台" />
        <a-badge v-else-if="form.settleChannel === 4" color="orange" text="活动方出款" />
      </a-descriptions-item>
      <a-descriptions-item label="结算状态">
        <a-tag v-if="form.settleStatus === 1" color="warning">
          <template #icon>
            <exclamation-circle-outlined />
          </template>
          未结算
        </a-tag>
        <a-tag v-else-if="form.settleStatus === 2" color="success">
          <template #icon>
            <check-circle-outlined />
          </template>
          已结算
        </a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="结算周期">
        {{ form.settlePeriod === 1 ? '日结' : form.settlePeriod === 2 ? '月结' : '--' }}
      </a-descriptions-item>
      <a-descriptions-item label="结算时间">{{ form.settleTime }}</a-descriptions-item>
      <a-descriptions-item label="交易总额">{{ form.transTotal }}</a-descriptions-item>
      <a-descriptions-item label="交易笔数">{{ form.transCount }}</a-descriptions-item>
      <a-descriptions-item v-if="hasPurview('0')" label="通道手续费">{{ form.chnTotalFee }}</a-descriptions-item>
      <a-descriptions-item label="商户手续费">{{ form.merchTotalFee }}</a-descriptions-item>
      <a-descriptions-item label="税点">{{ form.taxPoint }}</a-descriptions-item>
      <a-descriptions-item label="用户类型">
        <a-badge v-if="form.userType === 1" color="pink" text="大区" />
        <a-badge v-else-if="form.userType === 2" color="blue" text="运营中心" />
        <a-badge v-else-if="form.userType === 3" color="cyan" text="代理商" />
        <a-badge v-else-if="form.userType === 5" color="purple" text="代理商(子代)" />
      </a-descriptions-item>
      <a-descriptions-item label="用户手续费">{{ form.userTotalFee }}</a-descriptions-item>
      <a-descriptions-item label="用户总分润">{{ form.userTotalProfit }}</a-descriptions-item>
      <a-descriptions-item label="用户编号">{{ form.userNo }}</a-descriptions-item>
      <a-descriptions-item v-if="hasPurview('0')" label="用户级别">{{ form.userLevel }}</a-descriptions-item>
      <a-descriptions-item label="补扣税比率">{{ form.addTaxRatio }}</a-descriptions-item>
      <!-- <a-descriptions-item label="用户税额">{{ form.taxAmount }}</a-descriptions-item> -->
      <a-descriptions-item label="用户税差额">{{ form.addTaxAmount }}</a-descriptions-item>
      <a-descriptions-item label="用户结算金额">{{ form.settleAmount }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';
import { hasPurview } from '@/utils/permission';

export default {
  name: 'ProfitStatisticsDetail',
  props: {
    visible: Boolean,
    detail: Object,
    channelCodes: Array
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {},
      hasPurview
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const filterTransChnCode = code => {
      const item = props.channelCodes.find(c => c.channelCode === code);
      return item?.channelName || '--';
    };

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible,
      filterTransChnCode
    };
  }
};
</script>
