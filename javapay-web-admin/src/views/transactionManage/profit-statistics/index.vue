<template>
  <div class="ele-body">
    <a-spin :spinning="spinning" tip="下载中, 请稍候...">
      <!-- 搜索表单 -->
      <div class="block-interval">
        <a-card :bordered="false">
          <a-form layout="inline" :model="where">
            <a-row :gutter="[0, 16]">
              <a-form-item label="结算单号">
                <a-input v-model:value.trim="where.settleFlowNo" placeholder="请输入结算单号" allow-clear />
              </a-form-item>
              <a-form-item label="交易通道">
                <a-select v-model:value="where.transChnCode" style="width: 200px" placeholder="请选择" allow-clear>
                  <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">{{
                    channelName
                  }}</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="分润类型">
                <a-select v-model:value="where.profitType" style="width: 200px" placeholder="请选择" allow-clear>
                  <a-select-option :value="1">交易分润</a-select-option>
                  <a-select-option :value="2">营销活动分润</a-select-option>
                  <a-select-option :value="3">机构提现分润</a-select-option>
                  <a-select-option :value="4">机构代付分润</a-select-option>
                  <a-select-option :value="5">终端激活奖励分润</a-select-option>
                  <a-select-option :value="6">终端达标奖励分润</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="结算周期">
                <a-select v-model:value="where.settlePeriod" style="width: 200px" placeholder="请选择" allow-clear>
                  <a-select-option :value="1">日结</a-select-option>
                  <a-select-option :value="2">月结</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="结算状态">
                <a-select v-model:value="where.settleStatus" style="width: 200px" placeholder="请选择" allow-clear>
                  <a-select-option :value="1">未结算</a-select-option>
                  <a-select-option :value="2">已结算</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="分润出款方式">
                <a-select v-model:value="where.settleChannel" style="width: 200px" placeholder="请选择" allow-clear>
                  <a-select-option :value="0">交易通道</a-select-option>
                  <a-select-option :value="1">平台税筹账户</a-select-option>
                  <a-select-option :value="2">一代税筹账户</a-select-option>
                  <a-select-option :value="3">展业平台</a-select-option>
                  <a-select-option :value="4">活动方出款</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="结算日期">
                <a-date-picker v-model:value="where.settleTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
              </a-form-item>
              <a-form-item label="交易通道开始日期">
                <a-date-picker v-model:value="where.transChnBeginDate" format="YYYY-MM-DD" valueFormat="YYYYMMDD" />
              </a-form-item>
              <a-form-item label="交易通道结束日期">
                <a-date-picker v-model:value="where.transChnEndDate" format="YYYY-MM-DD" valueFormat="YYYYMMDD" />
              </a-form-item>
              <a-form-item label="用户编号" v-purview="'0'">
                <a-input v-model:value.trim="where.userNo" placeholder="请输入用户编号" allow-clear />
              </a-form-item>
              <a-form-item label="用户类型" v-purview="'0'">
                <a-select v-model:value="where.userType" style="width: 200px" placeholder="请选择" allow-clear>
                  <a-select-option :value="1">大区</a-select-option>
                  <a-select-option :value="2">运营中心</a-select-option>
                  <a-select-option :value="3">代理商</a-select-option>
                  <a-select-option :value="5">代理商(子代)</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="用户等级" v-purview="'0'">
                <a-input v-model:value.trim="where.userLevel" placeholder="请输入用户等级" allow-clear />
              </a-form-item>
              <a-form-item label="开始日期">
                <a-date-picker v-model:value="where.searchBeginTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
              </a-form-item>
              <a-form-item label="结束日期">
                <a-date-picker v-model:value="where.searchEndTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
              </a-form-item>
              <a-form-item class="ele-text-center">
                <a-space>
                  <a-button type="primary" @click="reload">查询</a-button>
                  <a-button @click="reset">重置</a-button>
                </a-space>
              </a-form-item>
            </a-row>
          </a-form>
        </a-card>
      </div>

      <!-- 表格 -->
      <div>
        <a-card :bordered="false" class="table-height">
          <ele-pro-table
            ref="table"
            row-key="id"
            :datasource="datasource"
            :columns="columns"
            :where="where"
            v-model:selection="selection"
            :scroll="{ x: 'max-content' }"
          >
            <!-- table上边工具栏 -->
            <template #toolbar>
              <a-space>
                <a-button @click="manualStatisticsClick" v-if="hasPurview(['0'])">
                  <template #icon>
                    <node-expand-outlined />
                  </template>
                  <span>手动统计</span>
                </a-button>
                <a-button @click="handleExportExcel">
                  <template #icon>
                    <download-outlined />
                  </template>
                  <span>导出excel</span>
                </a-button>
              </a-space>
            </template>

            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'settleStatus'">
                <a-tag v-if="record.settleStatus === 1" color="warning">
                  <template #icon>
                    <exclamation-circle-outlined />
                  </template>
                  未结算
                </a-tag>
                <a-tag v-else-if="record.settleStatus === 2" color="success">
                  <template #icon>
                    <check-circle-outlined />
                  </template>
                  已结算
                </a-tag>
              </template>

              <template v-if="column.key === 'profitType'">
                <a-tag v-if="record.profitType === 1" color="pink">交易分润</a-tag>
                <a-tag v-else-if="record.profitType === 2" color="cyan">营销活动分润</a-tag>
                <a-tag v-else-if="record.profitType === 3" color="blue">机构提现分润</a-tag>
                <a-tag v-else-if="record.profitType === 4" color="purple">机构代付分润</a-tag>
                <a-tag v-else-if="record.profitType === 5" color="pink">终端激活奖励分润</a-tag>
                <a-tag v-else-if="record.profitType === 6" color="cyan">终端达标奖励分润</a-tag>
              </template>

              <template v-else-if="column.key === 'userType'">
                <a-badge v-if="record.userType === 1" color="pink" text="大区" />
                <a-badge v-else-if="record.userType === 2" color="blue" text="运营中心" />
                <a-badge v-else-if="record.userType === 3" color="cyan" text="代理商" />
                <a-badge v-else-if="record.userType === 5" color="purple" text="代理商(子代)" />
              </template>

              <template v-else-if="column.key === 'settleChannel'">
                <a-badge v-if="record.settleChannel === 1" color="pink" text="平台税筹账户" />
                <a-badge v-else-if="record.settleChannel === 0" color="purple" text="交易通道" />
                <a-badge v-else-if="record.settleChannel === 2" color="blue" text="一代税筹账户" />
                <a-badge v-else-if="record.settleChannel === 3" color="cyan" text="展业平台" />
                <a-badge v-else-if="record.settleChannel === 4" color="orange" text="活动方出款" />
              </template>

              <template v-else-if="column.key === 'action'">
                <a-space>
                  <a @click="handleDetail(record)">详情</a>
                </a-space>
              </template>
            </template>
          </ele-pro-table>
        </a-card>
      </div>
    </a-spin>
    <!-- 详情 -->
    <ProfitStatisticsDetail v-model:visible="showDetail" :detail="current" :channelCodes="channelCodes" />

    <!-- 手动统计 -->
    <ProfitStatisticsManualStatisticsHandle v-if="showManualStatistics" v-model:visible="showManualStatistics" @done="reload" />
  </div>
</template>

<script>
import { ProfitStatisticsApi } from '@/api/transactionManage/ProfitStatisticsApi';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import ProfitStatisticsDetail from './ProfitStatisticsDetail.vue';
import { hasPurview } from '@/utils/permission';
import ProfitStatisticsManualStatisticsHandle from './ProfitStatisticsManualStatisticsHandle.vue';
import dayjs from 'dayjs';
import { message } from 'ant-design-vue';

export default {
  name: 'ProfitStatistics',
  components: {
    ProfitStatisticsDetail,
    ProfitStatisticsManualStatisticsHandle
  },
  data() {
    const isHideCol = !hasPurview('0');
    return {
      hasPurview,
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left',
          hideCol: isHideCol
        },
        {
          title: '用户编号',
          dataIndex: 'userNo',
          align: 'center'
        },
        {
          title: '用户类型',
          dataIndex: 'userType',
          key: 'userType',
          align: 'center'
        },
        {
          title: '用户级别',
          dataIndex: 'userLevel',
          align: 'center',
          hideCol: isHideCol
        },
        {
          title: '交易通道',
          dataIndex: 'transChnCode',
          customRender: ({ text }) => {
            const item = this.channelCodes.find(c => c.channelCode === text);
            return item?.channelName || '--';
          }
        },
        {
          title: '结算单号',
          dataIndex: 'settleFlowNo'
        },
        {
          title: '结算状态',
          dataIndex: 'settleStatus',
          key: 'settleStatus',
          align: 'center'
        },
        {
          title: '结算周期',
          dataIndex: 'settlePeriod',
          align: 'center',
          customRender: ({ text }) => {
            if (text === 1) return '日结';
            else if (text === 2) return '月结';
            return '--';
          }
        },
        {
          title: '结算时间',
          dataIndex: 'settleTime'
        },
        {
          title: '分润出款方式',
          dataIndex: 'settleChannel',
          key: 'settleChannel',
          align: 'center'
        },
        {
          title: '通道手续费',
          dataIndex: 'chnTotalFee',
          align: 'center',
          hideCol: isHideCol
        },
        {
          title: '商户手续费',
          dataIndex: 'merchTotalFee',
          align: 'center'
        },
        {
          title: '分润类型',
          dataIndex: 'profitType',
          key: 'profitType',
          align: 'center'
        },
        {
          title: '交易通道日期',
          dataIndex: 'transChnDate',
          align: 'center'
        },
        {
          title: '交易笔数',
          dataIndex: 'transCount',
          align: 'center'
        },
        {
          title: '交易总额',
          dataIndex: 'transTotal',
          align: 'center'
        },
        {
          title: '税点',
          dataIndex: 'taxPoint',
          align: 'center'
        },
        {
          title: '用户手续费',
          dataIndex: 'userTotalFee',
          align: 'center'
        },
        {
          title: '用户总分润',
          dataIndex: 'userTotalProfit',
          align: 'center'
        },
        {
          title: '补扣税比率',
          dataIndex: 'addTaxRatio',
          align: 'center'
        },
        // {
        //   title: '用户税额',
        //   dataIndex: 'taxAmount',
        //   align: 'center'
        // },
        {
          title: '用户税差额',
          dataIndex: 'addTaxAmount',
          align: 'center'
        },
        {
          title: '用户结算金额',
          dataIndex: 'settleAmount',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right',
          width: 150
        }
      ].filter(i => !i.hideCol),
      // 表格搜索条件
      where: {
        searchBeginTime: dayjs().format('YYYY-MM-DD'),
        searchEndTime: dayjs().format('YYYY-MM-DD')
      },
      selection: [],
      current: null,
      channelCodes: [],
      showDetail: false,
      showEdit: false,
      spinning: false,
      showManualStatistics: false
    };
  },
  mounted() {
    this.getChannelCodes();
  },
  methods: {
    async handleExportExcel() {
      if (!(this.where.searchBeginTime || this.where.searchEndTime)) {
        message.warning('请选择开始、结束日期');
        return;
      }
      this.spinning = true;
      const res = await ProfitStatisticsApi.downloadTransProfit(this.where).catch(() => {
        this.spinning = false;
      });
      this.spinning = false;
      const fileReader = new FileReader();
      fileReader.onload = function () {
        try {
          // 说明是普通对象数据，后台转换失败
          const jsonData = JSON.parse(fileReader.result);
          message.error(jsonData.message);
        } catch (err) {
          const contentDisposition = res.headers['content-disposition'];
          let fileName = decodeURIComponent(contentDisposition.substring(contentDisposition.indexOf('=') + 1));
          fileName = '分润统计-下载.xlsx';
          // 解析对象失败，说明是正常的文件流
          let blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.style.display = 'none';
          a.href = url;
          a.download = fileName;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }
      };
      fileReader.readAsText(res?.data);
    },

    async getChannelCodes() {
      const data = await ChannelManageApi.list({ validStatus: 1 });
      this.channelCodes = data || [];
    },

    reload() {
      this.selection = [];
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {
        searchBeginTime: dayjs().format('YYYY-MM-DD'),
        searchEndTime: dayjs().format('YYYY-MM-DD')
      };
      this.selection = [];
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    handleEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    manualStatisticsClick() {
      this.showManualStatistics = true;
    },

    datasource({ page, limit, where, orders }) {
      return ProfitStatisticsApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
