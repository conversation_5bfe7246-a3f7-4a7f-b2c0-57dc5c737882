<template>
  <a-modal
    :width="500"
    :visible="visible"
    :confirm-loading="loading"
    title="手动统计"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules" :layout="'vertical'">
      <a-form-item label="交易通道日期" prop="transChnDate">
        <a-date-picker style="width: 100%;" v-model:value="form.transChnDate" format="YYYYMMDD" valueFormat="YYYYMMDD" />
      </a-form-item>
      <a-form-item label="分润类型" prop="profitType">
        <a-select v-model:value="form.profitType" placeholder="请选择">
          <a-select-option :value="1">交易分润</a-select-option>
          <a-select-option :value="2">营销活动分润</a-select-option>
          <a-select-option :value="3">机构提现分润</a-select-option>
          <a-select-option :value="4">机构代付分润</a-select-option>
          <a-select-option :value="5">终端激活奖励分润</a-select-option>
          <a-select-option :value="6">终端达标奖励分润</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="用户类型" v-purview="'0'" prop="userType">
        <a-select v-model:value="form.userType" placeholder="请选择">
          <a-select-option :value="1">大区</a-select-option>
          <a-select-option :value="2">运营中心</a-select-option>
          <a-select-option :value="3">代理商</a-select-option>
          <a-select-option :value="5">代理商(子代)</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="用户级别" v-if="form.userType === 5" prop="userLevel">
        <a-input v-model:value.trim="form.userLevel" placeholder="请输入用户级别" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { ProfitStatisticsApi } from '@/api/transactionManage/ProfitStatisticsApi';
import { message } from 'ant-design-vue';

export default {
  props: {
    visible: Boolean
  },
  emits: ['update:visible', 'done'],
  data() {
    return {
      //表单数据
      form: {},
      //提交状态
      loading: false,
      //表单规则
      rules: {
        profitType: [{ required: true, message: '请选择分润类型' }],
        transChnDate: [{ required: true, message: '请选择交易通道日期' }],
        // userType: [{ required: true, message: '请选择用户类型' }],
        userLevel: [{ required: true, message: '请输入用户级别' }]
      }
    };
  },
  watch: {
    'form.userType'(val) {
      if (val !== 5) {
        this.form.userLevel = 1;
      } else {
        this.form.userLevel = '';
      }
    }
  },
  methods: {
    //传值更新父组件的值
    updateVisible(value) {
      this.$emit('update:visible', value);
    },

    //提交
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let result = ProfitStatisticsApi.manualStatisticsHandle(this.form);
      result
        .then(res => {
          // 移除加载框
          this.loading = false;
          // 提示添加成功
          message.success(res.message);
          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    }
  }
};
</script>
