<template>
  <a-modal
    :width="1000"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2" title="交易基本信息">
      <!-- <a-descriptions-item label="交易ID">{{ form.transId }}</a-descriptions-item> -->
      <a-descriptions-item label="交易流水单号">{{ form.flowNo }}</a-descriptions-item>
      <a-descriptions-item label="交易通道">
        <template v-for="({ channelCode, channelName }, key) in transChnList" :key="key">
          <a-tag v-if="form.transChn === channelCode" color="pink">{{ channelName }}</a-tag>
        </template>
      </a-descriptions-item>
      <a-descriptions-item label="费率编号">{{ form.templateNo }}</a-descriptions-item>
      <a-descriptions-item label="交易金额(元)">{{ form.transAmount }}</a-descriptions-item>
      <a-descriptions-item label="通道商户名称">{{ form.chnMerchName }}</a-descriptions-item>
      <a-descriptions-item label="通道商户编号">{{ form.chnMerchNo }}</a-descriptions-item>
      <a-descriptions-item label="借贷标识">
        <a-tag v-if="form.debitType === 1" color="pink">加款</a-tag>
        <a-tag v-else-if="form.debitType === 2" color="cyan">减款</a-tag>
        <a-tag v-else-if="form.debitType === 3" color="purple">不记账</a-tag>
      </a-descriptions-item>
    </a-descriptions>

    <a-descriptions :column="2" title="商户费率信息">
      <a-descriptions-item label="商户编号">{{ form.merchNo }}</a-descriptions-item>
      <a-descriptions-item label="商户费率版本号">{{ form.merchRateVersion }}</a-descriptions-item>
      <a-descriptions-item label="商户费率(%)">{{ form.merchRate }}</a-descriptions-item>
      <a-descriptions-item label="商户D0服务费费率(%)">{{ form.merchD0Rate }}</a-descriptions-item>
      <a-descriptions-item label="商户D0服务单笔服务费(元)">{{ form.merchD0SingleFee }}</a-descriptions-item>
      <a-descriptions-item label="商户交易手续费(元)">{{ form.merchFee }}</a-descriptions-item>
      <a-descriptions-item label="商户D0手续费(元)">{{ form.merchD0Fee }}</a-descriptions-item>
      <a-descriptions-item label="商户结算金额(元)">{{ form.settleAmount }}</a-descriptions-item>
    </a-descriptions>

    <a-descriptions :column="2" title="平台分润信息" v-if="hasPurview(['0'])">
      <a-descriptions-item label="通道费率(%)">{{ form.chnRate }}</a-descriptions-item>
      <a-descriptions-item label="通道D0服务费费率(%)">{{ form.chnD0Rate }}</a-descriptions-item>
      <a-descriptions-item label="通道D0单笔服务费(元)">{{ form.chnD0SingleFee }}</a-descriptions-item>
      <a-descriptions-item label="通道交易手续费(元)">{{ form.chnFee }}</a-descriptions-item>
      <a-descriptions-item label="通道D0手续费(元)">{{ form.chnD0Fee }}</a-descriptions-item>
      <a-descriptions-item label="平台交易总分润(元)">{{ form.plfTransProfit }}</a-descriptions-item>
      <a-descriptions-item label="平台D0分润(元)">{{ form.plfD0Profit }}</a-descriptions-item>
    </a-descriptions>

    <a-descriptions :column="2" title="大区分润信息" v-if="hasPurview(['0', '1'])">
      <a-descriptions-item label="大区编号">{{ form.regionNo }}</a-descriptions-item>
      <a-descriptions-item label="大区费率版本号">{{ form.regionRateVersion }}</a-descriptions-item>
      <a-descriptions-item label="大区返佣比例">{{ form.regionRateRatio }}</a-descriptions-item>
      <a-descriptions-item label="大区费率(%)">{{ form.regionRate }}</a-descriptions-item>
      <a-descriptions-item label="大区D0服务费费率(%)">{{ form.regionD0Rate }}</a-descriptions-item>
      <a-descriptions-item label="大区D0服务单笔费用(元)">{{ form.regionD0SingleFee }}</a-descriptions-item>
      <a-descriptions-item label="大区费率成本金额(元)">{{ form.regionFee }}</a-descriptions-item>
      <a-descriptions-item label="大区D0成本金额(元)">{{ form.regionD0Fee }}</a-descriptions-item>
      <a-descriptions-item label="大区费率分润金额(元)">{{ form.regionProfit }}</a-descriptions-item>
      <a-descriptions-item label="大区D0分润金额(元)">{{ form.regionD0Profit }}</a-descriptions-item>
    </a-descriptions>

    <a-descriptions :column="2" title="运营中心分润信息" v-if="hasPurview(['0', '1', '2'])">
      <a-descriptions-item label="运营中心编号">{{ form.branchNo }}</a-descriptions-item>
      <a-descriptions-item label="运营中心费率版本号">{{ form.branchRateVersion }}</a-descriptions-item>
      <a-descriptions-item label="运营中心返佣比例">{{ form.branchRateRatio }}</a-descriptions-item>
      <a-descriptions-item label="运营中心费率(%)">{{ form.branchRate }}</a-descriptions-item>
      <a-descriptions-item label="运营中心D0服务费费率(%)">{{ form.branchD0Rate }}</a-descriptions-item>
      <a-descriptions-item label="运营中心D0服务单笔费用(元)">{{ form.branchD0SingleFee }}</a-descriptions-item>
      <a-descriptions-item label="运营中心费率成本金额(元)">{{ form.branchFee }}</a-descriptions-item>
      <a-descriptions-item label="运营中心D0成本金额(元)">{{ form.branchD0Fee }}</a-descriptions-item>
      <a-descriptions-item label="运营中心费率分润金额(元)">{{ form.branchProfit }}</a-descriptions-item>
      <a-descriptions-item label="运营中心D0分润金额(元)">{{ form.branchD0Profit }}</a-descriptions-item>
    </a-descriptions>

    <a-divider orientation="left" :orientationMargin="0">代理商分润信息</a-divider>
    <template v-for="(item, key) in form.agentFeeObjList || []" :key="key">
      <a-descriptions :column="2">
        <a-descriptions-item label="代理商编号">{{ item.agentNo }}</a-descriptions-item>
        <a-descriptions-item label="代理商费率版本号">{{ item.agentRateVersion }}</a-descriptions-item>
        <a-descriptions-item label="代理商返佣比例">{{ item.agentRateRatio }}</a-descriptions-item>
        <a-descriptions-item label="代理商费率(%)">{{ item.agentRate }}</a-descriptions-item>
        <a-descriptions-item label="代理商D0服务费费率(%)">{{ item.agentD0Rate }}</a-descriptions-item>
        <a-descriptions-item label="代理商D0服务单笔费用(元)">{{ item.agentD0SingleFee }}</a-descriptions-item>
        <a-descriptions-item label="代理商费率成本金额(元)">{{ item.agentFee }}</a-descriptions-item>
        <a-descriptions-item label="代理商D0成本金额(元)">{{ item.agentD0Fee }}</a-descriptions-item>
        <a-descriptions-item label="代理商费率分润金额(元)">{{ item.agentProfit }}</a-descriptions-item>
        <a-descriptions-item label="代理商D0分润金额(元)">{{ item.agentD0Profit }}</a-descriptions-item>
      </a-descriptions>
      <a-divider dashed style="margin-bottom: 15px" v-if="key !== form.agentFeeObjList?.length - 1"
      /></template>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';
import { hasPurview } from '@/utils/permission';
import { deepCopy } from '@/utils/util';

export default {
  name: 'TransFeesDetail',
  props: {
    visible: Boolean,
    detail: Object,
    transChnList: Array
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {},
      hasPurview
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form =deepCopy( Object.assign({}, props.detail));

        data.form.agentFeeObjList = data.form.agentFeeObjList || [];

        if (!hasPurview(['5'])) {
          const oneLevelAgentKeys = Object.keys(props.detail).filter(key => key.startsWith('agent') && key !== 'agentFeeObjList');
          const oneLevelAgentFeeObj = {};
          oneLevelAgentKeys.forEach(key => {
            oneLevelAgentFeeObj[key] = props.detail[key];
          });
          oneLevelAgentFeeObj.agentNo = props.detail.oneLevelAgentNo;
          data.form.agentFeeObjList.push(oneLevelAgentFeeObj);
        }
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
