<template>
  <div class="ele-body">
    <a-spin :spinning="spinning" tip="下载中, 请稍候...">
      <!-- 搜索表单 -->
      <div class="block-interval">
        <a-card :bordered="false">
          <a-form layout="inline" :model="where">
            <a-row :gutter="[0, 16]">
              <a-form-item label="大区编号" v-purview="['0']">
                <a-input v-model:value.trim="where.regionNo" placeholder="请输入大区编号" allow-clear />
              </a-form-item>
              <a-form-item label="运营中心编号" v-purview="['0', '1']">
                <a-input v-model:value.trim="where.branchNo" placeholder="请输入运营中心编号" allow-clear />
              </a-form-item>
              <!-- <a-form-item label="一级代理商编号" v-purview="['0', '1', '2']">
              <a-input v-model:value.trim="where.oneLevelAgentNo" placeholder="请输入一级代理商编号" allow-clear />
            </a-form-item> -->
              <a-form-item label="代理商编号" v-purview="['0', '1', '2']">
                <a-input v-model:value.trim="where.agentNo" placeholder="请输入代理商编号" allow-clear />
              </a-form-item>
              <a-form-item label="用户编号">
                <a-input v-model:value.trim="where.merchNo" placeholder="请输入用户编号" allow-clear />
              </a-form-item>
              <a-form-item label="通道商户编号">
                <a-input v-model:value.trim="where.chnMerchNo" placeholder="请输入通道商户编号" allow-clear />
              </a-form-item>
              <a-form-item label="流水单号">
                <a-input v-model:value.trim="where.flowNo" placeholder="请输入流水单号" allow-clear />
              </a-form-item>
              <a-form-item label="服务费单号">
                <a-input v-model:value.trim="where.serviceOrderNo" placeholder="请输入服务费单号" allow-clear />
              </a-form-item>
              <a-form-item label="交易参考号">
                <a-input v-model:value.trim="where.rrn" placeholder="请输入交易参考号" allow-clear />
              </a-form-item>
              <a-form-item label="终端SN">
                <a-input v-model:value.trim="where.termSn" placeholder="请输入终端SN" allow-clear />
              </a-form-item>
              <a-form-item label="交易类型">
                <a-select v-model:value="where.transType" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option :value="1">刷卡(贷记)</a-select-option>
                  <a-select-option :value="2">刷卡(借记)</a-select-option>
                  <a-select-option :value="3">云闪付(贷记)</a-select-option>
                  <a-select-option :value="4">云闪付(借记)</a-select-option>
                  <a-select-option :value="5">微信</a-select-option>
                  <a-select-option :value="6">支付宝</a-select-option>
                  <a-select-option :value="7">信用卡优惠类</a-select-option>
                  <a-select-option :value="8">支付宝大额</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="交易状态">
                <a-select v-model:value="where.transStatus" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option :value="1">已创建</a-select-option>
                  <a-select-option :value="2">交易成功</a-select-option>
                  <a-select-option :value="3">交易失败</a-select-option>
                  <a-select-option :value="4">交易进行中</a-select-option>
                  <a-select-option :value="5">请求已受理</a-select-option>
                  <a-select-option :value="6">支付结果待查</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="是否收取D0费用">
                <a-select v-model:value="where.isChargeD0Fee" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option :value="1">是</a-select-option>
                  <a-select-option :value="0">否</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="交易金额范围(开始)">
                <a-input v-model:value.trim="where.transStartAmount" placeholder="请输入交易金额范围(开始)" allow-clear />
              </a-form-item>
              <a-form-item label="交易金额范围(结束)">
                <a-input v-model:value.trim="where.transEndAmount" placeholder="请输入交易金额范围(结束)" allow-clear />
              </a-form-item>
              <a-form-item label="交易日期">
                <a-date-picker v-model:value="where.transDate" format="YYYY-MM-DD" valueFormat="YYYYMMDD" />
              </a-form-item>
              <a-form-item label="开始日期">
                <a-date-picker v-model:value="where.searchBeginTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
              </a-form-item>
              <a-form-item label="结束日期">
                <a-date-picker v-model:value="where.searchEndTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
              </a-form-item>
              <a-form-item class="ele-text-center">
                <a-space>
                  <a-button type="primary" @click="reload">查询</a-button>
                  <a-button @click="reset">重置</a-button>
                  <a-button @click="handleExportExcel">
                    <template #icon>
                      <download-outlined />
                    </template>
                    <span>查询并导出</span>
                  </a-button>
                </a-space>
              </a-form-item>
            </a-row>
          </a-form>
        </a-card>
      </div>

      <!-- 表格 -->
      <div>
        <a-card :bordered="false" class="table-height">
          <ele-pro-table
            ref="table"
            row-key="id"
            :datasource="datasource"
            :columns="columns"
            :where="where"
            v-model:selection="selection"
            :scroll="{ x: 'max-content' }"
          >
            <template #toolbar>
              <div style="margin-bottom: 10px">
                <a-space>
                  <span>
                    <span style="margin-right: 20px">
                      <span style="color: red">交易总额:</span>
                      <span style="color: black">{{ transFeeSummary.transAmount }}</span>
                    </span>

                    <span style="margin-right: 20px">
                      <span style="color: red">商户交易手续费总额:</span>
                      <span style="color: black">{{ transFeeSummary.merchFee }}</span>
                    </span>

                    <span style="margin-right: 20px">
                      <span style="color: red">商户D0服务费总额:</span>
                      <span style="color: black">{{ transFeeSummary.merchD0Fee }}</span>
                    </span>

                    <span style="margin-right: 20px" v-if="hasPurview(['0'])">
                      <span style="color: red">平台交易分润总额:</span>
                      <span style="color: black">{{ transFeeSummary.plfTransProfit }}</span>
                    </span>

                    <span style="margin-right: 20px" v-if="hasPurview(['0'])">
                      <span style="color: red">平台D0分润金额:</span>
                      <span style="color: black">{{ transFeeSummary.plfD0Profit }}</span>
                    </span>

                    <span style="margin-right: 20px" v-if="hasPurview(['0', '1'])">
                      <span style="color: red">大区分润总金额:</span>
                      <span style="color: black">{{ transFeeSummary.regionProfit }}</span>
                    </span>

                    <span style="margin-right: 20px" v-if="!hasPurview(['5', '3'])">
                      <span style="color: red">运营中心分润总金额:</span>
                      <span style="color: black">{{ transFeeSummary.branchProfit }}</span>
                    </span>

                    <span style="margin-right: 20px">
                      <span style="color: red">代理商分润总金额:</span>
                      <span style="color: black">{{ transFeeSummary.agentProfit }}</span>
                    </span>
                  </span>
                </a-space>
              </div>
            </template>
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'debitType'">
                <a-tag v-if="record.debitType === 1" color="pink">加款</a-tag>
                <a-tag v-else-if="record.debitType === 2" color="pink">减款</a-tag>
                <a-tag v-else-if="record.debitType === 3" color="cyan">不记账</a-tag>
              </template>

              <template v-if="column.key === 'chnCostMethod'">
                <a-tag v-if="record.chnCostMethod === 1" color="pink">平台计算</a-tag>
                <a-tag v-else-if="record.chnCostMethod === 2" color="cyan">对账导入</a-tag>
              </template>
              <template
                v-if="
                  ['isCalculateFee', 'isChnCumulate', 'isCumulate', 'isChargeD0Fee', 'isServiceOrder', 'isTriggerRisk'].includes(column.key)
                "
              >
                <a-tag v-if="record[column.key] === 0">否</a-tag>
                <a-tag v-else-if="record[column.key] === 1" color="cyan">是</a-tag>
              </template>

              <template v-if="column.key === 'transStatus'">
                <a-tag v-if="record.transStatus === 1">已创建</a-tag>
                <a-tag v-else-if="record.transStatus === 2" color="success">交易成功</a-tag>
                <a-tag v-else-if="record.transStatus === 3" color="error">交易失败</a-tag>
                <a-tag v-else-if="record.transStatus === 4" color="purple">交易进行中</a-tag>
                <a-tag v-else-if="record.transStatus === 5" color="pink">请求已受理</a-tag>
                <a-tag v-else-if="record.transStatus === 6" color="cyan">支付结果待查</a-tag>
              </template>

              <template v-if="column.key === 'transChn'">
                <template v-for="({ channelCode, channelName }, key) in transChnList" :key="key">
                  <a-tag v-if="record.transChn === channelCode" color="pink">{{ channelName }}</a-tag>
                </template>
              </template>

              <template v-if="column.key === 'serviceOrderType'">
                <a-tag v-if="record.serviceOrderType === 1">服务费</a-tag>
                <a-tag v-else-if="record.serviceOrderType === 2">Sim流量费</a-tag>
              </template>

              <template v-if="column.key === 'transType'">
                <a-tag v-if="record.transType === 1">刷卡(贷记)</a-tag>
                <a-tag v-else-if="record.transType === 2">刷卡(借记)</a-tag>
                <a-tag v-else-if="record.transType === 3">云闪付(贷记)</a-tag>
                <a-tag v-else-if="record.transType === 4">云闪付(借记)</a-tag>
                <a-tag v-else-if="record.transType === 5">微信</a-tag>
                <a-tag v-else-if="record.transType === 6">支付宝</a-tag>
                <a-tag v-else-if="record.transType === 7">信用卡优惠类</a-tag>
                <a-tag v-else-if="record.transType === 8">支付宝大额</a-tag>
              </template>
              <!-- table操作栏按钮 -->
              <template v-else-if="column.key === 'action'">
                <a-space>
                  <a @click="handleDetail(record)">详情</a>
                  <template
                    v-if="
                      record.transStatus === 2 &&
                        (record.isServiceOrder === 0 || (record.isServiceOrder === 1 && record.serviceOrderType === 2))
                    "
                  >
                    <a-divider type="vertical" />
                    <a @click="handleTransFeeDetail(record)">手续费详情</a>
                  </template>
                </a-space>
              </template>
            </template>
          </ele-pro-table>
        </a-card>
      </div>

      <PosTransactionDetail v-model:visible="showDetail" :detail="current" :transChnList="transChnList" />

      <TransFeesDetail v-model:visible="showTransFeeDetail" :detail="current" :transChnList="transChnList" />
    </a-spin>
  </div>
</template>

<script>
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import { PosTransactionApi } from '@/api/transactionManage/PosTransactionApi';
import { hasPurview } from '@/utils/permission';
import PosTransactionDetail from './pos-transaction-detail.vue';
import TransFeesDetail from './trans-fees-detail.vue';
import dayjs from 'dayjs';
import { message } from 'ant-design-vue';

export default {
  name: 'PosTransaction',
  components: {
    PosTransactionDetail,
    TransFeesDetail
  },
  data() {
    return {
      // 表格搜索条件
      where: {
        searchBeginTime: dayjs().format('YYYY-MM-DD'),
        searchEndTime: dayjs().format('YYYY-MM-DD')
      },
      selection: [],
      current: null,
      showDetail: false,
      showTransFeeDetail: false,
      spinning: false,
      //交易通道列表
      transChnList: [],
      transFeeSummary: {},
      // 表格列配置
      columns: [
        {
          title: '交易ID',
          dataIndex: 'id',
          align: 'center',
          fixed: 'left'
        },
        {
          title: '商户名称',
          dataIndex: 'merchName',
          align: 'center'
        },
        {
          title: '商户编号',
          dataIndex: 'merchNo',
          align: 'center'
        },
        {
          title: '通道商户名称',
          dataIndex: 'chnMerchName',
          align: 'center'
        },
        {
          title: '通道商户编号',
          dataIndex: 'chnMerchNo',
          align: 'center'
        },
        {
          title: '终端SN',
          dataIndex: 'termSn',
          align: 'center'
        },
        {
          title: '交易创建时间',
          dataIndex: 'createTime',
          align: 'center'
        },
        {
          title: '交易类型',
          dataIndex: 'transType',
          key: 'transType',
          align: 'center'
        },
        {
          title: '交易参考号',
          dataIndex: 'rrn',
          align: 'center'
        },
        {
          title: '交易金额',
          dataIndex: 'transAmount',
          align: 'center'
        },
        {
          title: '交易状态',
          dataIndex: 'transStatus',
          key: 'transStatus',
          align: 'center'
        },
        {
          title: '交易完成时间',
          dataIndex: 'finishTime',
          align: 'center'
        },
        {
          title: '手续费',
          dataIndex: ['transFee', 'merchFee'],
          align: 'center'
        },
        {
          title: '结算金额',
          dataIndex: 'settleAmount',
          align: 'center'
        },
        {
          title: '付款卡号',
          dataIndex: 'payCardNoMask',
          align: 'center'
        },
        {
          title: '发卡行名称',
          dataIndex: 'bankName',
          align: 'center'
        },
        {
          title: '大区编号',
          dataIndex: 'regionNo',
          hideCol: !hasPurview('0'),
          align: 'center'
        },
        {
          title: '运营中心编号',
          dataIndex: 'branchNo',
          hideCol: !hasPurview(['0', '1']),
          align: 'center'
        },
        {
          title: '一级代理商编号',
          dataIndex: 'oneLevelAgentNo',
          hideCol: !hasPurview(['0', '1', '2']),
          align: 'center'
        },
        {
          title: '直属代理商编号',
          dataIndex: 'agentNo',
          align: 'center'
        },
        {
          title: '流水单号',
          dataIndex: 'flowNo',
          align: 'center'
        },
        {
          title: '是否计算手续费',
          dataIndex: 'isCalculateFee',
          key: 'isCalculateFee',
          align: 'center'
        },
        {
          title: '是否收取D0费用',
          dataIndex: 'isChargeD0Fee',
          key: 'isChargeD0Fee',
          align: 'center'
        },
        {
          title: '通道是否累计',
          dataIndex: 'isChnCumulate',
          key: 'isChnCumulate',
          align: 'center'
        },
        {
          title: '商户是否累计',
          dataIndex: 'isCumulate',
          key: 'isCumulate',
          align: 'center'
        },
        {
          title: '是否服务费订单',
          dataIndex: 'isServiceOrder',
          key: 'isServiceOrder',
          align: 'center'
        },
        {
          title: '服务费订单编号',
          dataIndex: 'serviceOrderNo',
          align: 'center'
        },
        {
          title: '服务费订单金额',
          dataIndex: 'serviceOrderAmt',
          align: 'center'
        },
        {
          title: '服务费订单类型',
          dataIndex: 'serviceOrderType',
          key: 'serviceOrderType',
          align: 'center'
        },
        {
          title: '交易通道',
          dataIndex: 'transChn',
          key: 'transChn',
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          width: 180,
          align: 'center',
          fixed: 'right'
        }
      ].filter(i => !i.hideCol)
    };
  },
  async mounted() {
    this.transChnList = await ChannelManageApi.list({ validStatus: 1 });
    this.getTransFeeSummaryData();
  },
  methods: {
    async handleExportExcel() {
      if (!(this.where.searchBeginTime || this.where.searchEndTime)) {
        message.warning('请选择开始、结束日期');
        return;
      }

      this.reload();

      this.spinning = true;
      const res = await PosTransactionApi.exportExcel(this.where).catch(() => {
        this.spinning = false;
      });
      this.spinning = false;
      const fileReader = new FileReader();
      fileReader.onload = function () {
        try {
          // 说明是普通对象数据，后台转换失败
          const jsonData = JSON.parse(fileReader.result);
          message.error(jsonData.message);
        } catch (err) {
          const contentDisposition = res.headers['content-disposition'];
          let fileName = decodeURIComponent(contentDisposition.substring(contentDisposition.indexOf('=') + 1));
          fileName = fileName ? fileName.replace("utf-8''", '') : '交易明细-下载';
          fileName = fileName.endsWith('.xlsx') ? fileName : `${fileName}.xlsx`;
          // 解析对象失败，说明是正常的文件流
          let blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.style.display = 'none';
          a.href = url;
          a.download = fileName;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }
      };
      fileReader.readAsText(res?.data);
    },

    hasPurview,
    async handleTransFeeDetail(row) {
      // 合并两个对象，只有当后面对象的字段有值时才覆盖前面的字段
      const merged = { ...row.transFee };
      Object.keys(row).forEach(key => {
        const value = row[key];
        // 只有当值不为 null、undefined、空字符串时才覆盖
        if (value !== null && value !== undefined && value !== '') {
          merged[key] = value;
        }
      });
      this.current = { ...row, ...merged };

      this.showTransFeeDetail = true;
    },

    reload() {
      this.selection = [];
      this.$refs.table.reload({ page: 1 });
      this.getTransFeeSummaryData();
    },

    reset() {
      this.where = {
        searchBeginTime: dayjs().format('YYYY-MM-DD'),
        searchEndTime: dayjs().format('YYYY-MM-DD')
      };
      this.selection = [];
      this.$refs.table.reload({ page: 1, where: this.where });
      this.getTransFeeSummaryData();
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    //列表查询统计
    async getTransFeeSummaryData() {
      this.transFeeSummary = (await PosTransactionApi.posSum(this.where)) || {};
    },

    datasource({ page, limit, where, orders }) {
      return PosTransactionApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
