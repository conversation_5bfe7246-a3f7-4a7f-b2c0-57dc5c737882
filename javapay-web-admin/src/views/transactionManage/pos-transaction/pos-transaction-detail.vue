<template>
  <a-modal :width="880" :visible="visible" title="详情" :body-style="{ paddingBottom: '20px' }" @update:visible="updateVisible">
    <a-descriptions :column="2">
      <a-descriptions-item label="交易ID">{{ form.id }}</a-descriptions-item>
      <a-descriptions-item label="商户名称">{{ form.merchName }}</a-descriptions-item>
      <a-descriptions-item label="商户编号">{{ form.merchNo }}</a-descriptions-item>
      <a-descriptions-item label="通道商户名称">{{ form.chnMerchName }}</a-descriptions-item>
      <a-descriptions-item label="通道商户编号">{{ form.chnMerchNo }}</a-descriptions-item>
      <a-descriptions-item label="终端SN">{{ form.termSn }}</a-descriptions-item>
      <a-descriptions-item label="交易创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="交易类型">
        <a-tag v-if="form.transType === 1">刷卡(贷记)</a-tag>
        <a-tag v-else-if="form.transType === 2">刷卡(借记)</a-tag>
        <a-tag v-else-if="form.transType === 3">云闪付(贷记)</a-tag>
        <a-tag v-else-if="form.transType === 4">云闪付(借记)</a-tag>
        <a-tag v-else-if="form.transType === 5">微信</a-tag>
        <a-tag v-else-if="form.transType === 6">支付宝</a-tag>
        <a-tag v-else-if="form.transType === 7">信用卡优惠类</a-tag>
        <a-tag v-else-if="form.transType === 8">支付宝大额</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="交易参考号">{{ form.rrn }}</a-descriptions-item>
      <a-descriptions-item label="交易金额">{{ form.transAmount }}</a-descriptions-item>
      <a-descriptions-item label="交易状态">
        <a-tag v-if="form.transStatus === 1">已创建</a-tag>
        <a-tag v-else-if="form.transStatus === 2" color="success">交易成功</a-tag>
        <a-tag v-else-if="form.transStatus === 3" color="error">交易失败</a-tag>
        <a-tag v-else-if="form.transStatus === 4" color="purple">交易进行中</a-tag>
        <a-tag v-else-if="form.transStatus === 5" color="pink">请求已受理</a-tag>
        <a-tag v-else-if="form.transStatus === 6" color="cyan">支付结果待查</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="交易完成时间">{{ form.finishTime }}</a-descriptions-item>
      <a-descriptions-item label="手续费">{{ form.transFee?.merchFee }}</a-descriptions-item>
      <a-descriptions-item label="结算金额">{{ form.settleAmount }}</a-descriptions-item>
      <a-descriptions-item label="付款卡号">{{ form.payCardNoMask }}</a-descriptions-item>
      <a-descriptions-item label="发卡行名称">{{ form.bankName }}</a-descriptions-item>
      <a-descriptions-item v-if="hasPurview(['0'])" label="大区编号">{{ form.regionNo }}</a-descriptions-item>
      <a-descriptions-item v-if="hasPurview(['0', '1'])" label="运营中心编号">{{ form.branchNo }}</a-descriptions-item>
      <a-descriptions-item v-if="hasPurview(['0', '1', '2'])" label="一级代理商编号">{{ form.oneLevelAgentNo }}</a-descriptions-item>
      <a-descriptions-item label="直属代理商编号">{{ form.agentNo }}</a-descriptions-item>
      <a-descriptions-item label="流水单号">{{ form.flowNo }}</a-descriptions-item>
      <a-descriptions-item label="是否计算手续费">{{ filterWhether(form.isCalculateFee) }}</a-descriptions-item>
      <a-descriptions-item label="是否收取D0费用">{{ filterWhether(form.isChargeD0Fee) }}</a-descriptions-item>
      <a-descriptions-item label="通道是否累计">{{ filterWhether(form.isChnCumulate) }}</a-descriptions-item>
      <a-descriptions-item label="商户是否累计">{{ filterWhether(form.isCumulate) }}</a-descriptions-item>
      <a-descriptions-item label="是否服务费订单">{{ filterWhether(form.isServiceOrder) }}</a-descriptions-item>
      <a-descriptions-item label="服务费订单编号">{{ form.serviceOrderNo }}</a-descriptions-item>
      <a-descriptions-item label="服务费订单金额">{{ form.serviceOrderAmt }}</a-descriptions-item>
      <a-descriptions-item label="服务费订单类型">
        <a-tag v-if="form.serviceOrderType === 1">服务费</a-tag>
        <a-tag v-else-if="form.serviceOrderType === 2">Sim流量费</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="交易通道">{{ form.transChn }}</a-descriptions-item>
    </a-descriptions>

    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>
<script>
import { reactive, toRefs, watchEffect } from 'vue';
import { hasPurview } from '@/utils/permission';

export default {
  props: {
    visible: Boolean,
    detail: Object,
    transChnList: Array
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const filterWhether = value => {
      if (value === 1) return '是';
      return '否';
    };

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible,
      filterWhether,
      hasPurview
    };
  }
};
</script>
