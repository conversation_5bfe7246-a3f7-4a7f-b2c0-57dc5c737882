<template>
  <a-modal
    :width="1000"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2" title="交易基本信息">
      <a-descriptions-item label="交易支付ID">{{ form.transId }}</a-descriptions-item>
      <a-descriptions-item label="交易支付单号">{{ form.orderNo }}</a-descriptions-item>
      <a-descriptions-item label="通道名称">
        <template v-for="({ channelCode, channelName }, key) in transChnList" :key="key">
          <a-tag v-if="form.chnCode === channelCode" color="pink">{{ channelName }}</a-tag>
        </template>
      </a-descriptions-item>
      <a-descriptions-item label="通道商户编号">{{ form.chnMerchNo }}</a-descriptions-item>
      <a-descriptions-item label="交易支付日期">{{ form.transChnDate }}</a-descriptions-item>
      <a-descriptions-item label="交易支付金额">{{ form.transAmount }}</a-descriptions-item>
      <a-descriptions-item label="活动订单ID">{{ form.serviceOrderId }}</a-descriptions-item>
      <a-descriptions-item label="活动支付订单号">{{ form.serviceOrderNo }}</a-descriptions-item>
      <a-descriptions-item label="活动订单类型">
        <a-tag v-if="form.serviceOrderType === 1">服务费</a-tag>
        <a-tag v-else-if="form.serviceOrderType === 2">Sim流量费</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="配置ID">{{ form.configId }}</a-descriptions-item>
      <a-descriptions-item label="活动订单金额">{{ form.serviceFeeAmt }}</a-descriptions-item>
      <a-descriptions-item label="活动订单实收金额" v-if="hasPurview(['0'])">{{ form.serviceActualAmt }}</a-descriptions-item>
    </a-descriptions>

    <a-descriptions :column="2" title="平台返现信息" v-if="hasPurview(['0'])">
      <a-descriptions-item label="平台利润(元)">{{ form.platformProfit }}</a-descriptions-item>
    </a-descriptions>

    <a-descriptions :column="2" title="大区返现信息" v-if="hasPurview(['0', '1'])">
      <a-descriptions-item label="大区编号">{{ form.regionNo }}</a-descriptions-item>
      <a-descriptions-item label="活动政策ID">{{ form.regionPolicyId }}</a-descriptions-item>
      <a-descriptions-item label="大区利润(元)">{{ form.regionProfit }}</a-descriptions-item>
      <a-descriptions-item label="活动返现金额(元)">{{ form.regionCashback }}</a-descriptions-item>
    </a-descriptions>

    <a-descriptions :column="2" title="运营中心返现信息" v-if="hasPurview(['0', '1', '2'])">
      <a-descriptions-item label="运营中心编号">{{ form.branchNo }}</a-descriptions-item>
      <a-descriptions-item label="活动政策ID">{{ form.branchPolicyId }}</a-descriptions-item>
      <a-descriptions-item label="运营中心利润(元)">{{ form.branchProfit }}</a-descriptions-item>
      <a-descriptions-item label="活动返现金额(元)">{{ form.branchCashback }}</a-descriptions-item>
    </a-descriptions>

    <a-divider orientation="left" :orientationMargin="0">代理商返现信息</a-divider>
    <template v-for="(item, key) in form.agentFeeObjList || []" :key="key">
      <a-descriptions :column="2">
        <a-descriptions-item label="代理商编号">{{ item.agentNo }}</a-descriptions-item>
        <a-descriptions-item label="活动政策ID">{{ item.agentPolicyId }}</a-descriptions-item>
        <a-descriptions-item label="代理商利润(元)">{{ item.agentProfit }}</a-descriptions-item>
        <a-descriptions-item label="活动返现金额(元)">{{ item.agentCashback }}</a-descriptions-item>
        <a-descriptions-item label="代理商级别" v-if="hasPurview(['0'])">{{ item.agentLevel }}</a-descriptions-item>
      </a-descriptions>
      <a-divider dashed style="margin-bottom: 15px" v-if="key !== form.agentFeeObjList?.length - 1"
      /></template>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';
import { hasPurview } from '@/utils/permission';
import { deepCopy } from '@/utils/util';

export default {
  props: {
    visible: Boolean,
    detail: Object,
    transChnList: Array
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {},
      hasPurview
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = deepCopy(Object.assign({}, props.detail));


        data.form.agentFeeObjList = data.form.agentCashbackObjList || [];

        if (!hasPurview(['5'])) {
          const oneLevelAgentKeys = Object.keys(props.detail).filter(key => key.startsWith('agent') && key !== 'agentCashbackObjList');
          const oneLevelAgentFeeObj = {};
          oneLevelAgentKeys.forEach(key => {
            oneLevelAgentFeeObj[key] = props.detail[key];
          });
          oneLevelAgentFeeObj.agentLevel = 1;
          data.form.agentFeeObjList.push(oneLevelAgentFeeObj);
        }
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
