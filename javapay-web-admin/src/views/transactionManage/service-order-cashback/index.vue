<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="交易支付单号">
              <a-input v-model:value.trim="where.orderNo" placeholder="请输入交易支付单号" allow-clear />
            </a-form-item>
            <a-form-item label="通道商户编号">
              <a-input v-model:value.trim="where.chnMerchNo" placeholder="请输入通道商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="活动支付订单号">
              <a-input v-model:value.trim="where.serviceOrderNo" placeholder="请输入活动支付订单号" allow-clear />
            </a-form-item>
            <a-form-item label="大区编号" v-if="hasPurview(['0'])">
              <a-input v-model:value.trim="where.regionNo" placeholder="请输入大区编号" allow-clear />
            </a-form-item>
            <a-form-item label="运营中心编号" v-if="hasPurview(['0', '1'])">
              <a-input v-model:value.trim="where.branchNo" placeholder="请输入运营中心编号" allow-clear />
            </a-form-item>
            <a-form-item label="代理商编号" v-if="hasPurview(['0', '1', '2'])">
              <a-input v-model:value.trim="where.agentNo" placeholder="请输入代理商编号" allow-clear />
            </a-form-item>
            <a-form-item label="活动订单类型">
              <a-select v-model:value="where.serviceOrderType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">服务费</a-select-option>
                <a-select-option :value="2">Sim流量费</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="交易日期">
              <a-date-picker v-model:value="where.transChnDate" format="YYYY-MM-DD" valueFormat="YYYYMMDD" />
            </a-form-item>
            <a-form-item label="开始日期">
              <a-date-picker v-model:value="where.searchBeginTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item label="结束日期">
              <a-date-picker v-model:value="where.searchEndTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table
          ref="table"
          row-key="id"
          :datasource="datasource"
          :columns="columns"
          :where="where"
          v-model:selection="selection"
          :scroll="{ x: 'max-content' }"
        >
          <template #toolbar>
            <a-descriptions title="统计信息" :column="4" size="small">
              <a-descriptions-item label="交易总笔数">{{ summary.transCount }}</a-descriptions-item>
              <a-descriptions-item label="活动订单总金额">{{ summary.orderAmount }}</a-descriptions-item>
              <!-- <a-descriptions-item v-if="hasPurview('0')" label="活动订单实扣总金额">{{ summary.actualAmount }}</a-descriptions-item> -->
              <a-descriptions-item v-if="hasPurview('0')" label="平台总利润" :span="2">{{ summary.platformProfit }}</a-descriptions-item>
              <template v-if="hasPurview(['0', '1'])">
                <a-descriptions-item label="大区及团队总返现">{{ summary.regionCashback }}</a-descriptions-item>
                <a-descriptions-item label="大区总利润">{{ summary.regionProfit }}</a-descriptions-item>
              </template>
              <template v-if="hasPurview(['0', '1', '2'])">
                <a-descriptions-item label="运营中心及团队总返现">{{ summary.branchCashback }}</a-descriptions-item>
                <a-descriptions-item label="运营中心总利润">{{ summary.branchProfit }}</a-descriptions-item>
              </template>
              <a-descriptions-item label="代理商及团队总返现">{{ summary.agentCashback }}</a-descriptions-item>
              <a-descriptions-item label="代理商总利润">{{ summary.agentProfit }}</a-descriptions-item>
            </a-descriptions>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'serviceOrderType'">
              <a-tag v-if="record.serviceOrderType === 1">服务费</a-tag>
              <a-tag v-else-if="record.serviceOrderType === 2">Sim流量费</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <ServiceOrderCashbackDetail v-model:visible="showDetail" :detail="current" :transChnList="transChnList" />
  </div>
</template>

<script>
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import { ServiceOrderCashbackApi } from '@/api/transactionManage/ServiceOrderCashbackApi';
import { hasPurview } from '@/utils/permission';
import ServiceOrderCashbackDetail from './service-order-cashback-detail.vue';
import dayjs from 'dayjs';

export default {
  name: ' ServiceOrderCashback',
  components: {
    ServiceOrderCashbackDetail
  },
  data() {
    return {
      // 表格搜索条件
      where: {
        searchBeginTime: dayjs().format('YYYY-MM-DD'),
        searchEndTime: dayjs().format('YYYY-MM-DD')
      },
      selection: [],
      current: null,
      showDetail: false,
      //交易通道列表
      transChnList: [],
      summary: {},
      // 表格列配置
      columns: [
        {
          title: '交易支付ID',
          dataIndex: 'transId',
          align: 'center',
          fixed: 'left'
        },
        {
          title: '交易支付单号',
          dataIndex: 'orderNo',
          align: 'center'
        },
        {
          title: '通道名称',
          dataIndex: 'chnCode',
          align: 'center',
          customRender: ({ text }) => {
            const item = this.transChnList.find(c => c.channelCode === text);
            return item?.channelName || '--';
          }
        },
        {
          title: '通道商户编号',
          dataIndex: 'chnMerchNo',
          align: 'center'
        },
        {
          title: '交易支付日期',
          dataIndex: 'transChnDate',
          align: 'center'
        },
        {
          title: '交易支付金额',
          dataIndex: 'transAmount',
          align: 'center'
        },
        {
          title: '活动订单ID',
          dataIndex: 'serviceOrderId',
          align: 'center'
        },
        {
          title: '活动支付订单号',
          dataIndex: 'serviceOrderNo',
          align: 'center'
        },
        {
          title: '活动订单类型',
          dataIndex: 'serviceOrderType',
          key: 'serviceOrderType',
          align: 'center'
        },
        {
          title: '配置ID',
          dataIndex: 'configId',
          align: 'center'
        },
        {
          title: '活动订单金额',
          dataIndex: 'serviceFeeAmt',
          align: 'center'
        },
        {
          title: '活动订单实收金额',
          dataIndex: 'serviceActualAmt',
          hideCol: !hasPurview(['0']),
          align: 'center'
        },
        {
          title: '大区编号',
          dataIndex: 'regionNo',
          hideCol: !hasPurview('0'),
          align: 'center'
        },
        {
          title: '运营中心编号',
          dataIndex: 'branchNo',
          hideCol: !hasPurview(['0', '1']),
          align: 'center'
        },
        {
          title: '代理商编号',
          dataIndex: 'agentNo',
          hideCol: !hasPurview(['0', '1', '2']),
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          width: 180,
          align: 'center',
          fixed: 'right'
        }
      ].filter(i => !i.hideCol)
    };
  },
  async mounted() {
    this.getSummaryData();
    this.transChnList = (await ChannelManageApi.list({ validStatus: 1 })) || [];
  },
  methods: {
    hasPurview,

    async handleTransFeeDetail(row) {
      this.current = { ...row.transFee, ...row };
      this.showTransFeeDetail = true;
    },

    reload() {
      this.selection = [];
      this.$refs.table.reload({ page: 1 });
      this.getSummaryData();
    },

    reset() {
      this.where = {
        searchBeginTime: dayjs().format('YYYY-MM-DD'),
        searchEndTime: dayjs().format('YYYY-MM-DD')
      };
      this.selection = [];
      this.$refs.table.reload({ page: 1, where: this.where });
      this.getSummaryData();
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    //列表查询统计
    async getSummaryData() {
      this.summary = (await ServiceOrderCashbackApi.sum(this.where)) || {};
    },

    datasource({ page, limit, where, orders }) {
      return ServiceOrderCashbackApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
