<template>
  <div class="ele-body">
    <a-spin :spinning="spinning" tip="下载中, 请稍候...">
      <!-- 搜索表单 -->
      <div class="block-interval">
        <a-card :bordered="false">
          <a-form layout="inline" :model="where">
            <a-row :gutter="[0, 16]">
              <a-form-item label="批次号">
                <a-input v-model:value.trim="where.batchNo" placeholder="请输入批次号" allow-clear />
              </a-form-item>

              <a-form-item label="操作人类型">
                <a-select v-model:value="where.userType" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option v-for="(item, key) in userTypeEnum" :key="key" :value="item.value">{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item label="操作人编号">
                <a-input v-model:value.trim="where.userNo" placeholder="请输入操作人编号" allow-clear />
              </a-form-item>
              <a-form-item class="ele-text-center">
                <a-space>
                  <a-button type="primary" @click="reload">查询</a-button>
                  <a-button @click="reset">重置</a-button>
                </a-space>
              </a-form-item>
            </a-row>
          </a-form>
        </a-card>
      </div>

      <!-- 表格 -->
      <div>
        <a-card :bordered="false" class="table-height">
          <ele-pro-table
            ref="table"
            row-key="id"
            :datasource="datasource"
            :columns="columns"
            :where="where"
            v-model:current="selectedRow"
            :scroll="{ x: 'max-content' }"
          >
            <!-- table上边工具栏 -->
            <template #toolbar>
              <a-space>
                <a-button type="primary" @click="handleGenerate()">
                  <template #icon>
                    <plus-outlined />
                  </template>
                  <span>批量生成</span>
                </a-button>

                <a-button @click="handleBind" v-if="!hasPurview(['1', '2'])">
                  <template #icon>
                    <edit-outlined />
                  </template>
                  <span>码牌归属绑定</span>
                </a-button>

                <a-button @click="handleDownloadZip">
                  <template #icon>
                    <download-outlined />
                  </template>
                  <span>下载码牌.zip</span>
                </a-button>

                <a-button @click="handleDownloadExcel">
                  <template #icon>
                    <download-outlined />
                  </template>
                  <span>下载码牌编码.excel</span>
                </a-button>
              </a-space>
            </template>

            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'status'">
                <a-tag color="green" v-if="record.status === 1">
                  <template #icon> <check-circle-outlined /> </template>已生成</a-tag
                >
                <a-tag color="red" v-else>
                  <template #icon> <close-circle-outlined /> </template>未生成</a-tag
                >
              </template>

              <template v-else-if="column.key === 'downloadStatus'">
                <template v-for="(item, key) in downloadStatusEnum">
                  <a-badge :key="key" v-if="item.value === record.downloadStatus" :color="item.mark" :text="item.label" />
                </template>
              </template>

              <template v-else-if="column.key === 'userType'">
                <template v-for="(item, key) in userTypeEnum">
                  <a-tag :key="key" v-if="item.value === record.userType" :color="item.mark"
                  ><template #icon> <user-outlined /> </template>{{ item.label }}</a-tag
                  >
                </template>
              </template>

              <!-- table操作栏按钮 -->
              <template v-else-if="column.key === 'action'">
                <a-space>
                  <a @click="handleEditStepStatus(record)">修改制作状态</a>
                </a-space>
              </template>
            </template>
          </ele-pro-table>
        </a-card>
      </div>

      <!-- 生成 -->
      <a-modal
        :width="500"
        v-if="showGenerate"
        v-model:visible="showGenerate"
        :confirm-loading="loading"
        title="批量生成"
        :mask-closable="false"
        :body-style="{ paddingBottom: '8px' }"
        @ok="onConfirm"
      >
        <a-form ref="form" :model="form" :rules="rules" :label-col="{ style: { width: '120px' } }">
          <a-form-item label="码牌生成数量" name="count">
            <a-input-number v-model:value="form.count" placeholder="请填写生成数量" :min="1" style="width: 100%" />
          </a-form-item>
          <a-form-item label="是否指定代理商" name="isAppointAgent">
            <a-radio-group name="isAppointAgent" v-model:value="form.isAppointAgent" @change="changeIsAppointAgent">
              <a-radio :value="0">否</a-radio>
              <a-radio :value="1">是</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item v-if="form.isAppointAgent === 1" label="代理商编号" name="agentNo" @click="agentSelectMethod" :readonly="true">
            <a-input v-model:value="form.agentNo" placeholder="请填写代理商编号" :disabled="hasPurview('3')" allow-clear />
          </a-form-item>

          <a-form-item label="费率政策" name="policyId" v-if="form.isAppointAgent === 1">
            <a-select v-model:value="form.policyId" placeholder="请选择" style="width: 100%" :disabled="!isPolicyCanEdit" allow-clear>
              <a-select-option v-for="({ id, policyDesc }, key) in policyList" :key="key" :value="id">{{ policyDesc }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 修改制作状态 -->
      <a-modal
        :width="500"
        v-if="showEditStepStatus"
        v-model:visible="showEditStepStatus"
        :confirm-loading="loading"
        title="修改制作状态"
        :mask-closable="false"
        :body-style="{ paddingBottom: '8px' }"
        @ok="onConfirm"
      >
        <a-form ref="form" :model="form" :rules="rules">
          <a-form-item label="状态" name="downloadStatus">
            <a-select v-model:value="form.downloadStatus" style="width: 100%" placeholder="请选择" allow-clear>
              <a-select-option v-for="(item, key) in downloadStatusEnum" :key="key" :value="item.value">{{ item.label }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 绑定 -->
      <BindQrCard v-model:visible="showBind" v-if="showBind" :data="current" />

      <!-- 下载码牌 -->
      <QrcodeCardImageDownload v-model:visible="showEditDownloadQRCodeImage" :data="current" @done="reload" />

      <!-- 下载码牌编码 -->
      <QrcodeCardSNDownload v-model:visible="showEditDownloadQRCodeSN" :data="current" @done="reload" />

      <!-- 代理商选择弹窗 -->
      <AgentSelect v-if="showAgentList" v-model:visible="showAgentList" @done="setAgentInfo" />
    </a-spin>
  </div>
</template>

<script>
import { message } from 'ant-design-vue';
import { QrcodeCardApi } from '@/api/qrcodeCard/QrcodeCardApi';
import BindQrCard from '../qrcode-manage/BindQrCard.vue';
import QrcodeCardImageDownload from './qrcode-card-image-download.vue';
import QrcodeCardSNDownload from './qrcode-card-sn-download.vue';
import { RatePolicyApi } from '@/api/businessTeam/rate-policy/RatePolicyApi';
import AgentSelect from '../batch-manage/agent-select.vue';
import { hasPurview } from '@/utils/permission';

const downloadStatusEnum = [
  { label: '未下载', value: 0, mark: 'orange' },
  { label: '制作中', value: 1, mark: 'blue' },
  { label: '制作完成', value: 2, mark: 'green' }
];

const userTypeEnum = [
  { label: '平台运营', value: 0, mark: 'purple' },
  { label: '大区', value: 1, mark: 'pink' },
  { label: '运营中心', value: 2, mark: 'blue' },
  { label: '代理商', value: 3, mark: 'cyan' }
];

export default {
  name: 'QrcodeCardBatch',
  components: {
    BindQrCard,
    QrcodeCardImageDownload,
    QrcodeCardSNDownload,
    AgentSelect
  },
  data() {
    return {
      showBind: false,
      downloadStatusEnum,
      userTypeEnum,
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '批次号',
          dataIndex: 'batchNo',
          align: 'center',
          width: 160
        },
        {
          title: '生成数量',
          dataIndex: 'count',
          width: 120,
          align: 'center'
        },
        {
          title: '生成状态',
          dataIndex: 'status',
          key: 'status',
          align: 'center',
          width: 120
        },
        {
          title: '下载次数',
          dataIndex: 'downloadCount',
          width: 120,
          align: 'center'
        },
        {
          title: '下载状态',
          dataIndex: 'downloadStatus',
          key: 'downloadStatus',
          align: 'center',
          width: 120
        },
        {
          title: '操作人名称',
          dataIndex: 'userName',
          align: 'center'
        },
        {
          title: '操作人编号',
          dataIndex: 'userNo',
          align: 'center'
        },
        {
          title: '操作人类型',
          dataIndex: 'userType',
          key: 'userType',
          align: 'center',
          width: 140
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right',
          width: 140
        }
      ],
      // 表格搜索条件
      where: {},
      form: {},
      rules: {
        count: [{ required: true, message: '请输入数量' }],
        agentNo: [{ required: true, message: '请输入代理商编号' }],
        downloadStatus: [{ required: true, message: '请选择状态' }],
        policyId: [{ required: true, message: '请选择费率政策' }]
      },
      showGenerate: false,
      showEditStepStatus: false,
      showEditDownloadQRCodeImage: false,
      showEditDownloadQRCodeSN: false,
      selectedRow: null,
      current: null,
      loading: false,
      spinning: false,
      //费率政策列表
      policyList: [],
      //是否展示代理商选择页面
      showAgentList: false,
      //费率政策是否可编辑
      isPolicyCanEdit: false
    };
  },
  methods: {
    reload() {
      this.selectedRow = null;
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.selectedRow = null;
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    datasource({ page, limit, where, orders }) {
      return QrcodeCardApi.findBatchPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    },

    handleBind() {
      if (!this.selectedRow) return message.warning('请选择一条数据');
      const { batchNo } = this.selectedRow;
      this.current = { batchNo, objAppointMode: 1 };
      this.showBind = true;
    },

    changeIsAppointAgent() {
      if (!this.form.isAppointAgent) {
        this.form.agentNo = '';
      }
    },

    async handleDownloadZip() {
      if (!this.selectedRow) return message.warning('请选择一条数据');
      this.showEditDownloadQRCodeImage = true;
      this.current = this.selectedRow;
    },

    async handleDownloadExcel() {
      if (!this.selectedRow) return message.warning('请选择一条数据');
      this.showEditDownloadQRCodeSN = true;
      this.current = this.selectedRow;
    },

    handleGenerate() {
      this.form = { count: '', isAppointAgent: 0, agentNo: '' };
      this.showGenerate = true;

      if (hasPurview('3')) {
        this.form.agentNo = localStorage.getItem('SASS_ORG_CODE');
        //费率政策可点击，并且请求费率政策列表
        this.isPolicyCanEdit = true;
        this.getPolicyList({
          userNo: this.form.agentNo,
          userType: 3,
          policyType: 1
        });
      }
    },

    handleEditStepStatus({ id }) {
      this.form = { id, downloadStatus: null };
      this.showEditStepStatus = true;
    },

    async onConfirm() {
      await this.$refs.form.validate();
      this.loading = true;
      if (this.showGenerate) {
        //如果为true，说明是批量生成的
        //批量生成的，需要根据policyId匹配到policyNo
        //创建新字典型数据用于接收 param数组值
        this.policyList.forEach(item => {
          if (item.id === this.form.policyId) {
            this.form.policyNo = item.policyNo;
          }
        });
      }

      let result = QrcodeCardApi[this.showGenerate ? 'addBatch' : 'editStepStatus'](this.form);
      result
        .then(result => {
          this.loading = false;
          // 提示添加成功
          message.success(result.message);
          this.loading = false;
          this.showGenerate = this.showEditStepStatus = false;
          this.reload();
        })
        .catch(() => {
          this.loading = false;
        });
    },

    //代理商选择点击
    agentSelectMethod() {
      this.showAgentList = true;
    },

    //从代理商列表返回，配置相关数据
    setAgentInfo(info) {
      this.showAgentList = false;
      this.form.agentNo = info.agentNo;

      //获取到代理商编号之后，费率政策可编辑
      this.isPolicyCanEdit = true;
      //请求费率政策列表

      this.getPolicyList({
        userNo: this.form.agentNo,
        userType: 3,
        policyType: 1
      });
    },

    //获取费率政策列表
    async getPolicyList(param) {
      const data = await RatePolicyApi.listOfOrg(param);
      this.policyList = data || [];
    },

    hasPurview
  }
};
</script>
