<template>
  <a-modal
    :width="1500"
    :visible="visible"
    title="代理商选择"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @cancel="cancel"
    @ok="save"
  >
    <div class="ele-body">
      <!-- 搜索框内容 -->
      <div class="block-interval">
        <a-card :bordered="false">
          <a-form layout="inline" :model="where">
            <a-row :gutter="[0, 16]">
              <a-form-item label="代理商名称">
                <a-input v-model:value.trim="where.agentName" placeholder="请输入代理商名称" allow-clear />
              </a-form-item>

              <a-form-item label="代理商简称">
                <a-input v-model:value.trim="where.agentSname" placeholder="请输入代理商简称" allow-clear />
              </a-form-item>

              <a-form-item label="代理商编号">
                <a-input v-model:value.trim="where.agentNo" placeholder="请输入代理商编号" allow-clear />
              </a-form-item>

              <a-form-item class="ele-text-center">
                <a-space>
                  <a-button type="primary" @click="reload">查询</a-button>
                  <a-button @click="reset">重置</a-button>
                </a-space>
              </a-form-item>
            </a-row>
          </a-form>
        </a-card>
      </div>

      <!-- 表格内容 -->
      <div>
        <a-card :bordered="false" class="table-height">
          <ele-pro-table
            ref="table"
            row-key="id"
            :datasource="datasource"
            :columns="columns"
            :where="where"
            v-model:current="selectedRow"
            :scroll="{ x: 'max-content' }"
          >
            <!-- 表体的操作 -->
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'remitType'">
                <a-tag v-if="record.remitType === 1" color="cyan">平台清算</a-tag>
                <a-tag v-else-if="record.remitType === 2" color="pink">自行提现</a-tag>
                <span v-else>--</span>
              </template>

              <template v-else-if="column.key === 'settleMode'">
                <a-tag v-if="record.settleMode === 1" color="cyan">日结</a-tag>
                <a-tag v-else-if="record.settleMode === 2" color="blue">月结</a-tag>
                <span v-else>--</span>
              </template>

              <template v-else-if="column.key === 'agentStatus'">
                <a-tag v-if="record.agentStatus?.checkStatus === 3" color="success">
                  <template #icon> <check-circle-outlined /> </template>正常</a-tag
                >
                <a-tag v-else-if="record.agentStatus?.checkStatus === 4" color="processing">
                  <template #icon> <sync-outlined :spin="true" /> </template> 审核中</a-tag
                >
                <a-tag v-else-if="record.agentStatus?.checkStatus === 5" color="error">
                  <template #icon> <close-circle-outlined /> </template> 驳回待编辑</a-tag
                >
              </template>
            </template>
          </ele-pro-table>
        </a-card>
      </div>
    </div>
  </a-modal>
</template>

  <script>
import { AgentCenterApi } from '@/api/businessTeam/agent-center/AgentCenterApi';
import { hasPurview } from '@/utils/permission';

export default {
  name: 'AgentSelect',
  props: {
    // 弹窗是否打开
    visible: Boolean
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      //表格查询条件
      where: {},
      //选择数据
      selectedRow: null,

      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left',
          hideCol: hasPurview(['1', '2'])
        },
        {
          title: '代理商编号',
          dataIndex: 'agentNo',
          align: 'center'
        },
        {
          title: '代理商名称',
          dataIndex: 'agentName'
        },
        {
          title: '代理商简称',
          dataIndex: 'agentSname'
        },
        {
          title: '归属大区编号',
          dataIndex: 'regionNo',
          align: 'center',
          hideCol: hasPurview(['1', '2'])
        },
        {
          title: '归属运营中心编号',
          dataIndex: 'branchNo',
          align: 'center',
          hideCol: hasPurview('2')
        },
        {
          title: '费率政策',
          dataIndex: 'policyDesc',
          hideCol: hasPurview('1')
        },
        {
          title: '法人姓名',
          dataIndex: 'legalName',
          align: 'center'
        },
        {
          title: '法人电话号码',
          dataIndex: 'legalTelMask',
          align: 'center'
        },
        {
          title: '联系人姓名',
          dataIndex: 'contactsName',
          align: 'center'
        },
        {
          title: '联系人电话号码',
          dataIndex: 'contactsTelMask',
          align: 'center'
        },
        {
          title: '清算类型',
          dataIndex: 'remitType',
          key: 'remitType',
          align: 'center',
          width: 100
        },
        {
          title: '结算方式',
          dataIndex: 'settleMode',
          key: 'settleMode',
          align: 'center',
          width: 100
        },
        {
          title: '审核状态',
          dataIndex: 'agentStatus',
          key: 'agentStatus',
          align: 'center',
          width: 100
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        }
      ].filter(i => !i.hideCol)
    };
  },
  methods: {
    save() {
      this.$emit('done', this.selectedRow);
      //关闭弹框
      this.updateVisible(false);

      this.where = {}; //清空查询条件
      this.selectedRow = null;
    },
    cancel() {
      this.where = {}; //清空查询条件
      this.selectedRow = null;
    },
    //重置
    reset() {
      this.where = {}; //清空查询条件
      this.selectedRow = null;
      /**
       * 为啥都清空了还要添加
       */
      this.$refs.table.reload({ page: 1, where: this.where });
    },
    //查询方法
    reload() {
      this.selectedRow = null;
      this.$refs.table.reload({ page: 1 });
    },
    /**
     * 更新编辑界面的弹框是否显示
     *
     * @param value true-显示，false-隐藏
     * <AUTHOR>
     * @date 2022/11/03 17:53
     */
    updateVisible(value) {
      this.$emit('update:visible', value);
    },

    datasource({ page, limit, where }) {
      return AgentCenterApi.findPage({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>

  <style>
</style>
