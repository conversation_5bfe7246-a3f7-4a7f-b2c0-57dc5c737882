<!-- 新增和编辑弹窗 -->
<template>
  <a-modal
    :width="600"
    :visible="visible"
    :confirm-loading="loading"
    title="下载码牌编码"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @cancel="cancel"
    @ok="save"
  >
    <a-form ref="form" :model="form">
      <a-form-item label="归属代理商编号:" name="agentNo">
        <a-input
          v-model:value="form.agentNo"
          placeholder="请输入归属代理商编号"
          @click="agentSelectMethod"
          :readonly="true"
          :disabled="hasPurview('3')"
          allow-clear
        />
      </a-form-item>
    </a-form>
  </a-modal>

  <!-- 代理商选择弹窗 -->
  <AgentSelect v-if="showAgentList" v-model:visible="showAgentList" @done="setAgentInfo" />
</template>

<script>
import { QrcodeCardApi } from '@/api/qrcodeCard/QrcodeCardApi';
import { message } from 'ant-design-vue';
import AgentSelect from './agent-select.vue';
import { hasPurview } from '@/utils/permission';

export default {
  name: 'QrcodeCardSNDownload',
  components: {
    AgentSelect
  },
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: Object.assign({}, this.data),
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false,
      //是否展示代理商选择页面
      showAgentList: false
    };
  },
  watch: {
    data() {
      if (this.data) {
        this.form = Object.assign({}, this.data);
        this.isUpdate = true;

        if (hasPurview('3')) {
          this.form.agentNo = localStorage.getItem('SASS_ORG_CODE');
        }
      } else {
        this.form = {};
        this.isUpdate = false;
      }
      if (this.$refs.form) {
        this.$refs.form.clearValidate();
      }
    }
  },
  methods: {
    //代理商选择点击
    agentSelectMethod() {
      this.showAgentList = true;
    },

    //从代理商列表返回，配置相关数据
    setAgentInfo(info) {
      this.showAgentList = false;
      this.form.agentNo = info.agentNo;
    },

    /**
     * 编辑
     */
    async save() {
      const { id, batchNo } = this.data;

      this.spinning = true;
      const data = await QrcodeCardApi.downloadExcel({ id, agentNo: this.form.agentNo }).catch(() => {
        this.spinning = false;
      });

      this.spinning = false;
      const fileReader = new FileReader();
      fileReader.onload = () => {
        try {
          // 说明是普通对象数据，后台转换失败
          const jsonData = JSON.parse(fileReader.result);
          message.error(jsonData.message);
          console.log('xinxi' + jsonData);
        } catch (err) {
          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);
          // 触发父组件done事件
          this.$emit('done');

          // 解析对象失败，说明是正常的文件流
          let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.style.display = 'none';
          a.href = url;
          a.download = `码牌编码-${batchNo}.xlsx`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }
      };
      fileReader.readAsText(data);
    },

    cancel() {
      this.form = Object.assign({}, this.data);
      this.$refs.form.clearValidate();
    },

    /**
     * 控制显示
     */
    updateVisible(value) {
      this.$emit('update:visible', value);
    },

    hasPurview
  }
};
</script>
