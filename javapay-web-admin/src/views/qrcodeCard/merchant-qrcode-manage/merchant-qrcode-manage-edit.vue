<!-- 新增和编辑弹窗 -->
<template>
  <a-modal
    :width="800"
    :visible="visible"
    :confirm-loading="loading"
    title="修改"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @cancel="cancel"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 7 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 17 }, sm: { span: 24 } }"
    >
      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="固定金额:" name="amount">
            <a-input v-model:value="form.amount" placeholder="请输入固定金额" allow-clear />
          </a-form-item>

          <a-form-item label="启用状态" name="enableStatus">
            <a-radio-group v-model:value="form.enableStatus" name="enableStatus">
              <a-radio :value="1">启用</a-radio>
              <a-radio :value="0">禁用</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="码牌名称:" name="qrcodeName">
            <a-input v-model:value="form.qrcodeName" placeholder="请输入码牌名称" allow-clear />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>
  
<script>
import { MerchantQrcodeApi } from '@/api/qrcodeCard/MerchantQrcodeApi';
import { message } from 'ant-design-vue';

export default {
  name: 'MerchantQRCodeManageEdit',
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: Object.assign({}, this.data),
      // 表单验证规则
      rules: {
        enableStatus: [{ required: true, message: '请选择启用状态' }],
        qrcodeName: [{ required: true, message: '请输入码牌名称' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false
    };
  },
  watch: {
    data() {
      if (this.data) {
        this.form = Object.assign({}, this.data);
        this.isUpdate = true;
      } else {
        this.form = {};
        this.isUpdate = false;
      }
      if (this.$refs.form) {
        this.$refs.form.clearValidate();
      }
    }
  },
  methods: {
    /**
     * 编辑
     */
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      // 执行编辑或修改
      result = MerchantQrcodeApi.edit(this.form);

      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 如果是新增，则form表单置空
          if (!this.isUpdate) {
            this.form = {};
          }

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    cancel() {
      this.form = Object.assign({}, this.data);
      this.$refs.form.clearValidate();
    },

    /**
     * 控制显示
     */
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
  