<template>
  <div class="ele-body">
    <!-- 搜索框内容 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
            </a-form-item>

            <a-form-item label="码牌编号">
              <a-input v-model:value.trim="where.qrcodeSn" placeholder="请输入码牌编号" allow-clear />
            </a-form-item>

            <a-form-item label="码牌名称">
              <a-input v-model:value.trim="where.qrcodeName" placeholder="请输入码牌名称" allow-clear />
            </a-form-item>

            <a-form-item label="批次号">
              <a-input v-model:value.trim="where.batchNo" placeholder="请输入批次号" allow-clear />
            </a-form-item>

            <a-form-item label="启用状态">
              <a-select v-model:value="where.enableStatus" style="width: 120px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">启用</a-select-option>
                <a-select-option :value="0">禁用</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="商户归属的代理商编号">
              <a-input v-model:value.trim="where.agentNoToMerch" placeholder="请输入商户归属的代理商编号" allow-clear />
            </a-form-item>

            <a-form-item label="码牌归属的代理商编号">
              <a-input v-model:value.trim="where.agentNoToQrcode" placeholder="请输入码牌归属的代理商编号" allow-clear />
            </a-form-item>

            <a-form-item label="生成码牌的用户编号">
              <a-input v-model:value.trim="where.userNo" placeholder="请输入生成码牌的用户编号" allow-clear />
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格内容 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- 表体的操作 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'enableStatus'">
              <a-tag v-if="record.enableStatus === 0" color="pink">禁用</a-tag>
              <a-tag v-else-if="record.enableStatus === 1" color="cyan">启用</a-tag>
            </template>

            <template v-else-if="column.key === 'userType'">
              <a-tag v-if="record.userType === 0" color="pink">平台运营</a-tag>
              <a-tag v-else-if="record.userType === 1" color="cyan">大区</a-tag>
              <a-tag v-else-if="record.userType === 2" color="blue">运营中心</a-tag>
              <a-tag v-else-if="record.userType === 3" color="代理商">关停交易</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleEdit(record)">修改</a>
                <a-divider type="vertical" />
                <a @click="handleShowDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 新增编辑 -->
    <MerchantQRCodeManageEdit v-model:visible="showEdit" :data="current" @done="reload" />

    <!-- 详情页面 -->
    <MerchantQRCodeManageDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>
  
  <script>
import { MerchantQrcodeApi } from '@/api/qrcodeCard/MerchantQrcodeApi';
import MerchantQRCodeManageEdit from './merchant-qrcode-manage-edit.vue';
import MerchantQRCodeManageDetail from './merchant-qrcode-manage-detail.vue';
import { hasPurview } from '@/utils/permission';

export default {
  name: 'ChannelManage',
  components: {
    MerchantQRCodeManageEdit,
    MerchantQRCodeManageDetail
  },
  data() {
    return {
      //表格查询条件
      where: {},
      //展示编辑页面传的参数
      current: null,
      //是否展示新增或者是编辑页面
      showEdit: false,
      //是否展示详情页面
      showDetail: false,

      //表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center'
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '码牌ID',
          dataIndex: 'qrcodeId',
          align: 'center'
        },
        {
          title: '码牌编号',
          dataIndex: 'qrcodeSn',
          align: 'center'
        },
        {
          title: '码牌名称',
          dataIndex: 'qrcodeName',
          align: 'center'
        },
        {
          title: '固定金额',
          dataIndex: 'amount',
          align: 'center'
        },
        {
          title: '批次号',
          dataIndex: 'batchNo',
          align: 'center'
        },
        {
          title: '启用状态',
          dataIndex: 'enableStatus',
          key: 'enableStatus',
          align: 'center'
        },
        {
          title: '生成码牌的用户类型',
          dataIndex: 'userType',
          key: 'userType',
          align: 'center',
          hideCol: hasPurview(['1', '2', '3'])
        },
        {
          title: '生成码牌的用户编号',
          dataIndex: 'userNo',
          align: 'center',
          hideCol: hasPurview(['1', '2', '3'])
        },
        {
          title: '商户归属的大区编号',
          dataIndex: 'regionNoOfMerch',
          align: 'center',
          hideCol: hasPurview(['1', '2', '3'])
        },
        {
          title: '商户归属的运营中心编号',
          dataIndex: 'branchNoOfMerch',
          align: 'center',
          hideCol: hasPurview(['2', '3'])
        },
        {
          title: '商户归属的代理商编号',
          dataIndex: 'agentNoToMerch',
          align: 'center',
          hideCol: hasPurview('3')
        },
        {
          title: '码牌归属的代理商编号',
          dataIndex: 'agentNoToQrcode',
          align: 'center',
          hideCol: hasPurview(['1', '2', '3'])
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 150,
          align: 'center'
        }
      ].filter(i => !i.hideCol),
    };
  },
  methods: {
    //查询方法
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    //重置
    reset() {
      this.where = {}; //清空查询条件
      /**
       * 为啥都清空了还要添加
       */
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    //新建或者编辑
    handleEdit(row) {
      this.showEdit = true;
      this.current = row;
    },

    //展示详情
    handleShowDetail(row) {
      this.showDetail = true;
      this.current = row;
    },

    //获取数据方法
    datasource({ page, limit, where }) {
      return MerchantQrcodeApi.findPages({ ...where, pageNo: page, pageSize: limit });
    },
    
  }
};
</script>
  
  <style>
</style>