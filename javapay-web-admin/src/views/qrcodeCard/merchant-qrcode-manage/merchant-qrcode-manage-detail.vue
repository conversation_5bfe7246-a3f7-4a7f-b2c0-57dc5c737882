<template>
  <a-modal
    :width="1000"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2" bordered>
      <a-descriptions-item label="商户编号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="码牌ID">{{ form.qrcodeId }}</a-descriptions-item>
      <a-descriptions-item label="码牌编号">{{ form.qrcodeSn }}</a-descriptions-item>
      <a-descriptions-item label="码牌名称">{{ form.qrcodeName }}</a-descriptions-item>
      <a-descriptions-item label="固定金额">{{ form.amount }}</a-descriptions-item>
      <a-descriptions-item label="批次号">{{ form.batchNo }}</a-descriptions-item>
      <a-descriptions-item label="启用状态">
        <a-tag v-if="form.enableStatus === 0" color="warning">禁用</a-tag>
        <a-tag v-else-if="form.enableStatus === 1" color="processing">启用</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="生成码牌的用户类型" v-if="!hasPurview(['1', '2', '3'])">
        <a-tag v-if="form.userType === 0" color="warning">平台运营</a-tag>
        <a-tag v-else-if="form.userType === 1" color="processing">大区</a-tag>
        <a-tag v-else-if="form.userType === 2" color="success">运营中心</a-tag>
        <a-tag v-else-if="form.userType === 3" color="error">关停交易</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="生成码牌的用户编号" v-if="!hasPurview(['1', '2', '3'])">{{ form.userNo }}</a-descriptions-item>
      <a-descriptions-item label="商户归属的大区编号" v-if="!hasPurview(['1', '2', '3'])">{{ form.regionNoOfMerch }}</a-descriptions-item>
      <a-descriptions-item label="商户归属的运营中心编号" v-if="!hasPurview(['2', '3'])">{{ form.branchNoOfMerch }}</a-descriptions-item>
      <a-descriptions-item label="商户归属的代理商编号" v-if="!hasPurview('3')">{{ form.agentNoToMerch }}</a-descriptions-item>

      <a-descriptions-item label="码牌归属的代理商编号" v-if="!hasPurview(['1', '2', '3'])">{{ form.agentNoToQrcode }}</a-descriptions-item>
    </a-descriptions>


    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>
  
  <script>
import { reactive, toRefs, watchEffect } from 'vue';
import { hasPurview } from '@/utils/permission';

export default {
  name: 'MerchantQRCodeManageDetail',
  props: {
    visible: Boolean,
    detail: Object,
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible,
      hasPurview,
    };
  }
};
</script>
  