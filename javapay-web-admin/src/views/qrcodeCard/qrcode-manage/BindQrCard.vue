<template>
  <a-modal
    :width="600"
    :visible="visible"
    :confirm-loading="loading"
    title="码牌归属绑定"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 6 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 18 }, sm: { span: 24 } }"
    >
      <a-form-item label="批次号" name="batchNo" v-if="form.objAppointMode === 1">
        <a-input v-model:value="form.batchNo" placeholder="请输入批次号" allow-clear disabled />
      </a-form-item>
      <a-form-item label="代理商编号" name="agentNo">
        <a-input
          v-model:value="form.agentNo"
          placeholder="请输入归属代理商编号"
          @click="agentSelectMethod"
          :readonly="true"
          :disabled="hasPurview('3')"
          allow-clear
        />
      </a-form-item>
      <a-form-item label="费率政策" name="policyId">
        <a-select v-model:value="form.policyId" placeholder="请选择" style="width: 100%" :disabled="!isPolicyCanEdit" allow-clear>
          <a-select-option v-for="({ id, policyDesc }, key) in policyList" :key="key" :value="id">{{ policyDesc }}</a-select-option>
        </a-select>
      </a-form-item>
      <template v-if="form.objAppointMode === 2">
        <a-form-item label="开始SN号" name="startSn">
          <a-input v-model:value="form.startSn" placeholder="请输入开始SN号" allow-clear />
        </a-form-item>
        <a-form-item label="结束SN号" name="endSn">
          <a-input v-model:value="form.endSn" placeholder="请输入结束SN号" allow-clear />
        </a-form-item>
      </template>
    </a-form>
  </a-modal>

  <!-- 代理商选择弹窗 -->
  <AgentSelect v-if="showAgentList" v-model:visible="showAgentList" @done="setAgentInfo" />
</template>

<script>
import { message } from 'ant-design-vue';
import { QrcodeCardApi } from '@/api/qrcodeCard/QrcodeCardApi';
import { RatePolicyApi } from '@/api/businessTeam/rate-policy/RatePolicyApi';
import AgentSelect from '../batch-manage/agent-select.vue';
import { hasPurview } from '@/utils/permission';

export default {
  name: 'BindQRCard',
  components: {
    AgentSelect
  },
  props: {
    visible: Boolean,
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {},
      //费率政策数组
      policyList: [],
      //是否展示代理商选择页面
      showAgentList: false,
      //费率政策是否可编辑
      isPolicyCanEdit: false,
      // 表单验证规则
      rules: {
        agentNo: [{ required: true, message: '请输入归属代理商编号' }],
        batchNo: [{ required: true, message: '请输入批次号' }],
        startSn: [{ required: true, message: '请输入开始SN号' }],
        endSn: [{ required: true, message: '请输入结束SN号' }],
        policyId: [{ required: true, message: '请选择费率政策' }]
      },
      // 提交状态
      loading: false
    };
  },
  mounted() {
    this.form = Object.assign({}, this.data);

    if (hasPurview('3')) {
      this.form.agentNo = localStorage.getItem('SASS_ORG_CODE');
      //费率政策可点击，并且请求费率政策列表
      this.isPolicyCanEdit = true;
      this.getPolicyList({
        userNo: this.form.agentNo,
        userType: 3,
        policyType: 1
      });
    }
  },
  methods: {
    //代理商选择点击
    agentSelectMethod() {
      this.showAgentList = true;
    },

    //从代理商列表返回，配置相关数据
    setAgentInfo(info) {
      this.showAgentList = false;
      this.form.agentNo = info.agentNo;

      //获取到代理商编号之后，费率政策可编辑
      this.isPolicyCanEdit = true;
      //请求费率政策列表

      this.getPolicyList({
        userNo: this.form.agentNo,
        userType: 3,
        policyType: 1
      });
    },

    //获取费率政策列表
    async getPolicyList(param) {
      const data = await RatePolicyApi.listOfOrg(param);
      this.policyList = data || [];
    },

    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      //如果为true，说明是批量生成的
      //批量生成的，需要根据policyId匹配到policyNo
      //创建新字典型数据用于接收 param数组值
      this.policyList.forEach(item => {
        if (item.id === this.form.policyId) {
          this.form.policyNo = item.policyNo;
        }
      });

      QrcodeCardApi.bindQrcode(this.form)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    },

    hasPurview
  }
};
</script>
