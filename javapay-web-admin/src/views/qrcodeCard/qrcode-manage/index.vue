<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="码牌编号">
              <a-input v-model:value.trim="where.qrcodeSn" placeholder="请输入码牌编号" allow-clear />
            </a-form-item>

            <a-form-item label="生成码牌的用户编号" v-if="!hasPurview(['1', '2', '3'])">
              <a-input v-model:value.trim="where.userNo" placeholder="请输入生成码牌的用户编号" allow-clear />
            </a-form-item>

            <a-form-item label="归属代理商编号">
              <a-input v-model:value.trim="where.agentNo" placeholder="请输入归属代理商编号" allow-clear />
            </a-form-item>

            <a-form-item label="批次编号">
              <a-input v-model:value.trim="where.batchNo" placeholder="请输入批次号" allow-clear />
            </a-form-item>

            <a-form-item label="绑定状态">
              <a-select v-model:value="where.bindingStatus" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">未绑定</a-select-option>
                <a-select-option :value="1">已绑定</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="操作人类型" v-if="!hasPurview(['1', '2', '3'])">
              <a-select v-model:value="where.userType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option v-for="(item, key) in userTypeEnum" :key="key" :value="item.value">{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="费率政策ID">
              <a-input v-model:value.trim="where.policyId" placeholder="请输入批次号" allow-clear />
            </a-form-item>

            <a-form-item label="费率政策编号">
              <a-input v-model:value.trim="where.policyNo" placeholder="请输入批次号" allow-clear />
            </a-form-item>

            <a-form-item label="开始日期">
              <a-date-picker v-model:value="where.searchBeginTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item label="结束日期">
              <a-date-picker v-model:value="where.searchEndTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table
          ref="table"
          row-key="id"
          :datasource="datasource"
          :columns="columns"
          :where="where"
          :scroll="{ x: 'max-content' }"
          v-model:current="selectedRow"
        >
          <!-- table上边工具栏 -->
          <template #toolbar>
            <a-space>
              <a-button @click="handleBind()" v-if="!hasPurview(['1', '2'])">
                <template #icon>
                  <edit-outlined />
                </template>
                <span>码牌归属绑定</span>
              </a-button>
            </a-space>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'openStatus'">
              <a-switch :checked="record.openStatus === 1" @change="checked => editState(checked, record)" />
            </template>

            <template v-else-if="column.key === 'bindingStatus'">
              <a-tag color="green" v-if="record.bindingStatus === 1">
                <template #icon> <check-circle-outlined /> </template>已绑定</a-tag
              >
              <a-tag v-else>
                <template #icon> <close-circle-outlined /> </template>未绑定</a-tag
              >
            </template>

            <template v-else-if="column.key === 'userType'">
              <template v-for="(item, key) in userTypeEnum" :key="key">
                <a-tag v-if="item.value === record.userType" :color="item.mark"
                ><template #icon> <user-outlined /> </template>{{ item.label }}
                </a-tag>
              </template>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
                <!-- <a-divider type="vertical" />
                <a @click="handleEditInfo(record)">修改</a> -->
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 编辑基本信息 -->
    <EditInfo v-model:visible="showEditInfo" :data="current" @done="reload" />

    <!-- 详情 -->
    <DetailInfo v-model:visible="showDetail" :detail="current" />

    <!-- 绑定 -->
    <BindQrCard v-model:visible="showBind" v-if="showBind" :data="current" @done="reload" />
  </div>
</template>

<script>
import { QrcodeCardApi } from '@/api/qrcodeCard/QrcodeCardApi';
import { message } from 'ant-design-vue';
import EditInfo from './EditInfo.vue';
import DetailInfo from './DetailInfo.vue';
import BindQrCard from './BindQrCard.vue';
import { RatePolicyApi } from '@/api/businessTeam/rate-policy/RatePolicyApi';
import { hasPurview } from '@/utils/permission';

export const userTypeEnum = [
  { label: '平台运营', value: 0, mark: 'purple' },
  { label: '大区', value: 1, mark: 'pink' },
  { label: '运营中心', value: 2, mark: 'blue' },
  { label: '代理商', value: 3, mark: 'cyan' }
];

export default {
  name: 'QrcodeManage',
  components: { EditInfo, DetailInfo, BindQrCard },
  data() {
    return {
      userTypeEnum,
      selectedRow: null,
      showBind: false,
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '码牌编号',
          dataIndex: 'qrcodeSn',
          align: 'center'
        },
        {
          title: '生成码牌的用户ID',
          dataIndex: 'userId',
          align: 'center',
          hideCol: hasPurview(['1', '2', '3'])
        },
        {
          title: '生成码牌角色编号',
          dataIndex: 'userNo',
          align: 'center',
          hideCol: hasPurview(['1', '2', '3'])
        },
        {
          title: '生成码牌的用户类型',
          dataIndex: 'userType',
          key: 'userType',
          width: 160,
          align: 'center'
        },
        {
          title: '批次号',
          dataIndex: 'batchNo',
          align: 'center'
        },
        {
          title: '费率政策ID',
          dataIndex: 'policyId',
          align: 'center'
        },
        {
          title: '费率政策编号',
          dataIndex: 'policyNo',
          align: 'center'
        },
        {
          title: '归属代理商ID',
          dataIndex: 'agentId'
        },
        {
          title: '归属代理商编号',
          dataIndex: 'agentNo'
        },
        {
          title: '绑定状态',
          dataIndex: 'bindingStatus',
          key: 'bindingStatus',
          width: 160,
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right',
          width: 100
        }
      ].filter(i => !i.hideCol),
      // 表格搜索条件
      where: {},
      current: null,
      showDetail: false,
      showEditInfo: false,
      policyList: [] //费率政策数组
    };
  },
  methods: {
    reload() {
      this.selectedRow = null;
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.selectedRow = null;
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },
    handleBind() {
      this.current = { objAppointMode: 2 };
      this.showBind = true;
    },

    async editState(checked, row) {
      const qrcodeSn = row.qrcodeSn;
      const status = checked ? 1 : 0;
      const result = await QrcodeCardApi.changeQrcodeStatus({ qrcodeSn, openStatus: status });
      message.success(result.message);
      row.openStatus = status;
    },

    handleEditInfo({ qrcodeSn, qrcodeName, remark, amount }) {
      this.current = { qrcodeSn, qrcodeName, remark, amount };
      this.showEditInfo = true;
    },

    //获取费率政策列表
    async getPolicyList() {
      const data = await RatePolicyApi.listOfOrg({ agentNo: '', userType: 3, policyType: 1});
      this.policyList = data || [];
    },

    datasource({ page, limit, where, orders }) {
      return QrcodeCardApi.findInfoPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    },

    hasPurview
  }
};
</script>
