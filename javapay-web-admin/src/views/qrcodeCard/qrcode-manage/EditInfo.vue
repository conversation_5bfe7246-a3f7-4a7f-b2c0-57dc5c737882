<template>
  <a-modal
    :width="600"
    :visible="visible"
    :confirm-loading="loading"
    title="修改"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @cancel="cancel"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 6 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 18 }, sm: { span: 24 } }"
    >
      <a-form-item label="码牌名称" name="qrcodeName">
        <a-input v-model:value="form.qrcodeName" placeholder="请输入码牌名称" allow-clear />
      </a-form-item>
      <a-form-item label="码牌描述" name="remark">
        <a-textarea v-model:value="form.remark" placeholder="请输入码牌描述" :auto-size="{ minRows: 2, maxRows: 5 }" />
      </a-form-item>
      <a-form-item label="固定金额(元)" name="amount">
        <a-input-number v-model:value="form.amount" placeholder="请输入固定金额" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { QrcodeCardApi } from '@/api/qrcodeCard/QrcodeCardApi';

export default {
  props: {
    visible: Boolean,
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: Object.assign({}, this.data),
      // 表单验证规则
      rules: {
        qrcodeName: [{ required: true, message: '请输入码牌名称' }],
        amount: [{ required: true, message: '请输入固定金额' }],
      },
      // 提交状态
      loading: false
    };
  },
  watch: {
    data() {
      if (this.data) {
        this.form = Object.assign({}, this.data);
      }
      if (this.$refs.form) {
        this.$refs.form.clearValidate();
      }
    }
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      QrcodeCardApi.editQrcodeInfo (this.form)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    cancel() {
      this.form = Object.assign({}, this.data);
      this.$refs.form.clearValidate();
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
