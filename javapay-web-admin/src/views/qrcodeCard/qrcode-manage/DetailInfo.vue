<template>
  <a-modal
    :width="800"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
    :footer="null"
  >
    <a-descriptions :column="2">
      
      <a-descriptions-item label="码牌编号">{{ form.qrcodeSn }}</a-descriptions-item>
      <a-descriptions-item label="生成码牌的用户ID" v-if="!hasPurview(['1', '2', '3'])">{{ form.userId }}</a-descriptions-item>
      <a-descriptions-item label="生成码牌角色编号" v-if="!hasPurview(['1', '2', '3'])">{{ form.userNo }}</a-descriptions-item>
      <a-descriptions-item label="生成码牌的用户类型">
        <template v-for="(item, key) in userTypeEnum">
          <a-tag :key="key" v-if="item.value === form.userType" :color="item.mark">{{ item.label }}</a-tag>
        </template>
      </a-descriptions-item>
      <a-descriptions-item label="批次号">{{ form.batchNo }}</a-descriptions-item>

      <a-descriptions-item label="费率政策ID">{{ form.policyId }}</a-descriptions-item>
      <a-descriptions-item label="费率政策编号">{{ form.policyNo }}</a-descriptions-item>
      <a-descriptions-item label="归属代理商ID">{{ form.agentId }}</a-descriptions-item>
      <a-descriptions-item label="归属代理商编号">{{ form.agentNo }}</a-descriptions-item>
      <a-descriptions-item label="绑定状态">
        <a-tag color="green" v-if="form.bindingStatus === 1">
          <template #icon> <check-circle-outlined /> </template>已绑定
        </a-tag>
        <a-tag color="red" v-else>
          <template #icon> <close-circle-outlined /> </template>未绑定
        </a-tag>
      </a-descriptions-item>
    </a-descriptions>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';
import { hasPurview } from '@/utils/permission';

const userTypeEnum = [
  { label: '平台运营', value: 0, mark: 'purple' },
  { label: '大区', value: 1, mark: 'pink' },
  { label: '运营中心', value: 2, mark: 'blue' },
  { label: '代理商', value: 3, mark: 'cyan' }
];

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible,
      userTypeEnum,
      hasPurview
    };
  }
};
</script>
