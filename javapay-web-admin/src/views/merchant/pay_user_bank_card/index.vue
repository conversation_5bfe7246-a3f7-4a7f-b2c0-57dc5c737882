<template>
  <div class="ele-body">
    <!-- 搜索框内容 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="银行户名">
              <a-input v-model:value.trim="where.bankAccountName" placeholder="请输入银行户名" allow-clear />
            </a-form-item>

            <a-form-item label="付款用户类型">
              <a-select v-model:value="where.payuserType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">平台付款用户</a-select-option>
                <a-select-option :value="2">家庭会员用户</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="银行卡类型">
              <a-select v-model:value="where.cardType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">借记卡</a-select-option>
                <a-select-option :value="2">贷记卡</a-select-option>
                <a-select-option :value="3">准贷记卡</a-select-option>
                <a-select-option :value="4">预付费卡</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="是否签约">
              <a-select v-model:value="where.isSign" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">已签约</a-select-option>
                <a-select-option :value="0">未签约</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="有效状态">
              <a-select v-model:value="where.validStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">无效</a-select-option>
                <a-select-option :value="1">有效</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- 表体的操作 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'cardType'">
              <a-tag v-if="record.cardType === 1" color="pink">借记卡</a-tag>
              <a-tag v-if="record.cardType === 2" color="blue">贷记卡</a-tag>
              <a-tag v-if="record.cardType === 3" color="cyan">准贷记卡</a-tag>
              <a-tag v-if="record.cardType === 4" color="purple">预付费卡</a-tag>
            </template>

            <template v-if="column.key === 'payuserType'">
              <a-tag v-if="record.payuserType === 1" color="pink">平台付款用户</a-tag>
              <a-tag v-if="record.payuserType === 2" color="blue">家庭会员用户</a-tag>
            </template>

            <template v-if="column.key === 'validStatus'">
              <a-tag v-if="record.validStatus === 0" color="pink">无效</a-tag>
              <a-tag v-if="record.validStatus === 1" color="blue">有效</a-tag>
            </template>

            <template v-if="column.key === 'isSign'">
              <a-tag v-if="record.isSign === 0" color="pink">未签约</a-tag>
              <a-tag v-if="record.isSign === 1" color="blue">已签约</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-popconfirm title="确定要删除此行数据吗？" @confirm="remove(record)">
                  <a class="ele-text-danger">删除</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>
  </div>
</template>

<script>
import { PayUserBankCardApi } from '@/api/merchant/PayUserBankCardApi';
import { message } from 'ant-design-vue';

export default {
  name: 'PayUserBankCard',
  data() {
    return {
      //表格查询条件
      where: {},
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center'
        },
        {
          title: '付款用户ID',
          dataIndex: 'payuserId',
          align: 'center'
        },
        {
          title: '银行户名',
          dataIndex: 'bankAccountName',
          align: 'center'
        },
        {
          title: '银行预留身份证号',
          dataIndex: 'idCardNoMask',
          align: 'center'
        },
        {
          title: '银行预留手机号',
          dataIndex: 'mobileMask',
          align: 'center'
        },
        {
          title: '银行账号',
          dataIndex: 'bankAccountNoMask',
          align: 'center'
        },
        {
          title: '银行名称',
          dataIndex: 'bankName',
          align: 'center'
        },
        {
          title: '银行行别代码',
          dataIndex: 'typeCode',
          align: 'center'
        },
        {
          title: '银行卡类型',
          dataIndex: 'cardType',
          key: 'cardType',
          align: 'center'
        },
        {
          title: '付款用户类型',
          dataIndex: 'payuserType',
          key: 'payuserType',
          align: 'center'
        },
        {
          title: '有效状态',
          dataIndex: 'validStatus',
          key: 'validStatus',
          align: 'center'
        },
        {
          title: '是否签约',
          dataIndex: 'isSign',
          key: 'isSign',
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right',
          width: 150
        }
      ]
    };
  },

  methods: {
    //查询方法
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    //重置
    reset() {
      this.where = {}; //清空查询条件

      this.$refs.table.reload({ page: 1, where: this.where });
    },

    //删除
    async remove(row) {
      const result = await PayUserBankCardApi.delete({ id: row.id });
      message.success(result.message);
      this.reload();
    },

    //获取数据
    datasource({ page, limit, where, orders }) {
      return PayUserBankCardApi.getPayUserBankCardPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>

<style>
</style>