<template>
  <a-modal
    :width="800"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新增'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" layout="vertical">
      <!-- 文件信息 -->
      <a-row :gutter="[16, 16]">
        <template v-for="(item, key) in fileList" :key="key">
          <a-col :span="6">
            <a-form-item :label="`${item.label}`" :required="item.required">
              <a-upload
                v-model:file-list="item.fileData"
                accept=".png, .jpg, .jpeg"
                :max-count="1"
                list-type="picture-card"
                :before-upload="file => handleSelectFile(file, item)"
                @preview="handlePreviewFile"
              >
                <div v-if="!item.fileData?.length">
                  <plus-outlined />
                  <div style="margin-top: 8px">Upload</div>
                </div>
              </a-upload>
            </a-form-item>
          </a-col>
        </template>
      </a-row>

      <!-- 预览图片 -->
      <a-image
        :style="{ display: 'none' }"
        :src="previewImage"
        :preview="{ visible: previewVisible, onVisibleChange: setPreviewVisible }"
      />
    </a-form>
  </a-modal>
</template>

<script>
import { Upload, message } from 'ant-design-vue';
import { compressorImage } from '@/utils/image-compressor-util';
import { FastMerchImageApi } from '@/api/merchant/FastMerchImageApi';

export default {
  props: {
    visible: Boolean,
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      form: {},
      // 文件列表
      fileList: [
        {
          label: '门头照',
          fileType: 11,
          fileData: [],
          required: true
        },

        {
          label: '经营场所照片',
          fileType: 12,
          fileData: [],
          required: true
        },

        {
          label: '收银台照片',
          fileType: 13,
          fileData: [],
          required: true
        }
      ],
      // 预览图片
      previewImage: '',
      previewVisible: false,
      // 提交loading
      loading: false,
      // 是否更新
      isUpdate: false
    };
  },
  mounted() {
    if (this.data) {
      this.isUpdate = true;

      // 处理表单
      this.form = Object.assign({}, this.data);

      this.getFiles();
    }
  },
  methods: {
    async getFiles() {
      const data = await FastMerchImageApi.detail({ id: this.data.id });
      const fileListMap = data.imageJsonList || [];
      // 处理图片
      if (fileListMap?.length) {
        this.fileList.forEach(f => {
          const item = fileListMap.find(i => i.imageType === f.fileType);
          if (item) {
            f.fileData = item.imagePath ? [{ url: item.imagePath }] : [];
            f.fileName = item.imageName;
          }
        });
      }
    },

    /**
     * 提交表单
     */
    async save() {
      // 校验文件
      await this.validateFileList();

      // 修改加载框为正在加载
      this.loading = true;

      // 处理图片
      const fileDTOList = this.fileList
        .map(item => {
          if (item.fileData?.[0]) {
            return {
              fileName: item.fileName,
              fileData: item.fileData[0].url,
              fileType: item.fileType,
              suffixType: item.suffixType || 'png'
            };
          }
          return null;
        })
        .filter(f => !!f);

      const params = {
        fileDTOList
      };

      let result = null;

      // 执行编辑或修改方法
      if (this.isUpdate) {
        params.id = this.form.id;
        result = FastMerchImageApi.edit(params);
      } else {
        result = FastMerchImageApi.add(params);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    /**
     * 选中文件
     * @param {*} file
     * @param {*} item 当前图片项
     */
    handleSelectFile(file, item) {
      compressorImage(file).then(({ url, mime }) => {
        item.fileData = [{ url }];
        item.fileName = file.uid + file.name;
        item.suffixType = mime.split('/')[1];
      });

      return Upload.LIST_IGNORE;
    },

    /**
     * 校验图片是否上传
     */
    validateFileList() {
      return new Promise((resolve, reject) => {
        if (this.fileList.every(item => !item.required || item.fileData?.length)) {
          resolve();
        } else {
          message.warn('请检查是否有图片未上传');
          reject();
        }
      });
    },

    handlePreviewFile(file) {
      this.previewImage = file?.url;
      this.setPreviewVisible(true);
    },

    setPreviewVisible(visible) {
      this.previewVisible = visible;
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
