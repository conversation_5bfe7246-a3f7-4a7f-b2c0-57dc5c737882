<template>
  <a-modal
    :width="1000"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '30px' }"
    @update:visible="updateVisivle"
  >
    <a-form :layout="'vertical'">
      <a-row :gutter="[16, 16]">
        <template v-for="(item, key) in fileList" :key="key">
          <a-col :md="6" :sm="24" :xs="24">
            <a-form-item :label="`${item.label}`">
              <a-upload
                v-model:file-list="item.fileData"
                accept=".png, .jpg, .jpeg"
                list-type="picture-card"
                @preview="handlePreviewFile"
                disabled
              >
                <div v-if="!item.fileData?.length">
                  <plus-outlined />
                  <div style="margin-top: 8px">Upload</div>
                </div>
              </a-upload>
            </a-form-item>
          </a-col>
        </template>
      </a-row>

      <!-- 预览图片 -->
      <a-image
        :style="{ display: 'none' }"
        :src="previewImage"
        :preview="{ visible: previewVisible, onVisibleChange: setPreviewVisible }"
      />
    </a-form>

    <template #footer>
      <a-button @click="updateVisivle(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { FastMerchImageApi } from '@/api/merchant/FastMerchImageApi';

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  data() {
    return {
      form: {},
      // 预览图片
      previewImage: '',
      previewVisible: false,
      //图片数组
      fileList: [
        {
          label: '门头照',
          fileType: 11,
          fileData: []
        },
        {
          label: '经营场所照片',
          fileType: 12,
          fileData: []
        },

        {
          label: '收银台照片',
          fileType: 13,
          fileData: []
        }
      ]
    };
  },
  mounted() {
    if (this.detail) {
      this.form = Object.assign({}, this.detail);

      this.getFiles();
    }
  },
  methods: {
    async getFiles() {
      const data = await FastMerchImageApi.detail({ id: this.detail.id });
      // 处理图片
      const fileListMap = data.imageJsonList || [];
      fileListMap.forEach(fileItem => {
        const findItem = this.fileList.find(item => item.fileType === fileItem.imageType);
        if (findItem) {
          findItem.fileData = fileItem.imagePath ? [...findItem.fileData, { url: fileItem.imagePath, id: fileItem.id }] : findItem.fileData;
        }
      });
    },
    handlePreviewFile(file) {
      this.previewImage = file?.url;
      this.setPreviewVisible(true);
    },

    setPreviewVisible(visible) {
      this.previewVisible = visible;
    },

    updateVisivle(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
