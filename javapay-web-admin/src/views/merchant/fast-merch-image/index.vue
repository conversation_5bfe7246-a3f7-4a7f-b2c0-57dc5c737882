<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="使用次数">
              <a-input-number v-model:value="where.usedNumber" placeholder="使用次数" allow-clear autocomplete="off" style="width: 100%" />
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>
          <template #bodyCell="{ column, record }">
            <!-- table操作栏按钮 -->
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
                <a @click="handleEdit(record)">修改</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 编辑 -->
    <FastMerchImageEdit v-if="showEdit" v-model:visible="showEdit" :data="current" @done="reload" />

    <!-- 详情 -->
    <FastMerchImageDetail v-if="showDetail" v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import FastMerchImageEdit from './fast-merch-image-edit.vue';
import FastMerchImageDetail from './fast-merch-image-detail.vue';
import { hasPurview } from '@/utils/permission';
import { FastMerchImageApi } from '@/api/merchant/FastMerchImageApi';

export default {
  name: 'FastMerchImage',
  components: {
    FastMerchImageEdit,
    FastMerchImageDetail
  },
  data() {
    return {
      hasPurview,
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          align: 'center'
        },
        {
          title: '使用次数',
          dataIndex: 'usedNumber',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          align: 'center'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime',
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          width: 160,
          fixed: 'right',
          align: 'center'
        }
      ],
      // 表格搜索条件
      where: {},
      showEdit: false,
      showDetail: false,
      current: null
    };
  },
  methods: {
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where, orders }) {
      return FastMerchImageApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
