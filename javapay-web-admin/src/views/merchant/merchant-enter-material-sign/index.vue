<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="代理商编号" v-if="!hasPurview(['3'])">
              <a-input v-model:value.trim="where.agentNo" placeholder="请输入代理商编号" allow-clear />
            </a-form-item>
            <a-form-item label="商户简称">
              <a-input v-model:value.trim="where.merchantAbbr" placeholder="请输入商户简称" allow-clear />
            </a-form-item>
            <a-form-item label="资料分组名称">
              <a-select v-model:value="where.materialCode" style="width: 205px" :options="materialCodes" placeholder="请选择" allow-clear />
            </a-form-item>
            <a-form-item label="开始日期">
              <a-date-picker v-model:value="where.searchBeginTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item label="结束日期">
              <a-date-picker v-model:value="where.searchEndTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- table上边工具栏 -->
          <template #toolbar>
            <a-space>
              <a-button type="primary" v-if="hasPurview(['3'])" @click="handleAdd">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>

          <template #bodyCell="{ column, record }">
            <!-- table操作栏按钮 -->
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
                <template v-if="hasPurview(['3'])">
                  <a-divider type="vertical" />
                  <a @click="handleEdit(record)">修改</a>
                </template>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 编辑 -->
    <MerchantEnterMaterialSignEdit v-model:visible="showEdit" :data="current" @done="reload" v-if="showEdit" />

    <!-- 详情 -->
    <MerchantEnterMaterialSignDetail v-model:visible="showDetail" :detail="current" :channelCodes="channelCodes" />
  </div>
</template>

<script>
import { MerchantEnterMaterialSignApi } from '@/api/merchant/MerchantEnterMaterialSignApi';
import { MerchantEnterMaterialGroupApi } from '@/api/merchant/MerchantEnterMaterialGroupApi';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import MerchantEnterMaterialSignEdit from './merchant-enter-material-sign-edit.vue';
import MerchantEnterMaterialSignDetail from './merchant-enter-material-sign-detail.vue';
import { hasPurview } from '@/utils/permission';

export default {
  name: 'MerchantEnterMaterialSign',
  components: {
    MerchantEnterMaterialSignEdit,
    MerchantEnterMaterialSignDetail
  },
  data() {
    return {
      // 表格搜索条件
      where: {},
      current: null,
      showEdit: false,
      showDetail: false,
      materialCodes: [],
      channelCodes: [],
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '代理商编号',
          dataIndex: 'agentNo',
          align: 'center',
          hideCol: hasPurview(['3'])
        },
        {
          title: '商户简称',
          dataIndex: 'merchantAbbr'
        },
        {
          title: '资料分组名称',
          dataIndex: 'materialCode',
          align: 'center',
          customRender: ({ text }) => {
            const item = this.materialCodes.find(c => c.value === text);
            return item?.label || '--';
          }
        },
        {
          title: '银联行业名称',
          dataIndex: 'unionMccName'
        },
        {
          title: '微信行业名称',
          dataIndex: 'wechatMccName'
        },
        {
          title: '支付宝行业名称',
          dataIndex: 'alipayMccName'
        },
        {
          title: '经营类目',
          dataIndex: 'categoryName'
        },
        {
          title: '场景描述',
          dataIndex: 'sceneDesc',
          customRender: ({ text }) => {
            return text || '--';
          },
          width: 180
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right',
          width: 160
        }
      ].filter(i => !i.hideCol)
    };
  },
  mounted() {
    this.initMaterialCodes();
    this.initChannelCodes();
  },
  methods: {
    /**
     * 加载资料分组编号
     */
    async initMaterialCodes() {
      const data = await MerchantEnterMaterialGroupApi.list();
      const formatData = data.map(d => {
        return { label: d.materialName, value: d.materialCode };
      });
      this.materialCodes = formatData;
    },

    async initChannelCodes() {
      const data = await ChannelManageApi.list({ validStatus: 1 });
      this.channelCodes = data || [];
    },

    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    async handleDetail(row) {
      const data = await MerchantEnterMaterialSignApi.detail({ id: row.id });
      this.current = data || {};
      this.showDetail = true;
    },

    handleAdd() {
      this.current = null;
      this.showEdit = true;
    },

    async handleEdit(row) {
      const data = await MerchantEnterMaterialSignApi.detail({ id: row.id });
      this.current = data || {};
      this.showEdit = true;
    },

    hasPurview,

    datasource({ page, limit, where, orders }) {
      return MerchantEnterMaterialSignApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
