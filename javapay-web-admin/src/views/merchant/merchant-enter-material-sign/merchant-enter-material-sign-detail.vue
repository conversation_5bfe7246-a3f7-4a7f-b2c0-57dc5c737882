<template>
  <a-modal
    :width="750"
    :visible="visible"
    :maskClosable="false"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '30px' }"
    @update:visible="updateVisible"
  >
    <!-- 易生/富有 -->
    <a-descriptions :column="2" v-if="form.materialCode === 1">
      <a-descriptions-item label="商户简称" :sapn="2">{{ form.merchantAbbr }}</a-descriptions-item>
      <a-descriptions-item label="银联行业类别">{{ form.unionMccName }}</a-descriptions-item>
      <a-descriptions-item label="微信行业类别">{{ form.wechatMccName }}</a-descriptions-item>
      <a-descriptions-item label="支付宝行业类别">{{ form.alipayMccName }}</a-descriptions-item>
    </a-descriptions>
    <!-- 快付通 -->
    <a-descriptions :column="2" v-if="form.materialCode === 2">
      <a-descriptions-item label="商户简称" :sapn="2">{{ form.merchantAbbr }}</a-descriptions-item>
      <a-descriptions-item label="银联行业类别">{{ form.unionMccName }}</a-descriptions-item>
      <a-descriptions-item label="经营类目">{{ form.categoryName }}</a-descriptions-item>
      <a-descriptions-item label="场景描述" :sapn="2">{{ form.sceneDesc }}</a-descriptions-item>
    </a-descriptions>
    <!-- 公共 -->
    <a-descriptions :column="2" v-if="form.materialCode">
      <a-descriptions-item label="创建人">{{ form.createUserId }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改人">{{ form.lastModifyUserId }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>

    <a-collapse :active-key="['1']">
      <a-collapse-panel key="1" header="通道使用状态">
        <a-descriptions :column="2" size="small">
          <template v-for="(item, key) in form.chnMaterialUsedDTOList || []" :key="key">
            <a-descriptions-item label="支付通道">{{ filterChannelCode(item.channelCode) }}</a-descriptions-item>
            <a-descriptions-item label="使用状态">
              <a-tag v-if="item.useStatus === 0">未使用</a-tag>
              <a-tag v-else-if="item.useStatus === 1" color="success">已绑定</a-tag>
              <a-tag v-else-if="item.useStatus === 2" color="warning">已使用</a-tag>
            </a-descriptions-item>
          </template>
        </a-descriptions>
      </a-collapse-panel>
    </a-collapse>

    <a-collapse :active-key="['1']" v-if="form.materialCode === 1" style="margin-top: 15px">
      <a-collapse-panel key="1" header="资质图片信息">
        <a-form layout="vertical">
          <a-row :gutter="16">
            <a-col :md="8" :sm="24" :xs="24" v-for="(item, idx) in form.imagesChannel || []" :key="idx">
              <a-form-item :label="item.label">
                <a-upload :file-list="[{ url: item.imagePath }]" list-type="picture-card" disabled />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-collapse-panel>
    </a-collapse>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
export default {
  props: {
    visible: Boolean,
    detail: Object,
    channelCodes: Array,
  },
  emits: ['update:visible'],
  data() {
    return {
      form: {},
      uploadFileList: [
        { label: '门头照', fileType: 11 },
        { label: '店内环境照片', fileType: 12 },
        { label: '收银台照片', fileType: 13 }
      ]
    };
  },
  watch: {
    detail() {
      // 处理图片
      if (this.detail?.imagesChannel) {
        this.detail.imagesChannel.forEach(c => {
          const item = this.uploadFileList.find(i => i.fileType === c.imageType);
          c.label = item?.label;
        });
      }

      this.form = Object.assign({}, this.detail);
    }
  },
  methods: {
    filterChannelCode(channelCode) {
      const item = this.channelCodes.find(i => i.channelCode === channelCode);
      return item?.channelName || channelCode;
    },
    filterCategory(value) {
      const item = this.category.find(i => i.value === value);
      return item?.label || value;
    },
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
