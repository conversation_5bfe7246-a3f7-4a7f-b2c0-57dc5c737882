<template>
  <a-modal
    :width="900"
    :visible="visible"
    :title="isUpdate ? '修改' : '新建'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
  >
    <!-- 操作步骤 -->
    <a-steps v-if="!isUpdate" :current="currentStep" type="navigation" size="small">
      <a-step title="选择资料分组" :description="checkedMaterialName" />
      <a-step title="录入资料" />
    </a-steps>

    <a-card v-if="!isUpdate && currentStep === 0" :bordered="false">
      <a-radio-group v-model:value="form.materialCode" :options="materialCodeRadios" @change="changeMaterialCodeRadio" />
    </a-card>

    <a-form v-if="currentStep === 1" ref="form" :model="form" :rules="rules" layout="vertical">
      <!-- 易生/富有 -->
      <template v-if="form.materialCode === 1">
        <a-divider orientation="left" :orientationMargin="0" dashed>商户基本信息</a-divider>
        <a-row :gutter="16">
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item label="商户简称" name="merchantAbbr">
              <a-input v-model:value="form.merchantAbbr" placeholder="请输入商户简称" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item label=" ">
              <div class="red-text">*商户简称不要出现省市区名称</div>
              <div class="red-text">*商户简称要按照：字号+行业+组织形式来填写，如：超人+理发+店</div>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item label="银联行业类别" name="unionMcc">
              <a-cascader
                :options="unionMccCode"
                :value="form.unionMccName ? [form.unionMccSmallClass, form.unionMccName] : ['请选择']"
                :showSearch="{ filter: searchUnionMcc }"
                @change="doneUnionMcc"
              />
              <!-- <a-row>
                <a-col :md="12">
                  <a-form-item-rest>
                    <a-select
                      v-model:value="form.unionMccSmallClass"
                      style="width: 100%"
                      placeholder="请选择"
                      showSearch
                      :options="unionMccSmallClass"
                      @change="form.unionMcc = form.unionMccName = ''"
                    />
                  </a-form-item-rest>
                </a-col>
                <a-col :md="12">
                  <a-select
                    :value="form.unionMccName || '输入名称搜索'"
                    style="width: 100%"
                    :options="unionMccCode"
                    showSearch
                    optionFilterProp="label"
                    @focus="loadUnionMccCode"
                    @select="doneUnionMcc"
                  />
                </a-col>
              </a-row> -->
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" :xs="24" />
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item label="微信行业类别" name="wechatMcc">
              <a-cascader
                :options="wechatMcc"
                :value="form.wechatMccName ? [form.wechatMccBigClass, form.wechatMccSmallClass, form.wechatMccName] : ['请选择']"
                :showSearch="{ filter: searchUnionMcc }"
                @change="doneWechatMcc"
              />

              <!-- <a-row>
                <a-col :md="12">
                  <a-form-item-rest>
                    <a-row>
                      <a-col :md="12">
                        <a-select
                          showSearch
                          v-model:value="form.wechatMccBigClass"
                          style="width: 100%"
                          placeholder="请选择"
                          :options="wechatMccBigClass"
                          @change="form.wechatMccSmallClass = form.wechatMcc = form.wechatMccName = ''"
                        />
                      </a-col>
                      <a-col :md="12">
                        <a-select
                          :disabled="!form.wechatMccBigClass"
                          v-model:value="form.wechatMccSmallClass"
                          style="width: 100%"
                          placeholder="请选择"
                          showSearch
                          :options="wechatMccSmallClass"
                          @change="form.wechatMcc = form.wechatMccName = ''"
                          @focus="focusWechatMccSmallClass('wechatMcc')"
                        />
                      </a-col>
                    </a-row>
                  </a-form-item-rest>
                </a-col>
                <a-col :md="6">
                  <a-select
                    :value="form.wechatMccName || '输入名称搜索'"
                    style="width: 100%"
                    :options="wechatMcc"
                    showSearch
                    optionFilterProp="label"
                    @focus="focusWechatMcc('wechatMcc')"
                    @select="doneWechatMcc"
                  />
                </a-col>
              </a-row> -->
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item label="支付宝行业类别" name="alipayMcc">
              <a-cascader
                :options="alipayMcc"
                :value="form.alipayMccName ? [form.alipayMccBigClass, form.alipayMccSmallClass, form.alipayMccName] : ['请选择']"
                :showSearch="{ filter: searchUnionMcc }"
                @change="doneAlipayMcc"
              />

              <!-- <a-row>
                <a-col :md="12">
                  <a-form-item-rest>
                    <a-row>
                      <a-col :md="12">
                        <a-select
                          showSearch
                          v-model:value="form.alipayMccBigClass"
                          @change="form.alipayMccSmallClass = form.alipayMcc = form.alipayMccName = ''"
                          style="width: 100%"
                          placeholder="请选择"
                          :options="alipayMccBigClass"
                        />
                      </a-col>
                      <a-col :md="12">
                        <a-select
                          :disabled="!form.alipayMccBigClass"
                          v-model:value="form.alipayMccSmallClass"
                          style="width: 100%"
                          placeholder="请选择"
                          showSearch
                          :options="alipayMccSmallClass"
                          @change="form.alipayMcc = form.alipayMccName = ''"
                          @focus="focusWechatMccSmallClass('alipayMcc')"
                        />
                      </a-col>
                    </a-row>
                  </a-form-item-rest>
                </a-col>
                <a-col :md="6">
                  <a-select
                    :value="form.alipayMccName || '输入名称搜索'"
                    style="width: 100%"
                    showSearch
                    :options="alipayMcc"
                    optionFilterProp="label"
                    @focus="focusWechatMcc('alipayMcc')"
                    @select="doneAlipayMcc"
                  />
                </a-col>
              </a-row> -->
            </a-form-item>
          </a-col>
        </a-row>

        <a-divider orientation="left" :orientationMargin="0" dashed>商户资质图片信息</a-divider>
        <a-form ref="fileForm" :model="fileForm" layout="vertical">
          <a-row :gutter="16">
            <a-col :md="8" :sm="24" :xs="24" v-for="(item, idx) in fileForm.uploadFileList" :key="idx">
              <a-form-item
                :label="item.label"
                :name="['uploadFileList', idx, 'fileData']"
                :rules="[{ validator: async (rule, value) => await validatorFile(value, item) }, { required: item.required, message: '' }]"
              >
                <a-upload
                  v-model:file-list="item.fileData"
                  accept=".png, .jpg, .jpeg, .gif"
                  :multiple="false"
                  list-type="picture-card"
                  :before-upload="file => beforeUpload(file, item)"
                  @remove="() => handleRemoveFile(item)"
                >
                  <div v-if="!item.fileData?.length">
                    <plus-outlined />
                    <div style="margin-top: 8px">Upload</div>
                  </div>
                </a-upload>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </template>
      <!-- END 易生/富有 -->
      <!-- 快付通 -->
      <template v-if="form.materialCode === 2">
        <a-divider orientation="left" :orientationMargin="0" dashed>商户基本信息</a-divider>
        <a-row :gutter="16">
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item label="商户简称" name="merchantAbbr">
              <a-input v-model:value="form.merchantAbbr" placeholder="请输入商户简称" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item label=" ">
              <div class="red-text">*商户简称中不要出现省市区名称</div>
              <div class="red-text">*商户简称中不要出现建筑，置业，房屋，投资，理财，美容，百货</div>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item label="银联行业类别" name="unionMcc">
              <a-select
                :value="form.unionMccName || '请选择'"
                style="width: 100%"
                :options="unionMccEpos"
                showSearch
                optionFilterProp="label"
                @select="doneUnionMccEpos"
              />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item label="经营类目" name="category">
              <a-select :value="form.categoryName || '请选择'" style="width: 100%" :options="category" @select="doneCategory" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item label="场景描述" name="sceneDesc">
              <a-textarea v-model:value="form.sceneDesc" placeholder="请输入场景描述" allow-clear />
            </a-form-item>
          </a-col>
        </a-row>
      </template>
      <!-- END 快付通 -->
    </a-form>

    <!-- 自定义页脚按钮 -->
    <template #footer>
      <a-button key="back" @click="updateVisible(false)">取消</a-button>
      <a-button v-if="currentStep === 0" key="next" type="primary" @click="nextStep">下一步</a-button>
      <a-button v-else key="submit" type="primary" :loading="loading" @click="save">确定</a-button>
    </template>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { MerchantEnterMaterialSignApi } from '@/api/merchant/MerchantEnterMaterialSignApi';
import { MerchantEnterMaterialGroupApi } from '@/api/merchant/MerchantEnterMaterialGroupApi';

export default {
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      currentStep: 0,
      materialCodeRadios: [],
      checkedMaterialName: '',
      // 表单数据
      form: {},
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false,
      // 表单验证规则
      rules: {
        merchantAbbr: [
          { required: true, message: '请输入商户简称' },
          { validator: this.noSpaceValidator, trigger: 'blur' }
        ],
        unionMcc: [{ required: true, message: '请选择银联行业类别', trigger: 'blur' }],
        wechatMcc: [{ required: true, message: '请选择微信行业类别', trigger: 'blur' }],
        alipayMcc: [{ required: true, message: '请选择支付宝行业类别', trigger: 'blur' }],
        category: [{ required: true, message: '请选择经营类目', trigger: 'blur' }],
        sceneDesc: [
          { required: true, message: '请输入场景描述' },
          { validator: this.noSpaceValidator, trigger: 'blur' }
        ]
      },
      fileForm: {
        // 需要上传的图片列表
        uploadFileList: [
          {
            label: '门头照',
            fileType: 11,
            required: true
          },
          {
            label: '店内环境照片',
            fileType: 12,
            required: true
          },
          {
            label: '收银台照片',
            fileType: 13,
            required: true
          }
        ]
      },
      wechatMccBigClass: [],
      wechatMccSmallClass: [],
      wechatMcc: [],
      unionMccEpos: [],
      category: [],
      unionMccSmallClass: [],
      unionMccCode: [],
      alipayMccBigClass: [],
      alipayMccSmallClass: [],
      alipayMcc: []
    };
  },
  mounted() {
    if (this.data) {
      // 设置图片信息
      if (this.data.imagesChannel) {
        this.fileForm.uploadFileList.forEach(f => {
          const item = this.data.imagesChannel.find(i => i.imageType === f.fileType);
          if (item) {
            // fileData设置为数组便于后续操作
            f.fileData = item.imagePath ? [{ url: item.imagePath }] : [];
            f.fileName = item.imageName;
          }
        });
      }

      this.form = Object.assign({}, this.data);
      this.isUpdate = true;

      this.nextStep();
    } else {
      this.isUpdate = false;
      this.initMaterialCodeRadios();
    }
  },
  methods: {
    //表单验证，去空格方法
    noSpaceValidator(rule, value, callback) {
      if (/\s/.test(value)) {
        callback(new Error('输入不应包含空格'));
      } else {
        callback();
      }
    },
    /**
     * 保存
     */
    async save() {
      // 校验基本信息表单
      await this.$refs.form.validate();
      // 校验图片表单&处理图片信息
      if (this.$refs.fileForm) {
        await this.$refs.fileForm.validate();

        const fileList = this.fileForm.uploadFileList;
        this.form.fileDTOList = fileList
          .map(item => {
            if (item.fileData?.[0]) {
              return {
                fileName: item.fileName,
                fileData: item.fileData[0].url,
                fileType: item.fileType,
                suffixType: item.suffixType || 'png'
              };
            }
            return null;
          })
          .filter(f => !!f);
      }
      // 修改加载框为正在加载
      this.loading = true;

      let result = null;
      // 执行添加或修改
      if (this.isUpdate) {
        result = MerchantEnterMaterialSignApi.edit(this.form);
      } else {
        result = MerchantEnterMaterialSignApi.add(this.form);
      }

      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    /**
     * 点击下一步
     */
    nextStep() {
      if (!this.form.materialCode) return;

      // materialCode  1: 易生/富有; 2:快付通
      switch (this.form.materialCode) {
        case 1:
          this.loadOption();
          break;
        case 2:
          this.loadEposOption();
          // 设置经营类目默认
          this.form.categoryName = '个人';
          this.form.category = '160520250';
          break;
        default:
          break;
      }

      this.currentStep = 1;
    },

    /**
     * 加载扫码所需配置
     */
    loadOption() {
      this.loadUnionMccCode();
      this.focusWechatMcc('wechatMcc');
      this.focusWechatMcc('alipayMcc');
    },

    /**
     * 加载Epos所需配置
     */
    loadEposOption() {
      this.initCategory();
      this.initUnionMccEpos();
    },

    doneUnionMccEpos(code, { label, value }) {
      this.form.unionMcc = value;
      this.form.unionMccName = label;
    },

    doneCategory(code, { label, value }) {
      this.form.category = value;
      this.form.categoryName = label;
    },

    doneUnionMcc(value, selectedOptions) {
      this.form.unionMcc = value[1];
      this.form.unionMccName = selectedOptions[1]?.label;

      this.form.unionMccSmallClass = value[0];
    },

    doneWechatMcc(value, selectedOptions) {
      this.form.wechatMcc = value[2];
      this.form.wechatMccName = selectedOptions[2]?.label;

      this.form.wechatMccBigClass = value[0];
      this.form.wechatMccSmallClass = value[1];
    },

    doneAlipayMcc(value, selectedOptions) {
      this.form.alipayMcc = value[2];
      this.form.alipayMccName = selectedOptions[2]?.label;

      this.form.alipayMccBigClass = value[0];
      this.form.alipayMccSmallClass = value[1];
    },

    changeMaterialCodeRadio(e) {
      const item = this.materialCodeRadios.find(i => i.value === e.target.value);
      this.checkedMaterialName = item?.label;
    },

    async focusWechatMccSmallClass(mccTypeKey) {
      const data = await MerchantEnterMaterialSignApi[mccTypeKey]({ mccLevel: 2, mccBigClass: this.form[mccTypeKey + 'BigClass'] });
      const formatData = data.map(d => {
        return { label: d.mccSmallClass, value: d.mccSmallClass };
      });

      this[mccTypeKey + 'SmallClass'] = formatData;
    },

    async focusWechatMcc(mccTypeKey, mccName = '') {
      const data = await MerchantEnterMaterialSignApi[mccTypeKey]({
        mccLevel: 3,
        mccName
      });

      const mccBigClassArr = data.map(i => i.mccBigClass);
      const allMccBigClass = Array.from(new Set(mccBigClassArr));
      const mccBigClassOptions = allMccBigClass.map(a => {
        return {
          label: a,
          value: a,
          children: []
        };
      });

      mccBigClassOptions.forEach(m => {
        const currentClass = data.filter(d => d.mccBigClass === m.label);

        const mccSmallClassArr = currentClass.map(i => i.mccSmallClass);
        const allMccSmallClass = Array.from(new Set(mccSmallClassArr));

        const mccSmallClassOptions = allMccSmallClass.map(a => {
          const currentMcc = data.filter(d => d.mccBigClass === m.label && d.mccSmallClass === a);

          const options = currentMcc.map(a => {
            return {
              label: a.mccName,
              value: a.mcc,
              isLeaf: true
            };
          });

          return {
            label: a,
            value: a,
            children: options
          };
        });

        m.children = mccSmallClassOptions;
      });

      this[mccTypeKey] = mccBigClassOptions;
    },

    /**
     * 加载资料分组编号
     */
    async initMaterialCodeRadios() {
      const data = await MerchantEnterMaterialGroupApi.list();
      const formatData = data.map(d => {
        return { label: d.materialName, value: d.materialCode };
      });
      this.materialCodeRadios = formatData;

      // 默认选中第一项
      if (formatData[0]) {
        this.form.materialCode = formatData[0].value;
        this.checkedMaterialName = formatData[0].label;
      }
    },

    /**
     * 加载经营类目(快捷)
     */
    async initCategory() {
      const data = await MerchantEnterMaterialSignApi.chnKftCategory();
      const formatData = data.map(d => {
        return { label: d.mccName, value: d.mcc };
      });
      this.category = formatData;
    },

    /**
     * 加载银联MCC(快捷)
     */
    async initUnionMccEpos() {
      const data = await MerchantEnterMaterialSignApi.unionMccEpos();
      const formatData = data.map(d => {
        return { label: d.mccName, value: d.mcc };
      });
      this.unionMccEpos = formatData;
    },

    /**
     * 加载银联MCC(扫码)
     */
    async initUnionMccCode() {
      const data = await MerchantEnterMaterialSignApi.unionMccCode({ mccLevel: 1, mccSmallClass: '' });
      const formatData = data.map(d => {
        return { label: d.mccSmallClass, value: d.mccSmallClass, isLeaf: false };
      });
      this.unionMccSmallClass = formatData;
    },

    searchUnionMcc(inputValue, path) {
      return path.some(option => option.label.indexOf(inputValue) > -1 || option.value.indexOf(inputValue) > -1);
    },

    async loadUnionMccCode(mccName = '') {
      this.unionMccCode = [];
      let data = await MerchantEnterMaterialSignApi.unionMccCode({ mccLevel: 2, mccName });
      data = data || [];

      const mccSmallClassArr = data.map(i => i.mccSmallClass);
      const allMccSmallClass = Array.from(new Set(mccSmallClassArr));
      const mccSmallClassOptions = allMccSmallClass.map(a => {
        return {
          label: a,
          value: a,
          children: []
        };
      });
      mccSmallClassOptions.forEach(m => {
        const currentClass = data.filter(d => d.mccSmallClass === m.label);
        const formatClass = currentClass.map(c => {
          return {
            label: c.mccName,
            value: c.mcc,
            isLeaf: true
          };
        });
        m.children = formatClass;
      });
      console.log(mccSmallClassOptions);
      this.unionMccCode = mccSmallClassOptions;
    },

    /**
     * 获取微信、支付宝MCC
     * @param {*} mccTypeKey 必传 wechatMcc | alipayMcc
     */
    async initWechatOrAlipayMcc(mccTypeKey) {
      const data = await MerchantEnterMaterialSignApi[mccTypeKey]({ mccLevel: 1 });
      const formatData = data.map(d => {
        return { label: d.mccBigClass, value: d.mccBigClass, isLeaf: false, mccLevel: 1 };
      });

      this[mccTypeKey + 'BigClass'] = formatData;
    },

    /**
     * 选中文件
     * @param {*} file
     * @param {*} item 当前图片信息
     */
    beforeUpload(file, item) {
      const isLt3M = file.size / 1024 / 1024 <= 3;
      if (!isLt3M) {
        message.error('图片大小不能超过3M!');
        setTimeout(() => {
          item.fileData = [];
        }, 0);
        return false;
      }

      // file转base64
      file2Base64(file).then(data => {
        item.fileData = [{ url: data }];
        item.fileName = file.uid + file.name;
        item.suffixType = file.type.split('/')[1];
      });

      return false;
    },

    handleRemoveFile(item) {
      item.fileData = [];
      item.suffixType = '';
    },

    /**
     * 图片非空校验
     */
    async validatorFile(val, { label, required }) {
      if (required && !val?.length) {
        return Promise.reject(`请上传${label}`);
      }
      return Promise.resolve();
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};

// 图片文件转base64
function file2Base64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });
}
</script>

<style scoped>
.red-text {
  color: red; /* 设置文字颜色为红色 */
  font-size: 12px; /* 设置字体大小为12像素 */
}
</style>
