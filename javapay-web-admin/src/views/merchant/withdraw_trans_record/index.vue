<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="通道商户号">
              <a-input v-model:value.trim="where.chnMerchNo" placeholder="请输入通道商户号" allow-clear />
            </a-form-item>
            <a-form-item label="通道流水号">
              <a-input v-model:value.trim="where.chnOrderNo" placeholder="请输入通道流水号" allow-clear />
            </a-form-item>
            <a-form-item label="通道响应码">
              <a-input v-model:value.trim="where.chnResCode" placeholder="请输入通道响应码" allow-clear />
            </a-form-item>
            <a-form-item label="商户号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户号" allow-clear />
            </a-form-item>
            <a-form-item label="交易流水号">
              <a-input v-model:value.trim="where.orderNo" placeholder="请输入交易流水号" allow-clear />
            </a-form-item>
            <a-form-item label="结算账户类型">
              <a-select v-model:value="where.accountType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option value="A">企业商户</a-select-option>
                <a-select-option value="B">个体工商户</a-select-option>
                <a-select-option value="C">小微商户</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="到账状态">
              <a-select v-model:value="where.receivedStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <!-- <a-select-option :value="1">小微</a-select-option> -->
              </a-select>
            </a-form-item>
            <a-form-item label="交易通道">
              <a-select v-model:value="where.channelCode" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
                  {{ channelName }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'accountType'">
              <a-tag v-if="record.accountType == 'A'" color="cyan">企业商户</a-tag>
              <a-tag v-else-if="record.accountType == 'B'" color="blue">个体工商户</a-tag>
              <a-tag v-else-if="record.accountType == 'C'" color="purple">小微商户</a-tag>
            </template>

            <template v-else-if="column.key === 'receivedStatus'">
              <!-- <a-tag v-if="record.accountType == 'A'" color="cyan">企业商户</a-tag> -->
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <WithdrawTransRecordDetail v-model:visible="showDetail" :detail="current" :channelCodes="channelCodes" />
  </div>
</template>

<script>
import { WithdrawTransRecordApi } from '@/api/merchant/WithdrawTransRecordApi';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import WithdrawTransRecordDetail from './withdraw-trans-record-detail.vue';

export default {
  name: 'MerchWithdrawTransRecord',
  components: {
    WithdrawTransRecordDetail
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '结算账户类型',
          key: 'accountType',
          dataIndex: 'accountType',
          align: 'center'
        },
        {
          title: '结算账户姓名',
          dataIndex: 'bankAccountName'
        },
        {
          title: '结算账户卡号',
          dataIndex: 'bankAccountNoMask'
        },
        {
          title: '银行名称',
          dataIndex: 'bankName'
        },
        {
          title: '交易通道',
          dataIndex: 'channelCode',
          align: 'center',
          customRender: ({ text }) => {
            const item = this.channelCodes.find(c => c.channelCode === text);
            return item?.channelName || '--';
          }
        },
        {
          title: '通道商户号',
          dataIndex: 'chnMerchNo'
        },
        {
          title: '通道流水号',
          dataIndex: 'chnOrderNo'
        },
        {
          title: '通道响应码',
          dataIndex: 'chnResCode'
        },
        {
          title: '通道响应描述',
          dataIndex: 'chnResDesc',
          width: 200
        },
        {
          title: '手续费金额(元)',
          dataIndex: 'feeAmt'
        },
        {
          title: '商户号',
          dataIndex: 'merchantNo'
        },
        {
          title: '交易流水号',
          dataIndex: 'orderNo'
        },
        {
          title: '到账金额(元)',
          dataIndex: 'receivedAmt'
        },
        {
          title: '响应描述',
          dataIndex: 'receivedDesc',
          width: 200
        },
        {
          title: '到账状态',
          key: 'receivedStatus',
          dataIndex: 'receivedStatus',
          align: 'center'
        },
        {
          title: '交易笔数',
          dataIndex: 'transCount'
        },
        {
          title: '提现金额(元)',
          dataIndex: 'withdrawAmt'
        },
        {
          title: '操作',
          key: 'action',
          width: 100,
          align: 'center',
          fixed: 'right'
        }
      ],
      // 表格搜索条件
      where: {},
      current: null,
      showDetail: false,
      channelCodes: []
    };
  },
  async mounted() {
    const data = await ChannelManageApi.list({ validStatus: 1 });
    this.channelCodes = data || [];
  },
  methods: {
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

   async datasource({ page, limit, where, orders }) {
      return WithdrawTransRecordApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
