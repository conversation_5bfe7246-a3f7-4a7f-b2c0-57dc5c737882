<template>
  <a-modal
    :width="800"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="结算账户类型">
        <a-tag v-if="form.accountType == 'A'" color="cyan">企业商户</a-tag>
        <a-tag v-else-if="form.accountType == 'B'" color="blue">个体工商户</a-tag>
        <a-tag v-else-if="form.accountType == 'C'" color="purple">小微商户</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="结算账户姓名">{{ form.bankAccountName }}</a-descriptions-item>
      <a-descriptions-item label="结算账户卡号">{{ form.bankAccountNoMask }}</a-descriptions-item>
      <a-descriptions-item label="银行名称">{{ form.bankName }}</a-descriptions-item>
      <a-descriptions-item label="交易通道">
        <template v-for="({ channelCode, channelName }, key) in channelCodes" :key="key">
          <a-badge v-if="form.channelCode === channelCode" color="purple" :text="channelName" />
        </template>
      </a-descriptions-item>
      <a-descriptions-item label="通道商户号">{{ form.chnMerchNo }}</a-descriptions-item>
      <a-descriptions-item label="通道流水号">{{ form.chnOrderNo }}</a-descriptions-item>
      <a-descriptions-item label="通道响应码">{{ form.chnResCode }}</a-descriptions-item>
      <a-descriptions-item label="通道响应描述">{{ form.chnResDesc }}</a-descriptions-item>
      <a-descriptions-item label="手续费金额(元)">{{ form.feeAmt }}</a-descriptions-item>
      <a-descriptions-item label="商户号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="交易流水号">{{ form.orderNo }}</a-descriptions-item>
      <a-descriptions-item label="到账金额(元)">{{ form.receivedAmt }}</a-descriptions-item>
      <a-descriptions-item label="响应描述">{{ form.receivedDesc }}</a-descriptions-item>
      <a-descriptions-item label="到账状态">{{ form.receivedStatus }}</a-descriptions-item>
      <a-descriptions-item label="交易笔数">{{ form.transCount }}</a-descriptions-item>
      <a-descriptions-item label="提现金额(元)">{{ form.withdrawAmt }}</a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

export default {
  props: {
    visible: Boolean,
    detail: Object,
    channelCodes: Array
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
