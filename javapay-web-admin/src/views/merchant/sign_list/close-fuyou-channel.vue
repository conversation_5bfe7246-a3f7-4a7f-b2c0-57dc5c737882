<template>
  <a-modal
    :width="600"
    :visible="visible"
    :confirm-loading="loading"
    title="关闭商户"
    :body-style="{ paddingBottom: '8px' }"
    :mask-closable="false"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 6 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 18 }, sm: { span: 24 } }"
    >
      <a-form-item label="关闭商户原因" name="closeReason">
        <a-input v-model:value.trim="form.closeReason" placeholder="请输入关闭商户原因" allow-clear />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

  <script>
import { message } from 'ant-design-vue';
import { ChannelMerchManageApi } from '@/api/aisleManage/ChannelMerchManageApi';

export default {
  props: {
    visible: Boolean,
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {},
      // 表单验证规则
      rules: {
        closeReason: [
          { required: true, message: '请输入关闭商户原因' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ]
      },
      // 提交状态
      loading: false
    };
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      ChannelMerchManageApi.closeChlMerch({
        chnMerchNo: this.data.chnMerchNo,
        closeReason: this.form.closeReason
      })
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
