<template>
  <a-modal :width="750" :visible="visible" title="图片详情" :mask-closable="false" :body-style="{ paddingBottom: '20px' }" @update:visible="updateVisible">
    <a-form layout="vertical">
      <a-row>
        <a-col v-for="(item, key) in imageList" :key="key" :span="6">
          <a-form-item :label="item.label">
            <a-upload
              :file-list="[{ url: item.imagePath }]"
              list-type="picture-card"
              @preview="() => handlePreview(item.imagePath)"
              disabled
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <!-- 预览图片 -->
    <a-image :style="{ display: 'none' }" :src="previewImage" :preview="{ visible: previewVisible, onVisibleChange: setPreviewVisible }" />

    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

const imageTypeEnum = [
  { label: '法人身份证正面', value: 1 },
  { label: '法人身份证反面', value: 2 },
  { label: '银行卡图片卡号面', value: 3 },
  { label: '银行卡图片背面', value: 4 },
  { label: '法人人脸正面照片', value: 5 },
  { label: '法人手持身份证正面', value: 6 },
  { label: '营业执照照片', value: 7 },
  { label: '税务登记证照片', value: 8 },
  { label: '组织机构代码证照片', value: 9 },
  { label: '三方协议合同照片', value: 10 },
  { label: '门头照', value: 11 },
  { label: '店内环境照片', value: 12 },
  { label: '收银台照片', value: 13 },
  { label: '电子签名图像', value: 14 },
  { label: '协议照', value: 15 },
  { label: '开户许可证', value: 16 }
];

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      imageList: [],
      previewVisible: false,
      previewImage: ''
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        props.detail?.forEach(f => {
          imageTypeEnum.forEach(i => {
            if (i.value === f.imageType) {
              f.label = i.label;
            }
          });
        });

        data.imageList = props.detail || [];
      }
    });

    const handlePreview = imagePath => {
      data.previewImage = imagePath;
      setPreviewVisible(true);
    };

    const setPreviewVisible = visible => {
      data.previewVisible = visible;
    };

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible,
      handlePreview,
      setPreviewVisible
    };
  }
};
</script>
