<template>
  <a-modal
    :width="600"
    :visible="visible"
    :confirm-loading="loading"
    title="支付通道配置"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      label-align="left"
      :label-col="{ md: { span: 7 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 17 }, sm: { span: 24 } }"
    >
      <a-tabs v-model:activeKey="activeKey" type="card">
        <a-tab-pane key="1" tab="微信">
          <a-form-item label="付款码支付(B扫C)">
            <a-radio-group v-model:value="form.wechatQrcodeCode">
              <a-radio :value="1">A</a-radio>
              <a-radio :value="2">B</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="公众号支付(C扫B)">
            <a-radio-group v-model:value="form.wechatGzhCode">
              <a-radio :value="1">A</a-radio>
              <a-radio :value="2">B</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="微信小程序支付">
            <a-radio-group v-model:value="form.wechatAppletCode">
              <a-radio :value="1">A</a-radio>
              <a-radio :value="2">B</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-tab-pane>
        <a-tab-pane key="2" tab="支付宝">
          <a-form-item label="付款码支付(B扫C)">
            <a-radio-group v-model:value="form.alipayQrcodeCode">
              <a-radio :value="1">A</a-radio>
              <a-radio :value="2">B</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="公众号支付(C扫B)">
            <a-radio-group v-model:value="form.alipayGzhCode">
              <a-radio :value="1">A</a-radio>
              <a-radio :value="2">B</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-tab-pane>
        <a-tab-pane key="3" tab="银联">
          <a-form-item label="付款码支付(B扫C)">
            <a-radio-group v-model:value="form.unionpayQrcodeCode">
              <a-radio :value="1">A</a-radio>
              <a-radio :value="2">B</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="公众号支付(C扫B)">
            <a-radio-group v-model:value="form.unionpayGzhCode">
              <a-radio :value="1">A</a-radio>
              <a-radio :value="2">B</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-tab-pane>
      </a-tabs>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { MerchantApi } from '@/api/merchant/MerchantApi';

export default {
  name: 'MerchantRouteConfig',
  props: {
    visible: Boolean,
    merchantNo: String
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {},
      // 提交状态
      loading: false,
      activeKey: '1'
    };
  },
  created() {
    this.merchantNo && this.getDetail();
  },
  methods: {
    async getDetail() {
      const info = await MerchantApi.tradeChlRouteDetail({ merchantNo: this.merchantNo });
      this.form = Object.assign({}, info);
    },

    async save() {
      this.loading = true;

      MerchantApi.tradeChlRouteEdit(this.form)
        .then(result => {
          this.loading = false;

          message.success(result.message);

          this.updateVisible(false);

          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
