<template>
  <div class="ele-body">
    <a-spin :spinning="spinning" tip="下载中, 请稍候...">
      <!-- 搜索表单 -->
      <div class="block-interval">
        <a-card :bordered="false">
          <a-form layout="inline" :model="where">
            <a-row :gutter="[0, 16]">
              <a-form-item label="支付通道">
                <a-select v-model:value="where.channelCode" style="width: 200px" placeholder="请选择" allow-clear>
                  <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
                    {{ channelName }}
                  </a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item label="通道商户编号">
                <a-input v-model:value.trim="where.chnMerchNo" placeholder="请输入通道商户编号" allow-clear />
              </a-form-item>
              <a-form-item label="通道商户简称">
                <a-input v-model:value.trim="where.chnMerchName" placeholder="请输入通道商户简称" allow-clear />
              </a-form-item>
              <a-form-item label="通道商户名称">
                <a-input v-model:value.trim="where.chnMerchFullName" placeholder="请输入通道商户名称" allow-clear />
              </a-form-item>
              <a-form-item label="商户编号">
                <a-input v-model:value.trim="where.merchantNo" placeholder="商户编号" allow-clear />
              </a-form-item>
              <a-form-item label="进件状态">
                <a-select v-model:value="where.reportRespCode" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option :value="-1">任务待发起</a-select-option>
                  <a-select-option :value="0">初始状态</a-select-option>
                  <a-select-option :value="1">审核中</a-select-option>
                  <a-select-option :value="2">入网成功</a-select-option>
                  <a-select-option :value="3">入网失败</a-select-option>
                  <a-select-option :value="4">产品待开通</a-select-option>
                  <a-select-option :value="5">产品开通失败</a-select-option>
                  <a-select-option :value="6">文件待上传</a-select-option>
                  <a-select-option :value="7">文件上传失败</a-select-option>
                  <a-select-option :value="8">电子协议待生成</a-select-option>
                  <a-select-option :value="9">电子协议生成失败</a-select-option>
                  <a-select-option :value="10">电子协议待签署</a-select-option>
                  <a-select-option :value="11">电子协议签署失败</a-select-option>
                  <a-select-option :value="12">查询状态失败</a-select-option>
                  <a-select-option :value="14">报备银联商户</a-select-option>
                  <a-select-option :value="15">待选择结算卡</a-select-option>
                  <a-select-option :value="16">报备银联终端</a-select-option>
                  <a-select-option :value="17">待上传结算卡图片</a-select-option>
                  <a-select-option :value="18">平台预审核中</a-select-option>
                  <a-select-option :value="99">已关闭</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="商户类型">
                <a-select v-model:value="where.isMicro" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option :value="0">企业商户</a-select-option>
                  <a-select-option :value="1">个人商户</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="结算类型">
                <a-select v-model:value="where.accountType" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option value="G">对公</a-select-option>
                  <a-select-option value="S">对私</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="是否法人结算">
                <a-select v-model:value="where.isLegalSettle" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option :value="1">是</a-select-option>
                  <a-select-option :value="0">否</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="开始日期">
                <a-date-picker v-model:value="where.searchBeginTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
              </a-form-item>
              <a-form-item label="结束日期">
                <a-date-picker v-model:value="where.searchEndTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
              </a-form-item>
              <a-form-item class="ele-text-center">
                <a-space>
                  <a-button type="primary" @click="reload">查询</a-button>
                  <a-button @click="reset">重置</a-button>
                </a-space>
              </a-form-item>
            </a-row>
          </a-form>
        </a-card>
      </div>

      <!-- 表格 -->
      <div>
        <a-card :bordered="false" class="table-height">
          <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
            <!-- table上边工具栏 -->
            <template #toolbar>
              <div>
                <a-space>
                  <a-button @click="handleExportExcel">
                    <template #icon>
                      <download-outlined />
                    </template>
                    <span>导出excel</span>
                  </a-button>
                </a-space>
              </div>
            </template>
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'reportRespCode'">
                <a-tag v-if="record.reportRespCode === 0" color="warning">初始状态</a-tag>
                <a-tag v-else-if="record.reportRespCode === -1" color="processing">任务待发起</a-tag>
                <a-tag v-else-if="record.reportRespCode === 1" color="processing">审核中</a-tag>
                <a-tag v-else-if="record.reportRespCode === 2" color="success">入网成功</a-tag>
                <a-tag v-else-if="record.reportRespCode === 3" color="error">入网失败</a-tag>
                <a-tag v-else-if="record.reportRespCode === 4" color="processing">产品待开通</a-tag>
                <a-tag v-else-if="record.reportRespCode === 5" color="error">产品开通失败</a-tag>
                <a-tag v-else-if="record.reportRespCode === 6" color="processing">文件待上传</a-tag>
                <a-tag v-else-if="record.reportRespCode === 7" color="error">文件上传失败</a-tag>
                <a-tag v-else-if="record.reportRespCode === 8" color="processing">电子协议待生成</a-tag>
                <a-tag v-else-if="record.reportRespCode === 9" color="error">电子协议生成失败</a-tag>
                <a-tag v-else-if="record.reportRespCode === 10" color="processing">电子协议待签署</a-tag>
                <a-tag v-else-if="record.reportRespCode === 11" color="error">电子协议签署失败</a-tag>
                <a-tag v-else-if="record.reportRespCode === 12" color="error">查询状态失败</a-tag>
                <a-tag v-else-if="record.reportRespCode === 14" color="processing">报备银联商户</a-tag>
                <a-tag v-else-if="record.reportRespCode === 15" color="success">待选择结算卡</a-tag>
                <a-tag v-else-if="record.reportRespCode === 16" color="error">报备银联终端</a-tag>
                <a-tag v-else-if="record.reportRespCode === 17" color="processing">待上传结算卡图片</a-tag>
                <a-tag v-else-if="record.reportRespCode === 18" color="processing">平台预审核中</a-tag>
                <a-tag v-else-if="record.reportRespCode === 99">已关闭</a-tag>
              </template>

              <template v-else-if="['wechatOpenStatus', 'alipayOpenStatus', 'unionpayOpenStatus', 'eposOpenStatus'].includes(column.key)">
                <a-tag color="error" v-if="record[column.key] === -1">限制开通</a-tag>
                <a-tag color="warning" v-else-if="record[column.key] === 0">未开通</a-tag>
                <a-tag color="success" v-else-if="record[column.key] === 1">已开通</a-tag>
                <a-tag color="processing" v-else-if="record[column.key] === 2">审核中</a-tag>
                <a-tag color="error" v-else-if="record[column.key] === 3">开通失败</a-tag>
              </template>


              <template v-if="column.key === 'isMicro'">
                <a-tag v-if="record.isMicro === 0" color="cyan">企业商户</a-tag>
                <a-tag v-else-if="record.isMicro === 1" color="blue">个人商户</a-tag>
              </template>

              <template v-if="column.key === 'accountType'">
                <a-tag v-if="record.accountType === 'G'" color="cyan">对公</a-tag>
                <a-tag v-else-if="record.accountType === 'S'" color="blue">对私</a-tag>
              </template>

              <template v-if="column.key === 'isLegalSettle'">
                <a-tag color="success" v-if="record.isLegalSettle === 1">是</a-tag>
                <a-tag v-else>否</a-tag>
              </template>

              <!-- table操作栏按钮 -->
              <template v-else-if="column.key === 'action'">
                <a-space>
                  <a @click="handleDetail(record)">详情</a>
                  <a-divider type="vertical" />
                  <a @click="handleDetailImage(record)">查看图片</a>
                  <template v-if="hasPurview(['0']) && record.reportRespCode === 18">
                    <a-divider type="vertical" />
                    <a @click="handleCheck(record)">审核</a>
                  </template>
                  <template v-if="hasPurview('0') && !!record.chnMerchNo && record.reportRespCode !== 99">
                    <a-divider type="vertical" />
                    <a @click="closeChlMerch(record)">注销</a>
                  </template>
                </a-space>
              </template>
            </template>
          </ele-pro-table>
        </a-card>
      </div>

      <!-- 商户进件详情 -->
      <MerchantSignDetail v-model:visible="showDetail" :detail="current" :channelCodes="channelCodes" />

      <DetailImage v-model:visible="showDetailImage" :detail="current" />

      <AuditDetail v-model:visible="showCheck" :detail="current2" :channelCodes="channelCodes" @done="reload" />

      <CloseFuYouChannel v-if="showCloseFuYouChannel" v-model:visible="showCloseFuYouChannel" :data="current3" @done="reload" />
    </a-spin>
  </div>
</template>

<script>
import MerchantSignDetail from './SignDetail.vue';
import AuditDetail from './AuditDetail.vue';
import CloseFuYouChannel from './close-fuyou-channel.vue';
import DetailImage from './DetailImage.vue';
import { MerchantApi } from '@/api/merchant/MerchantApi';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import { message } from 'ant-design-vue';
import { hasPurview } from '@/utils/permission';

export default {
  name: 'SignList',
  components: {
    MerchantSignDetail,
    AuditDetail,
    DetailImage,
    CloseFuYouChannel
  },
  data() {
    return {
      showCloseFuYouChannel: false,
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '支付通道',
          dataIndex: 'channelCode',
          align: 'center',
          customRender: ({ text }) => {
            const item = this.channelCodes.find(c => c.channelCode === text);
            return item?.channelName || '--';
          }
        },
        {
          title: '通道商户简称',
          dataIndex: 'chnMerchName',
          align: 'center'
        },
        {
          title: '通道商户编号',
          dataIndex: 'chnMerchNo',
          align: 'center'
        },
        {
          title: '通道商户名称',
          dataIndex: 'chnMerchFullName',
          align: 'center'
        },
        {
          title: '商户ID',
          dataIndex: 'merchantId',
          align: 'center'
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '商户类型',
          dataIndex: 'isMicro',
          key: 'isMicro',
          align: 'center'
        },
        {
          title: '进件状态',
          dataIndex: 'reportRespCode',
          key: 'reportRespCode',
          align: 'center'
        },
        {
          title: '进件结果',
          dataIndex: 'reportRespMsg',
          width: 200
        },
        {
          title: '结算类型',
          dataIndex: 'accountType',
          key: 'accountType',
          align: 'center'
        },
        {
          title: '是否法人结算',
          dataIndex: 'isLegalSettle',
          key: 'isLegalSettle',
          align: 'center'
        },
        {
          title: '结算卡ID',
          dataIndex: 'bankCardId',
          align: 'center'
        },
        {
          title: '微信开通状态',
          dataIndex: 'wechatOpenStatus',
          key: 'wechatOpenStatus',
          align: 'center'
        },
        {
          title: '支付宝开通状态',
          dataIndex: 'alipayOpenStatus',
          key: 'alipayOpenStatus',
          align: 'center'
        },
        {
          title: '云闪付开通状态',
          dataIndex: 'unionpayOpenStatus',
          key: 'unionpayOpenStatus',
          align: 'center'
        },
        {
          title: 'EPOS开通状态',
          dataIndex: 'eposOpenStatus',
          key: 'eposOpenStatus',
          align: 'center'
        },
        {
          title: '报备来源',
          dataIndex: 'reportSource',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          align: 'center'
        }
      ],
      // 表格搜索条件
      where: {},
      current: null,
      current2: null,
      current3: null,
      showDetail: false,
      showDetailImage: false,
      showCheck: false,
      spinning: false,
      channelCodes: []
    };
  },
  mounted() {
    this.getChannelList();
  },
  methods: {
    closeChlMerch(row) {
      this.current3 = row;
      this.showCloseFuYouChannel = true;
    },
    async handleExportExcel() {
      if (!(this.where.searchBeginTime || this.where.searchEndTime)) {
        message.warning('请选择开始、结束日期');
        return;
      }
      this.spinning = true;
      const res = await MerchantApi.exportSignListExcel(this.where).catch(() => {
        this.spinning = false;
      });
      this.spinning = false;
      const fileReader = new FileReader();
      fileReader.onload = function () {
        try {
          // 说明是普通对象数据，后台转换失败
          const jsonData = JSON.parse(fileReader.result);
          message.error(jsonData.message);
        } catch (err) {
          const contentDisposition = res.headers['content-disposition'];
          let fileName = decodeURIComponent(contentDisposition.substring(contentDisposition.indexOf('=') + 1));
          fileName = fileName ? fileName.replace("utf-8''", '') + '.xlsx' : '商户进件列表-下载.xlsx';
          // 解析对象失败，说明是正常的文件流
          let blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.style.display = 'none';
          a.href = url;
          a.download = fileName;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }
      };
      fileReader.readAsText(res?.data);
    },
    async getChannelList() {
      const data = await ChannelManageApi.list({ validStatus: 1 });
      this.channelCodes = data || [];
    },

    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    async handleCheck({ id }) {
      const [form, imageList] = await Promise.all([MerchantApi.signDetail({ id }), MerchantApi.detailImage({ id })]).catch(error => {
        console.log(error);
      });
      this.current2 = { form, imageList };
      this.showCheck = true;
    },

    async handleDetail({ id }) {
      const data = await MerchantApi.signDetail({ id });
      this.current = data;
      this.showDetail = true;
    },

    async handleDetailImage({ id }) {
      const detail = await MerchantApi.detailImage({ id });
      this.current = detail;
      this.showDetailImage = true;
    },

    datasource({ page, limit, where, orders }) {
      return MerchantApi.signList({ ...where, ...orders, pageNo: page, pageSize: limit });
    },
    hasPurview
  }
};
</script>
