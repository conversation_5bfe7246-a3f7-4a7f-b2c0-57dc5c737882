<template>
  <a-modal
    :width="1000"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="支付通道">
        <template v-for="({ channelCode, channelName }, key) in channelCodes" :key="key">
          <a-badge v-if="form.channelCode === channelCode" color="purple" :text="channelName" />
        </template>
      </a-descriptions-item>

      <a-descriptions-item label="通道商户名称">{{ form.chnMerchFullName }}</a-descriptions-item>
      <a-descriptions-item label="商户ID">{{ form.merchantId }}</a-descriptions-item>
      <a-descriptions-item label="商户编号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="通道商户简称">{{ form.chnMerchName }}</a-descriptions-item>
      <a-descriptions-item label="渠道商户编号">{{ form.chnMerchNo }}</a-descriptions-item>
      <a-descriptions-item label="商户类型">
        <a-tag v-if="form.isMicro === 0" color="cyan">企业商户</a-tag>
        <a-tag v-else-if="form.isMicro === 1" color="blue">个人商户</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="企业名称">{{ form.licenseName }}</a-descriptions-item>
      <a-descriptions-item label="企业注册地址" :span="2">{{ form.licenseAddr }}</a-descriptions-item>
      <a-descriptions-item label="法人姓名">{{ form.legalName }}</a-descriptions-item>
      <a-descriptions-item label="法人手机号">{{ form.legalTelMask }}</a-descriptions-item>
      <a-descriptions-item label="营业执照号">{{ form.licenseNo }}</a-descriptions-item>
      <a-descriptions-item label="营业执照有效期">{{
        form.licenseStartDate ? `${form.licenseStartDate}-${form.licenseEndDate}` : '--'
      }}</a-descriptions-item>
      <a-descriptions-item label="经营省市区" :span="2">{{
        `${form.provinceName || '--'}/${form.cityName || '--'}/${form.countyName || '--'}`
      }}</a-descriptions-item>
      <a-descriptions-item label="经营详细地址" :span="2">{{ form.addressDetails }}</a-descriptions-item>
      <a-descriptions-item label="结算卡ID">{{ form.bankCardId }}</a-descriptions-item>
      <a-descriptions-item label="结算类型">
        <a-tag v-if="form.accountType === 'G'" color="cyan">对公</a-tag>
        <a-tag v-else-if="form.accountType === 'S'" color="blue">对私</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="是否法人结算">
        <a-tag color="success" v-if="form.isLegalSettle === 1">是</a-tag>
        <a-tag v-else>否</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="进件状态" :span="2">
        <a-tag v-if="form.reportRespCode === 0" color="warning">初始状态</a-tag>
        <a-tag v-else-if="form.reportRespCode === -1" color="processing">任务待发起</a-tag>
        <a-tag v-else-if="form.reportRespCode === 1" color="processing">审核中</a-tag>
        <a-tag v-else-if="form.reportRespCode === 2" color="success">入网成功</a-tag>
        <a-tag v-else-if="form.reportRespCode === 3" color="error">入网失败</a-tag>
        <a-tag v-else-if="form.reportRespCode === 4" color="processing">产品待开通</a-tag>
        <a-tag v-else-if="form.reportRespCode === 5" color="error">产品开通失败</a-tag>
        <a-tag v-else-if="form.reportRespCode === 6" color="processing">文件待上传</a-tag>
        <a-tag v-else-if="form.reportRespCode === 7" color="error">文件上传失败</a-tag>
        <a-tag v-else-if="form.reportRespCode === 8" color="processing">电子协议待生成</a-tag>
        <a-tag v-else-if="form.reportRespCode === 9" color="error">电子协议生成失败</a-tag>
        <a-tag v-else-if="form.reportRespCode === 10" color="processing">电子协议待签署</a-tag>
        <a-tag v-else-if="form.reportRespCode === 11" color="error">电子协议签署失败</a-tag>
        <a-tag v-else-if="form.reportRespCode === 12" color="error">查询状态失败</a-tag>
        <a-tag v-else-if="form.reportRespCode === 14" color="processing">报备银联商户</a-tag>
        <a-tag v-else-if="form.reportRespCode === 15" color="success">待选择结算卡</a-tag>
        <a-tag v-else-if="form.reportRespCode === 16" color="error">报备银联终端</a-tag>
        <a-tag v-else-if="form.reportRespCode === 17" color="processing">待上传结算卡图片</a-tag>
        <a-tag v-else-if="form.reportRespCode === 18" color="processing">平台预审核中</a-tag>
        <a-tag v-else-if="form.reportRespCode === 99">已关闭</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="进件结果" :span="2">{{ form.reportRespMsg }}</a-descriptions-item>
      <a-descriptions-item label="请求数据" :span="2">{{ form.requestData }}</a-descriptions-item>
      <a-descriptions-item label="通道微信类目">{{ form.wechatMcc }}</a-descriptions-item>
      <a-descriptions-item label="微信开通状态">
        <a-tag color="error" v-if="form.wechatOpenStatus === -1">限制开通</a-tag>
        <a-tag color="warning" v-else-if="form.wechatOpenStatus === 0">未开通</a-tag>
        <a-tag color="success" v-else-if="form.wechatOpenStatus === 1">已开通</a-tag>
        <a-tag color="processing" v-else-if="form.wechatOpenStatus === 2">审核中</a-tag>
        <a-tag color="error" v-else-if="form.wechatOpenStatus === 3">开通失败</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="通道支付宝类目">{{ form.alipayMcc }}</a-descriptions-item>
      <a-descriptions-item label="支付宝开通状态">
        <a-tag color="error" v-if="form.alipayOpenStatus === -1">限制开通</a-tag>
        <a-tag color="warning" v-else-if="form.alipayOpenStatus === 0">未开通</a-tag>
        <a-tag color="success" v-else-if="form.alipayOpenStatus === 1">已开通</a-tag>
        <a-tag color="processing" v-else-if="form.alipayOpenStatus === 2">审核中</a-tag>
        <a-tag color="error" v-else-if="form.alipayOpenStatus === 3">开通失败</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="通道银联类目">{{ form.unionMcc }}</a-descriptions-item>
      <a-descriptions-item label="云闪付开通状态">
        <a-tag color="error" v-if="form.unionpayOpenStatus === -1">限制开通</a-tag>
        <a-tag color="warning" v-else-if="form.unionpayOpenStatus === 0">未开通</a-tag>
        <a-tag color="success" v-else-if="form.unionpayOpenStatus === 1">已开通</a-tag>
        <a-tag color="processing" v-else-if="form.unionpayOpenStatus === 2">审核中</a-tag>
        <a-tag color="error" v-else-if="form.unionpayOpenStatus === 3">开通失败</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="EPOS开通状态">
        <a-tag color="error" v-if="form.eposOpenStatus === -1">限制开通</a-tag>
        <a-tag color="warning" v-else-if="form.eposOpenStatus === 0">未开通</a-tag>
        <a-tag color="success" v-else-if="form.eposOpenStatus === 1">已开通</a-tag>
        <a-tag color="processing" v-else-if="form.eposOpenStatus === 2">审核中</a-tag>
        <a-tag color="error" v-else-if="form.eposOpenStatus === 3">开通失败</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="报备来源">{{ form.reportSource }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

export default {
  name: 'MerchantSignDetail',
  props: {
    visible: Boolean,
    detail: Object,
    channelCodes: Array
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
