<template>
  <a-modal
    :width="750"
    :visible="visible"
    title="审核"
    :body-style="{ paddingBottom: '8px' }"
    :mask-closable="false"
    @update:visible="updateVisible"
  >
    <a-tabs v-model:activeKey="activeKey" type="card">
      <a-tab-pane key="1" tab="基本信息">
        <a-descriptions :column="2">
          <a-descriptions-item label="支付通道">
            <template v-for="({ channelCode, channelName }, key) in channelCodes" :key="key">
              <a-badge v-if="form.channelCode === channelCode" color="purple" :text="channelName" />
            </template>
          </a-descriptions-item>
          <a-descriptions-item label="通道商户名称">{{ form.chnMerchFullName }}</a-descriptions-item>
          <a-descriptions-item label="商户编号">{{ form.merchantNo }}</a-descriptions-item>
          <a-descriptions-item label="通道商户简称">{{ form.chnMerchName }}</a-descriptions-item>
          <a-descriptions-item label="渠道商户编号">{{ form.chnMerchNo }}</a-descriptions-item>
          <a-descriptions-item label="通道微信类目">{{ form.wechatMcc }}</a-descriptions-item>
          <a-descriptions-item label="通道支付宝类目">{{ form.alipayMcc }}</a-descriptions-item>
          <a-descriptions-item label="通道银联类目">{{ form.unionMcc }}</a-descriptions-item>
        </a-descriptions>
      </a-tab-pane>
      <a-tab-pane key="2" tab="图片信息">
        <a-form layout="vertical">
          <a-row>
            <a-col v-for="(item, key) in imageList" :key="key" :span="6">
              <a-form-item :label="item.label">
                <a-upload
                  :file-list="[{ url: item.imagePath }]"
                  list-type="picture-card"
                  @preview="() => handlePreview(item.imagePath)"
                  disabled
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
        <!-- 预览图片 -->
        <a-image
          :style="{ display: 'none' }"
          :src="previewImage"
          :preview="{ visible: previewVisible, onVisibleChange: setPreviewVisible }"
        />
      </a-tab-pane>
    </a-tabs>

    <a-form>
      <a-form-item label="审核描述" required>
        <a-textarea v-model:value="checkForm.auditMessage" :auto-size="{ minRows: 3, maxRows: 5 }" />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-button type="primary" :loading="loading" @click="handleAudit(1)"><check-outlined />通过</a-button>
      <a-button type="danger" :loading="loading" @click="handleAudit(0)"><close-outlined />驳回</a-button>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';
import { message } from 'ant-design-vue';
import { MerchantApi } from '@/api/merchant/MerchantApi';

export default {
  props: {
    visible: Boolean,
    detail: Object,
    channelCodes: Array
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {},
      checkForm: {},
      imageList: [],
      activeKey: '1',
      previewVisible: false,
      previewImage: '',
      loading: false
    });

    const imageTypeEnum = [
      { label: '法人身份证正面', value: 1 },
      { label: '法人身份证反面', value: 2 },
      { label: '银行卡图片卡号面', value: 3 },
      { label: '银行卡图片背面', value: 4 },
      { label: '法人人脸正面照片', value: 5 },
      { label: '法人手持身份证正面', value: 6 },
      { label: '营业执照照片', value: 7 },
      { label: '税务登记证照片', value: 8 },
      { label: '组织机构代码证照片', value: 9 },
      { label: '三方协议合同照片', value: 10 },
      { label: '门头照', value: 11 },
      { label: '店内环境照片', value: 12 },
      { label: '收银台照片', value: 13 },
      { label: '电子签名图像', value: 14 },
      { label: '协议照', value: 15 },
      { label: '开户许可证', value: 16 }
    ];

    const watch = watchEffect(async () => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail.form);

        const imageList = props.detail.imageList || [];
        imageList.forEach(f => {
          imageTypeEnum.forEach(i => {
            if (i.value === f.imageType) {
              f.label = i.label;
            }
          });
        });
        data.imageList = imageList;

        data.checkForm.auditMessage = '';
      }
    });

    /**
     * 发起审核操作
     * @param {*} auditStatus 审核状态 1 通过 ; 0 驳回
     */
    const handleAudit = async auditStatus => {
      const { id } = data.form;
      const { auditMessage } = data.checkForm;

      if (!auditMessage) return message.warning('请填写审核描述!');

      // 修改加载框为正在加载
      data.loading = true;

      MerchantApi.audit({ auditStatus, id, auditMessage })
        .then(result => {
          // 移除加载框
          data.loading = false;

          // 提示修改成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          updateVisible(false);

          // 触发父组件done事件
          context.emit('done');
        })
        .catch(() => {
          data.loading = false;
        });
    };

    const handlePreview = imagePath => {
      data.previewImage = imagePath;
      setPreviewVisible(true);
    };

    const setPreviewVisible = visible => {
      data.previewVisible = visible;
    };

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible,
      handlePreview,
      handleAudit,
      setPreviewVisible
    };
  }
};
</script>
<style scoped>
.rate-title {
  border-left: 5px solid;
  border-color: var(--warning-color);
  padding-left: 10px;
}

.card-title-background {
  background-color: #f5f5f5;
  height: 2em;
  line-height: 2em;
  margin-bottom: 2em;
}

::v-deep(.ant-form-horizontal) .ant-form-item-label {
  text-align: center;
  background-color: #f3f5f7;
}
</style>
