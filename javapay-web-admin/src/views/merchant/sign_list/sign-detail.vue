<template>
  <a-modal
    :width="800"
    :visible="visible"
    title="进件详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '16px' }"
    @update:visible="updateVisible"
    :footer="null"
  >
    <a-tabs v-model:activeKey="activeKey" type="card">
      <a-tab-pane key="1" tab="基本信息">
        <a-tabs v-model:activeKey="activeInfoKey" centered>
          <a-tab-pane key="1" tab="商户信息">
            <a-descriptions :column="1">
              <a-descriptions-item label="商户名称">{{ form.merchantName }}</a-descriptions-item>
              <a-descriptions-item label="商户编号">{{ form.merchantNo }}</a-descriptions-item>
              <a-descriptions-item label="商户类型">
                <a-tag v-if="form.merchantType === '0'" color="cyan">小微</a-tag>
                <a-tag v-else-if="form.merchantType === '1'" color="blue">个体工商户</a-tag>
                <a-tag v-else-if="form.merchantType === '2'" color="purple">企业</a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="签约状态">
                <span v-if="form.signStatus === 1" class="ele-text-success">已签约</span>
                <span v-else class="ele-text-danger">未签约</span>
              </a-descriptions-item>
              <a-descriptions-item label="有效状态">
                <span v-if="form.validStatus === 1" class="ele-text-success">已签约</span>
                <span v-else class="ele-text-danger">未签约</span>
              </a-descriptions-item>
              <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
            </a-descriptions>
          </a-tab-pane>
          <a-tab-pane key="2" tab="法人信息">
            <a-descriptions :column="1">
              <a-descriptions-item label="法人身份证头像面">
                <a-image v-if="form.legalCertHeadPhoto" :width="180" :src="form.legalCertHeadPhoto" /><span v-else>暂无</span>
              </a-descriptions-item>
              <a-descriptions-item label="法人身份证国徽面">
                <a-image v-if="form.legalCertBackPhoto" :width="180" :src="form.legalCertBackPhoto" /><span v-else>暂无</span>
              </a-descriptions-item>
              <a-descriptions-item label="法人手持证件照">
                <a-image v-if="form.legalHoldCertPhoto" :width="180" :src="form.legalHoldCertPhoto" /><span v-else>暂无</span>
              </a-descriptions-item>
              <a-descriptions-item label="法人姓名">{{ form.legalName }}</a-descriptions-item>
              <a-descriptions-item label="法人性别">{{ form.legalSex }}</a-descriptions-item>
              <a-descriptions-item label="法人地址">{{ form.legalAddr }}</a-descriptions-item>
              <a-descriptions-item label="法人证件号码">{{ form.legalCertNo }}</a-descriptions-item>
              <a-descriptions-item label="证件有效期">{{ form.legalCertStartDate }} - {{ form.legalCertEndDate }}</a-descriptions-item>
              <a-descriptions-item label="手机号">{{ form.legalTel }}</a-descriptions-item>
            </a-descriptions>
          </a-tab-pane>
          <a-tab-pane key="3" tab="营业信息">
            <a-descriptions :column="1">
              <a-descriptions-item label="商户门头照">
                <a-image v-if="form.merchFrontPhoto" :width="180" :src="form.merchFrontPhoto" /><span v-else>暂无</span>
              </a-descriptions-item>
              <a-descriptions-item label="门店内景照">
                <a-image v-if="form.ShopInteriorPhoto" :width="180" :src="form.ShopInteriorPhoto" /><span v-else>暂无</span>
              </a-descriptions-item>
              <a-descriptions-item label="门店收银台">
                <a-image v-if="form.ShopCashierPhoto" :width="180" :src="form.ShopCashierPhoto" /><span v-else>暂无</span>
              </a-descriptions-item>
              <template v-if="form.merchantType != '0'">
                <a-descriptions-item label="营业执照">
                  <a-image v-if="form.licensePhoto" :width="180" :src="form.licensePhoto" /><span v-else>暂无</span>
                </a-descriptions-item>
                <a-descriptions-item label="营业执照有效期">{{ form.licenseStartDate }}-{{ form.licenseEndDate }}</a-descriptions-item>
                <a-descriptions-item label="统一社会信用代码">{{ form.licenseNo }}</a-descriptions-item>
                <a-descriptions-item label="营业执照全称">{{ form.licenseName }}</a-descriptions-item>
              </template>
              <a-descriptions-item label="商户简称">{{ form.merchantSname }}</a-descriptions-item>
              <a-descriptions-item label="注册地址">{{ form.licenseAddr }}</a-descriptions-item>
              <a-descriptions-item label="营业地址/定位地址">{{ form.positionAddr }}</a-descriptions-item>
            </a-descriptions>
          </a-tab-pane>
          <a-tab-pane key="4" tab="结算信息">
            <a-descriptions :column="1">
              <a-descriptions-item label="结算卡类型">
                <a-tag v-if="form.settleMethod === 'S'" color="cyan">对私借记卡</a-tag>
                <a-tag v-else-if="form.settleMethod === 'G'" color="blue">对公账户</a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="银行卡正面">
                <a-image v-if="form.bankCardHeadPhoto" :width="180" :src="form.bankCardHeadPhoto" /><span v-else>暂无</span>
              </a-descriptions-item>
              <a-descriptions-item label="银行卡反面">
                <a-image v-if="form.bankCardBackPhoto" :width="180" :src="form.bankCardBackPhoto" /><span v-else>暂无</span>
              </a-descriptions-item>
              <a-descriptions-item label="预留手机号">{{ form.mobile }}</a-descriptions-item>
              <a-descriptions-item label="结算户名">{{ form.bankAccountName }}</a-descriptions-item>
              <a-descriptions-item label="结算账号">{{ form.bankAccountNo }}</a-descriptions-item>
              <a-descriptions-item label="开户银行">{{ form.bankName }}</a-descriptions-item>
              <a-descriptions-item label="开户地区">{{ form.bankArea }}</a-descriptions-item>
              <a-descriptions-item label="开户支行">{{ form.bankBranch }}</a-descriptions-item>
            </a-descriptions>
          </a-tab-pane>
          <a-tab-pane key="5" tab="通讯信息">
            <a-descriptions :column="1">
              <a-descriptions-item label="联系人姓名">{{ form.contactsName }}</a-descriptions-item>
              <a-descriptions-item label="联系人电话">{{ form.contactsTel }}</a-descriptions-item>
              <a-descriptions-item label="联系人邮箱">{{ form.contactsEmail }}</a-descriptions-item>
              <a-descriptions-item label="联系人地址">{{ form.contactsAddr }}</a-descriptions-item>
              <a-descriptions-item label="客服电话">{{ form.customerServiceTel }}</a-descriptions-item>
              <a-descriptions-item label="拓展经理（业务员姓名)">{{ form.salesmanName }}</a-descriptions-item>
            </a-descriptions>
          </a-tab-pane>
        </a-tabs>
      </a-tab-pane>
      <a-tab-pane v-for="(item, key) in form.signInfo || []" :key="String(-(key + 1))" :tab="item.payOrgCodeDesc">
        <a-descriptions :column="2" title="应答信息" bordered>
          <a-descriptions-item label="进件时间">{{ item.reportTime }}</a-descriptions-item>
          <a-descriptions-item label="进件状态">{{ item.reportRespCode }}</a-descriptions-item>
          <a-descriptions-item label="进件结果" :span="2">{{ item.reportRespMsg }}</a-descriptions-item>
          <a-descriptions-item label="渠道商户编号">{{ item.chnMerchNo }}</a-descriptions-item>
          <a-descriptions-item label="注册名称">{{ item.chnMerchName }}</a-descriptions-item>
        </a-descriptions>
        <a-descriptions :column="2" title="费率信息" bordered style="margin-top: 20px">
          <a-descriptions-item label="微信费率">{{ item.wechatRate }}</a-descriptions-item>
          <a-descriptions-item label="支付宝费率">{{ item.alipayRate }}</a-descriptions-item>
          <a-descriptions-item label="银联(<=1000) 借记卡费率">{{ item.UnionpayDebitRate }}</a-descriptions-item>
          <a-descriptions-item label="银联(<=1000) 贷记卡费率">{{ item.UnionpayCreditRate }}</a-descriptions-item>
          <a-descriptions-item label="银联(>1000) 借记卡费率">{{ item.debitRate }}</a-descriptions-item>
          <a-descriptions-item label="银联(>1000) 贷记卡费率">{{ item.creditRate }}</a-descriptions-item>
          <a-descriptions-item label="扫码快付 垫资费">{{ item.addD0Rate }}</a-descriptions-item>
        </a-descriptions>
      </a-tab-pane>
    </a-tabs>
  </a-modal>
</template>

<script>
import { reactive, toRefs } from 'vue';
import { MerchantApi } from '@/api/merchant/MerchantApi';

export default {
  props: {
    visible: Boolean,
    itemId: String
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {},
      activeKey: '1',
      activeInfoKey: '1'
    });

    getDetail();
    async function getDetail() {
      const info = await MerchantApi.signDetail({ id: props.itemId });
      data.form = Object.assign({}, info);
    }

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      updateVisible,
    };
  }
};
</script>
