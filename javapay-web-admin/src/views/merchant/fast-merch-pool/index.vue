<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="法人姓名">
              <a-input v-model:value.trim="where.legalName" placeholder="法人姓名" allow-clear />
            </a-form-item>
            <a-form-item label="营业执照名称">
              <a-input v-model:value.trim="where.licenseName" placeholder="营业执照名称" allow-clear />
            </a-form-item>
            <a-form-item label="营业执照编号">
              <a-input v-model:value.trim="where.licenseNo" placeholder="营业执照编号" allow-clear />
            </a-form-item>
            <a-form-item label="归属地">
              <a-cascader
                :key="cascaderKey"
                style="width: 205px"
                v-model:value="areaCodeValue"
                :options="regionsData"
                :load-data="loadAreaData"
                placeholder="请选择"
                allow-clear
                @change="selectedAreaValue"
              />
              <!-- <a-radio-group v-model:value="areaRange" @change="changeAreaRange">
                <a-radio-button :value="2">省</a-radio-button>
                <a-radio-button :value="3">市</a-radio-button>
                <a-cascader
                  :key="cascaderKey"
                  style="width: 205px"
                  v-model:value="areaCodeValue"
                  :options="regionsData"
                  :load-data="loadAreaData"
                  placeholder="请选择"
                  allow-clear
                  @change="selectedAreaValue"
                />
              </a-radio-group> -->
            </a-form-item>
            <a-form-item label="使用状态">
              <a-select v-model:value="where.usedStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">已使用</a-select-option>
                <a-select-option :value="0">未使用</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="门店照上传状态">
              <a-select v-model:value="where.shopImageUploadStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">已上传</a-select-option>
                <a-select-option :value="0">未上传</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="营业执照上传状态">
              <a-select v-model:value="where.licenseImageUploadStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">已上传</a-select-option>
                <a-select-option :value="0">未上传</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
              <a-button type="primary" @click="showBatchUpload = true">
                <span>批量导入资料</span>
              </a-button>
            </a-space>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'usedStatus'">
              <a-tag color="success" v-if="record.usedStatus === 1">已使用</a-tag>
              <a-tag v-else>未使用</a-tag>
            </template>
            <template v-if="column.key === 'shopImageUploadStatus'">
              <a-tag color="success" v-if="record.shopImageUploadStatus === 1">已上传</a-tag>
              <a-tag v-else>未上传</a-tag>
            </template>
            <template v-if="column.key === 'licenseImageUploadStatus'">
              <a-tag color="success" v-if="record.licenseImageUploadStatus === 1">已上传</a-tag>
              <a-tag v-else>未上传</a-tag>
            </template>

            <template v-else-if="column.key === 'merchGrade'">
              <a-tag v-if="record.merchGrade === 'A'" color="pink">企业商户</a-tag>
              <a-tag v-else-if="record.merchGrade === 'B'" color="cyan">个体工商户</a-tag>
              <a-tag v-else-if="record.merchGrade === 'C'" color="blue">小微商户</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
                <a @click="handleEdit(record)">修改</a>
                <a @click="handleSupplyImages(record)">上传营业执照</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>
    <!-- 商户修改 -->
    <FastMerchPoolEdit v-if="showEdit" v-model:visible="showEdit" :data="current" :channel-codes="channelCodes" @done="reload" />

    <!-- 商户详情 -->
    <FastMerchPoolDetail v-if="showDetail" v-model:visible="showDetail" :detail="current" />

    <SupplyImages v-if="showSupplyImages" v-model:visible="showSupplyImages" :data="current" @done="reload" />

    <BatchUpload v-if="showBatchUpload" v-model:visible="showBatchUpload" @done="reload" />
  </div>
</template>

<script>
import FastMerchPoolEdit from './fast-merch-pool-edit.vue';
import FastMerchPoolDetail from './fast-merch-pool-detail.vue';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import { hasPurview } from '@/utils/permission';
import { AreaApi } from '@/api/base/AreaApi';
import { FastMerchPoolApi } from '@/api/merchant/FastMerchPoolApi';
import SupplyImages from './supply-images.vue';
import BatchUpload from './BatchUpload.vue';

export default {
  name: 'FastMerchPool',
  components: {
    FastMerchPoolEdit,
    FastMerchPoolDetail,
    SupplyImages,
    BatchUpload
  },
  data() {
    return {
      hasPurview,
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '商户等级',
          dataIndex: 'merchGrade',
          key: 'merchGrade',
          align: 'center'
        },
        {
          title: '法人姓名',
          dataIndex: 'legalName'
        },
        {
          title: '营业执照名称',
          dataIndex: 'licenseName'
        },
        {
          title: '营业执照编号',
          dataIndex: 'licenseNo'
        },
        {
          title: '归属省编码',
          dataIndex: 'provCode'
        },
        {
          title: '归属省名称',
          dataIndex: 'provName'
        },
        {
          title: '归属市编码',
          dataIndex: 'cityCode'
        },
        {
          title: '归属市名称',
          dataIndex: 'cityName'
        },
        {
          title: '使用状态',
          dataIndex: 'usedStatus',
          key: 'usedStatus',
          align: 'center'
        },
        {
          title: '门店照上传状态',
          dataIndex: 'shopImageUploadStatus',
          key: 'shopImageUploadStatus',
          align: 'center'
        },
        {
          title: '营业执照上传状态',
          dataIndex: 'licenseImageUploadStatus',
          key: 'licenseImageUploadStatus',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          width: 220,
          fixed: 'right',
          align: 'center'
        }
      ],
      // 表格搜索条件
      where: {},
      showEdit: false,
      showDetail: false,
      showSupplyImages: false,
      showBatchUpload: false,
      channelCodes: [],
      current: null,

      regionsData: [],
      areaCodeValue: [],
      areaRange: 2,
      cascaderKey: 0
    };
  },
  mounted() {
    this.getChannelList();
    this.loadAreaData();
  },
  methods: {
    changeAreaRange() {
      this.cascaderKey++;
      this.areaCodeValue = [];
      this.regionsData = [];
      this.selectedAreaValue();
      this.loadAreaData();
    },
    async loadAreaData(selectedOptions) {
      const targetOption = selectedOptions ? selectedOptions[selectedOptions.length - 1] : { level: 1, code: '' };
      const { level, code } = targetOption;

      const data = await AreaApi.list({ level: level + 1, status: 1, parentCode: code });
      const filterData = data.map(d => {
        return { label: d.areaName, value: d.areaCode, code: d.areaCode, isLeaf: this.areaRange === level + 1, level: d.level };
      });

      if (level === 1) {
        this.regionsData = filterData;
      } else {
        targetOption.children = filterData;
      }
    },

    handleSupplyImages(record) {
      this.current = record;
      this.showSupplyImages = true;
    },

    selectedAreaValue(value) {
      [this.where.provCode, this.where.cityCode] = value || [];
    },

    async getChannelList() {
      const data = await ChannelManageApi.list({ validStatus: 1 });
      this.channelCodes = data;
    },

    reload() {
      this.selectedRow = null;
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.areaCodeValue = [];
      this.selectedRow = null;
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where, orders }) {
      return FastMerchPoolApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
