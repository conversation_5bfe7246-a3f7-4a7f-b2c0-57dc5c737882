<template>
  <a-modal
    :width="1000"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '30px' }"
    @update:visible="updateVisivle"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="商户等级">
        <a-tag v-if="form.merchGrade === 'A'" color="pink">企业商户</a-tag>
        <a-tag v-else-if="form.merchGrade === 'B'" color="cyan">个体工商户</a-tag>
        <a-tag v-else-if="form.merchGrade === 'C'" color="blue">小微商户</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="法人姓名">{{ form.legalName }}</a-descriptions-item>
      <a-descriptions-item label="营业执照名称">{{ form.licenseName }}</a-descriptions-item>
      <a-descriptions-item label="营业执照编号">{{ form.licenseNo }}</a-descriptions-item>
      <a-descriptions-item label="营业执照有效期">{{
        form.licenseStartDate ? `${form.licenseStartDate}-${form.licenseEndDate}` : '--'
      }}</a-descriptions-item>
      <a-descriptions-item label="营业执照注册地址">{{ form.licenseAddr }}</a-descriptions-item>
      <a-descriptions-item label="归属省编码">{{ form.provCode }}</a-descriptions-item>
      <a-descriptions-item label="归属省名称">{{ form.provName }}</a-descriptions-item>
      <a-descriptions-item label="归属市编码">{{ form.cityCode }}</a-descriptions-item>
      <a-descriptions-item label="归属市名称">{{ form.cityName }}</a-descriptions-item>
      <a-descriptions-item label="门店照上传状态">
        <a-tag color="success" v-if="form.shopImageUploadStatus === 1">已上传</a-tag>
        <a-tag v-else>未上传</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="营业执照上传状态">
        <a-tag color="success" v-if="form.licenseImageUploadStatus === 1">已上传</a-tag>
        <a-tag v-else>未上传</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="使用状态" :span="2">
        <a-tag color="success" v-if="form.usedStatus === 1">已使用</a-tag>
        <a-tag v-else>未使用</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>

    <a-collapse :active-key="['1']">
      <a-collapse-panel key="1" header="图片信息">
        <a-form :layout="'vertical'">
          <a-row :gutter="[16, 16]">
            <template v-for="(item, key) in fileList" :key="key">
              <a-col :md="6" :sm="24" :xs="24">
                <a-form-item :label="`${item.label}`">
                  <a-upload
                    v-model:file-list="item.fileData"
                    accept=".png, .jpg, .jpeg"
                    list-type="picture-card"
                    @preview="handlePreviewFile"
                    disabled
                  >
                    <div v-if="!item.fileData?.length">
                      <plus-outlined />
                      <div style="margin-top: 8px">Upload</div>
                    </div>
                  </a-upload>
                </a-form-item>
              </a-col>
            </template>
          </a-row>
          <!-- 预览图片 -->
          <a-image
            :style="{ display: 'none' }"
            :src="previewImage"
            :preview="{ visible: previewVisible, onVisibleChange: setPreviewVisible }"
          />
        </a-form>
      </a-collapse-panel>
    </a-collapse>

    <template #footer>
      <a-button @click="updateVisivle(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { FastMerchPoolApi } from '@/api/merchant/FastMerchPoolApi';

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  data() {
    return {
      form: {},
      // 预览图片
      previewImage: '',
      previewVisible: false,
      //图片数组
      fileList: [
        {
          label: '营业执照照片',
          fileType: 7,
          fileData: []
        },
        {
          label: '门头照',
          fileType: 11,
          fileData: []
        },
        {
          label: '经营场所照片',
          fileType: 12,
          fileData: []
        },

        {
          label: '收银台照片',
          fileType: 13,
          fileData: []
        }
      ]
    };
  },
  mounted() {
    if (this.detail) {
      this.form = Object.assign({}, this.detail);

      this.getFiles();
    }
  },
  methods: {
    async getFiles() {
      const data = await FastMerchPoolApi.detail({ id: this.detail.id });
      // 处理图片
      const fileListMap = data.imageJsonList || [];
      fileListMap.forEach(fileItem => {
        const findItem = this.fileList.find(item => item.fileType === fileItem.imageType);
        if (findItem) {
          findItem.fileData = fileItem.imagePath ? [...findItem.fileData, { url: fileItem.imagePath, id: fileItem.id }] : findItem.fileData;
        }
      });
    },
    handlePreviewFile(file) {
      this.previewImage = file?.url;
      this.setPreviewVisible(true);
    },

    setPreviewVisible(visible) {
      this.previewVisible = visible;
    },

    updateVisivle(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
