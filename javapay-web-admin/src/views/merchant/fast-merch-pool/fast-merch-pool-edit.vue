<template>
  <a-modal
    :width="800"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新增'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules" layout="vertical">
      <a-tabs v-model:activeKey="activeTabKey" type="card">
        <a-tab-pane key="0" tab="基本信息">
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="商户等级" name="merchGrade">
                <a-select v-model:value="form.merchGrade" style="width: 100%" placeholder="请选择" disabled>
                  <a-select-option value="A">企业商户</a-select-option>
                  <a-select-option value="B">个体工商户</a-select-option>
                  <a-select-option value="C">小微商户</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="归属地" name="provCode">
                <a-cascader
                  v-model:value="officeAreaValue"
                  :options="officeRegionsData"
                  :load-data="options => loadAreaData(options)"
                  :allow-clear="false"
                  placeholder="请选择"
                  @change="selectedOfficeAreaValue"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="法人姓名" name="legalName">
                <a-input v-model:value="form.legalName" placeholder="请输入法人姓名" allow-clear />
              </a-form-item>

              <a-form-item label="营业执照编号" name="licenseNo">
                <a-input v-model:value="form.licenseNo" placeholder="请输入营业执照编号" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="营业执照名称" name="licenseName">
                <a-input v-model:value="form.licenseName" placeholder="请输入营业执照名称" allow-clear />
              </a-form-item>
              <a-form-item label="营业执照有效期" name="licenseEndDate">
                <a-space>
                  <a-date-picker v-model:value="form.licenseStartDate" :showToday="false" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
                  -
                  <a-date-picker v-model:value="form.licenseEndDate" :showToday="false" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD">
                    <template #renderExtraFooter>
                      <a-space>
                        <a-typography-text keyboard @click="handleQuickOptions(10)">十年</a-typography-text>
                        <a-typography-text keyboard @click="handleQuickOptions(20)">二十年</a-typography-text>
                        <a-typography-text keyboard @click="handleQuickOptions(30)">三十年</a-typography-text>
                        <a-typography-text keyboard @click="handleQuickOptions('长期')">长期</a-typography-text>
                      </a-space>
                    </template>
                  </a-date-picker>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-tab-pane>

        <!-- 图片信息 -->
        <a-tab-pane key="4" tab="图片信息">
          <a-row :gutter="24">
            <a-col :span="6" v-for="(item, key) in Object.values(fileMap)" :key="key">
              <a-form-item :label="item.label" :required="item.required">
                <a-upload
                  v-model:file-list="item.fileData"
                  accept=".png,.jpg,.jpeg,.gif"
                  :multiple="false"
                  list-type="picture-card"
                  :before-upload="file => beforeUpload(file, item)"
                  @remove="() => handleRemove(item)"
                  @preview="() => handlePreview(item)"
                >
                  <div v-if="!item.fileData?.length">
                    <plus-outlined />
                    <div style="margin-top: 8px">Upload</div>
                  </div>
                </a-upload>
              </a-form-item>
            </a-col>
          </a-row>
        </a-tab-pane>
      </a-tabs>
    </a-form>

    <!-- 预览图片 -->
    <a-image :style="{ display: 'none' }" :src="previewImage" :preview="{ visible: previewVisible, onVisibleChange: setPreviewVisible }" />
  </a-modal>
</template>

<script>
import { message, Upload } from 'ant-design-vue';
import { AreaApi } from '@/api/base/AreaApi';
import { deepCopy } from '@/utils/util';
import { compressorImage } from '@/utils/image-compressor-util';
import { FastMerchPoolApi } from '@/api/merchant/FastMerchPoolApi';
import dayjs from 'dayjs';

export default {
  props: {
    visible: Boolean,
    data: Object,
    channelCodes: Array
  },
  emits: ['done', 'update:visible'],
  data() {
    const _this = this;
    return {
      activeTabKey: '0',
      // 表单数据
      form: {
        merchGrade: 'B'
      },
      //图片数组
      fileMap: {
        7: {
          label: '营业执照照片',
          fileType: 7,
          required: true
        },
        11: {
          label: '门头照',
          fileType: 11,
          required: false
        },
        12: {
          label: '经营场所照片',
          fileType: 12,
          required: false
        },

        13: {
          label: '收银台照片',
          fileType: 13,
          required: false
        }
      },
      officeAreaValue: [],
      officeRegionsData: [],
      // 表单验证规则
      rules: {
        legalName: [{ required: true, message: '请填写法人姓名' }],
        licenseEndDate: [{ required: true, message: '请选择有效期开始和结束时间',validator: (rule, value) => {
          if (value && _this.form.licenseStartDate) {
            return Promise.resolve();
          }
          return Promise.reject('请选择有效期开始和结束时间');
        } ,trigger: 'submit'}],
        licenseName: [{ required: true, message: '请填写营业执照名称' }],
        licenseNo: [{ required: true, message: '请填写营业执照编号' }],
        provCode: [{ required: true, message: '请选择' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false,
      //图片预览的数据
      previewVisible: false,
      previewImage: ''
    };
  },
  created() {
    if (this.data) {
      this.isUpdate = true;
      Object.assign(this.form, this.data);

      if (this.data.provCode) {
        this.officeAreaValue = [this.data.provCode];
      }

      if (this.data.licenseEndDate === '长期') {
        this.form.licenseEndDate = '2099-12-31';
      }

      // 反显图片
      this.getFiles();
    }
    this.loadAreaData();
  },
  methods: {
    async getFiles() {
      const data = await FastMerchPoolApi.detail({ id: this.data.id });
      const fileListMap = data.imageJsonList || [];
      // 处理图片
      if (fileListMap?.length) {
        Object.values(this.fileMap).forEach(f => {
          const item = fileListMap.find(i => i.imageType === f.fileType);
          if (item) {
            f.fileData = item.imagePath ? [{ url: item.imagePath }] : [];
            f.fileName = item.imageName;
          }
        });
      }
    },

    async save() {
      // 校验表单
      await this.$refs.form.validate();

      //校验图片是否上传完整
      await this.validateFileList();
      this.form.fileDTOList = Object.values(this.fileMap)
        .map(item => {
          if (item.fileData?.[0]) {
            return {
              fileName: item.fileName,
              fileData: item.fileData[0].url,
              fileType: item.fileType,
              suffixType: item.suffixType || 'png'
            };
          }
          return null;
        })
        .filter(f => !!f);

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      // 执行编辑或修改方法
      if (this.isUpdate) {
        result = FastMerchPoolApi.edit(this.form);
      } else {
        result = FastMerchPoolApi.add(this.form);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    async loadAreaData(selectedOptions, totalLevel = 1) {
      const targetOption = selectedOptions ? selectedOptions[selectedOptions.length - 1] : { level: 1, code: '' };
      const { level, code } = targetOption;
      const data = await AreaApi.list({ level: level + 1, status: 1, parentCode: code });
      const filterData = data.map(d => {
        return { label: d.areaName, value: d.areaCode, code: d.areaCode, isLeaf: level > totalLevel - 1, level: d.level };
      });
      if (level === 1) {
        this.officeRegionsData = deepCopy(filterData);
      } else {
        targetOption.children = filterData;
      }
    },

    selectedOfficeAreaValue(value, options) {
      this.form.provCode = value[0] || '';
      this.form.provName = options[0].label;
      // this.form.cityName = options[1].label;
    },

    beforeUpload(file, item) {
      compressorImage(file).then(({ url, mime }) => {
        item.fileData = [{ url }];
        item.fileName = file.uid + file.name;
        item.suffixType = mime.split('/')[1];
      });

      return Upload.LIST_IGNORE;
    },

    handleRemove(item) {
      item.fileData = [];
      item.suffixType = '';
    },

    handlePreview({ fileData }) {
      this.previewImage = fileData[0].url;
      this.setPreviewVisible(true);
    },

    setPreviewVisible(visible) {
      this.previewVisible = visible;
    },

    validateFileList() {
      return new Promise((resolve, reject) => {
        if (Object.values(this.fileMap).every(item => !item.required || item.fileData?.length)) {
          resolve();
        } else {
          message.warn('请检查是否有图片未上传');
          reject();
        }
      });
    },

    handleQuickOptions(value) {
      switch (value) {
        case '长期':
          this.form.licenseEndDate = '2099-12-31';
          break;
        default:
          if (this.form.licenseStartDate) {
            // eslint-disable-next-line no-case-declarations
            const date = dayjs(this.form.licenseStartDate).add(Number(value), 'year').format('YYYY-MM-DD');
            this.form.licenseEndDate = date;
          }
          break;
      }
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
