<template>
  <a-modal
    :width="630"
    :visible="visible"
    :confirm-loading="loading"
    title="批量导入资料"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules" :label-col="{ style: { width: '100px' } }">
      <a-form-item label="上传文件" name="file">
        <a-upload-dragger
          :file-list="form.file ? [form.file] : []"
          accept=".xlsx,.xls"
          :maxCount="1"
          :before-upload="beforeUpload"
          @remove="handleRemove"
          style="padding: 24px 0"
        >
          <p class="ant-upload-drag-icon">
            <cloud-upload-outlined />
          </p>
          <p class="ant-upload-hint" style="margin-bottom: 10px">将文件拖到此处，或点击上传</p>
          <div class="ele-text-center">
            <a @click.stop="downloadTemplateFile">下载模板文件 </a>
          </div>
        </a-upload-dragger>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { FastMerchPoolApi } from '@/api/merchant/FastMerchPoolApi';

export default {
  props: {
    visible: Boolean,
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {},
      // 表单验证规则
      rules: {
        file: [{ required: true, message: '请上传文件' }]
      },
      // 提交状态
      loading: false
    };
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      const formData = new FormData();
      formData.append('file', this.form.file);

      FastMerchPoolApi.excelBatchImport(formData)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    beforeUpload(file) {
      this.form.file = file;
      return false;
    },

    handleRemove() {
      this.form.file = null;
    },

    async downloadTemplateFile() {
      const res = await FastMerchPoolApi.downloadTemplateFile({}).catch(error => {
        message.destroy();
        const reader = new FileReader(); //创建一个FileReader实例
        reader.readAsText(error, 'utf-8'); //读取文件,结果用字符串形式表示
        reader.onload = function () {
          const { message: errorMsg } = JSON.parse(reader.result);
          message.error(errorMsg || '下载失败');
        };
      });

      const fileReader = new FileReader();
      fileReader.onload = () => {
        try {
          // 说明是普通对象数据，后台转换失败
          const jsonData = JSON.parse(fileReader.result);
          message.error(jsonData.message);
        } catch (err) {
          const fileName = '模板文件.xlsx';

          // 解析对象失败，说明是正常的文件流
          let blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.style.display = 'none';
          a.href = url;
          a.download = fileName;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }
      };
      fileReader.readAsText(res?.data);
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
