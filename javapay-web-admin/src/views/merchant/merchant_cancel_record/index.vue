<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="登录账号">
              <a-input v-model:value.trim="where.loginAccount" placeholder="请输入登录账号" allow-clear />
            </a-form-item>
            <a-form-item label="商户名称">
              <a-input v-model:value.trim="where.merchantName" placeholder="请输入商户名称" allow-clear />
            </a-form-item>
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table
          ref="table"
          row-key="id"
          :datasource="datasource"
          :columns="columns"
          :where="where"
          :scroll="{ x: 'max-content' }"
        >
          <template #bodyCell="{ column, record }">
            <!-- table操作栏按钮 -->
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>
    <!-- 详情 -->
    <MerchantCancelRecordDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { MerchantCancelRecordApi } from '@/api/merchant/MerchantCancelRecordApi';
import MerchantCancelRecordDetail from './merchant-cancel-record-detail.vue';

export default {
  name: 'MerchantCancel',
  components: {
    MerchantCancelRecordDetail
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '商户名称',
          dataIndex: 'merchantName'
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo'
        },
        {
          title: '登录账号',
          dataIndex: 'loginAccount'
        },
        {
          title: '法人手机号',
          dataIndex: 'legalTelMask'
        },
        {
          title: '代理名称',
          dataIndex: 'agentName',
          align: 'center'
        },
        {
          title: '代理编号',
          dataIndex: 'agentNo'
        },
        {
          title: 'C端登录用户ID',
          dataIndex: 'userId'
        },
        {
          title: 'app端登录用户ID',
          dataIndex: 'customerId',
          align: 'center'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          width: 120,
          align: 'center',
          fixed: 'right'
        }
      ],
      // 表格搜索条件
      where: {},
      selection: [],
      current: null,
      showDetail: false,
      checkform: {},
      showCheck: false,
      loading: false
    };
  },
  methods: {
    reload() {
      this.selection = [];
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.selection = [];
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    handleCheck(row) {
      this.checkform = Object.assign({
        checkStatus: 2,
        remarks: '',
        merchantNo: row.merchantNo,
        id: row.id
      });
      this.showCheck = true;
    },

    async datasource({ page, limit, where }) {
      return MerchantCancelRecordApi.findPage({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>
@/api/merchant/MerchantCancelRecordApi