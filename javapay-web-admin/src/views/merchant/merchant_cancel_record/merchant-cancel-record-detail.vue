<template>
  <a-modal :width="1000" :visible="visible" title="详情" :mask-closable="false" :body-style="{ paddingBottom: '20px' }" @update:visible="updateVisible">
    <a-descriptions bordered :column="2">
      <a-descriptions-item label="商户名称">{{ form.merchantName }}</a-descriptions-item>
      <a-descriptions-item label="商户编号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="登录账号">{{ form.loginAccount }}</a-descriptions-item>
      <a-descriptions-item label="法人手机号">{{ form.legalTelMask }}</a-descriptions-item>
      <a-descriptions-item label="代理名称">{{ form.agentName }}</a-descriptions-item>
      <a-descriptions-item label="代理编号">{{ form.agentNo }}</a-descriptions-item>
      <a-descriptions-item label="C端登录用户ID">{{ form.userId }}</a-descriptions-item>
      <a-descriptions-item label="app端登录用户ID">{{ form.customerId }}</a-descriptions-item>
      <a-descriptions-item label="创建人">{{ form.createUserId }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
    </a-descriptions>

    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

export default {
  name: 'MerchantCancelRecordDetail',
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
