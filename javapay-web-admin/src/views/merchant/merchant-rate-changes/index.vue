<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="政策编号">
              <a-input v-model:value.trim="where.policyNo" placeholder="请输入政策编号" allow-clear />
            </a-form-item>
            <a-form-item label="费率编号">
              <a-input v-model:value.trim="where.templateNo" placeholder="请输入费率编号" allow-clear />
            </a-form-item>
            <a-form-item label="支付通道">
              <a-select v-model:value="where.channelCode" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
                  {{ channelName }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="操作用户ID" v-purview="'0'">
              <a-input v-model:value.trim="where.optUserId" placeholder="请输入操作用户ID" allow-clear />
            </a-form-item>
            <a-form-item label="操作用户编号" v-purview="'0'">
              <a-input v-model:value.trim="where.optUserNo" placeholder="请输入操作用户编号" allow-clear />
            </a-form-item>
            <a-form-item label="费率类型">
              <a-select v-model:value="where.rateType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">扫码费率</a-select-option>
                <a-select-option :value="2">刷卡费率</a-select-option>
                <a-select-option :value="3">EPOS费率</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="通道商户费率修改状态">
              <a-select v-model:value="where.chnEditStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">未修改</a-select-option>
                <a-select-option :value="1">已修改</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="通道同步状态">
              <a-select v-model:value="where.chnSyncStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">未同步</a-select-option>
                <a-select-option :value="1">成功</a-select-option>
                <a-select-option :value="2">失败</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="商户费率修改状态">
              <a-select v-model:value="where.merchEditStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">未修改</a-select-option>
                <a-select-option :value="1">已修改</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'rateType'">
              <a-tag v-if="record.rateType === 1">扫码费率</a-tag>
              <a-tag v-else-if="record.rateType === 2">刷卡费率</a-tag>
              <a-tag v-else-if="record.rateType === 3">EPOS费率</a-tag>
            </template>
            <template v-else-if="column.key === 'chnEditStatus'">
              <a-tag v-if="record.chnEditStatus === 0">未修改</a-tag>
              <a-tag v-else-if="record.chnEditStatus === 1" color="success">已修改</a-tag>
            </template>
            <template v-else-if="column.key === 'chnSyncStatus'">
              <a-tag v-if="record.chnSyncStatus === 0">未同步</a-tag>
              <a-tag v-else-if="record.chnSyncStatus === 1" color="success">成功</a-tag>
              <a-tag v-else-if="record.chnSyncStatus === 2" color="error">失败</a-tag>
            </template>
            <template v-else-if="column.key === 'merchEditStatus'">
              <a-tag v-if="record.merchEditStatus === 0">未修改</a-tag>
              <a-tag v-else-if="record.merchEditStatus === 1" color="success">已修改</a-tag>
            </template>
            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">变更详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <MerchantRateChangesDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { MerchantApi } from '@/api/merchant/MerchantApi';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import MerchantRateChangesDetail from './merchant-rate-changes-detail.vue';
import { hasPurview } from '@/utils/permission';

export default {
  name: 'MerchantRateChanges',
  components: {
    MerchantRateChangesDetail
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          hideCol: !hasPurview('0')
        },
        {
          title: '支付通道',
          dataIndex: 'channelCode',
          align: 'center',
          customRender: ({ text }) => {
            const item = this.channelCodes.find(c => c.channelCode === text);
            return item?.channelName || '--';
          }
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo'
        },
        {
          title: '费率编号',
          dataIndex: 'templateNo',
          align: 'center'
        },
        {
          title: '政策编号',
          dataIndex: 'policyNo',
          align: 'center'
        },
        {
          title: '费率类型',
          dataIndex: 'rateType',
          key: 'rateType',
          align: 'center'
        },
        {
          title: '通道商户费率修改状态',
          dataIndex: 'chnEditStatus',
          key: 'chnEditStatus',
          align: 'center'
        },
        {
          title: '通道同步状态',
          dataIndex: 'chnSyncStatus',
          key: 'chnSyncStatus',
          align: 'center'
        },
        {
          title: '商户费率修改状态',
          dataIndex: 'merchEditStatus',
          key: 'merchEditStatus',
          align: 'center'
        },
        {
          title: '版本号',
          dataIndex: 'version',
          align: 'center'
        },
        {
          title: '失败原因',
          dataIndex: 'failReason',
          width: 180
        },
        {
          title: '操作用户ID',
          dataIndex: 'optUserId',
          hideCol: !hasPurview('0')
        },
        {
          title: '操作用户编号',
          dataIndex: 'optUserNo',
          hideCol: !hasPurview('0')
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '修改人',
          dataIndex: 'lastModifyUserId'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          width: 100,
          align: 'center',
          fixed: 'right'
        }
      ].filter(i => !i.hideCol),
      // 表格搜索条件
      where: {},
      current: null,
      channelCodes: [],
      showDetail: false
    };
  },
  async mounted() {
    const data = await ChannelManageApi.list({ validStatus: 1 });
    this.channelCodes = data;
  },
  methods: {
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = {
        rateInfoDTO: row.rateInfo,
        oriRateInfoDTO: row.oldRateInfo
      };
      this.showDetail = true;
    },

    datasource({ page, limit, where, orders }) {
      return MerchantApi.merchantRateEditPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
