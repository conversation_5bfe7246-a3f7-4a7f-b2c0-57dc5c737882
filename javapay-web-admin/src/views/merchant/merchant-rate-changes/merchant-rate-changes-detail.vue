<template>
  <a-modal
    title="费率变更详情"
    :width="1000"
    :visible="visible"
    :body-style="{ paddingBottom: '8px' }"
    :mask-closable="false"
    @update:visible="updateVisible"
  >
    <a-alert message="左侧为新内容, 右侧为旧内容" show-icon type="info" style="margin-bottom: 24px" />
    <a-form :label-col="{ md: { span: 6 }, sm: { span: 24 } }" :wrapper-col="{ md: { span: 18 }, sm: { span: 24 } }" :colon="false">
      <a-form-item
        :class="{
          'info-changed': isInfoChanged(form.rateInfoDTO[infoKey], form.oriRateInfoDTO[infoKey]),
          'hide-label': infoKey === 'divider'
        }"
        v-for="({ label, infoKey }, keyi) in rateInfoMap[form.rateInfoDTO.rateType]"
        :key="keyi"
        :label="label"
      >
        <template v-if="infoKey !== 'divider'">
          <a-input v-for="(item, keyc) in [form.rateInfoDTO, form.oriRateInfoDTO]" :key="keyc" :value="item[infoKey]" readonly />
        </template>
        <a-divider v-else style="height: 100%" orientation="left" orientationMargin="0" dashed>{{ label }}</a-divider>
      </a-form-item>
    </a-form>

    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

// 扫码相关的字段
const allScanRateFieldsMap = [
  { field: 'creditRate', name: '银联标准贷记卡费率(%)' },
  { field: 'debitRate', name: '银联标准借记卡费率(%)' },
  { field: 'debitFeeMax', name: '银联标准借记卡手续费封顶(元)' },
  { field: 'nfcCreditRate', name: '银联云闪付贷记卡费率(%)' },
  { field: 'nfcDebitRate', name: '银联云闪付借记卡费率(%)' },
  { field: 'aliPayRate', name: '支付宝扫码费率(%)' },
  { field: 'aliPayLargeRate', name: '支付宝大额费率(%)' },
  { field: 'wechatRate', name: '微信扫码费率(%)' },
  { field: 'creditQrD0Rate', name: '贷记卡D0附加费率(%)' },
  { field: 'creditQrD0SingleFee', name: '贷记卡D0附加单笔(元)' },
  { field: 'debitQrD0Rate', name: '借记卡D0附加费率(%)' },
  { field: 'debitQrD0SingleFee', name: '借记卡D0附加单笔(元)' }
];

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    // 费率信息
    const rateInfoMap = {
      1: [],
      2: [
        { label: 'POS收单交易', infoKey: 'divider' },
        { label: '银联标准贷记卡费率(%)', infoKey: 'creditRate' },
        { label: '银联标准借记卡费率(%)', infoKey: 'debitRate' },
        { label: '银联标准借记卡手续费封顶值(元)', infoKey: 'debitFeeMax' },
        { label: '银联云闪付贷记卡费率(%)', infoKey: 'nfcCreditRate' },
        { label: '银联云闪付借记卡费率(%)', infoKey: 'nfcDebitRate' },
        { label: '刷卡贷记卡D0附加费率(%)', infoKey: 'creditD0Rate' },
        { label: '刷卡借记卡D0附加费率(%)', infoKey: 'debitD0Rate' },
        { label: '刷卡贷记卡D0附加单笔费用(元)', infoKey: 'creditD0SingleFee' },
        { label: '刷卡借记卡D0附加单笔费用(元)', infoKey: 'debitD0SingleFee' }
      ],
      3: [
        { label: '无卡交易', infoKey: 'divider' },
        { label: '无卡贷记卡费率(%)', infoKey: 'creditEposRate' },
        { label: '无卡贷记卡D0费率(%)', infoKey: 'creditEposD0Rate' },
        { label: '无卡借记卡D0费率(%)', infoKey: 'debitEposD0Rate' },
        { label: '无卡借记卡费率(%)', infoKey: 'debitEposRate' },
        { label: '无卡贷记卡D0附加单笔费用(元)', infoKey: 'creditEposD0SingleFee' },
        { label: '无卡借记卡D0附加单笔费用(元)', infoKey: 'debitEposD0SingleFee' }
      ]
    };

    // 设置表单默认值
    function formDefaults() {
      return {
        rateInfoDTO: {},
        oriRateInfoDTO: {}
      };
    }
    const data = reactive({
      form: formDefaults()
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = formDefaults();

        Object.keys(data.form).forEach(k => {
          data.form[k] = props.detail[k]
            ? typeof props.detail[k] === 'string'
              ? JSON.parse(props.detail[k])
              : props.detail[k]
            : data.form[k];
        });

        const rateKeys = Object.keys(data.form.rateInfoDTO || {});
        const targetScanRateList = allScanRateFieldsMap.filter(item => rateKeys.includes(item.field));
        const scanRateList = targetScanRateList.map(item => {
          return {
            label: item.name,
            infoKey: item.field
          };
        });
        rateInfoMap[1] = [{ label: '线下收单', infoKey: 'divider' }, ...scanRateList];
      }
    });

    const isInfoChanged = (current, old) => {
      if ([current, old].every(i => i !== 0 && !i)) return false;
      return current !== old;
    };

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible,
      rateInfoMap,
      isInfoChanged
    };
  }
};
</script>

<style lang="less" scoped>
:deep(.ant-form-item-label) {
  text-align: center;
  background-color: #f1f3f4;
}
.info-changed {
  :deep(.ant-form-item-label) {
    background-color: #fa541cc9;
  }
}
.hide-label {
  :deep(.ant-form-item-label) {
    display: none;
  }
  .ant-divider-horizontal.ant-divider-with-text {
    margin: 0;
  }
}
:deep(.ant-row) {
  align-items: flex-start;
  .ant-form-item-control-input-content {
    display: flex;
    .ant-input,
    .ant-select-selector {
      border-radius: 0;
    }
  }
}
:deep(.ant-select-disabled.ant-select:not(.ant-select-customize-input)) .ant-select-selector {
  background-color: transparent;
}
</style>
