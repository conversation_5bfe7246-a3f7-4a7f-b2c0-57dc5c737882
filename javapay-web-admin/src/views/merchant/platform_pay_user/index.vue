<template>
  <div class="ele-body">
    <!-- 搜索框内容 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="用户姓名">
              <a-input v-model:value.trim="where.realName" placeholder="请输入用户姓名" allow-clear />
            </a-form-item>

            <a-form-item label="付款用户ID">
              <a-input v-model:value.trim="where.id" placeholder="请输入付款用户ID" allow-clear />
            </a-form-item>

            <a-form-item label="微信用户唯一标识">
              <a-input v-model:value.trim="where.wxUserId" placeholder="请输入通道名称" allow-clear />
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- 表体的操作 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'isSetPayPwd'">
              <a-tag v-if="record.isSetPayPwd === 0" color="cyan">否</a-tag>
              <a-tag v-else-if="record.isSetPayPwd === 1" color="pink">是</a-tag>
            </template>
            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-popconfirm title="确定要删除此行数据吗？" @confirm="remove(record)">
                  <a class="ele-text-danger">删除</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>
  </div>
</template>

<script>

import { PlatformPayUserApi } from '@/api/merchant/PlatformPayUserApi';
import { message } from 'ant-design-vue';

export default {
  name: 'PlatformPayUser',
  data() {
    return {
      //表格查询条件
      where: {},
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
        },
        {
          title: '用户姓名',
          dataIndex: 'realName',
          align: 'center',
        },
        {
          title: '用户身份证',
          dataIndex: 'idCardMask',
          align: 'center',
        },
        {
          title: '用户手机号',
          dataIndex: 'mobileMask',
          align: 'center',
        },
        {
          title: '是否设置支付密码',
          dataIndex: 'isSetPayPwd',
          key: 'isSetPayPwd',
          align: 'center',
        },
        {
          title: '微信用户唯一标识',
          dataIndex: 'wxUserId',
          align: 'center',
        },
        {
          title: '支付宝用户唯一标识',
          dataIndex: 'aliUserId',
          align: 'center',
        },
        {
          title: '云闪付用户唯一标识',
          dataIndex: 'ysfUserId',
          align: 'center',
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right',
          width: 150
        }
      ],
    }
  },

  methods: {
    //查询方法
    reload() {
      this.$refs.table.reload({ page : 1});
    },

    //重置
    reset() {
      this.where = {}; //清空查询条件

      this.$refs.table.reload({page:1,where:this.where})
    },

    //删除
    async remove(row) {
      const result = await PlatformPayUserApi.delete({ id: row.id });
      message.success(result.message);
      this.reload();
    },

    //获取数据
    datasource({ page, limit, where, orders }) {
      return PlatformPayUserApi.getPlatformPayUserPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }

};
</script>

<style>
</style>
