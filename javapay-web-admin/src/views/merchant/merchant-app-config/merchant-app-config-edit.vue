<template>
  <a-modal
    :width="1200"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新建'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @cancel="cancel"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 7 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 17 }, sm: { span: 24 } }"
    >
      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="商户编号" name="merchantNo">
            <a-input
              style="width: 100%"
              v-model:value="form.merchantNo"
              placeholder="请输入商户编号"
              allow-clear
            />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="等级编号" name="appType">
            <a-select
              v-model:value="form.appType"
              placeholder="请选择"
              style="width: 100%"
            >
              <a-select-option :value="1">微信公众号</a-select-option>
              <a-select-option :value="2">微信小程序</a-select-option>
              <a-select-option :value="3">支付宝生活号</a-select-option>
              <a-select-option :value="4">云闪付小程序</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24" v-if="form.appType == 1">
          <a-form-item label="微信公钥" name="wxSecretKey">
            <a-input
              style="width: 100%"
              v-model:value="form.secretPayParamDTO.wxSecretKey"
              placeholder="请输入微信公钥"
              allow-clear
            />
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24" v-if="form.appType == 1">
          <a-form-item label="微信APP_ID" name="wxAppId">
            <a-input
              style="width: 100%"
              v-model:value="form.secretPayParamDTO.wxAppId"
              placeholder="请输入微信APP_ID"
              allow-clear
            />
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24" v-if="form.appType == 3">
          <a-form-item
            label="支付宝第三方应用APP_ID"
            name="aliThirdAppid"
          >
            <a-input
              style="width: 100%"
              v-model:value="form.secretPayParamDTO.aliThirdAppid"
              placeholder="请输入支付宝第三方应用APP_ID"
              allow-clear
            />
          </a-form-item>
          <a-form-item
            label="支付宝第三方应用私钥"
            name="aliThirdPriKey"
          >
            <a-input
              style="width: 100%"
              v-model:value="form.secretPayParamDTO.aliThirdPriKey"
              placeholder="请输入支付宝第三方应用私钥"
              allow-clear
            />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24" v-if="form.appType == 3">
          <a-form-item label="支付宝公钥" name="aliPubKey">
            <a-input
              style="width: 100%"
              v-model:value="form.secretPayParamDTO.aliPubKey"
              placeholder="请输入支付宝公钥"
              allow-clear
            />
          </a-form-item>
          <a-form-item
            label="支付宝第三方应用公钥"
            name="aliThirdPubKey"
          >
            <a-input
              style="width: 100%"
              v-model:value="form.secretPayParamDTO.aliThirdPubKey"
              placeholder="请输入支付宝第三方应用公钥"
              allow-clear
            />
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24" v-if="form.appType == 4">
          <a-form-item label="云闪付APP_ID" name="ysfAppid">
            <a-input
              style="width: 100%"
              v-model:value="form.secretPayParamDTO.ysfAppid"
              placeholder="请输入云闪付APP_ID"
              allow-clear
            />
          </a-form-item>
          <a-form-item
            label="云闪付加密密钥"
            name="ysfEncryptKey"
          >
            <a-input
              style="width: 100%"
              v-model:value="form.secretPayParamDTO.ysfEncryptKey"
              placeholder="请输入云闪付加密密钥"
              allow-clear
            />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24" v-if="form.appType == 4">
          <a-form-item
            label="云闪付应用APP_ID"
            name="ysfSubAppid"
          >
            <a-input
              style="width: 100%"
              v-model:value="form.secretPayParamDTO.ysfSubAppid"
              placeholder="请输入云闪付应用APP_ID"
              allow-clear
            />
          </a-form-item>
          <a-form-item label="云闪付签名密钥" name="ysfSignKey">
            <a-input
              style="width: 100%"
              v-model:value="form.secretPayParamDTO.ysfSignKey"
              placeholder="请输入云闪付签名密钥"
              allow-clear
            /> 
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from "ant-design-vue";
import { MerchantAppConfigApi } from "@/api/merchant/MerchantAppConfigApi";

export default {
  name: "MerchantAppConfigEdit",
  components: {},
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    data: Object,
  },
  emits: ["done", "update:visible"],
  data() {
    return {
      // 表单数据
      form: {
        secretPayParamDTO: {},
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false,
      // 表单验证规则
      rules: {
        merchantNo: [{ required: true, message: "请输入商户编号" }],
        appType: [{ required: true, message: "请选择应用类型" }],
      },
    };
  },
  mounted() {
    if (this.data) {
      this.form = Object.assign({}, this.data);
      this.isUpdate = true;
    } else {
      (this.form = { secretPayParamDTO: {} }), (this.isUpdate = false);
    }

    if (this.$refs.form) {
      this.$refs.form.clearValidate();
    }
  },
  methods: {
    /**
     * 保存和编辑
     */
    async save() {
      // 校验表单
      await this.$refs.form.validate();
      // 修改加载框为正在加载
      this.loading = true;

      this.form.appConfigParamsDTO = this.form.secretPayParamDTO;
      let result = null;
      // 执行编辑或修改
      if (this.isUpdate) {
        result = MerchantAppConfigApi.edit(this.form);
      } else {
        result = MerchantAppConfigApi.add(this.form);
      }

      result
        .then((result) => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 如果是新增，则form表单置空
          if (!this.isUpdate) {
            this.form = { secretPayParamDTO: {} };
          }

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit("done");
        })
        .catch(() => {
          this.loading = false;
        });
    },

    cancel() {
      this.form = {
        secretPayParamDTO: {},
      };
      this.$refs.form.clearValidate();
    },

    /**
     * 更新编辑界面的弹框是否显示
     *
     * @param value true-显示，false-隐藏
     */
    updateVisible(value) {
      this.$emit("update:visible", value);
    },
  },
};
</script>

<style>
.coupon-margin {
  margin-top: 20px;
}

.coupon-bottom {
  margin-bottom: 50px;
}

.coupon-div-left {
  margin-left: 48px;
}
</style>
