<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
            </a-form-item>

            <a-form-item label="应用类型">
              <a-select v-model:value="where.appType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">微信公众号</a-select-option>
                <a-select-option :value="2">微信小程序</a-select-option>
                <a-select-option :value="3">支付宝生活号</a-select-option>
                <a-select-option :value="4">云闪付小程序</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- table上边工具栏 -->
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'appType'">
              <a-tag v-if="record.appType === 1" color="pink">微信公众号</a-tag>
              <a-tag v-else-if="record.appType === 2" color="cyan">微信小程序</a-tag>
              <a-tag v-else-if="record.appType === 3" color="blue">支付宝生活号</a-tag>
              <a-tag v-else-if="record.appType === 4" color="purple">云闪付小程序</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
                <a-divider type="vertical" />
                <a @click="handleEdit(record)">修改</a>
                <a-divider type="vertical" />
                <a-popconfirm title="确定要删除此行数据吗？" @confirm="remove(record)">
                  <a class="ele-text-danger">删除</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>
    <!-- 编辑 -->
    <MerchantAppConfigEdit v-model:visible="showEdit" :data="current" @done="reload" v-if="showEdit" />

    <!-- 详情 -->
    <MerchantAppConfigDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>
        
  <script>
import { MerchantAppConfigApi } from '@/api/merchant/MerchantAppConfigApi';
import { message } from 'ant-design-vue';
import MerchantAppConfigEdit from './merchant-app-config-edit.vue';
import MerchantAppConfigDetail from './merchant-app-config-detail.vue';

export default {
  name: 'MerchantAppConfig',
  components: {
    MerchantAppConfigEdit,
    MerchantAppConfigDetail
  },
  data() {
    return {
      // 表格搜索条件
      where: {},
      current: null,
      showEdit: false,
      showDetail: false,
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '商户ID',
          dataIndex: 'merchantId',
          align: 'center'
        },
        {
          title: '应用类型',
          dataIndex: 'appType',
          key: 'appType',
          align: 'center'
        },
        {
          title: '微信公钥',
          dataIndex: ['secretPayParamDTO', 'wxSecretKey'],
          align: 'center'
        },
        {
          title: '微信APP_ID',
          dataIndex: ['secretPayParamDTO', 'wxAppId'],
          align: 'center'
        },
        {
          title: '支付宝第三方应用APP_ID',
          dataIndex: ['secretPayParamDTO', 'aliThirdAppid'],
          align: 'center'
        },
        {
          title: '支付宝公钥',
          dataIndex: ['secretPayParamDTO', 'aliPubKey'],
          align: 'center'
        },
        {
          title: '支付宝第三方应用公钥',
          dataIndex: ['secretPayParamDTO', 'aliThirdPubKey'],
          align: 'center'
        },
        {
          title: '支付宝第三方应用私钥',
          dataIndex: ['secretPayParamDTO', 'aliThirdPriKey'],
          align: 'center'
        },
        {
          title: '云闪付APP_ID',
          dataIndex: ['secretPayParamDTO', 'ysfAppid'],
          align: 'center'
        },
        {
          title: '云闪付应用APP_ID',
          dataIndex: ['secretPayParamDTO', 'ysfSubAppid'],
          align: 'center'
        },
        {
          title: '云闪付加密密钥',
          dataIndex: ['secretPayParamDTO', 'ysfEncryptKey'],
          align: 'center'
        },
        {
          title: '云闪付签名密钥',
          dataIndex: ['secretPayParamDTO', 'ysfSignKey'],
          align: 'center'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right',
          width: 180
        }
      ]
    };
  },
  methods: {
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    handleEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    async remove(row) {
      const result = await MerchantAppConfigApi.delete({ id: row.id });
      message.success(result.message);
      this.reload();
    },

    datasource({ page, limit, where, orders }) {
      return MerchantAppConfigApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
        
        <style>
</style>