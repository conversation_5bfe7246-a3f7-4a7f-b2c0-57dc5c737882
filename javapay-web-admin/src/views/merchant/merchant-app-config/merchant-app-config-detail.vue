<template>
  <a-modal
    :width="750"
    :visible="visible"
    :maskClosable="false"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '30px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="商户编号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="商户ID">{{ form.merchantId }}</a-descriptions-item>
      <a-descriptions-item label="等级编号">
        <a-tag v-if="form.appType === 1" color="pink">微信公众号</a-tag>
        <a-tag v-else-if="form.appType === 2" color="cyan">微信小程序</a-tag>
        <a-tag v-else-if="form.appType === 3" color="blue">支付宝生活号</a-tag>
        <a-tag v-else-if="form.appType === 4" color="purple">云闪付小程序</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="微信公钥" v-if="form.appType === 1">{{ form.appConfigParamsDTO.wxSecretKey }}</a-descriptions-item>
      <a-descriptions-item label="微信APP_ID" v-if="form.appType === 2">{{ form.appConfigParamsDTO.wxAppId }}</a-descriptions-item>
      
      <a-descriptions-item label="支付宝第三方应用APP_ID" v-if="form.appType === 3">{{ form.appConfigParamsDTO.aliThirdAppid }}</a-descriptions-item>
      <a-descriptions-item label="支付宝公钥" v-if="form.appType === 3">{{ form.appConfigParamsDTO.aliPubKey }}</a-descriptions-item>
      <a-descriptions-item label="支付宝第三方应用公钥" v-if="form.appType === 3">{{ form.appConfigParamsDTO.aliThirdPubKey }}</a-descriptions-item>
      <a-descriptions-item label="支付宝第三方应用私钥" v-if="form.appType === 3">{{ form.appConfigParamsDTO.aliThirdPriKey }}</a-descriptions-item>

      <a-descriptions-item label="云闪付APP_ID" v-if="form.appType === 4">{{ form.appConfigParamsDTO.ysfAppid }}</a-descriptions-item>
      <a-descriptions-item label="云闪付应用APP_ID" v-if="form.appType === 4">{{ form.appConfigParamsDTO.ysfSubAppid }}</a-descriptions-item>
      <a-descriptions-item label="云闪付加密密钥" v-if="form.appType === 4">{{ form.appConfigParamsDTO.ysfEncryptKey }}</a-descriptions-item>
      <a-descriptions-item label="云闪付签名密钥" v-if="form.appType === 4">{{ form.appConfigParamsDTO.ysfSignKey }}</a-descriptions-item>
    </a-descriptions>

    <br />

    <a-descriptions :column="2">
      <a-descriptions-item label="创建人">{{ form.createUserId }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改人">{{ form.lastModifyUserId }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>

    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>
      
      <script>
export default {
  name: 'MerchantAppConfigDetail',
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  data() {
    return {
      form: {}
    };
  },
  watch: {
    //监控，外面点击切换就换值
    detail() {
      this.form = Object.assign({}, this.detail);
      this.form.appConfigParamsDTO = this.detail.secretPayParamDTO;
    }
  },
  methods: {
    //更新显示值
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
      
      <style>
</style>