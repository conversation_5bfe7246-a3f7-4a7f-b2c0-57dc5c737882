<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="通道编号">
              <a-input v-model:value.trim="where.channelCode" placeholder="请输入通道编号" allow-clear />
            </a-form-item>
            <a-form-item label="付款用户类型">
              <a-select v-model:value="where.payuserType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">平台付款用户</a-select-option>
                <a-select-option :value="2">商家会员用户</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="签约协议号">
              <a-input v-model:value.trim="where.agreementNo" placeholder="请输入签约协议号" allow-clear />
            </a-form-item>
            <a-form-item label="有效状态">
              <a-select v-model:value="where.validStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">无效</a-select-option>
                <a-select-option :value="0">有效</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="通道商户编号">
              <a-input v-model:value.trim="where.chnMerchNo" placeholder="请输入通道商户编号" allow-clear />
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'payuserType'">
              <a-tag v-if="record.payuserType === 1" color="pink">平台付款用户</a-tag>
              <a-tag v-else-if="record.payuserType === 2" color="cyan">商家会员用户</a-tag>
            </template>

            <template v-else-if="column.key === 'validStatus'">
              <a-tag v-if="record.validStatus === 1" color="success">有效</a-tag>
              <a-tag v-else-if="record.validStatus === 0" color="error">无效</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <PaycardSignRecordDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

  <script>
import { PaycardSignRecordApi } from '@/api/merchant/PaycardSignRecordApi';
import PaycardSignRecordDetail from './paycard-sign-record-detail.vue'

export default {
  name: 'PaycardSignRecord',
  components: {
    PaycardSignRecordDetail
  },
  data() {
    return {
      // 表格搜索条件
      where: {},
      current: null,
      showDetail: false,
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '付款用户类型',
          dataIndex: 'payuserType',
          key: 'payuserType',
          align: 'center'
        },
        {
          title: '通道商户ID',
          dataIndex: 'chnMerchId',
          align: 'center'
        },
        {
          title: '签约协议号',
          dataIndex: 'agreementNo',
          align: 'center'
        },
        {
          title: '通道商户编号',
          dataIndex: 'chnMerchNo',
          align: 'center'
        },
        {
          title: '通道编号',
          dataIndex: 'channelCode',
          align: 'center'
        },
        {
          title: '有效状态',
          dataIndex: 'validStatus',
          key: 'validStatus',
          align: 'center'
        },
        {
          title: '付款用户ID',
          dataIndex: 'payuserId',
          align: 'center'
        },
        {
          title: '付款银行卡ID',
          dataIndex: 'paycardId',
          align: 'center'
        },
        {
          title: '商户ID',
          dataIndex: 'merchantId',
          align: 'center'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right',
          width: 180
        }
      ]
    };
  },
  methods: {
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where, orders }) {
      return PaycardSignRecordApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>

<style>
</style>
