<template>
  <a-modal
    :width="750"
    :visible="visible"
    :maskClosable="false"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '30px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="商户编号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="付款用户类型">
        <a-tag v-if="form.payuserType === 1" color="pink">平台付款用户</a-tag>
        <a-tag v-if="form.payuserType === 2" color="cyan">商家会员用户</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="通道商户ID">{{ form.chnMerchId }}</a-descriptions-item>
      <a-descriptions-item label="签约协议号">{{ form.agreementNo }}</a-descriptions-item>
      <a-descriptions-item label="通道商户编号">{{ form.chnMerchNo }}</a-descriptions-item>
      <a-descriptions-item label="通道编号">{{ form.channelCode }}</a-descriptions-item>
      <a-descriptions-item label="付款用户类型">
        <a-tag v-if="form.payuserType === 1" color="success">平台付款用户</a-tag>
        <a-tag v-if="form.payuserType === 2" color="error">商家会员用户</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="付款用户ID">{{ form.payuserId }}</a-descriptions-item>
      <a-descriptions-item label="付款银行卡ID">{{ form.paycardId }}</a-descriptions-item>
      <a-descriptions-item label="商户ID">{{ form.merchantId }}</a-descriptions-item>
    </a-descriptions>

    <br />

    <a-descriptions :column="2">
      <a-descriptions-item label="创建人">{{ form.createUserId }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改人">{{ form.lastModifyUserId }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>

    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

  <script>
export default {
  name: 'PaycardSignRecordDetail',
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  data() {
    return {
      form: {}
    };
  },
  watch: {
    //监控，外面点击切换就换值
    detail() {
      this.form = Object.assign({}, this.detail);
    }
  },
  methods: {
    //更新显示值
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>

<style>
</style>
