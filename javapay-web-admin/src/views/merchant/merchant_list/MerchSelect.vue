<template>
  <a-modal
    :width="1000"
    :visible="visible"
    title="商户选择"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <!-- 搜索框内容 -->
    <a-card :bordered="false">
      <a-form layout="inline" :model="where">
        <a-row :gutter="[0, 16]">
          <a-form-item label="商户编号">
            <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
          </a-form-item>
          <a-form-item label="商户名称">
            <a-input v-model:value.trim="where.merchantName" placeholder="请输入商户名称" allow-clear />
          </a-form-item>
          <a-form-item label="登录账号">
            <a-input v-model:value.trim="where.loginAccount" placeholder="登录账号" allow-clear />
          </a-form-item>
          <a-form-item class="ele-text-center">
            <a-space>
              <a-button type="primary" @click="reload">查询</a-button>
              <a-button @click="reset">重置</a-button>
            </a-space>
          </a-form-item>
        </a-row>
      </a-form>
    </a-card>

    <!-- 表格内容 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table
          ref="table"
          row-key="id"
          :datasource="datasource"
          :columns="columns"
          :where="where"
          v-model:selection="selectedRows"
          :rowSelection="{ preserveSelectedRowKeys: true }"
          :scroll="{ x: 'max-content' }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'grade'">
              <a-tag v-if="record.grade == 'A'" color="cyan">企业商户</a-tag>
              <a-tag v-else-if="record.grade == 'B'" color="blue">个体工商户</a-tag>
              <a-tag v-else-if="record.grade == 'C'" color="purple">小微商户</a-tag>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>
  </a-modal>
</template>

<script>
import { MerchantApi } from '@/api/merchant/MerchantApi';
export default {
  props: {
    // 弹窗是否打开
    visible: Boolean
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      //表格查询条件
      where: {},
      //选择数据
      selectedRows: [],

      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '商户名称',
          dataIndex: 'merchantName'
        },
        {
          title: '法人姓名',
          dataIndex: 'legalName',
          align: 'center'
        },
        {
          title: '登录账号',
          dataIndex: 'loginAccount',
          align: 'center',
          customRender: ({ text }) => {
            return text ? text.replace(/(\d{3})\d*(\d{4})/, '$1 **** $2') : '--';
          }
        }
      ]
    };
  },
  methods: {
    save() {
      this.$emit('done', this.selectedRows || []);
      //关闭弹框
      this.updateVisible(false);
    },

    //重置
    reset() {
      this.where = {};
      this.selectedRows = [];

      this.$refs.table.reload({ page: 1, where: this.where });
    },

    //查询方法
    reload() {
      this.selectedRows = [];
      this.$refs.table.reload({ page: 1 });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    },

    datasource({ page, limit, where, orders }) {
      return MerchantApi.merchantPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>

<style></style>
