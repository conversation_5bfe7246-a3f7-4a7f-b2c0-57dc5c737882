<template>
  <a-modal
    :width="750"
    :visible="visible"
    :title="optType ? '审核' : '商户详情'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
  >
    <a-form layout="vertical" ref="formRef" :model="verifyForm">
      <a-tabs v-model:activeKey="activeKey">
        <!-- 基本信息 -->
        <a-tab-pane key="1" tab="基本信息">
          <a-divider orientation="left" :orientationMargin="0" dashed>基本信息</a-divider>
          <a-row :gutter="44">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="商户名称" name="merchantName">
                <a-input v-model:value="form.merchantName" readonly />
              </a-form-item>
              <a-form-item label="商户行业类别" name="mccName">
                <a-textarea v-model:value="form.mccName" :auto-size="{ minRows: 1, maxRows: 3 }" readonly />
              </a-form-item>
              <a-form-item label="商户注册所在省市区" name="cityCode">
                <a-cascader v-model:value="areaCodeValue" :options="regionsData" disabled />
              </a-form-item>
              <a-form-item label="费率政策编号" name="policyNo" v-purview="['0', '3']">
                <a-input v-model:value="form.policyNo" readonly />
              </a-form-item>
              <a-form-item label="商户定位开关" name="positionSwitchStatus">
                <a-select v-model:value="form.positionSwitchStatus" class="ele-fluid" disabled>
                  <a-select-option :value="1">开</a-select-option>
                  <a-select-option :value="0">关</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="交易状态" name="isAllowTrans">
                <a-select v-model:value="form.isAllowTrans" class="ele-fluid" disabled>
                  <a-select-option :value="1">开启</a-select-option>
                  <a-select-option :value="0">关闭</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="创建时间" name="createTime">
                <a-input v-model:value="form.createTime" readonly />
              </a-form-item>
              <a-form-item label="注销状态" name="cancelStatus">
                <a-select v-model:value="form.cancelStatus" class="ele-fluid" disabled>
                  <a-select-option :value="0">未注销</a-select-option>
                  <a-select-option :value="1">注销中</a-select-option>
                  <a-select-option :value="2">已注销</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="商户编号" name="merchantNo">
                <a-input v-model:value="form.merchantNo" readonly />
              </a-form-item>
              <a-form-item label="商户类型" name="grade">
                <a-select v-model:value="form.grade" class="ele-fluid" disabled>
                  <a-select-option value="A">企业商户</a-select-option>
                  <a-select-option value="B">个体工商户</a-select-option>
                  <a-select-option value="C">小微商户</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="商户注册详细地址" name="positionAddr">
                <a-textarea v-model:value="form.positionAddr" :auto-size="{ minRows: 1, maxRows: 3 }" readonly />
              </a-form-item>
              <a-form-item label="费率政策名称" name="policyDesc" v-purview="['0', '3']">
                <a-input v-model:value="form.policyDesc" readonly />
              </a-form-item>
              <a-form-item label="商户状态" name="validStatus">
                <a-select v-model:value="form.validStatus" class="ele-fluid" disabled>
                  <a-select-option :value="1">有效</a-select-option>
                  <a-select-option :value="0">无效</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="提现状态" name="isAllowWithdraw">
                <a-select v-model:value="form.isAllowWithdraw" class="ele-fluid" disabled>
                  <a-select-option :value="1">开启</a-select-option>
                  <a-select-option :value="0">关闭</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="商户入网状态" name="authStatus">
                <a-select v-model:value="form.authStatus" class="ele-fluid" disabled>
                  <a-select-option :value="0">待认证</a-select-option>
                  <a-select-option :value="1">认证待审核</a-select-option>
                  <a-select-option :value="2">审核不通过</a-select-option>
                  <a-select-option :value="3">认证通过</a-select-option>
                  <a-select-option :value="4">入网成功</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-tab-pane>

        <!-- 认证信息 -->
        <a-tab-pane key="2" tab="认证信息">
          <a-divider orientation="left" :orientationMargin="0" dashed>法人信息</a-divider>
          <a-row :gutter="44">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="法人姓名" name="legalName">
                <a-input v-model:value="form.merchantDetail.legalName" readonly />
              </a-form-item>
              <a-form-item label="法人证件类型" name="legalCertType">
                <a-select v-model:value="form.merchantDetail.legalCertType" class="ele-fluid" disabled>
                  <a-select-option v-for="({ label, value }, key) in certTypeEnum" :key="key" :value="value">{{ label }}</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="法人居住地址" name="legalAddr">
                <a-textarea v-model:value="form.merchantDetail.legalAddr" :auto-size="{ minRows: 1, maxRows: 3 }" readonly />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="法人手机号" name="legalTelMask">
                <a-input :value="form.merchantDetail[optType ? 'legalTelCipher' : 'legalTelMask']" readonly />
              </a-form-item>
              <a-form-item label="法人证件号" name="legalCertNoMask">
                <a-input :value="form.merchantDetail[optType ? 'legalCertNoCipher' : 'legalCertNoMask']" readonly />
              </a-form-item>
            </a-col>
          </a-row>

          <a-divider orientation="left" :orientationMargin="0" dashed>联系人信息</a-divider>
          <a-row :gutter="44">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="联系人姓名" name="contactsName">
                <a-input v-model:value="form.merchantDetail.contactsName" readonly />
              </a-form-item>
              <a-form-item label="联系人证件类型" name="contactsCertType">
                <a-select v-model:value="form.merchantDetail.contactsCertType" class="ele-fluid" disabled>
                  <a-select-option v-for="({ label, value }, key) in certTypeEnum" :key="key" :value="value">{{ label }}</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="联系人居住地址" name="contactsAddr">
                <a-textarea v-model:value="form.merchantDetail.contactsAddr" :auto-size="{ minRows: 1, maxRows: 3 }" readonly />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="联系人手机号" name="contactsTelMask">
                <a-input :value="form.merchantDetail[optType ? 'contactsTelCipher' : 'contactsTelMask']" readonly />
              </a-form-item>
              <a-form-item label="联系人证件号" name="contactsCertNoMask">
                <a-input :value="form.merchantDetail[optType ? 'contactsCertNoCipher' : 'contactsCertNoMask']" readonly />
              </a-form-item>
            </a-col>
          </a-row>

          <a-divider orientation="left" :orientationMargin="0" dashed>营业执照信息</a-divider>
          <a-row :gutter="44">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="营业执照全称" name="licenseName">
                <a-input v-model:value="form.merchantDetail.licenseName" readonly />
              </a-form-item>
              <a-form-item label="企业注册地址" name="licenseAddr">
                <a-textarea v-model:value="form.merchantDetail.licenseAddr" :auto-size="{ minRows: 1, maxRows: 3 }" readonly />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="统一社会信用代码" name="licenseNo">
                <a-input v-model:value="form.merchantDetail.licenseNo" readonly />
              </a-form-item>
              <a-form-item label="营业执照有效期" name="contactsCertNoMask">
                <a-space>
                  <a-input v-model:value="form.merchantDetail.licenseStartDate" readonly />
                  -
                  <a-input v-model:value="form.merchantDetail.licenseEndDate" readonly />
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-tab-pane>

        <a-tab-pane key="3" tab="图片信息">
          <a-row>
            <a-col v-for="(item, key) in form.fileDTOList" :key="key" :span="6">
              <a-form-item :label="item.label">
                <a-upload
                  :file-list="[{ url: item.imagePath }]"
                  list-type="picture-card"
                  @preview="() => handlePreview(item.imagePath)"
                  disabled
                />
              </a-form-item>
            </a-col>
          </a-row>
          <!-- 预览图片 -->
          <a-image
            :style="{ display: 'none' }"
            :src="previewImage"
            :preview="{ visible: previewVisible, onVisibleChange: setPreviewVisible }"
          />
        </a-tab-pane>
      </a-tabs>

      <template v-if="optType">
        <a-divider orientation="left" style="margin-bottom: 22px; border-color: orange">请完成审核</a-divider>

        <a-form-item label="认证状态" name="authStatus" :rules="[{ required: true, message: '请选择认证状态' }]">
          <a-select v-model:value="verifyForm.authStatus" class="ele-fluid">
            <a-select-option :value="2">审核不通过</a-select-option>
            <a-select-option :value="3">审核通过</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="认证描述" name="authMsg" :rules="[{ required: true, message: '请填写认证描述' }]">
          <a-textarea v-model:value="verifyForm.authMsg" :auto-size="{ minRows: 3, maxRows: 5 }" />
        </a-form-item>
      </template>
    </a-form>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
      <a-button v-if="optType" type="primary" :loading="loading" @click="handleAudit"><check-outlined />确定</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs } from 'vue';
import { AreaApi } from '@/api/base/AreaApi';
import { MerchantApi } from '@/api/merchant/MerchantApi';
import { certTypeEnum } from '@/config/enumerate';
import { message } from 'ant-design-vue';

export default {
  props: {
    optType: Number,
    visible: Boolean,
    merchantId: String
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {
        merchantDetail: {}
      },
      activeKey: '1',
      previewVisible: false,
      previewImage: '',
      certTypeEnum,
      areaCodeValue: [],
      regionsData: [],
      loading: false,
      formRef: '',
      verifyForm: {}
    });

    init();
    async function init() {
      await getDetail();

      await loadAreaData();

      data.areaCodeValue = [data.form.province, data.form.city, data.form.country];

      const provinceItem = data.regionsData.find(r => r.value === data.form.province);
      if (provinceItem) {
        await loadAreaData([provinceItem]);
        const cityItem = provinceItem.children.find(r => r.value === data.form.city);
        cityItem && loadAreaData([cityItem]);
      }
    }

    const imageTypeEnum = [
      { label: '法人身份证正面', value: 1 },
      { label: '法人身份证反面', value: 2 },
      { label: '银行卡图片卡号面', value: 3 },
      { label: '银行卡图片背面', value: 4 },
      { label: '法人人脸正面照片', value: 5 },
      { label: '法人手持身份证正面', value: 6 },
      { label: '营业执照照片', value: 7 },
      { label: '税务登记证照片', value: 8 },
      { label: '组织机构代码证照片', value: 9 },
      { label: '三方协议合同照片', value: 10 },
      { label: '门头照', value: 11 },
      { label: '店内环境照片', value: 12 },
      { label: '收银台照片', value: 13 },
      { label: '电子签名图像', value: 14 },
      { label: '协议照', value: 15 },
      { label: '开户许可证', value: 16 }
    ];

    async function getDetail() {
      const info = await MerchantApi.merchantDetail({ id: props.merchantId });
      data.form = Object.assign({}, info);
      if (data.form.fileDTOList) {
        data.form.fileDTOList.forEach(f => {
          imageTypeEnum.forEach(i => {
            if (i.value === f.imageType) {
              f.label = i.label;
            }
          });
        });
      }
    }

    async function loadAreaData(selectedOptions) {
      const targetOption = selectedOptions ? selectedOptions[selectedOptions.length - 1] : { level: 1, code: '' };
      const { level, code } = targetOption;

      const areaList = await AreaApi.list({ level: level + 1, status: 1, parentCode: code });
      const filterData = areaList.map(d => {
        return { label: d.areaName, value: d.areaCode, code: d.areaCode, isLeaf: level > 2, level: d.level };
      });

      if (level === 1) {
        data.regionsData = filterData;
      } else {
        targetOption.children = filterData;
      }
    }

    const handlePreview = imagePath => {
      data.previewImage = imagePath;
      setPreviewVisible(true);
    };

    const setPreviewVisible = visible => {
      data.previewVisible = visible;
    };

    const handleAudit = async () => {
      await data.formRef.validate();

      const { authMsg, authStatus } = data.verifyForm;

      // 修改加载框为正在加载
      data.loading = true;

      MerchantApi.enterpriseMerchAudit({ id: data.form.id, authMsg, authStatus })
        .then(result => {
          // 移除加载框
          data.loading = false;

          // 提示修改成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          updateVisible(false);

          // 触发父组件done事件
          context.emit('done');
        })
        .catch(() => {
          data.loading = false;
        });
    };

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      updateVisible,
      handlePreview,
      setPreviewVisible,
      handleAudit
    };
  }
};
</script>
<style scoped>
:deep(.ant-select-disabled.ant-select:not(.ant-select-customize-input)) .ant-select-selector {
  background-color: transparent;
  color: var(--text-color);
}
</style>
