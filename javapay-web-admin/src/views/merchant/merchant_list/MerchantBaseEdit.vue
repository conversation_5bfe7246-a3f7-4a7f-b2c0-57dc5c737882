<template>
  <a-modal
    :width="750"
    :visible="visible"
    title="修改基本信息"
    :mask-closable="false"
    :confirm-loading="loading"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form layout="vertical" ref="formRef" :model="form" :rules="rules">
      <a-tabs v-model:activeKey="activeKey">
        <!-- 基本信息 -->
        <a-tab-pane key="1" tab="基本信息">
          <a-divider orientation="left" :orientationMargin="0" dashed>法人信息</a-divider>
          <a-row :gutter="44">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="法人姓名" name="legalName">
                <a-input v-model:value="form.legalName" />
              </a-form-item>
              <a-form-item label="法人证件类型" name="legalCertType">
                <a-select v-model:value="form.legalCertType" class="ele-fluid">
                  <a-select-option v-for="({ label, value }, key) in certTypeEnum" :key="key" :value="value">{{ label }}</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="法人居住地址" name="legalAddr">
                <a-textarea v-model:value="form.legalAddr" :auto-size="{ minRows: 1, maxRows: 3 }" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="法人手机号" name="legalTel">
                <a-input v-model:value="form.legalTel" />
              </a-form-item>
              <a-form-item label="法人证件号" name="legalCertNo">
                <a-input v-model:value="form.legalCertNo" />
              </a-form-item>
              <a-form-item label="证件有效期">
                <a-space>
                  <a-date-picker v-model:value="form.legalCertStartDate" valueFormat="YYYY-MM-DD" />
                  -
                  <a-date-picker v-model:value="form.legalCertEndDate" valueFormat="YYYY-MM-DD" />
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>

          <a-divider orientation="left" :orientationMargin="0" dashed>联系人信息</a-divider>
          <a-row :gutter="44">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="联系人姓名" name="contactsName">
                <a-input v-model:value="form.contactsName" />
              </a-form-item>
              <a-form-item label="联系人证件类型" name="contactsCertType">
                <a-select v-model:value="form.contactsCertType" class="ele-fluid">
                  <a-select-option v-for="({ label, value }, key) in certTypeEnum" :key="key" :value="value">{{ label }}</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="联系人居住地址" name="contactsAddr">
                <a-textarea v-model:value="form.contactsAddr" :auto-size="{ minRows: 1, maxRows: 3 }" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="联系人手机号" name="contactsTel">
                <a-input v-model:value="form.contactsTel" />
              </a-form-item>
              <a-form-item label="联系人证件号" name="contactsCertNo">
                <a-input v-model:value="form.contactsCertNo" />
              </a-form-item>
            </a-col>
          </a-row>

          <a-divider orientation="left" :orientationMargin="0" dashed>营业执照信息</a-divider>
          <a-row :gutter="44">
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="营业执照全称" name="licenseName">
                <a-input v-model:value="form.licenseName" />
              </a-form-item>
              <a-form-item label="企业注册地址" name="licenseAddr">
                <a-textarea v-model:value="form.licenseAddr" :auto-size="{ minRows: 1, maxRows: 3 }" />
              </a-form-item>
              <a-form-item label="经营范围" name="licenseOperateRange">
                <a-textarea v-model:value="form.licenseOperateRange" :auto-size="{ minRows: 1, maxRows: 3 }" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :sm="24" :xs="24">
              <a-form-item label="统一社会信用代码" name="licenseNo">
                <a-input v-model:value="form.licenseNo" />
              </a-form-item>
              <a-form-item label="营业执照有效期">
                <a-space>
                  <a-input v-model:value="form.licenseStartDate" placeholder="格式: 2020-05-20" />
                  -
                  <a-input v-model:value="form.licenseEndDate" placeholder="格式: 2020-05-20" />
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-tab-pane>

        <a-tab-pane key="2" tab="图片信息">
          <a-row>
            <a-col v-for="(item, key) in form.fileDTOList" :key="key" :span="6">
              <a-form-item :label="item.label">
                <a-upload
                  v-model:file-list="item.fileData"
                  accept=".png,.jpg,.jpeg,.gif"
                  :multiple="false"
                  list-type="picture-card"
                  :before-upload="file => beforeUpload(file, item)"
                  @remove="() => handleRemoveImg(item)"
                  @preview="() => handlePreview(item)"
                  :disabled="!item.isEdit"
                >
                  <div v-if="!item.fileData?.length">
                    <plus-outlined />
                    <div style="margin-top: 8px">Upload</div>
                  </div>
                </a-upload>
              </a-form-item>
            </a-col>
          </a-row>
          <!-- 预览图片 -->
          <a-image
            :style="{ display: 'none' }"
            :src="previewImage"
            :preview="{ visible: previewVisible, onVisibleChange: setPreviewVisible }"
          />
        </a-tab-pane>
      </a-tabs>
    </a-form>
  </a-modal>
</template>

<script>
import { reactive, toRefs } from 'vue';
import { MerchantApi } from '@/api/merchant/MerchantApi';
import { certTypeEnum } from '@/config/enumerate';
import { message } from 'ant-design-vue';
import { deepCopy } from '@/utils/util';
import { phoneReg } from 'ele-admin-pro';
import dayjs from 'dayjs';

export default {
  props: {
    visible: Boolean,
    merchantId: String,
    merchantNo: String
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {
        fileDTOList: []
      },
      activeKey: '1',
      previewVisible: false,
      previewImage: '',
      certTypeEnum,
      loading: false,
      formRef: '',
      rules: {
        legalName: [{ required: true, message: '请输入法人姓名' }],
        legalTel: [
          { required: true, message: '请输入法人手机号' },
          { pattern: phoneReg, message: '手机号码格式错误', trigger: 'blur' }
        ],
        legalCertType: [{ required: true, message: '请选择法人证件类型' }],
        legalCertNo: [{ required: true, message: '请输入法人证件号码' }]
      }
    });

    init();
    async function init() {
      await getDetail();
    }

    const imageTypeEnum = [
      { label: '法人身份证正面', value: 1 },
      { label: '法人身份证反面', value: 2 },
      { label: '银行卡图片卡号面', value: 3 },
      { label: '银行卡图片背面', value: 4 },
      { label: '法人人脸正面照片', value: 5 },
      { label: '法人手持身份证正面', value: 6 },
      { label: '营业执照照片', value: 7 },
      { label: '税务登记证照片', value: 8 },
      { label: '组织机构代码证照片', value: 9 },
      { label: '三方协议合同照片', value: 10 },
      { label: '门头照', value: 11 },
      { label: '店内环境照片', value: 12 },
      { label: '收银台照片', value: 13 },
      { label: '电子签名图像', value: 14 },
      { label: '协议照', value: 15 },
      { label: '开户许可证', value: 16 }
    ];

    async function getDetail() {
      const info = await MerchantApi.merchantDetailDetail({ merchantId: props.merchantId, merchantNo: props.merchantNo });
      data.form = Object.assign({}, info.merchantDetailResponse || {}, { fileDTOList: info.imageList || [] });

      data.form.legalCertStartDate = data.form.legalCertStartDate ? dayjs(data.form.legalCertStartDate).format('YYYY-MM-DD') : '';
      data.form.legalCertEndDate = data.form.legalCertEndDate ? dayjs(data.form.legalCertEndDate).format('YYYY-MM-DD') : '';

      if (data.form.fileDTOList) {
        const isEditImageTypes = [1, 2, 7];

        data.form.fileDTOList.forEach(f => {
          if (isEditImageTypes.includes(f.imageType)) {
            f.isEdit = true;
          }

          f.fileData = f.imagePath ? [{ url: f.imagePath }] : [];

          imageTypeEnum.forEach(i => {
            if (i.value === f.imageType) {
              f.label = i.label;
            }
          });
        });

        data.form.fileDTOList.sort(function (a, b) {
          return a.imageType - b.imageType;
        });
      }
    }

    async function save() {
      //校验表单
      await data.formRef.validate();

      const params = deepCopy(data.form);
      params.fileDTOList = params.fileDTOList.map(item => {
        let fileData = null;

        if (item.fileData[0]) {
          fileData = item.fileData[0].url.startsWith('http') ? null : item.fileData[0].url;
        }
        return {
          fileName: item.fileName || item.imageName,
          fileData,
          fileType: item.imageType,
          suffixType: item.suffixType || 'png'
        };
      });

      //修改加载框为正在加载
      data.loading = true;

      MerchantApi.merchantDetailEdit(params)
        .then(result => {
          // 移除加载框
          data.loading = false;
          // 提示添加成功
          message.success(result.message);
          // 关闭弹框，通过控制visible的值，传递给父组件
          updateVisible(false);
          // 触发父组件done事件
          context.emit('done');
        })
        .catch(() => {
          data.loading = false;
        });
    }

    const beforeUpload = (file, item) => {
      getBase64(file).then(data => {
        item.fileData = [{ url: data }];
        item.fileName = file.uid + file.name;
        item.suffixType = file.type.split('/')[1];
      });
      return false;
    };

    const handleRemoveImg = item => {
      item.fileData = [];
      item.suffixType = '';
    };

    const handlePreview = ({ fileData }) => {
      data.previewImage = fileData[0].url;
      setPreviewVisible(true);
    };

    const setPreviewVisible = visible => {
      data.previewVisible = visible;
    };

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      updateVisible,
      handlePreview,
      setPreviewVisible,
      beforeUpload,
      handleRemoveImg,
      save
    };
  }
};
// 图片文件转base64
function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });
}
</script>
