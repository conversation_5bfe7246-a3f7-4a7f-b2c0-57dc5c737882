<template>
  <a-modal
    :width="750"
    :visible="visible"
    title="费率信息"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
  >
    <a-form
      ref="formRef"
      :model="data"
      hideRequiredMark
      :label-col="{ md: { span: 17 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 7 }, sm: { span: 24 } }"
    >
      <a-tabs v-model:activeKey="activeRateKey" type="card">
        <a-tab-pane v-for="(item, key) in rateDTOList" :tab="`${item.channelName}`" :key="String(key)">
          <RateModule
            v-for="(rateItem, key2) in item.rateInfoDTO"
            :key="key2"
            :rate-item="rateItem"
            :name-prefix="['rateDTOList', key, 'rateInfoDTO', key2]"
            :bankList="bankList"
            disabled
          />
        </a-tab-pane>
      </a-tabs>
    </a-form>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs } from 'vue';
import RateModule from '../../business-team/_components/RateModule.vue';

export default {
  components: { RateModule },
  props: {
    visible: Boolean,
    detail: Array,
    channelCodes: Array,
    bankList: Array,
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      rateDTOList: [],
      activeRateKey: '0'
    });

    const channelCodes = props.detail.map(i => i.channelCode) || [];
    const allChannel = Array.from(new Set(channelCodes));
    var rateList = [];

    allChannel.forEach(channelCode => {
      const channelItem = props.channelCodes.find(c => channelCode === c.channelCode);
      const channelName = channelItem?.channelName;

      const rateInfoByCode = props.detail.filter(c => c.channelCode === channelCode);

      rateInfoByCode.forEach(r => {
        r.rateRatio = 100;
        const rateFields = ['withdrawRate', 'withdrawSingleFee'];
        rateFields.forEach(f => (r.rateInfoDTO[f] = 0));
      });

      rateInfoByCode.sort(function (a, b) {
        return a.rateType - b.rateType;
      });

      rateList.push({
        channelCode,
        channelName,
        rateInfoDTO: rateInfoByCode
      });
    });

    data.rateDTOList = Object.assign([], rateList);

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      updateVisible,
      data
    };
  }
};
</script>
