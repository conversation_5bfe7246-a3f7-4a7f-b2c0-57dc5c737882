<template>
  <a-modal
    :width="600"
    :visible="visible"
    :confirm-loading="loading"
    title="修改商户费率政策"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 6 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 18 }, sm: { span: 24 } }"
    >
      <a-form-item label="修改后的费率政策" name="policyNoOfNew">
        <a-select v-model:value="form.policyNoOfNew" placeholder="请选择" class="ele-fluid">
          <a-select-option v-for="(item, key) in ratePolicyList" :value="item.policyNo" :key="key"> {{ item.policyDesc }} </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="修改方式">
        <a-radio-group name="appointType" v-model:value="form.appointType">
          <a-radio :value="1">指定费率政策修改</a-radio>
          <a-radio :value="2">指定商户修改</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="需修改的费率政策" name="policyNo" v-if="form.appointType === 1">
        <a-select v-model:value="form.policyNo" placeholder="请选择" class="ele-fluid">
          <a-select-option v-for="(item, key) in ratePolicyList" :value="item.policyNo" :key="key"> {{ item.policyDesc }} </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="选择商户" name="merchNoList" v-if="form.appointType === 2">
        <a-textarea
          :value="form.merchNoList.join(',')"
          placeholder="请选择"
          :auto-size="{ minRows: 2 }"
          allow-clear
          @click="showMerchList = true"
        />
      </a-form-item>
    </a-form>

    <MerchSelect v-if="showMerchList" v-model:visible="showMerchList" @done="setMerchRows" />
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { MerchantApi } from '@/api/merchant/MerchantApi';
import { RatePolicyApi } from '@/api/businessTeam/rate-policy/RatePolicyApi';
import MerchSelect from './MerchSelect.vue';

export default {
  components: { MerchSelect },
  props: {
    visible: Boolean
  },
  emits: ['update:visible'],
  data() {
    return {
      // 表单数据
      form: { merchNoList: [], appointType: 1, policyType: 2 },
      // 表单验证规则
      rules: {
        policyNoOfNew: [{ required: true, message: '请选择' }],
        policyNo: [{ required: true, message: '请选择' }],
        merchNoList: [{ required: true, message: '请选择' }]
      },
      // 提交状态
      loading: false,
      ratePolicyList: [],
      showMerchList: false
    };
  },
  created() {
    this.getRatePolicyList();
  },
  methods: {
    setMerchRows(rows) {
      rows = rows || [];
      this.form.merchNoList = rows.map(i => i.merchantNo);
    },
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      MerchantApi.changeRatePolicy(this.form)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);
        })
        .catch(() => {
          this.loading = false;
        });
    },

    async getRatePolicyList() {
      const ratePolicyList = await RatePolicyApi.list({ userType: 3 ,policyType: 2});
      this.ratePolicyList = ratePolicyList;
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
