<template>
  <a-modal
    :width="600"
    :visible="visible"
    :confirm-loading="loading"
    title="商户费率文件下载"
    :body-style="{ paddingBottom: '8px' }"
    :mask-closable="false"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 5 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 19 }, sm: { span: 24 } }"
    >
      <a-form-item label="支付通道" name="channelCode">
        <a-select v-model:value="form.channelCode" class="ele-fluid" placeholder="请选择">
          <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">{{
            channelName
          }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="费率类型" name="rateType">
        <a-select v-model:value="form.rateType" class="ele-fluid" placeholder="请选择">
          <a-select-option :value="1">扫码费率</a-select-option>
          <a-select-option :value="2">刷卡费率</a-select-option>
          <a-select-option :value="3">EPOS费率</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="费率政策">
        <a-select v-model:value="form.policyNo" class="ele-fluid" placeholder="可选" allow-clear>
          <a-select-option :value="item.policyNo" v-for="item in policyNoList" :key="item.id">{{ item.policyDesc }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="选择商户">
        <a-textarea :value="selectMerchName" placeholder="可选" :auto-size="{ minRows: 2 }" readonly @click="showMerchList = true" />
      </a-form-item>
      <MerchSelect v-if="showMerchList" v-model:visible="showMerchList" @done="setMerchInfo" /> </a-form
    ></a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { MerchantApi } from '@/api/merchant/MerchantApi';
import MerchSelect from './MerchSelect.vue';

export default {
  components: { MerchSelect },
  props: {
    visible: Boolean,
    channelCodes: Array,
    policyNoList: Array
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {},
      // 表单验证规则
      rules: {
        channelCode: [{ required: true, message: '请选择支付通道' }],
        rateType: [{ required: true, message: '请选择费率类型' }]
      },
      // 提交状态
      loading: false,
      showMerchList: false,
      selectMerch: []
    };
  },
  computed: {
    selectMerchName() {
      return this.selectMerch.map(i => i.merchantName || i.merchantNo).join(',');
    }
  },
  methods: {
    setMerchInfo(rows) {
      this.selectMerch = rows;
      this.form.merchIds = rows.map(i => i.id);
    },
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      const res = await MerchantApi.downloadMerchantRateExcel(this.form).catch(error => {
        this.loading = false;

        message.destroy();

        const reader = new FileReader(); //创建一个FileReader实例
        reader.readAsText(error, 'utf-8'); //读取文件,结果用字符串形式表示
        reader.onload = function () {
          const { message: errorMsg } = JSON.parse(reader.result);
          message.error(errorMsg || '下载失败');
        };
      });

      this.loading = false;

      const fileReader = new FileReader();
      fileReader.onload = () => {
        try {
          // 说明是普通对象数据，后台转换失败
          const jsonData = JSON.parse(fileReader.result);
          message.error(jsonData.message);
        } catch (err) {
          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          const contentDisposition = res.headers['content-disposition'];
          let fileName = decodeURIComponent(contentDisposition.substring(contentDisposition.indexOf('=') + 1));
          fileName = fileName ? fileName.replace("utf-8''", '') + '.xlsx' : '商户费率-下载.xlsx';

          // 解析对象失败，说明是正常的文件流
          let blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.style.display = 'none';
          a.href = url;
          a.download = fileName;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }
      };
      fileReader.readAsText(res?.data);
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
