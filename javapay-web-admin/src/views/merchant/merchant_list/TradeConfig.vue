<template>
  <a-modal
    :width="750"
    :visible="visible"
    :confirm-loading="loading"
    title="交易配置"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :label-col="{ md: { span: 7 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 17 }, sm: { span: 24 } }"
    >
      <template v-for="(item, key) in form.tradeChlRouteRequests" :key="key">
        <a-divider orientation="left" dashed orientationMargin="0">{{ item.label }}</a-divider>
        <a-row :gutter="16">
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item
              label="通道选择"
              :name="['tradeChlRouteRequests', key, 'channelCode']"
              :rules="[{ required: false, message: '请选择通道' }]"
            >
              <a-select
                v-model:value="item.channelCode"
                class="ele-fluid"
                placeholder="请选择"
                @change="() => selectChannel(item)"
                @dropdownVisibleChange="open => open && getPayOrgList(item.payMethod)"
              >
                <a-select-option v-for="({ channelName, channelCode }, index) in payOrgList" :key="index" :value="channelCode">
                  {{ channelName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24" :xs="24">
            <a-form-item
              label="商户选择"
              :name="['tradeChlRouteRequests', key, 'chnMerchNo']"
              :rules="[{ required: false, message: '请选择商户' }]"
            >
              <a-select
                v-model:value="item.chnMerchNo"
                class="ele-fluid"
                placeholder="请选择"
                :disabled="!item.channelCode"
                @change="(value, option) => selectMerchant(item, option)"
                @dropdownVisibleChange="open => open && getMerchantList(item)"
              >
                <a-select-option
                  v-for="({ chnMerchName, chnMerchNo }, index) in merchantList"
                  :key="index"
                  :value="chnMerchNo"
                >
                  {{ chnMerchName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </template>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { MerchantApi } from '@/api/merchant/MerchantApi';
import { deepCopy } from '@/utils/util';

export default {
  name: 'TradeConfig',
  props: {
    visible: Boolean,
    merchantNo: String
  },
  emits: ['update:visible'],
  data() {
    return {
      // 表单数据
      form: {
        merchantNo: this.merchantNo,
        tradeChlRouteRequests: [
          {
            label: '银联二维码支付',
            payMethod: 1
          },
          {
            label: '支付宝支付',
            payMethod: 3
          },
          {
            label: '微信支付',
            payMethod: 2
          },
          {
            label: 'EPOS支付',
            payMethod: 4
          },
          {
            label: 'POS刷卡',
            payMethod: 5
          }
        ]
      },
      // 提交状态
      loading: false,
      payOrgList: [],
      merchantList: []
    };
  },
  created() {
    this.getTradeConfigDetail();
    this.getPayOrgList();
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      const params = deepCopy(this.form);
      params.tradeChlRouteRequests = params.tradeChlRouteRequests.map(t => {
        return {
          chnMerchNo: t.chnMerchNo,
          chnMerchName: t.chnMerchName,
          merchantNo: this.merchantNo,
          id: t.id,
          channelCode: t.channelCode,
          payMethod: t.payMethod
        };
      });

      MerchantApi.tradeConfigEdit(params)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);
        })
        .catch(() => {
          this.loading = false;
        });
    },

    async getTradeConfigDetail() {
      const data = await MerchantApi.tradeConfigDetail({ merchantNo: this.merchantNo });
      if (data?.length) {
        this.form.tradeChlRouteRequests.forEach((i, index) => {
          data.forEach(d => {
            if (i.payMethod === d.payMethod) {
              this.form.tradeChlRouteRequests[index] = Object.assign(i, d);

              this.merchantList.push({
                chnMerchName: d.chnMerchName,
                chnMerchNo: d.chnMerchNo
              });
            }
          });
        });
      }
    },

    async getPayOrgList(payMethod = '') {
      this.payOrgList = [];
      const data = await MerchantApi.payOrgList({
        merchantNo: this.merchantNo,
        payMethod
      });
      this.payOrgList = data;
    },

    selectChannel(item) {
      item.chnMerchName = '';
      item.chnMerchNo = '';
    },

    async getMerchantList({ payMethod, channelCode }) {
      this.merchantList = [];
      const data = await MerchantApi.merchantList({
        merchantNo: this.merchantNo,
        payMethod,
        channelCode
      });
      this.merchantList = data || [];
    },

    selectMerchant(item, { key }) {
      const { chnMerchName } = this.merchantList[key];
      item.chnMerchName = chnMerchName;
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
<style scoped>
.ant-divider-horizontal.ant-divider-with-text {
  margin-top: 0;
}
</style>
