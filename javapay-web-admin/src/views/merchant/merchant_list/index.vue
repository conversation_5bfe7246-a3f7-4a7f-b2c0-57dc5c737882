<template>
  <div class="ele-body">
    <a-spin :spinning="spinning" tip="下载中, 请稍候...">
      <!-- 搜索表单 -->
      <div class="block-interval">
        <a-card :bordered="false">
          <a-form layout="inline" :model="where">
            <a-row :gutter="[0, 16]">
              <a-form-item label="商户名称">
                <a-input v-model:value.trim="where.merchantName" placeholder="商户名称" allow-clear />
              </a-form-item>
              <a-form-item label="商户编号">
                <a-input v-model:value.trim="where.merchantNo" placeholder="商户编号" allow-clear />
              </a-form-item>
              <a-form-item label="登录账号">
                <a-input v-model:value.trim="where.loginAccount" placeholder="登录账号" allow-clear />
              </a-form-item>
              <a-form-item label="法人姓名">
                <a-input v-model:value.trim="where.legalName" placeholder="法人姓名" allow-clear />
              </a-form-item>
              <a-form-item label="费率政策" v-if="hasPurview(['0', '3'])">
                <a-select v-model:value="where.policyNo" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option :value="item.policyNo" v-for="item in policyNoList" :key="item.id">{{
                    item.policyDesc
                  }}</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="归属大区" v-if="!hasPurview(['1', '2', '3'])">
                <a-input v-model:value.trim="where.regionNo" placeholder="归属大区" allow-clear />
              </a-form-item>
              <a-form-item label="归属运营" v-if="!hasPurview(['2', '3'])">
                <a-input v-model:value.trim="where.branchNo" placeholder="归属运营中心" allow-clear />
              </a-form-item>
              <a-form-item label="归属一级代理" v-if="!hasPurview(['1', '2', '3'])">
                <a-input v-model:value.trim="where.agentNo" placeholder="归属一级代理" allow-clear />
              </a-form-item>
              <a-form-item label="直属代理商编号">
                <a-input v-model:value.trim="where.directAgentNo" placeholder="直属代理商编号" allow-clear />
              </a-form-item>
              <a-form-item label="商户类型">
                <a-select v-model:value="where.grade" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option value="A">企业商户</a-select-option>
                  <a-select-option value="B">个体工商户</a-select-option>
                  <a-select-option value="C">小微商户</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="入网状态">
                <a-select v-model:value="where.authStatus" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option :value="0">待认证</a-select-option>
                  <a-select-option :value="1">认证待审核</a-select-option>
                  <a-select-option :value="2">审核不通过</a-select-option>
                  <a-select-option :value="3">认证通过</a-select-option>
                  <a-select-option :value="4">入网成功</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="有效状态">
                <a-select v-model:value="where.validStatus" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option :value="1">有效</a-select-option>
                  <a-select-option :value="0">无效</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="注销状态">
                <a-select v-model:value="where.cancelStatus" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option :value="0">未注销</a-select-option>
                  <a-select-option :value="1">注销中</a-select-option>
                  <a-select-option :value="2">已注销</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="定位开关">
                <a-select v-model:value="where.positionSwitchStatus" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option :value="1">开</a-select-option>
                  <a-select-option :value="0">关</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="风控等级">
                <a-select v-model:value="where.riskGrade" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option v-for="i in 10" :key="i" :value="i">{{ i }}级</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="开始日期">
                <a-date-picker v-model:value="where.searchBeginTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
              </a-form-item>
              <a-form-item label="结束日期">
                <a-date-picker v-model:value="where.searchEndTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
              </a-form-item>
              <a-form-item label="定位省市区">
                <a-radio-group v-model:value="areaRange" @change="changeAreaRange">
                  <a-radio-button :value="2">省</a-radio-button>
                  <a-radio-button :value="3">市</a-radio-button>
                  <a-radio-button :value="4">区县</a-radio-button>
                  <a-cascader
                    :key="cascaderKey"
                    style="width: 205px"
                    v-model:value="areaCodeValue"
                    :options="regionsData"
                    :load-data="loadAreaData"
                    placeholder="请选择"
                    allow-clear
                    @change="selectedAreaValue"
                  />
                </a-radio-group>
              </a-form-item>
              <a-form-item class="ele-text-center">
                <a-space>
                  <a-button type="primary" @click="reload">查询</a-button>
                  <a-button @click="reset">重置</a-button>
                </a-space>
              </a-form-item>
            </a-row>
          </a-form>
        </a-card>
      </div>

      <!-- 表格 -->
      <div>
        <a-card :bordered="false" class="table-height">
          <ele-pro-table
            ref="table"
            row-key="id"
            :datasource="datasource"
            :columns="columns"
            :where="where"
            v-model:current="selectedRow"
            :scroll="{ x: 'max-content' }"
          >
            <template #toolbar>
              <a-space>
                <!-- <a-button @click="showEditMerchRate = true" v-if="hasPurview(['3'])">
                  <template #icon>
                    <edit-outlined />
                  </template>
                  <span>修改费率政策</span>
                </a-button> -->

                <a-button @click="handleBaseEdit">
                  <template #icon>
                    <edit-outlined />
                  </template>
                  <span>修改基本信息</span>
                </a-button>

                <a-button @click="handleLimitDetail">
                  <template #icon>
                    <funnel-plot-outlined />
                  </template>
                  <span>交易限制/累计信息</span>
                </a-button>

                <!-- <a-button @click="handleTradeConfig">
                  <template #icon>
                    <node-expand-outlined />
                  </template>
                  <span>交易配置</span>
                </a-button> -->

                <!-- <a-button @click="showFileDownload = true" v-if="!hasPurview(['1', '2'])">
                  <template #icon>
                    <download-outlined />
                  </template>
                  <span>商户费率下载</span>
                </a-button> -->
              </a-space>
            </template>
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'authStatus'">
                <a-tag v-if="record.authStatus === 0">待认证</a-tag>
                <a-tag v-else-if="record.authStatus === 1" color="warning">认证待审核</a-tag>
                <a-tag v-else-if="record.authStatus === 2" color="error">审核不通过</a-tag>
                <a-tag v-else-if="record.authStatus === 3" color="processing">认证通过</a-tag>
                <a-tag v-else-if="record.authStatus === 4" color="success">入网成功</a-tag>
                <span v-else>--</span>
              </template>

              <template v-if="column.key === 'grade'">
                <a-tag v-if="record.grade == 'A'" color="cyan">企业商户</a-tag>
                <a-tag v-else-if="record.grade == 'B'" color="blue">个体工商户</a-tag>
                <a-tag v-else-if="record.grade == 'C'" color="purple">小微商户</a-tag>
              </template>

              <template v-if="column.key === 'validStatus'">
                <a-tag color="success" v-if="record.validStatus === 1">有效</a-tag>
                <a-tag v-else>无效</a-tag>
              </template>

              <template v-if="column.key === 'cancelStatus'">
                <a-tag v-if="record.cancelStatus === 0">未注销</a-tag>
                <a-tag v-else-if="record.cancelStatus === 1" color="warning">注销中</a-tag>
                <a-tag v-else-if="record.cancelStatus === 2" color="error">已注销</a-tag>
                <span v-else>--</span>
              </template>

              <template v-else-if="column.key === 'positionSwitchStatus'">
                <check-outlined v-if="record.positionSwitchStatus === 1" :style="{ color: '#87d068' }" />
                <close-outlined v-else :style="{ color: '#f50' }" />
              </template>

              <!-- table操作栏按钮 -->
              <template v-else-if="column.key === 'action'">
                <a-space>
                  <a @click="handleDetail(record)">详情</a>
                  <a @click="handleRateDetail(record)">费率信息</a>
                  <template v-if="hasPurview('0')">
                    <a-divider type="vertical" />
                    <a @click="handleEdit(record)">修改</a>
                    <template v-if="record.authStatus === 1">
                      <a-divider type="vertical" />
                      <a @click="handleVerify(record)">审核</a>
                    </template>
                  </template>
                </a-space>
              </template>
            </template>
          </ele-pro-table>
        </a-card>
      </div>
    </a-spin>
    <!-- 商户修改 -->
    <MerchantEdit v-if="showEdit" v-model:visible="showEdit" :merchantId="merchantId" @done="reload" />

    <!-- 商户详情 -->
    <MerchantDetail v-if="showDetail" v-model:visible="showDetail" :merchantId="merchantId" :optType="optType" @done="reload" />

    <!-- 修改基本信息 -->
    <MerchantBaseEdit
      v-if="showBaseEdit"
      v-model:visible="showBaseEdit"
      :merchantId="merchantId"
      :merchantNo="merchantName"
      @done="reload"
    />

    <!-- 交易限制/累计信息 -->
    <CumulativeLimitDetail v-if="showLimitDetail" v-model:visible="showLimitDetail" :merchantNo="merchantId" :merchantName="merchantName" />

    <!-- 交易配置 -->
    <TradeConfig v-if="showTradeConfig" v-model:visible="showTradeConfig" :merchantNo="merchantId" />

    <!-- 修改费率政策 -->
    <EditMerchRate v-if="showEditMerchRate" v-model:visible="showEditMerchRate" />

    <RateFileDownload
      v-if="showFileDownload"
      v-model:visible="showFileDownload"
      :channelCodes="channelCodes"
      :policyNoList="policyNoList"
    />

    <MerchantRateDetail
      v-if="showRateDetail"
      v-model:visible="showRateDetail"
      :channelCodes="channelCodes"
      :bankList="bankList"
      :detail="current"
    />
  </div>
</template>

<script>
import { message } from 'ant-design-vue';
import MerchantEdit from './MerchantEdit.vue';
import MerchantBaseEdit from './MerchantBaseEdit.vue';
import MerchantDetail from './MerchantDetail.vue';
import CumulativeLimitDetail from './CumulativeLimitDetail.vue';
import EditMerchRate from './EditMerchRate.vue';
import RateFileDownload from './RateFileDownload.vue';
import MerchantRateDetail from './MerchantRateDetail.vue';
import TradeConfig from './TradeConfig.vue';
import { MerchantApi } from '@/api/merchant/MerchantApi';
import { MccCodeApi } from '@/api/base/MccCodeApi';
import { BankCodeManageApi } from '@/api/base/BankCodeManageApi';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import { RatePolicyApi } from '@/api/businessTeam/rate-policy/RatePolicyApi';
import { hasPurview } from '@/utils/permission';
import { AreaApi } from '@/api/base/AreaApi';

export default {
  name: 'MerchantList',
  components: {
    MerchantEdit,
    MerchantDetail,
    CumulativeLimitDetail,
    TradeConfig,
    EditMerchRate,
    MerchantBaseEdit,
    MerchantRateDetail,
    RateFileDownload
  },
  data() {
    return {
      hasPurview,
      showFileDownload: false,
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '归属大区',
          dataIndex: 'regionNo',
          align: 'center',
          hideCol: hasPurview(['1', '2', '3'])
        },
        {
          title: '归属运营中心',
          dataIndex: 'branchNo',
          align: 'center',
          hideCol: hasPurview(['2', '3'])
        },
        {
          title: '归属一级代理',
          dataIndex: 'agentNo',
          align: 'center',
          hideCol: hasPurview('3')
        },
        {
          title: '直属代理商编号',
          dataIndex: 'directAgentNo',
          align: 'center'
        },
        {
          title: '商户名称',
          dataIndex: 'merchantName'
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '商户类型',
          dataIndex: 'grade',
          key: 'grade',
          align: 'center'
        },
        {
          title: '法人姓名',
          dataIndex: 'legalName',
          align: 'center'
        },
        {
          title: '登录账号',
          dataIndex: 'loginAccount',
          align: 'center',
          customRender: ({ text }) => {
            return text ? text.replace(/(\d{3})\d*(\d{4})/, '$1 **** $2') : '--';
          }
        },
        {
          title: '行业类别',
          dataIndex: 'mcc',
          customRender: ({ text }) => {
            const item = this.mccList.find(c => c.mcc === text);
            return item?.mccName || '--';
          }
        },
        {
          title: '费率政策编号',
          dataIndex: 'policyNo',
          align: 'center',
          hideCol: !hasPurview(['0', '3'])
        },
        {
          title: '费率政策名称',
          dataIndex: 'policyDesc',
          align: 'center',
          hideCol: !hasPurview(['0', '3'])
        },
        {
          title: '入网状态',
          dataIndex: 'authStatus',
          key: 'authStatus',
          align: 'center'
        },
        {
          title: '风控等级',
          dataIndex: 'riskGrade',
          align: 'center'
        },
        {
          title: '定位开关',
          dataIndex: 'positionSwitchStatus',
          key: 'positionSwitchStatus',
          align: 'center'
        },
        {
          title: '有效状态',
          dataIndex: 'validStatus',
          key: 'validStatus',
          align: 'center'
        },
        {
          title: '注销状态',
          dataIndex: 'cancelStatus',
          key: 'cancelStatus',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          width: 200,
          fixed: 'right',
          align: 'center'
        }
      ].filter(i => !i.hideCol),
      // 表格搜索条件
      where: {},
      merchantId: '',
      merchantName: '',
      showEdit: false,
      showBaseEdit: false,
      showDetail: false,
      showLimitDetail: false,
      showTradeConfig: false,
      selectedRow: null,
      mccList: [],
      channelCodes: [],
      policyNoList: [],
      bankList: [],
      optType: null,
      spinning: false,
      showEditMerchRate: false,
      showRateDetail: false,
      current: null,
      regionsData: [],
      areaCodeValue: [],
      areaRange: 2,
      cascaderKey: 0
    };
  },
  mounted() {
    this.getMccList();
    this.getChannelList();
    this.getBankList();
    this.getPolicyNoList();
    this.loadAreaData();
  },
  methods: {
    changeAreaRange() {
      this.cascaderKey++;
      this.areaCodeValue = [];
      this.regionsData = [];
      this.selectedAreaValue();
      this.loadAreaData();
    },
    async loadAreaData(selectedOptions) {
      const targetOption = selectedOptions ? selectedOptions[selectedOptions.length - 1] : { level: 1, code: '' };
      const { level, code } = targetOption;

      const data = await AreaApi.list({ level: level + 1, status: 1, parentCode: code });
      const filterData = data.map(d => {
        return { label: d.areaName, value: d.areaCode, code: d.areaCode, isLeaf: this.areaRange === level + 1, level: d.level };
      });

      if (level === 1) {
        this.regionsData = filterData;
      } else {
        targetOption.children = filterData;
      }
    },

    selectedAreaValue(value) {
      [this.where.province, this.where.city, this.where.country] = value || [];
    },

    async getBankList() {
      const data = await BankCodeManageApi.list();
      this.bankList = data || [];
    },

    async getChannelList() {
      const data = await ChannelManageApi.list({ validStatus: 1 });
      this.channelCodes = data;
    },

    async getPolicyNoList() {
      const data = await RatePolicyApi.list({ policyType: 2 });
      const policyNoList = data || [];

      this.policyNoList = policyNoList.filter(i => i.userType === 3);
    },

    async getMccList() {
      const data = await MccCodeApi.list({ mccLevel: 2 });
      this.mccList = data || [];
    },

    reload() {
      this.selectedRow = null;
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.areaCodeValue = [];
      this.selectedRow = null;
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleEdit({ id }) {
      this.merchantId = id;
      this.showEdit = true;
    },

    handleDetail({ id }) {
      this.optType = 0;
      this.merchantId = id;
      this.showDetail = true;
    },

    async handleRateDetail({ id }) {
      const data = await MerchantApi.rateDetail({ merchantId: id });
      this.current = data || [];
      this.showRateDetail = true;
    },

    handleBaseEdit() {
      if (!this.selectedRow) return message.warning('请选择一条数据');
      this.merchantId = this.selectedRow.merchantId;
      this.merchantName = this.selectedRow.merchantNo;
      this.showBaseEdit = true;
    },

    handleVerify({ id }) {
      this.optType = 1;
      this.merchantId = id;
      this.showDetail = true;
    },

    handleLimitDetail() {
      if (!this.selectedRow) return message.warning('请选择一条数据');
      this.merchantId = this.selectedRow.merchantNo;
      this.merchantName = this.selectedRow.merchantName;
      this.showLimitDetail = true;
    },

    handleTradeConfig() {
      if (!this.selectedRow) return message.warning('请选择一条数据');
      this.merchantId = this.selectedRow.merchantNo;
      this.showTradeConfig = true;
    },

    async handleDownloadMerchantRateExcel() {
      console.log('下载商户费率');
      this.spinning = true;
      const data = await MerchantApi.downloadMerchantRateExcel(this.where).catch(() => {
        this.spinning = false;
      });
      this.spinning = false;
      const fileReader = new FileReader();
      fileReader.onload = function () {
        try {
          // 说明是普通对象数据，后台转换失败
          const jsonData = JSON.parse(fileReader.result);
          message.error(jsonData.message);
        } catch (err) {
          // 解析对象失败，说明是正常的文件流
          let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.style.display = 'none';
          a.href = url;
          a.download = `商户费率-下载.xlsx`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }
      };
      fileReader.readAsText(data);
    },

    datasource({ page, limit, where, orders }) {
      return MerchantApi.merchantPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
