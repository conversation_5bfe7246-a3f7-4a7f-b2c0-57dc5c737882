<template>
  <a-modal
    :width="600"
    :visible="visible"
    :confirm-loading="loading"
    title="商户编辑"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :label-col="{ md: { span: 5 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 19 }, sm: { span: 24 } }"
    >
      <a-form-item label="商户名称" name="merchantName" :rules="[{ required: true, message: '请输入商户名称' }]">
        <a-input v-model:value="form.merchantName" placeholder="商户名称" />
      </a-form-item>
      <a-form-item label="行业类别" name="mcc">
        <a-cascader
          class="edit-cascader"
          :options="mccData"
          :load-data="loadMccData"
          :placeholder="cascaderPlaceholder"
          :allow-clear="false"
          :displayRender="({ labels }) => labels[1]"
          @change="selectedMccValue"
        />
      </a-form-item>
      <a-form-item label="商户类型" name="grade">
        <a-select v-model:value="form.grade" placeholder="请选择">
          <a-select-option value="A">企业商户</a-select-option>
          <a-select-option value="B">个体工商户</a-select-option>
          <a-select-option value="C">小微商户</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="风控等级">
        <a-select v-model:value="form.riskGrade" style="width: 100%" placeholder="请选择">
          <a-select-option v-for="i in 10" :key="i" :value="i">{{ i }}级</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="定位开关">
        <a-switch
          checked-children="开"
          :checkedValue="1"
          un-checked-children="关"
          :unCheckedValue="0"
          v-model:checked="form.positionSwitchStatus"
        />
      </a-form-item>
      <a-form-item label="定位省市区" name="bankArea">
        <a-cascader
          v-model:value="areaCodeValue"
          :options="regionsData"
          :load-data="loadAreaData"
          placeholder="请选择"
          :allow-clear="false"
          @change="selectedAreaValue"
        />
      </a-form-item>
      <a-form-item label="详细地址">
        <a-select
          show-search
          v-model:value="form.positionAddr"
          placeholder="详细地址"
          not-found-content="输入关键字以查询"
          :filter-option="false"
          @search="getPoiAddress"
          @select="selectPoiAddress"
        >
          <a-select-option :value="item.id" v-for="(item, idx) in poidatasource" :key="idx">
            <div class="ant-list-item-meta-title">{{ item.locationName }}</div>
            <div class="ant-list-item-meta-description">{{ item.address }}</div>
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="有效状态" name="validStatus">
        <a-select v-model:value="form.validStatus" style="width: 100%" placeholder="请选择">
          <a-select-option :value="1">有效</a-select-option>
          <a-select-option :value="0">无效</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="交易状态" name="isAllowTrans">
        <a-radio-group v-model:value="form.isAllowTrans" name="radioGroup">
          <a-radio :value="1">开启</a-radio>
          <a-radio :value="0">关闭</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="提现状态" name="isAllowWithdraw">
        <a-radio-group v-model:value="form.isAllowWithdraw" name="radioGroup">
          <a-radio :value="1">开启</a-radio>
          <a-radio :value="0">关闭</a-radio>
        </a-radio-group>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { MerchantApi } from '@/api/merchant/MerchantApi';
import { MccCodeApi } from '@/api/base/MccCodeApi';
import { AreaApi } from '@/api/base/AreaApi';
import AMapLoader from '@amap/amap-jsapi-loader';
// 高德安全密钥配置
window._AMapSecurityConfig = {
  securityJsCode: 'bbea6227e3e4a5bdf8d8441dd3074764' //安全密钥
  // securityJsCode: '50e1be9980183d3c3f8ad93a1b80e5e8' //安全密钥
};
export default {
  name: 'MerchantEdit',
  props: {
    visible: Boolean,
    merchantId: String
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {},
      // 提交状态
      loading: false,
      mccData: [],
      cascaderPlaceholder: null,
      regionsData: [],
      areaCodeValue: [],
      cityName: '',
      originalInfo: {},
      areaName: '',
      poidatasource: [],
      autoComplete: null,
      debounceTimer: null // 新增防抖定时器
    };
  },
  async mounted() {
    await this.loadAreaData();
    this.merchantId && this.getDetail();
    // this.onPoiAddress()
    this.loadMccData([]);
  },
  methods: {
    async getDetail() {
      const info = await MerchantApi.merchantDetail({ id: this.merchantId });
      this.originalInfo = info || {};

      this.form.riskGrade = info.riskGrade;
      this.form.positionSwitchStatus = info.positionSwitchStatus;
      this.form.positionAddr = info.positionAddr;
      this.form.mcc = info.mcc;
      this.form.id = info.id;
      this.form.province = info.province;
      this.form.city = info.city;
      this.form.country = info.country;
      this.form.latitude = info.latitude;
      this.form.longitude = info.longitude;
      this.form.merchantName = info.merchantName;
      this.form.validStatus = info.validStatus;
      this.form.isAllowTrans = info.isAllowTrans;
      this.form.isAllowWithdraw = info.isAllowWithdraw;
      this.form.grade = info.grade;

      this.cascaderPlaceholder = info.mccName;

      this.areaCodeValue = [info.province, info.city, info.country];
      const provinceItem = this.regionsData.find(r => r.value === this.form.province);
      if (provinceItem) {
        this.areaName += provinceItem.label;
        await this.loadAreaData([provinceItem]);
        const cityItem = provinceItem.children.find(r => r.value === this.form.city);
        if (cityItem) {
          this.cityName = cityItem.label;
          this.areaName += cityItem.label;
          await this.loadAreaData([cityItem]);
          const countryItem = cityItem.children.find(r => r.value === this.form.country);
          this.areaName += countryItem?.label || '';
        }
      }
    },

    async loadMccData([option]) {
      const targetOption = option ? option : { mccLevel: 0, value: '' };
      const { mccLevel, value } = targetOption;

      const data = await MccCodeApi.list({ mccLevel: mccLevel + 1, parentMcc: value, status: 1 });
      const formatData = data.map(d => {
        return { label: d.mccName, value: d.mcc, isLeaf: mccLevel > 0, mccLevel: d.mccLevel };
      });

      if (mccLevel === 0) {
        this.mccData = formatData;
      } else {
        targetOption.children = formatData;
      }
    },

    async loadAreaData(selectedOptions) {
      const targetOption = selectedOptions ? selectedOptions[selectedOptions.length - 1] : { level: 1, code: '' };
      const { level, code } = targetOption;

      const data = await AreaApi.list({ level: level + 1, status: 1, parentCode: code });
      const filterData = data.map(d => {
        return { label: d.areaName, value: d.areaCode, code: d.areaCode, isLeaf: level > 2, level: d.level };
      });

      if (level === 1) {
        this.regionsData = filterData;
      } else {
        targetOption.children = filterData;
      }
    },

    selectedAreaValue(value, option) {
      [this.form.province, this.form.city, this.form.country] = value || [];
      this.form.positionAddr = '';
      this.form.latitude = '';
      this.form.longitude = '';
      this.areaName = option.map(o => o.label).join('');

      this.cityName = option[1]?.label;
    },

    async save() {
      await this.$refs.form.validate();

      if (!this.form.latitude) {
        message.warning('详细地址无效, 请更换!');
        return;
      }

      const params = Object.assign({}, this.form);

      this.loading = true;
      MerchantApi.merchantEdit(params)
        .then(result => {
          this.loading = false;

          message.success(result.message);

          this.updateVisible(false);

          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    selectedMccValue(value) {
      this.form.mcc = value[1];
    },

    selectPoiAddress(value, { key: index }) {
      const item = this.poidatasource[index];
      if (item) {
        this.form.positionAddr = item.address;
        this.form.latitude = item.latitude;
        this.form.longitude = item.longitude;
      }
    },

    async getPoiAddress(searchStr) {
      let areaNameArr = [];
      if (this.areaName) {
        const lastIndex = this.areaName.lastIndexOf(this.cityName);
        const provinceName = this.areaName.slice(0, lastIndex);
        const cityName = this.cityName;
        const countryName = this.areaName.slice(this.cityName.length + provinceName.length);

        areaNameArr = [provinceName, cityName, countryName];
      }

      clearTimeout(this.debounceTimer); // 清除之前的定时器

      // 设置新的防抖定时器（300毫秒延迟）
      this.debounceTimer = setTimeout(async () => {
        let searchKeywords = searchStr?.trim() || this.form.positionAddr?.trim() || '';

        // 当搜索关键词为空时不请求
        if (!searchKeywords) {
          this.poidatasource = [];
          return;
        }

        try {
          const data = await MerchantApi.queryLocationList({
            keyWord: searchKeywords,
            provinceCode: this.form.province || '',
            cityCode: this.form.city || '',
            countyCode: this.form.country || '',
            pageNo: 1,
            pageSize: 20
          });

          const filterList = data?.locationDetailList?.filter(i => i.longitude && i.latitude) || [];
          this.poidatasource = filterList;
        } catch (error) {
          console.error('位置查询失败:', error);
          this.poidatasource = [];
        }
      }, 500);
    },

    onPoiAddress(searchStr) {
      AMapLoader.load({
        key: 'a8be20945d2ee5379c5574d923b72215',
        // key: 'd3489f9c11a0f2cf4684106c4dfa738a',
        version: '2.0',
        plugins: ['AMap.AutoComplete', 'AMap.PlaceSearch']
      })
        .then(AMap => {
          // 配置项
          const autoOptions = {
            citylimit: true,
            city: this.cityName
          };

          this.autoComplete = new AMap.AutoComplete(autoOptions);

          let searchKeywords = searchStr || this.form.positionAddr || '';

          this.autoComplete.search(searchKeywords, (status, result) => {
            if (result?.info === 'OK') {
              const filterList = result.tips?.filter(i => !!i.location && typeof i.address === 'string');
              this.poidatasource = filterList || [];
            } else {
              this.poidatasource = [];
            }
          });
        })
        .catch(err => {
          console.error(err);
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
<style scoped lang="less">
::v-deep(.edit-cascader) .ant-select-selection-placeholder {
  color: var(--text-color);
}
::v-deep(.ant-select-selector) {
  height: auto !important;
  min-height: 32px;
  .ant-select-selection-item {
    overflow: visible;
    white-space: pre-wrap;
  }
}
</style>
