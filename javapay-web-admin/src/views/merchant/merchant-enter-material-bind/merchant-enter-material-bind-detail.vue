<template>
  <a-modal
    :width="750"
    :visible="visible"
    :maskClosable="false"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '30px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="商户编号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="商户简称">{{ form.merchantAbbr }}</a-descriptions-item>
      <a-descriptions-item label="支付通道">{{ filterChannelCode(form.channelCode) }}</a-descriptions-item>
      <a-descriptions-item label="资料进件状态">
        <a-tag v-if="form.materialEnterStatus === 0">待提交</a-tag>
        <a-tag v-else-if="form.materialEnterStatus === 1" color="processing">审核中</a-tag>
        <a-tag v-else-if="form.materialEnterStatus === 2" color="success">进件成功</a-tag>
        <a-tag v-else-if="form.materialEnterStatus === 3" color="error">进件失败</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="银联行业名称">{{ form.unionMccName }}</a-descriptions-item>
      <a-descriptions-item label="支付宝行业名称">{{ form.alipayMccName }}</a-descriptions-item>
      <a-descriptions-item label="微信行业名称" :span="2">{{ form.wechatMccName }}</a-descriptions-item>
      <a-descriptions-item label="经营类目">{{ form.categoryName }}</a-descriptions-item>
      <a-descriptions-item label="场景描述">{{ form.sceneDesc }}</a-descriptions-item>
      <a-descriptions-item label="创建人">{{ form.createUserId }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改人">{{ form.lastModifyUserId }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
export default {
  props: {
    visible: Boolean,
    detail: Object,
    channelCodes: Array,
  },
  emits: ['update:visible'],
  data() {
    return {
      form: {},
    };
  },
  watch: {
    detail() {
      this.form = Object.assign({}, this.detail);
    }
  },
  methods: {
    filterChannelCode(channelCode) {
      const item = this.channelCodes.find(i => i.channelCode === channelCode);
      return item?.channelName || channelCode;
    },
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
