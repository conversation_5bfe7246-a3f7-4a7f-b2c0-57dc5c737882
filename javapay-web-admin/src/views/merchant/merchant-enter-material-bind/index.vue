<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="商户简称">
              <a-input v-model:value.trim="where.merchantAbbr" placeholder="请输入商户简称" allow-clear />
            </a-form-item>
            <a-form-item label="支付宝行业名称">
              <a-input v-model:value.trim="where.alipayMccName" placeholder="请输入支付宝行业名称" allow-clear />
            </a-form-item>
            <a-form-item label="微信行业名称">
              <a-input v-model:value.trim="where.wechatMccName" placeholder="请输入微信行业名称" allow-clear />
            </a-form-item>
            <a-form-item label="银联行业名称">
              <a-input v-model:value.trim="where.unionMccName" placeholder="请输入银联行业名称" allow-clear />
            </a-form-item>
            <a-form-item label="支付通道">
              <a-select v-model:value="where.channelCode" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
                  {{ channelName }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="资料进件状态">
              <a-select v-model:value="where.materialEnterStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">待提交</a-select-option>
                <a-select-option :value="1">审核中</a-select-option>
                <a-select-option :value="2">进件成功</a-select-option>
                <a-select-option :value="3">进件失败</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="开始日期">
              <a-date-picker v-model:value="where.searchBeginTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item label="结束日期">
              <a-date-picker v-model:value="where.searchEndTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'materialEnterStatus'">
              <a-tag v-if="record.materialEnterStatus === 0">待提交</a-tag>
              <a-tag v-else-if="record.materialEnterStatus === 1" color="processing">审核中</a-tag>
              <a-tag v-else-if="record.materialEnterStatus === 2" color="success">进件成功</a-tag>
              <a-tag v-else-if="record.materialEnterStatus === 3" color="error">进件失败</a-tag>
            </template>
            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
                <template v-if="hasPurview(['3']) && record.materialEnterStatus === 0">
                  <a-divider type="vertical" />
                  <a-popconfirm title="确定要解绑吗？" @confirm="handleUnbind(record)">
                    <a>解绑</a>
                  </a-popconfirm>
                </template>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <MerchantEnterMaterialBindDetail v-model:visible="showDetail" :detail="current" :channelCodes="channelCodes" />
  </div>
</template>

<script>
import MerchantEnterMaterialBindDetail from './merchant-enter-material-bind-detail.vue';
import { MerchantEnterMaterialBindApi } from '@/api/merchant/MerchantEnterMaterialBindApi';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import { hasPurview } from '@/utils/permission';
import { message } from 'ant-design-vue';

export default {
  name: 'MerchantEnterMaterialBind',
  components: { MerchantEnterMaterialBindDetail },
  data() {
    return {
      channelCodes: [],
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '商户简称',
          dataIndex: 'merchantAbbr',
          align: 'center'
        },
        {
          title: '支付通道',
          dataIndex: 'channelCode',
          align: 'center',
          customRender: ({ text }) => {
            const item = this.channelCodes.find(c => c.channelCode === text);
            return item?.channelName || '--';
          }
        },
        {
          title: '资料进件状态',
          dataIndex: 'materialEnterStatus',
          key: 'materialEnterStatus',
          align: 'center'
        },
        {
          title: '银联行业名称',
          dataIndex: 'unionMccName'
        },
        {
          title: '支付宝行业名称',
          dataIndex: 'alipayMccName'
        },
        {
          title: '微信行业名称',
          dataIndex: 'wechatMccName'
        },

        {
          title: '经营类目',
          dataIndex: 'categoryName'
        },
        {
          title: '场景描述',
          dataIndex: 'sceneDesc',
          width: 200
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right',
          width: 180
        }
      ],
      // 表格搜索条件
      where: {},
      showDetail: false
    };
  },
  async mounted() {
    const data = await ChannelManageApi.list({ validStatus: 1 });
    this.channelCodes = data || [];
  },
  methods: {
    async handleUnbind(row) {
      const result = await MerchantEnterMaterialBindApi.unBind({ id: row.id });
      message.success(result.message);
      this.reload();
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    datasource({ page, limit, where, orders }) {
      return MerchantEnterMaterialBindApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    },

    hasPurview
  }
};
</script>
