<template>
  <a-modal :width="1000" :visible="visible" title="详情" :mask-closable="false" :body-style="{ paddingBottom: '20px' }" @update:visible="updateVisible">
    <a-descriptions bordered :column="2">
     
      <a-descriptions-item label="银行户名">{{ form.bankAccountName }}</a-descriptions-item>
      <a-descriptions-item label="银行账号">{{ form.bankAccountNoMask }}</a-descriptions-item>
      <a-descriptions-item label="银行名称">{{ form.bankName }}</a-descriptions-item>
      <a-descriptions-item label="开户支行名称">{{ form.bankBranch }}</a-descriptions-item>
      <a-descriptions-item label="银行行别代码">{{ form.typeCode }}</a-descriptions-item>
      <a-descriptions-item label="银行缩写">{{ form.bankSysbol }}</a-descriptions-item>
      <a-descriptions-item label="账户类型">
        <a-tag v-if="form.accountType === 'G'" color="orange">对公</a-tag>
        <a-tag v-else-if="form.accountType === 'S'" color="red">对私</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="卡类型">
        <a-tag v-if="form.cardType === 1" color="pink">借记卡</a-tag>
        <a-tag v-else-if="form.cardType === 2" color="blue">贷记卡</a-tag>
        <a-tag v-else-if="form.cardType === 3" color="cyan">准贷记卡</a-tag>
        <a-tag v-else-if="form.cardType === 4" color="purple">预付费卡</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="银行预留手机号">{{ form.mobileMask }}</a-descriptions-item>
      <a-descriptions-item label="银行预留身份证号">{{ form.idCardNoMask }}</a-descriptions-item>
      <a-descriptions-item label="联行号">{{ form.bankChannelNo }}</a-descriptions-item>
      <a-descriptions-item label="是否结算卡">
        <a-tag>{{ form.isSettleCard === 1 ? '是' : '否' }}</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="是否签约">
        <a-tag>{{ form.isSign === 1 ? '是' : '否' }}</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="商户ID">{{ form.merchantId }}</a-descriptions-item>
      <a-descriptions-item label="商户编号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="有效状态">
        <a-tag color="green" v-if="form.validStatus === 1">
          <template #icon> <check-circle-outlined /> </template>有效
        </a-tag>
        <a-tag color="red" v-else>
          <template #icon> <close-circle-outlined /> </template>无效
        </a-tag>
      </a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

export default {
  name: 'PaymentCardDetail',
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
