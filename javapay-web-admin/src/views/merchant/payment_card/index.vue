<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="银行户名">
              <a-input v-model:value.trim="where.bankAccountName" placeholder="请输入银行户名" allow-clear />
            </a-form-item>
            <a-form-item label="银行账号">
              <a-input v-model:value.trim="where.bankAccountNo" placeholder="请输入银行账号" allow-clear />
            </a-form-item>
            <a-form-item label="银行名称">
              <a-input v-model:value.trim="where.bankName" placeholder="请输入银行名称" allow-clear />
            </a-form-item>
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="预留手机号">
              <a-input v-model:value.trim="where.mobile" placeholder="请输入预留手机号" allow-clear />
            </a-form-item>
            <a-form-item label="有效状态">
              <a-select v-model:value="where.validStatus" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">有效</a-select-option>
                <a-select-option :value="0">无效</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="开始日期">
              <a-date-picker v-model:value="where.searchBeginTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item label="结束日期">
              <a-date-picker v-model:value="where.searchEndTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'cardType'">
              <a-tag v-if="record.cardType === 1" color="pink">借记卡</a-tag>
              <a-tag v-else-if="record.cardType === 2" color="blue">贷记卡</a-tag>
              <a-tag v-else-if="record.cardType === 3" color="cyan">准贷记卡</a-tag>
              <a-tag v-else-if="record.cardType === 4" color="purple">预付费卡</a-tag>
            </template>
            <template v-else-if="column.key === 'accountType'">
              <a-tag v-if="record.accountType === 'G'" color="orange">对公</a-tag>
              <a-tag v-else-if="record.accountType === 'S'" color="red">对私</a-tag>
            </template>
            <template v-else-if="column.key === 'validStatus'">
              <a-tag color="green" v-if="record.validStatus === 1">
                <template #icon> <check-circle-outlined /> </template>有效
              </a-tag>
              <a-tag color="red" v-else>
                <template #icon> <close-circle-outlined /> </template>无效
              </a-tag>
            </template>
            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <PaymentCardDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { MerchantApi } from '@/api/merchant/MerchantApi';
import PaymentCardDetail from './PaymentCardDetail.vue';

export default {
  name: 'MerchantPaymentCard',
  components: {
    PaymentCardDetail
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo'
        },
        {
          title: '银行户名',
          dataIndex: 'bankAccountName'
        },
        {
          title: '银行账号',
          dataIndex: 'bankAccountNoMask'
        },
        {
          title: '账户类型',
          key: 'accountType',
          dataIndex: 'accountType',
          align: 'center'
        },
        {
          title: '卡类型',
          key: 'cardType',
          dataIndex: 'cardType',
          align: 'center'
        },
        {
          title: '开户支行名称',
          dataIndex: 'bankBranch'
        },
        {
          title: '银行名称',
          dataIndex: 'bankName'
        },
        {
          title: '银行缩写',
          dataIndex: 'bankSysbol',
          align: 'center'
        },
        {
          title: '银行预留身份证号',
          dataIndex: 'idCardNoMask',
          align: 'center'
        },

        {
          title: '银行预留手机号',
          dataIndex: 'mobileMask',
          align: 'center'
        },
        {
          title: '有效状态',
          dataIndex: 'validStatus',
          key: 'validStatus',
          align: 'center'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 100,
          align: 'center'
        }
      ],
      // 表格搜索条件
      where: {},
      current: null,
      showDetail: false
    };
  },
  methods: {
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where, orders }) {
      return MerchantApi.paycardList({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
