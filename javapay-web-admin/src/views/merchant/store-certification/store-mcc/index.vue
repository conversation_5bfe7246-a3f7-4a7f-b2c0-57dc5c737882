<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="支付通道">
              <a-select v-model:value="where.channelCode" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
                  {{ channelName }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="店铺行业名称">
              <a-input v-model:value.trim="where.mccName" placeholder="请输入店铺行业名称" allow-clear />
            </a-form-item>
            <a-form-item label="店铺行业编码">
              <a-input v-model:value.trim="where.mcc" placeholder="请输入店铺行业编码" allow-clear />
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>
          <template #bodyCell="{ column, record }">
            <!-- table操作栏按钮 -->
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="handleEdit(record)">修改</a>
                <a-divider type="vertical" />
                <a-popconfirm title="确定要删除此记录吗？" @confirm="remove(record)">
                  <a class="ele-text-danger">删除</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <StoreMccEditVue v-if="showEdit" v-model:visible="showEdit" :data="current" :channel-codes="channelCodes" @done="reload" />
  </div>
</template>

<script>
import { StoreMccApi } from '@/api/merchant/StoreMccApi';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import StoreMccEditVue from './modules/StoreMccEdit.vue';
import { message } from 'ant-design-vue';

export default {
  name: 'StoreMcc',
  components: {
    StoreMccEditVue
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '支付通道',
          dataIndex: 'channelCode',
          align: 'center',
          customRender: ({ text }) => {
            const item = this.channelCodes.find(c => c.channelCode === text);
            return item?.channelName || '--';
          },
          width: 180
        },
        {
          title: '店铺行业编码',
          dataIndex: 'mcc',
          align: 'center',
          width: 180
        },
        {
          title: '店铺行业名称',
          dataIndex: 'mccName',
          width: 220
        },
        {
          title: '店铺行业描述',
          dataIndex: 'mccDescription',
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          align: 'center',
          width: 180
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime',
          align: 'center',
          width: 180
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          width: 150,
          fixed: 'right'
        }
      ],
      // 表格搜索条件
      where: {},
      channelCodes: [],
      showEdit: false,
      current: null
    };
  },
  mounted() {
    this.getChannelCodes();
  },
  methods: {
    async remove(row) {
      const result = await StoreMccApi.delete({ id: row.id });
      message.success(result.message);
      this.reload();
    },

    handleEdit(record) {
      this.current = record;
      this.showEdit = true;
    },

    async getChannelCodes() {
      const data = await ChannelManageApi.list({ validStatus: 1 });
      this.channelCodes = data || [];
    },

    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    datasource({ page, limit, where }) {
      return StoreMccApi.findPage({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>
