<template>
  <a-modal
    :width="600"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新增'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules" layout="vertical">
      <a-form-item label="支付通道" name="channelCode">
        <a-select v-model:value="form.channelCode" style="width: 100%" placeholder="请选择">
          <a-select-option :value="item.channelCode" v-for="item in channelCodes" :key="item.id">{{ item.channelName }} </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="店铺行业编码" name="mcc">
        <a-input v-model:value="form.mcc" placeholder="店铺行业编码" />
      </a-form-item>
      <a-form-item label="店铺行业名称" name="mccName">
        <a-textarea v-model:value="form.mccName" placeholder="店铺行业名称" :auto-size="{ minRows: 1, maxRows: 5 }" />
      </a-form-item>
      <a-form-item label="店铺行业描述" name="mccDescription">
        <a-textarea v-model:value="form.mccDescription" placeholder="店铺行业描述" :auto-size="{ minRows: 1, maxRows: 5 }" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { StoreMccApi } from '@/api/merchant/StoreMccApi';

export default {
  props: {
    visible: Boolean,
    data: Object,
    channelCodes: Array
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {
        channelCode: null, // 通道编号【必须】"
        mcc: '',
        mccName: '',
        mccDescription: ''
      },
      // 表单验证规则
      rules: {
        mcc: [{ required: true, message: '请输入店铺行业编码' }],
        mccName: [{ required: true, message: '请输入店铺行业名称' }],
        mccDescription: [{ required: true, message: '请输入店铺行业描述' }],
        channelCode: [{ required: true, message: '请选择' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false
    };
  },
  created() {
    if (this.data) {
      this.isUpdate = true;
      Object.keys(this.form).forEach(key => {
        this.form[key] = this.data[key];
      });

      this.form.id = this.data.id;
    }
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      // 执行编辑或修改方法
      if (this.isUpdate) {
        result = StoreMccApi.edit(this.form);
      } else {
        result = StoreMccApi.add(this.form);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
