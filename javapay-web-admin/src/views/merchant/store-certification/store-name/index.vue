<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="门店名称">
              <a-input v-model:value.trim="where.storeName" placeholder="请输入门店名称" allow-clear />
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>
          <template #bodyCell="{ column, record }">
            <!-- table操作栏按钮 -->
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="handleEdit(record)">修改</a>
                <a-divider type="vertical" />
                <a-popconfirm title="确定要删除此记录吗？" @confirm="remove(record)">
                  <a class="ele-text-danger">删除</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <StoreNameEdit v-if="showEdit" v-model:visible="showEdit" :data="current" @done="reload" />
  </div>
</template>

<script>
import { StoreNameApi } from '@/api/merchant/StoreNameApi';
import StoreNameEdit from './modules/StoreNameEdit.vue';
import { message } from 'ant-design-vue';

export default {
  name: 'StoreName',
  components: {
    StoreNameEdit
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '门店名称',
          dataIndex: 'storeName',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          width: 150,
          fixed: 'right'
        }
      ],
      // 表格搜索条件
      where: {},
      channelCodes: [],
      showEdit: false,
      current: null
    };
  },
  methods: {
    async remove(row) {
      const result = await StoreNameApi.delete({ id: row.id });
      message.success(result.message);
      this.reload();
    },

    handleEdit(record) {
      this.current = record;
      this.showEdit = true;
    },

    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    datasource({ page, limit, where }) {
      return StoreNameApi.findPage({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>
