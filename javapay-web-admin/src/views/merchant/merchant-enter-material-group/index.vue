<template>
  <div class="ele-body">
    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- table上边工具栏 -->
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>

          <template #bodyCell="{ column, record }">
            <!-- table操作栏按钮 -->
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="handleEdit(record)">修改</a>
                <a-divider type="vertical" />
                <a-popconfirm title="确定要同步吗？" @confirm="handleSync(record)">
                  <a>同步</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 编辑 -->
    <MerchantEnterMaterialGroupEdit
      v-model:visible="showEdit"
      :data="current"
      :channelCodes="channelCodes"
      @done="reload"
      v-if="showEdit"
    />
  </div>
</template>

<script>
import { MerchantEnterMaterialGroupApi } from '@/api/merchant/MerchantEnterMaterialGroupApi';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import MerchantEnterMaterialGroupEdit from './merchant-enter-material-group-edit.vue';
import { message } from 'ant-design-vue';

export default {
  name: 'MerchantEnterMaterialGroup',
  components: {
    MerchantEnterMaterialGroupEdit
  },
  data() {
    return {
      // 表格搜索条件
      where: {},
      current: null,
      showEdit: false,
      showDetail: false,
      channelCodes: [],
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '资料分组名称',
          dataIndex: 'materialName',
          align: 'center'
        },
        {
          title: '支持通道',
          dataIndex: 'channelCodes',
          key: 'channelCodes',
          width: 180,
          customRender: ({ text }) => {
            if (text?.length) {
              const channelCodes = [...text];
              channelCodes.forEach((i, index) => {
                const item = this.channelCodes.find(c => c.channelCode === i);
                if (item) {
                  channelCodes[index] = item.channelName;
                }
              });

              return channelCodes.join(',');
            }
            return '--';
          }
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right',
          width: 160
        }
      ]
    };
  },
  async mounted() {
    const data = await ChannelManageApi.list({ validStatus: 1 });
    this.channelCodes = data || [];
  },
  methods: {
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    async handleSync(row) {
      const result = await MerchantEnterMaterialGroupApi.sync({ id: row.id });
      message.success(result.message);
    },

    datasource({ page, limit, where, orders }) {
      return MerchantEnterMaterialGroupApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
