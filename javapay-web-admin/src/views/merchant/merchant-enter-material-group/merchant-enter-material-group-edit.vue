<template>
  <a-modal
    :width="600"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新建'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 6 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 18 }, sm: { span: 24 } }"
    >
      <a-form-item label="资料分组名称" name="materialName">
        <a-input v-model:value="form.materialName" placeholder="请输入资料分组名称" allow-clear />
      </a-form-item>
      <a-form-item label="支付通道" name="channelCodes">
        <a-select v-model:value="form.channelCodes" placeholder="请选择" allow-clear mode="multiple">
          <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
            {{ channelName }}
          </a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { MerchantEnterMaterialGroupApi } from '@/api/merchant/MerchantEnterMaterialGroupApi';

export default {
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    data: Object,
    // 支付通道列表
    channelCodes: Array
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {},
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false,
      // 表单验证规则
      rules: {
        materialName: [{ required: true, message: '请输入资料分组名称' }],
        channelCodes: [{ required: true, message: '请选择支付通道' }]
      }
    };
  },
  mounted() {
    if (this.data) {
      this.form = Object.assign({}, this.data);
      this.isUpdate = true;
    } else {
      this.isUpdate = false;
    }
  },
  methods: {
    /**
     * 保存
     */
    async save() {
      // 校验表单
      await this.$refs.form.validate();
      // 修改加载框为正在加载
      this.loading = true;

      let result = null;
      // 执行添加或修改
      if (this.isUpdate) {
        result = MerchantEnterMaterialGroupApi.edit(this.form);
      } else {
        result = MerchantEnterMaterialGroupApi.add(this.form);
      }

      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
