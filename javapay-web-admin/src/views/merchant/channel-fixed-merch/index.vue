<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="通道商户简称">
              <a-input v-model:value.trim="where.chnMerchName" placeholder="通道商户简称" allow-clear />
            </a-form-item>
            <a-form-item label="通道商户编码">
              <a-input v-model:value.trim="where.chnMerchNo" placeholder="通道商户编码" allow-clear />
            </a-form-item>
            <a-form-item label="支付通道">
              <a-select v-model:value="where.channelCode" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">{{
                  channelName
                }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="归属地">
              <a-radio-group v-model:value="areaRange" @change="changeAreaRange">
                <a-radio-button :value="2">省</a-radio-button>
                <a-radio-button :value="3">市</a-radio-button>
                <a-cascader
                  :key="cascaderKey"
                  style="width: 205px"
                  v-model:value="areaCodeValue"
                  :options="regionsData"
                  :load-data="loadAreaData"
                  placeholder="请选择"
                  allow-clear
                  @change="selectedAreaValue"
                />
              </a-radio-group>
            </a-form-item>
            <a-form-item label="使用状态">
              <a-select v-model:value="where.usedStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">已使用</a-select-option>
                <a-select-option :value="0">未使用</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'usedStatus'">
              <a-tag color="success" v-if="record.usedStatus === 1">已使用</a-tag>
              <a-tag v-else>未使用</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
                <a @click="handleEdit(record)">修改</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>
    <!-- 商户修改 -->
    <ChannelFixedMerchEdit v-if="showEdit" v-model:visible="showEdit" :data="current" :channel-codes="channelCodes" @done="reload" />

    <!-- 商户详情 -->
    <ChannelFixedMerchDetail v-if="showDetail" v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import ChannelFixedMerchEdit from './channel-fixed-merch-edit.vue';
import ChannelFixedMerchDetail from './channel-fixed-merch-detail.vue';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import { hasPurview } from '@/utils/permission';
import { AreaApi } from '@/api/base/AreaApi';
import { ChannelFixedMerchApi } from '@/api/merchant/ChannelFixedMerchApi';

export default {
  name: 'FixedMerchantList',
  components: {
    ChannelFixedMerchEdit,
    ChannelFixedMerchDetail
  },
  data() {
    return {
      hasPurview,
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '支付通道',
          dataIndex: 'channelCode',
          align: 'center',
          customRender: ({ text, record }) => {
            const item = this.channelCodes.find(c => c.channelCode === text);
            record.channelName = item?.channelName || '--';
            return record.channelName;
          }
        },
        {
          title: '通道商户简称',
          dataIndex: 'chnMerchName'
        },
        {
          title: '通道商户编码',
          dataIndex: 'chnMerchNo'
        },
        {
          title: '归属省编码',
          dataIndex: 'provCode'
        },
        {
          title: '归属省名称',
          dataIndex: 'provName'
        },
        {
          title: '归属市编码',
          dataIndex: 'cityCode'
        },
        {
          title: '归属市名称',
          dataIndex: 'cityName'
        },
        {
          title: '使用状态',
          dataIndex: 'usedStatus',
          key: 'usedStatus',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          width: 160,
          fixed: 'right',
          align: 'center'
        }
      ],
      // 表格搜索条件
      where: {},
      showEdit: false,
      showDetail: false,
      channelCodes: [],
      current: null,

      regionsData: [],
      areaCodeValue: [],
      areaRange: 2,
      cascaderKey: 0
    };
  },
  mounted() {
    this.getChannelList();
    this.loadAreaData();
  },
  methods: {
    changeAreaRange() {
      this.cascaderKey++;
      this.areaCodeValue = [];
      this.regionsData = [];
      this.selectedAreaValue();
      this.loadAreaData();
    },
    async loadAreaData(selectedOptions) {
      const targetOption = selectedOptions ? selectedOptions[selectedOptions.length - 1] : { level: 1, code: '' };
      const { level, code } = targetOption;

      const data = await AreaApi.list({ level: level + 1, status: 1, parentCode: code });
      const filterData = data.map(d => {
        return { label: d.areaName, value: d.areaCode, code: d.areaCode, isLeaf: this.areaRange === level + 1, level: d.level };
      });

      if (level === 1) {
        this.regionsData = filterData;
      } else {
        targetOption.children = filterData;
      }
    },

    selectedAreaValue(value) {
      [this.where.provCode, this.where.cityCode] = value || [];
    },

    async getChannelList() {
      const data = await ChannelManageApi.list({ validStatus: 1 });
      this.channelCodes = data;
    },

    reload() {
      this.selectedRow = null;
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.areaCodeValue = [];
      this.selectedRow = null;
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where, orders }) {
      return ChannelFixedMerchApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
