<template>
  <a-modal
    :width="800"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '30px' }"
    @update:visible="updateVisivle"
  >
    <a-descriptions :column="2" bordered>
      <a-descriptions-item label="支付通道" :span="2">
        <a-badge color="purple" :text="form.channelName" />
      </a-descriptions-item>
      <a-descriptions-item label="通道商户简称">{{ form.chnMerchName }}</a-descriptions-item>
      <a-descriptions-item label="通道商户编码">{{ form.chnMerchNo }}</a-descriptions-item>
      <a-descriptions-item label="归属省编码">{{ form.provCode }}</a-descriptions-item>
      <a-descriptions-item label="归属省名称">{{ form.provName }}</a-descriptions-item>
      <a-descriptions-item label="归属市编码">{{ form.cityCode }}</a-descriptions-item>
      <a-descriptions-item label="归属市名称">{{ form.cityName }}</a-descriptions-item>
      <a-descriptions-item label="使用状态" :span="2">
        <a-tag color="success" v-if="form.usedStatus === 1">已使用</a-tag>
        <a-tag v-else>未使用</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisivle(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  data() {
    return {
      form: {}
    };
  },
  mounted() {
    this.form = Object.assign({}, this.detail);
  },
  methods: {
    updateVisivle(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
