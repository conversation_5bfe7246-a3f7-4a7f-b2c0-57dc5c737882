<template>
  <a-modal
    :width="680"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新增'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules" layout="vertical">
      <a-row :gutter="24">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="支付通道" name="channelCode">
            <a-select v-model:value="form.channelCode" style="width: 100%" placeholder="请选择">
              <a-select-option :value="item.channelCode" v-for="item in channelCodes" :key="item.id">{{
                item.channelName
              }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="归属地" name="cityCode">
            <a-cascader
              v-model:value="officeAreaValue"
              :options="officeRegionsData"
              :load-data="options => loadAreaData(options)"
              :allow-clear="false"
              placeholder="请选择"
              @change="selectedOfficeAreaValue"
            />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="通道商户简称" name="chnMerchName">
            <a-input v-model:value="form.chnMerchName" placeholder="通道商户简称" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="通道商户编号" name="chnMerchNo">
            <a-input v-model:value="form.chnMerchNo" placeholder="通道商户编号" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="使用状态" name="usedStatus">
            <a-select v-model:value="form.usedStatus" style="width: 100%" placeholder="请选择">
              <a-select-option :value="1">已使用</a-select-option>
              <a-select-option :value="0">未使用</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { AreaApi } from '@/api/base/AreaApi';
import { deepCopy } from '@/utils/util';
import { ChannelFixedMerchApi } from '@/api/merchant/ChannelFixedMerchApi';

export default {
  props: {
    visible: Boolean,
    data: Object,
    channelCodes: Array
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {
        channelCode: null, // 通道编号【必须】"
        chnMerchName: '', // 通道商户简称【必须】"
        chnMerchNo: '', // 通道商户编码【必须】"
        cityCode: '', // 归属市编码【必须】"
        cityName: '', // 归属市名称【必须】"
        provCode: '', // 归属省编码【必须】"
        provName: '', // 归属省名称【必须】"
        usedStatus: 0
      },
      officeAreaValue: [],
      officeRegionsData: [],
      // 表单验证规则
      rules: {
        chnMerchName: [{ required: true, message: '请输入通道商户简称' }],
        chnMerchNo: [{ required: true, message: '请输入通道商户编号' }],
        channelCode: [{ required: true, message: '请选择' }],
        cityCode: [{ required: true, message: '请选择' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false
    };
  },
  created() {
    if (this.data) {
      this.isUpdate = true;
      Object.keys(this.form).forEach(key => {
        this.form[key] = this.data[key];
      });

      this.form.id = this.data.id;

      if (this.data.cityCode) {
        this.officeAreaValue = [this.data.provCode, this.data.cityCode];
      }
    }
    this.loadAreaData();
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      // 执行编辑或修改方法
      if (this.isUpdate) {
        result = ChannelFixedMerchApi.edit(this.form);
      } else {
        result = ChannelFixedMerchApi.add(this.form);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    async loadAreaData(selectedOptions, totalLevel = 2) {
      const targetOption = selectedOptions ? selectedOptions[selectedOptions.length - 1] : { level: 1, code: '' };
      const { level, code } = targetOption;
      const data = await AreaApi.list({ level: level + 1, status: 1, parentCode: code });
      const filterData = data.map(d => {
        return { label: d.areaName, value: d.areaCode, code: d.areaCode, isLeaf: level > totalLevel - 1, level: d.level };
      });
      if (level === 1) {
        this.officeRegionsData = deepCopy(filterData);
      } else {
        targetOption.children = filterData;
      }

      if (!selectedOptions && this.isUpdate) {
        const officeItem = this.officeRegionsData.find(r => r.value === this.form.provCode);
        if (officeItem) {
          this.loadAreaData([officeItem]);
        }
      }
    },

    selectedOfficeAreaValue(value, options) {
      [this.form.provCode, this.form.cityCode] = value || [];
      this.form.provName = options[0].label;
      this.form.cityName = options[1].label;
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
