<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="商户名称">
              <a-input v-model:value.trim="where.merchantName" placeholder="请输入商户名称" allow-clear />
            </a-form-item>
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="登录账号">
              <a-input v-model:value.trim="where.loginAccount" placeholder="请输入登录账号" allow-clear />
            </a-form-item>
            <a-form-item label="审核状态">
              <a-select v-model:value="where.checkStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">未审核</a-select-option>
                <a-select-option :value="1">审核通过</a-select-option>
                <a-select-option :value="2">审核不通过</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table
          ref="table"
          row-key="id"
          :datasource="datasource"
          :columns="columns"
          :where="where"
          :scroll="{ x: 'max-content' }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'checkStatus'">
              <a-tag v-if="record.checkStatus === 0" color="processing">未审核</a-tag>
              <a-tag v-else-if="record.checkStatus === 1" color="success">审核通过</a-tag>
              <a-tag v-else-if="record.checkStatus === 2" color="error">审核不通过</a-tag>
              <span v-else>--</span>
            </template>

            <!-- table操作栏按钮 -->
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
                <template v-if="record.checkStatus === 0">
                  <a-divider type="vertical" />
                  <a @click="handleCheck(record)">审核</a>
                </template>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>
    <!-- 详情 -->
    <MerchantCancelAuditDetail v-model:visible="showDetail" :detail="current" />

    <!-- 审核 -->
    <a-modal
      :width="500"
      v-if="showCheck"
      v-model:visible="showCheck"
      :confirm-loading="loading"
      title="审核"
      :mask-closable="false"
      :body-style="{ paddingBottom: '8px' }"
      @ok="onConfirmCheck"
    >
      <a-form
        ref="checkform"
        :model="checkform"
        :label-col="{ md: { span: 4 }, sm: { span: 24 } }"
        :wrapper-col="{ md: { span: 20 }, sm: { span: 24 } }"
      >
        <a-form-item label="审核状态" name="checkStatus">
          <a-select v-model:value="checkform.checkStatus" style="width: 100%" placeholder="请选择">
            <a-select-option :value="1">审核通过</a-select-option>
            <a-select-option :value="2">审核不通过</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="审核备注" name="remarks" :rules="[{ required: checkform.checkStatus === 3, message: '请输入审核备注' }]">
          <a-textarea v-model:value="checkform.remarks" :auto-size="{ minRows: 3, maxRows: 5 }" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>
  
  <script>
import { MerchantCancelAuditApi } from '@/api/merchant/MerchantCancelAuditApi';
import MerchantCancelAuditDetail from './merchant-cancel-audit-detail.vue';
import { message } from 'ant-design-vue';

export default {
  name: 'MerchantCancel',
  components: {
    MerchantCancelAuditDetail
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '商户名称',
          dataIndex: 'merchantName'
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo'
        },
        {
          title: '登录账号',
          dataIndex: 'loginAccount'
        },
        {
          title: '代理名称',
          dataIndex: 'agentName'
        },
        {
          title: '代理编号',
          dataIndex: 'agentNo'
        },
        {
          title: '法人手机号',
          dataIndex: 'legalTelMask',
          align: 'center'
        },
        {
          title: 'C端登录用户ID',
          dataIndex: 'userId',
          align: 'center'
        },
        {
          title: 'app端登录用户ID',
          dataIndex: 'customerId',
          align: 'center'
        },
        {
          title: '审核状态',
          dataIndex: 'checkStatus',
          key: 'checkStatus',
          align: 'center'
        },
        {
          title: '审核',
          dataIndex: 'auditRemark',
          width: 200,
          customRender: ({ text }) => text || '--'
        },
        {
          title: '审核人',
          dataIndex: 'checkUserId'
        },
        {
          title: '审核时间',
          dataIndex: 'checkTime'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          width: 150,
          align: 'center',
          fixed: 'right'
        }
      ],
      // 表格搜索条件
      where: {},
      selection: [],
      current: null,
      showDetail: false,
      checkform: {},
      showCheck: false,
      loading: false
    };
  },
  methods: {
    reload() {
      this.selection = [];
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.selection = [];
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    handleCheck(row) {
      this.checkform = Object.assign({
        checkStatus: 1,
        remarks: '',
        id: row.id
      });
      this.showCheck = true;
    },

    async onConfirmCheck() {
      await this.$refs.checkform.validate();

      this.loading = true;

      const result = await MerchantCancelAuditApi.checkCancel(this.checkform).catch(() => (this.loading = false));
      message.success(result.message);

      this.loading = false;
      this.showCheck = false;

      this.reload();
    },

    async datasource({ page, limit, where }) {
      return MerchantCancelAuditApi.findPage({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>
  