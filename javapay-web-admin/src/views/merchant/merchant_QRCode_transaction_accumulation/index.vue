<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table
          ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where"
          :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <!-- table操作栏按钮 -->
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <TransDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { MerchantQRCodeTransactionAccumulationApi } from '@/api/merchant/MerchantQRCodeTransactionAccumulationApi'
import TransDetail from './detail.vue'

export default {
  name: 'MerchantQRCodeTransactionAccumulation',
  components: { TransDetail },
  data() {
    return {
      // 表格搜索条件
      where: {},
      showDetail: false,
      current: null,
      //表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left',
        },
        {
          title: '商户ID',
          dataIndex: 'merchantId',
          align: 'center',
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo',
          align: 'center',
        },
        {
          title: '云闪付日累计金额',
          dataIndex: 'unionpayDayCumulate',
          align: 'center',
        },
        {
          title: '云闪付月累计金额',
          dataIndex: 'unionpayMonthCumulate',
          align: 'center',
        },
        {
          title: '微信日累计金额',
          dataIndex: 'wechatDayCumulate',
          align: 'center',
        },
        {
          title: '微信月累计金额',
          dataIndex: 'wechatMonthCumulate',
          align: 'center',
        },
        {
          title: '支付宝日累计金额',
          dataIndex: 'alipayDayCumulate',
          align: 'center',
        },
        {
          title: '支付宝月累计金额',
          dataIndex: 'alipayMonthCumulate',
          align: 'center',
        },
        {
          title: 'EPOS日累计金额',
          dataIndex: 'eposDayCumulate',
          align: 'center',
        },
        {
          title: 'EPOS月累计金额',
          dataIndex: 'eposMonthCumulate',
          align: 'center',
        },
        {
          title: 'EPOS自限额日累计金额',
          dataIndex: 'eposSelfDayCumulate',
          align: 'center',
        },
        {
          title: 'EPOS自限额月累计金额',
          dataIndex: 'eposSelfMonthCumulate',
          align: 'center',
        },
        {
          title: 'D0单日提现累计金额',
          dataIndex: 'd0DayWithdrawCumulate',
          align: 'center',
        },
        {
          title: 'POS手机Pay贷记卡日累计金额',
          dataIndex: 'posNfcCreditDayCumulate',
          align: 'center',
        },
        {
          title: 'POS手机Pay贷记卡月累计金额',
          dataIndex: 'posNfcCreditMonthCumulate',
          align: 'center',
        },
        {
          title: 'POS手机Pay借记卡日累计金额',
          dataIndex: 'posNfcDebitDayCumulate',
          align: 'center',
        },
        {
          title: 'POS手机Pay借记卡月累计金额',
          dataIndex: 'posNfcDebitMonthCumulate',
          align: 'center',
        },
        {
          title: 'POS芯片贷记卡日累计金额',
          dataIndex: 'posIcCreditDayCumulate',
          align: 'center',
        },
        {
          title: 'POS芯片贷记卡月累计金额',
          dataIndex: 'posIcCreditMonthCumulate',
          align: 'center',
        },
        {
          title: 'POS芯片借记卡日累计金额',
          dataIndex: 'posIcDebitDayCumulate',
          align: 'center',
        },
        {
          title: 'POS芯片借记卡月累计金额',
          dataIndex: 'posIcDebitMonthCumulate',
          align: 'center',
        },
        {
          title: 'POS磁条贷记卡日累计金额',
          dataIndex: 'posCiCreditDayCumulate',
          align: 'center',
        },
        {
          title: 'POS磁条贷记卡月累计金额',
          dataIndex: 'posCiCreditMonthCumulate',
          align: 'center',
        },
        {
          title: 'POS磁条借记卡日累计金额',
          dataIndex: 'posCiDebitDayCumulate',
          align: 'center',
        },
        {
          title: 'POS磁条借记卡月累计金额',
          dataIndex: 'posCiDebitMonthCumulate',
          align: 'center',
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right',
          width: 120
        }
      ]
    };
  },
  methods: {
    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    /**
     * 搜索按钮
     */
    reload() {
      this.selection = [];
      this.$refs.table.reload({ page: 1 });
    },

    /**
     * 重置搜索
     */
    reset() {
      this.where = {};
      this.selection = [];
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    /**
     * 获取表格数据
     */
    datasource({ page, limit, where }) {
      return MerchantQRCodeTransactionAccumulationApi.getMerchantQRCodeTransactionAccumulationPages({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>

<style></style>
