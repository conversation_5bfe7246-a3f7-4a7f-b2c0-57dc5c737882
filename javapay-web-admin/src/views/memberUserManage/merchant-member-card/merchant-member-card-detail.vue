<template>
  <a-modal
    :width="750"
    :visible="visible"
    :maskClosable="false"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '30px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="商户编号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="商户ID">{{ form.merchantId }}</a-descriptions-item>
      <a-descriptions-item label="会员编号">{{ form.memberNo }}</a-descriptions-item>
      <a-descriptions-item label="会员ID">{{ form.memberId }}</a-descriptions-item>
      <a-descriptions-item label="会员积分">{{ form.memberPoints }}</a-descriptions-item>
    </a-descriptions>
    <a-descriptions :column="2">
      <a-descriptions-item label="是否启用免密支付">
        <a-tag v-if="form.isEnablePwdfree === 1" color="success">是</a-tag>
        <a-tag v-else-if="form.isEnablePwdfree === 0" color="error">否</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="免密支付限额">{{ form.pwdfreePayLimit }}</a-descriptions-item>
      <a-descriptions-item label="是否设置支付密码">
        <a-tag v-if="form.isSetPayPwd === 1" color="success">是</a-tag>
        <a-tag v-else-if="form.isSetPayPwd === 0" color="error">否</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="会员支付密码">{{ form.payPassword }}</a-descriptions-item>
      <a-descriptions-item label="会员账号（UUID）">{{ form.memberUuid }}</a-descriptions-item>
      <a-descriptions-item label="会员钱包账户">{{ form.memberWalletUuid }}</a-descriptions-item>    
    </a-descriptions>
    <br />

    <a-descriptions :column="2">
      <a-descriptions-item label="创建人">{{ form.createUserId }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改人">{{ form.lastModifyUserId }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>

    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>
            
            <script>
export default {
  name: 'MerchantMemberCardDetail',
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  data() {
    return {
      form: {}
    };
  },
  watch: {
    //监控，外面点击切换就换值
    detail() {
      this.form = Object.assign({}, this.detail);
    }
  },
  methods: {
    //更新显示值
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
            
          <style>
</style>