<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="会员编号">
              <a-input v-model:value.trim="where.memberNo" placeholder="请输入会员编号" allow-clear />
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'isEnablePwdfree'">
              <a-tag v-if="record.isEnablePwdfree === 1" color="success">是</a-tag>
              <a-tag v-else-if="record.isEnablePwdfree === 0" color="error">否</a-tag>
            </template>

            <template v-if="column.key === 'isSetPayPwd'">
              <a-tag v-if="record.isSetPayPwd === 1" color="success">是</a-tag>
              <a-tag v-else-if="record.isSetPayPwd === 0" color="error">否</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <MerchantMemberCardDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>
        
        <script>
import { MerchantMemberCardApi } from '@/api/memberUserManage/MerchantMemberCardApi';
import MerchantMemberCardDetail from './merchant-member-card-detail.vue';

export default {
  name: 'MerchantMemberCard',
  components: {
    MerchantMemberCardDetail
  },
  data() {
    return {
      // 表格搜索条件
      where: {},
      current: null,
      showDetail: false,
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '商户ID',
          dataIndex: 'merchantId',
          align: 'center'
        },
        {
          title: '会员编号',
          dataIndex: 'memberNo',
          align: 'center'
        },
        {
          title: '会员ID',
          dataIndex: 'memberId',
          align: 'center'
        },
        {
          title: '会员积分',
          dataIndex: 'memberPoints',
          align: 'center'
        },
        {
          title: '是否启用免密支付',
          dataIndex: 'isEnablePwdfree',
          key: 'isEnablePwdfree',
          align: 'center'
        },
        {
          title: '免密支付限额',
          dataIndex: 'pwdfreePayLimit',
          align: 'center'
        },
        {
          title: '是否设置支付密码',
          dataIndex: 'isSetPayPwd',
          key: 'isSetPayPwd',
          align: 'center'
        },
        {
          title: '会员支付密码',
          dataIndex: 'payPassword',
          align: 'center'
        },
        {
          title: '会员账号（UUID）',
          dataIndex: 'memberUuid',
          align: 'center'
        },
        {
          title: '会员钱包账户',
          dataIndex: 'memberWalletUuid',
          align: 'center'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right',
          width: 180
        }
      ]
    };
  },
  methods: {
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where, orders }) {
      return MerchantMemberCardApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
        
      <style>
</style>