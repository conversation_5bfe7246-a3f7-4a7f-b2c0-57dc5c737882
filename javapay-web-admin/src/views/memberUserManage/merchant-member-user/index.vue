<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="会员编号">
              <a-input v-model:value.trim="where.memberNo" placeholder="请输入会员编号" allow-clear />
            </a-form-item>
            <a-form-item label="会员姓名">
              <a-input v-model:value.trim="where.realName" placeholder="请输入会员姓名" allow-clear />
            </a-form-item>
            <a-form-item label="会员昵称">
              <a-input v-model:value.trim="where.nickName" placeholder="请输入会员昵称" allow-clear />
            </a-form-item>
            <a-form-item label="会员等级">
              <a-select v-model:value="where.memberLevel" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option value="LV1">1级</a-select-option>
                <a-select-option value="LV2">2级</a-select-option>
                <a-select-option value="LV3">3级</a-select-option>
                <a-select-option value="LV4">4级</a-select-option>
                <a-select-option value="LV5">5级</a-select-option>
                <a-select-option value="LV6">6级</a-select-option>
                <a-select-option value="LV7">7级</a-select-option>
                <a-select-option value="LV8">8级</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'memberLevel'">
              <a-tag v-if="record.memberLevel === 'LV1'" color="pink">1级</a-tag>
              <a-tag v-else-if="record.memberLevel === 'LV2'" color="cyan">2级</a-tag>
              <a-tag v-else-if="record.memberLevel === 'LV3'" color="blue">3级</a-tag>
              <a-tag v-else-if="record.memberLevel === 'LV4'" color="purple">4级</a-tag>
              <a-tag v-else-if="record.memberLevel === 'LV5'" color="pink">5级</a-tag>
              <a-tag v-else-if="record.memberLevel === 'LV6'" color="cyan">6级</a-tag>
              <a-tag v-else-if="record.memberLevel === 'LV7'" color="blue">7级</a-tag>
              <a-tag v-else-if="record.memberLevel === 'LV8'" color="purple">8级</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <MerchantMemberUserDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>
          
          <script>
import { MerchantMemberUserApi } from '@/api/memberUserManage/MerchantMemberUserApi';
import MerchantMemberUserDetail from './merchant-member-user-detail.vue';

export default {
  name: 'MerchantMemberUser',
  components: {
    MerchantMemberUserDetail
  },
  data() {
    return {
      // 表格搜索条件
      where: {},
      current: null,
      showDetail: false,
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '商户ID',
          dataIndex: 'merchantId',
          align: 'center'
        },
        {
          title: '商户编码',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '会员ID',
          dataIndex: 'memberId',
          align: 'center'
        },
        {
          title: '会员编号',
          dataIndex: 'memberNo',
          align: 'center'
        },
        {
          title: '会员账号',
          dataIndex: 'memberUuid',
          align: 'center'
        },
        {
          title: '会员等级',
          dataIndex: 'memberLevel',
          key: 'memberLevel',
          align: 'center'
        },
        {
          title: '会员昵称',
          dataIndex: 'nickName',
          align: 'center'
        },
        {
          title: '会员姓名',
          dataIndex: 'realName',
          align: 'center'
        },
        {
          title: '出生日期',
          dataIndex: 'birthday',
          align: 'center'
        },
        {
          title: '性别',
          dataIndex: 'sex',
          align: 'center'
        },
        {
          title: '会员身份证',
          dataIndex: 'idCardMask',
          align: 'center'
        },
        {
          title: '会员手机号',
          dataIndex: 'mobileMask',
          align: 'center'
        },
        {
          title: '微信用户唯一标识',
          dataIndex: 'wxUserId',
          align: 'center'
        },
        {
          title: '支付宝用户唯一标识',
          dataIndex: 'aliUserId',
          align: 'center'
        },
        {
          title: '云闪付用户唯一标识',
          dataIndex: 'ysfUserId',
          align: 'center'
        },
        {
          title: '邀请码',
          dataIndex: 'invitationCode',
          align: 'center'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right',
          width: 180
        }
      ]
    };
  },
  methods: {
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where, orders }) {
      return MerchantMemberUserApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
          
        <style>
</style>