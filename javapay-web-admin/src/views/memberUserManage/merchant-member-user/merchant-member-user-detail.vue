<template>
  <a-modal
    :width="750"
    :visible="visible"
    :maskClosable="false"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '30px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="商户ID">{{ form.merchantId }}</a-descriptions-item>
      <a-descriptions-item label="商户编号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="会员编号">{{ form.memberNo }}</a-descriptions-item>
      <a-descriptions-item label="会员账号UUID">{{ form.memberUuid }}</a-descriptions-item>
      <a-descriptions-item label="会员等级">{{ form.memberLevel }}</a-descriptions-item>
      <a-descriptions-item label="会员昵称">{{ form.nickName }}</a-descriptions-item>
      <a-descriptions-item label="性别">{{ form.sex }}</a-descriptions-item>
      <a-descriptions-item label="出生日期">{{ form.birthday }}</a-descriptions-item>
      <a-descriptions-item label="会员姓名">{{ form.realName }}</a-descriptions-item>
      <a-descriptions-item label="会员身份证">{{ form.idCardMask }}</a-descriptions-item>
      <a-descriptions-item label="会员手机号">{{ form.mobileMask }}</a-descriptions-item>
      <a-descriptions-item label="微信用户唯一标识">{{ form.wxUserId }}</a-descriptions-item>
      <a-descriptions-item label="支付宝用户唯一标识">{{ form.aliUserId }}</a-descriptions-item>
      <a-descriptions-item label="云闪付用户唯一标识">{{ form.ysfUserId }}</a-descriptions-item>
      <a-descriptions-item label="邀请码">{{ form.invitationCode }}</a-descriptions-item>
    </a-descriptions>
    <br />

    <a-descriptions :column="2">
      <a-descriptions-item label="创建人">{{ form.createUserId }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改人">{{ form.lastModifyUserId }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>

    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>
              
              <script>
export default {
  name: 'MerchantMemberUserDetail',
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  data() {
    return {
      form: {}
    };
  },
  watch: {
    //监控，外面点击切换就换值
    detail() {
      this.form = Object.assign({}, this.detail);
    }
  },
  methods: {
    //更新显示值
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
              
<style>
</style>