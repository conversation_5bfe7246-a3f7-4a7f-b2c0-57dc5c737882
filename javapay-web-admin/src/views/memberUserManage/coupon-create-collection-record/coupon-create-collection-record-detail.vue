<template>
  <a-modal
    :width="750"
    :visible="visible"
    :maskClosable="false"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '30px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="商户ID">{{ form.merchantId }}</a-descriptions-item>
      <a-descriptions-item label="商户编号">{{ form.merchantNo }}</a-descriptions-item>

      <a-descriptions-item label="优惠券模板ID">{{ form.couponConfigId }}</a-descriptions-item>
      <a-descriptions-item label="模版名称">{{ form.couponName }}</a-descriptions-item>

      <a-descriptions-item label="优惠券类别">
        <a-tag v-if="form.couponClass === 1" color="pink">商家会员优惠券</a-tag>
        <a-tag v-else-if="form.couponClass === 2" color="cyan">商家会员优惠券</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="优惠券类型">
        <a-tag v-if="form.couponType === 1" color="pink">折扣券</a-tag>
        <a-tag v-else-if="form.couponType === 2" color="cyan">满减券</a-tag>
        <a-tag v-else-if="form.couponType === 3" color="purple">兑换券</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="优惠券模板状态">
        <a-tag v-if="form.couponTempStatus === 1" color="pink">可编辑</a-tag>
        <a-tag v-else-if="form.couponTempStatus === 2" color="cyan">已发布</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="优惠券使用场景">
        <a-tag v-if="form.useScenario === 1" color="pink">线上线下通用</a-tag>
        <a-tag v-else-if="form.useScenario === 2" color="cyan">线下使用</a-tag>
        <a-tag v-else-if="form.useScenario === 3" color="blue">线上使用</a-tag>
        <a-tag v-else-if="form.useScenario === 4" color="purple">指定门店使用</a-tag>
        <a-tag v-else-if="form.useScenario === 5" color="pink">指定品类使用</a-tag>
        <a-tag v-else-if="form.useScenario === 6" color="cyan">指定品牌使用</a-tag>
        <a-tag v-else-if="form.useScenario === 7" color="blue">指定商品使用</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="有效状态">
        <a-tag v-if="form.validStatus === 1" color="success">有效</a-tag>
        <a-tag v-else-if="form.validStatus === 0" color="error">无效</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="优惠券是否已被领取">
        <a-tag v-if="form.isReceived === 1" color="success">是</a-tag>
        <a-tag v-else-if="form.isReceived === 0" color="error">否</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="优惠券领取时间">{{ form.receiveTime }}</a-descriptions-item>

      <a-descriptions-item label="优惠券使用状态">
        <a-tag v-if="form.couponUseStatus === 1" color="pink">未使用</a-tag>
        <a-tag v-else-if="form.couponUseStatus === 2" color="cyan">已使用</a-tag>
        <a-tag v-else-if="form.couponUseStatus === 3" color="blue">已过期</a-tag>
        <a-tag v-else-if="form.couponUseStatus === 4" color="purple">已作废</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="券有效开始时间">{{ form.validityStartTime }}</a-descriptions-item>
      <a-descriptions-item label="券有效结束时间">{{ form.validityEndTime }}</a-descriptions-item>

      <a-descriptions-item label="是否有使用门槛">
        <a-tag v-if="form.isUseThreshold === 1" color="success">有门槛</a-tag>
        <a-tag v-else-if="form.isUseThreshold === 0" color="error">无门槛</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="使用门槛金额(元)" v-if="form.isUseThreshold === 1">{{ form.useThresholdAmount }}</a-descriptions-item>
      <a-descriptions-item label="减免金额(元)">{{ form.deductionAmount }}</a-descriptions-item>
      <a-descriptions-item label="折扣比例(%)">{{ form.discountRatio }}</a-descriptions-item>
      <a-descriptions-item label="折扣上限(元)">{{ form.discountUpLimit }}</a-descriptions-item>

      <a-descriptions-item label="是否允许叠加使用">
        <a-tag v-if="form.isAllowOverlayUse === 1" color="success">支持</a-tag>
        <a-tag v-else-if="form.isAllowOverlayUse === 0" color="error">不支持</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="允许领取数量限制">
        <a-tag v-if="form.receiptQuantityLimit === -1" color="pink">不限制</a-tag>
        <a-tag v-else color="blue">{{ form.receiptQuantityLimit }}</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="券可领取开始时间">{{ form.receiptStartTime }}</a-descriptions-item>
      <a-descriptions-item label="券可领取结束时间">{{ form.receiptEndTime }}</a-descriptions-item>

      <a-descriptions-item label="是否支持主动领取">
        <a-tag v-if="form.supportActiveReceipt === 1" color="success">支持</a-tag>
        <a-tag v-else-if="form.supportActiveReceipt === 0" color="error">不支持</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="全额退款后是否允许退还">
        <a-tag v-if="form.isAllowReturn === 1" color="success">退还</a-tag>
        <a-tag v-else-if="form.isAllowReturn === 0" color="error">不退还</a-tag>
      </a-descriptions-item>
    </a-descriptions>

    <br />

    <a-descriptions :column="2">
      <a-descriptions-item label="创建人">{{ form.createUserId }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改人">{{ form.lastModifyUserId }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>

    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>
            
            <script>
export default {
  name: 'CouponCreateCollectionRecordDetail',
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  data() {
    return {
      form: {}
    };
  },
  watch: {
    //监控，外面点击切换就换值
    detail() {
      this.form = Object.assign({}, this.detail);
      this.form.appConfigParamsDTO = this.detail.secretPayParamDTO;
    }
  },
  methods: {
    //更新显示值
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
            
<style>
</style>