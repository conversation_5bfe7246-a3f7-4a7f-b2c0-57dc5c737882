<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
            </a-form-item>

            <a-form-item label="优惠券名称">
              <a-input v-model:value.trim="where.couponName" placeholder="请输入优惠券模板名称" allow-clear />
            </a-form-item>

            <a-form-item label="有效状态">
              <a-select v-model:value="where.validStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">有效</a-select-option>
                <a-select-option :value="0">无效</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="优惠券类别">
              <a-select v-model:value="where.couponClass" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">商家会员优惠券</a-select-option>
                <a-select-option :value="2">第三方优惠券</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="优惠券类型">
              <a-select v-model:value="where.couponType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">折扣券</a-select-option>
                <a-select-option :value="2">满减券</a-select-option>
                <a-select-option :value="3">兑换券</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="优惠券使用状态">
              <a-select v-model:value="where.couponUseStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">未使用</a-select-option>
                <a-select-option :value="2">已使用</a-select-option>
                <a-select-option :value="3">已过期</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="优惠券是否已被领取">
              <a-select v-model:value="where.isReceived" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">是</a-select-option>
                <a-select-option :value="0">否</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="优惠券使用场景">
              <a-select v-model:value="where.useScenario" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">线上线下通用</a-select-option>
                <a-select-option :value="2">线下使用</a-select-option>
                <a-select-option :value="3">线上使用</a-select-option>
                <a-select-option :value="4">指定门店使用</a-select-option>
                <a-select-option :value="5">指定品类使用</a-select-option>
                <a-select-option :value="6">指定品牌使用</a-select-option>
                <a-select-option :value="7">指定商品使用</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="是否允许叠加使用">
              <a-select v-model:value="where.isAllowOverlayUse" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">是</a-select-option>
                <a-select-option :value="0">否</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="全额退款后是否允许退还">
              <a-select v-model:value="where.isAllowReturn" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">是</a-select-option>
                <a-select-option :value="0">否</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="是否有使用门槛">
              <a-select v-model:value="where.isUseThreshold" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">是</a-select-option>
                <a-select-option :value="0">否</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="是否支持主动领取">
              <a-select v-model:value="where.supportActiveReceipt" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">是</a-select-option>
                <a-select-option :value="0">否</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="会员编号">
              <a-input v-model:value.trim="where.memberNo" placeholder="请输入会员编号" allow-clear />
            </a-form-item>
            

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'validStatus'">
              <a-tag v-if="record.validStatus === 1" color="success">有效</a-tag>
              <a-tag v-else-if="record.validStatus === 0" color="error">无效</a-tag>
            </template>

            <template v-else-if="column.key === 'receiptQuantityLimit'">
              <a-tag v-if="record.receiptQuantityLimit === -1" color="success">不限制</a-tag>
              <text v-else>{{ record.receiptQuantityLimit }}</text>
            </template>

            <template v-else-if="column.key === 'couponClass'">
              <a-tag v-if="record.couponClass === 1" color="pink">商家会员优惠券</a-tag>
              <a-tag v-else-if="record.couponClass === 2" color="cyan">第三方优惠券</a-tag>
            </template>

            <template v-else-if="column.key === 'couponType'">
              <a-tag v-if="record.couponType === 1" color="pink">折扣券</a-tag>
              <a-tag v-else-if="record.couponType === 2" color="cyan">满减券</a-tag>
              <a-tag v-else-if="record.couponType === 3" color="purple">兑换券</a-tag>
            </template>

            <template v-else-if="column.key === 'couponUseStatus'">
              <a-tag v-if="record.couponUseStatus === 1" color="pink">未使用</a-tag>
              <a-tag v-else-if="record.couponUseStatus === 2" color="cyan">已使用</a-tag>
              <a-tag v-else-if="record.couponUseStatus === 3" color="cyan">已过期</a-tag>
              <a-tag v-else-if="record.couponUseStatus === 4" color="cyan">已作废</a-tag>
            </template>

            <template v-else-if="column.key === 'isAllowOverlayUse'">
              <a-tag v-if="record.isAllowOverlayUse === 1" color="success">是</a-tag>
              <a-tag v-else-if="record.isAllowOverlayUse === 0" color="error">否</a-tag>
            </template>

            <template v-else-if="column.key === 'supportActiveReceipt'">
              <a-tag v-if="record.supportActiveReceipt === 1" color="success">是</a-tag>
              <a-tag v-else-if="record.supportActiveReceipt === 0" color="error">否</a-tag>
            </template>

            <template v-else-if="column.key === 'isReceived'">
              <a-tag v-if="record.isReceived === 1" color="pink">是</a-tag>
              <a-tag v-else-if="record.isReceived === 0" color="cyan">否</a-tag>
            </template>

            <template v-else-if="column.key === 'useScenario'">
              <a-tag v-if="record.useScenario === 1" color="pink">线上线下通用</a-tag>
              <a-tag v-else-if="record.useScenario === 2" color="cyan">线下使用</a-tag>
              <a-tag v-else-if="record.useScenario === 3" color="blue">线上使用</a-tag>
              <a-tag v-else-if="record.useScenario === 4" color="purple">指定门店使用</a-tag>
              <a-tag v-else-if="record.useScenario === 5" color="pink">指定品类使用</a-tag>
              <a-tag v-else-if="record.useScenario === 6" color="cyan">指定品牌使用</a-tag>
              <a-tag v-else-if="record.useScenario === 7" color="blue">指定商品使用</a-tag>
            </template>

            <template v-else-if="column.key === 'isUseThreshold'">
              <a-tag v-if="record.isUseThreshold === 1" color="success">是</a-tag>
              <a-tag v-else-if="record.isUseThreshold === 0" color="error">否</a-tag>
            </template>

            <template v-else-if="column.key === 'isAllowReturn'">
              <a-tag v-if="record.isAllowReturn === 1" color="success">是</a-tag>
              <a-tag v-else-if="record.isAllowReturn === 0" color="error">否</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <CouponCreateCollectionRecordDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>
    
    <script>
import { CouponCreateCollectionRecordApi } from '@/api/memberUserManage/CouponCreateCollectionRecordApi';
import CouponCreateCollectionRecordDetail from './coupon-create-collection-record-detail.vue';

export default {
  name: 'CouponCreateCollectionRecord',
  components: {
    CouponCreateCollectionRecordDetail
  },
  data() {
    return {
      // 表格搜索条件
      where: {},
      current: null,
      showDetail: false,
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '优惠券名称',
          dataIndex: 'couponName',
          align: 'center'
        },
        {
          title: '优惠券类别',
          dataIndex: 'couponClass',
          key: 'couponClass',
          align: 'center'
        },
        {
          title: '优惠券类型',
          dataIndex: 'couponType',
          key: 'couponType',
          align: 'center'
        },
        {
          title: '优惠券使用场景',
          dataIndex: 'useScenario',
          key: 'useScenario',
          align: 'center'
        },
        {
          title: '有效状态',
          dataIndex: 'validStatus',
          key: 'validStatus',
          align: 'center'
        },
        {
          title: '优惠券是否已被领取',
          dataIndex: 'isReceived',
          key: 'isReceived',
          align: 'center'
        },
        {
          title: '优惠券领取时间',
          dataIndex: 'receiveTime',
          align: 'center'
        },
        {
          title: '优惠券使用状态',
          dataIndex: 'couponUseStatus',
          key: 'couponUseStatus',
          align: 'center'
        },
        {
          title: '券有效开始时间',
          dataIndex: 'validityStartTime',
          align: 'center'
        },
        {
          title: '券有效结束时间',
          dataIndex: 'validityEndTime',
          align: 'center'
        },
        {
          title: '是否有使用门槛',
          dataIndex: 'isUseThreshold',
          key: 'isUseThreshold',
          align: 'center'
        },
        {
          title: '使用门槛金额(元)',
          dataIndex: 'useThresholdAmount',
          align: 'center'
        },
        {
          title: '减免金额(元)',
          dataIndex: 'deductionAmount',
          align: 'center'
        },
        {
          title: '折扣比例(%)',
          dataIndex: 'discountRatio',
          align: 'center'
        },
        {
          title: '折扣上限(元)',
          dataIndex: 'discountUpLimit',
          align: 'center'
        },
        {
          title: '是否允许叠加使用',
          dataIndex: 'isAllowOverlayUse',
          key: 'isAllowOverlayUse',
          align: 'center'
        },
        {
          title: '允许领取数量限制',
          dataIndex: 'receiptQuantityLimit',
          key: 'receiptQuantityLimit',
          align: 'center'
        },
        {
          title: '券可领取开始时间',
          dataIndex: 'receiptStartTime',
          align: 'center'
        },
        {
          title: '券可领取结束时间',
          dataIndex: 'receiptEndTime',
          align: 'center'
        },
        {
          title: '是否支持主动领取',
          dataIndex: 'supportActiveReceipt',
          key: 'supportActiveReceipt',
          align: 'center'
        },
        {
          title: '全额退款后是否允许退还',
          dataIndex: 'isAllowReturn',
          key: 'isAllowReturn',
          align: 'center'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right',
          width: 180
        }
      ]
    };
  },
  methods: {
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where, orders }) {
      return CouponCreateCollectionRecordApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
    
  <style>
</style>