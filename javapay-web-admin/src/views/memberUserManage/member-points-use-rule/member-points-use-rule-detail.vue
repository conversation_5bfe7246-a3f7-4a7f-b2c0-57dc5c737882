<template>
  <a-modal
    :width="750"
    :visible="visible"
    :maskClosable="false"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '30px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="商户编号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="商户ID">{{ form.merchantId }}</a-descriptions-item>
      <a-descriptions-item label="优惠券模板ID">{{ form.couponConfigId }}</a-descriptions-item>
      <a-descriptions-item label="优惠券模板名称">{{ form.couponTempName }}</a-descriptions-item>
      <a-descriptions-item label="积分兑换类型">
        <a-tag v-if="form.redeemType === 1" color="pink">优惠券兑换</a-tag>
        <a-tag v-else-if="form.redeemType === 2" color="cyan">指定商品兑换</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="积分数量">{{ form.pointsQuantity }}</a-descriptions-item>
    </a-descriptions>
    <br />

    <a-descriptions :column="2">
      <a-descriptions-item label="创建人">{{ form.createUserId }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改人">{{ form.lastModifyUserId }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>

    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>
          
          <script>
export default {
  name: 'MemberPointsUseRuleDetail',
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  data() {
    return {
      form: {}
    };
  },
  watch: {
    //监控，外面点击切换就换值
    detail() {
      this.form = Object.assign({}, this.detail);
    }
  },
  methods: {
    //更新显示值
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
          
        <style>
</style>