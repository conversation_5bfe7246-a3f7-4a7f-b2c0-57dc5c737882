<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="优惠券模板名称">
              <a-input v-model:value.trim="where.couponTempName" placeholder="请输入优惠券模板名称" allow-clear />
            </a-form-item>
            <a-form-item label="积分兑换类型">
              <a-select v-model:value="where.redeemType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">优惠券兑换</a-select-option>
                <a-select-option :value="2">指定商品兑换</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'redeemType'">
              <a-tag v-if="record.redeemType === 1" color="pink">优惠券兑换</a-tag>
              <a-tag v-else-if="record.redeemType === 2" color="cyan">指定商品兑换</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <MemberPointsUseRuleDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>
        
        <script>
import { MemberPointsUseRuleApi } from '@/api/memberUserManage/MemberPointsUseRuleApi';
import MemberPointsUseRuleDetail from './member-points-use-rule-detail.vue';

export default {
  name: 'MemberGradeDefinition',
  components: {
    MemberPointsUseRuleDetail
  },
  data() {
    return {
      // 表格搜索条件
      where: {},
      current: null,
      showDetail: false,
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '商户ID',
          dataIndex: 'merchantId',
          align: 'center'
        },
        {
          title: '优惠券模板ID',
          dataIndex: 'couponConfigId',
          align: 'center'
        },
        {
          title: '优惠券模板名称',
          dataIndex: 'couponTempName',
          align: 'center'
        },
        {
          title: '积分兑换类型',
          dataIndex: 'redeemType',
          key: 'redeemType',
          align: 'center'
        },
        {
          title: '积分数量',
          dataIndex: 'pointsQuantity',
          align: 'center'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right',
          width: 180
        }
      ]
    };
  },
  methods: {
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where, orders }) {
      return MemberPointsUseRuleApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
        
      <style>
</style>