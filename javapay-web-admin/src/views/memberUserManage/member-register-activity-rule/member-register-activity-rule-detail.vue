<template>
  <a-modal
    :width="750"
    :visible="visible"
    :maskClosable="false"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '30px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="商户编号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="商户ID">{{ form.merchantId }}</a-descriptions-item>
      <a-descriptions-item label="活动起始时间">{{ form.activityStartTime }}</a-descriptions-item>
      <a-descriptions-item label="活动结束时间">{{ form.activityEndTime }}</a-descriptions-item>
      <a-descriptions-item label="是否赠送积分">
        <a-tag v-if="form.isGivePoints === 1" color="success">是</a-tag>
        <a-tag v-else-if="form.isGivePoints === 0" color="error">否</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="赠送积分数量">{{ form.pointsQuantity }}</a-descriptions-item>
    </a-descriptions>
    <br />

    <a-descriptions :column="2">
      <a-descriptions-item label="创建人">{{ form.createUserId }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改人">{{ form.lastModifyUserId }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>

    <a-divider orientation="left" :orientationMargin="0" v-if="form.couponDTOList" dashed>优惠券信息列表</a-divider>
    <div v-for="({ couponName, couponCount }, key) in form.couponDTOList" :key="key">
      <a-descriptions :column="2">
        <a-descriptions-item label="优惠券名称">{{ couponName }}</a-descriptions-item>
        <a-descriptions-item label="优惠券数量">{{ couponCount }}</a-descriptions-item>
      </a-descriptions>
    </div>

    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>
            
            <script>
export default {
  name: 'MemberRegisterActivityRuleDetail',
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  data() {
    return {
      form: {}
    };
  },
  watch: {
    //监控，外面点击切换就换值
    detail() {
      this.form = Object.assign({}, this.detail);
    }
  },
  methods: {
    //更新显示值
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
            
          <style>
</style>