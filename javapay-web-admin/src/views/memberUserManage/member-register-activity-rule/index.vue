<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="活动起始时间">
              <a-date-picker
                style="width: 100%"
                v-model:value="where.activityStartTime"
                format="YYYY-MM-DD HH:mm:ss"
                valueFormat="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择时间"
                :show-time="{ defaultValue: defaultTime }"
              />
            </a-form-item>

            <a-form-item label="活动结束时间">
              <a-date-picker
                style="width: 100%"
                v-model:value="where.activityEndTime"
                format="YYYY-MM-DD HH:mm:ss"
                valueFormat="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择时间"
                :show-time="{ defaultValue: defaultTime }"
              />
            </a-form-item>

            <a-form-item label="是否赠送积分">
              <a-select v-model:value="where.isGivePoints" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">是</a-select-option>
                <a-select-option :value="0">否</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'isGivePoints'">
              <a-tag v-if="record.isGivePoints === 1" color="success">是</a-tag>
              <a-tag v-else-if="record.isGivePoints === 0" color="error">否</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <MemberRegisterActivityRuleDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>
          
<script>
import { MemberRegisterActivityRuleApi } from '@/api/memberUserManage/MemberRegisterActivityRuleApi';
import MemberRegisterActivityRuleDetail from './member-register-activity-rule-detail.vue';
import dayjs from 'dayjs';

export default {
  name: 'MemberRegisterActivityRule',
  components: {
    MemberRegisterActivityRuleDetail
  },
  data() {
    return {
      // 表格搜索条件
      where: {},
      current: null,
      showDetail: false,
      defaultTime: dayjs('00:00:00', 'HH:mm:ss'),
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '商户ID',
          dataIndex: 'merchantId',
          align: 'center'
        },
        {
          title: '是否赠送积分',
          dataIndex: 'isGivePoints',
          key: 'isGivePoints',
          align: 'center'
        },
        {
          title: '活动起始时间',
          dataIndex: 'activityStartTime',
          align: 'center'
        },
        {
          title: '活动结束时间',
          dataIndex: 'activityEndTime',
          align: 'center'
        },
        {
          title: '赠送积分数量',
          dataIndex: 'pointsQuantity',
          align: 'center'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right',
          width: 180
        }
      ]
    };
  },
  methods: {
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where, orders }) {
      return MemberRegisterActivityRuleApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
          
        <style>
</style>