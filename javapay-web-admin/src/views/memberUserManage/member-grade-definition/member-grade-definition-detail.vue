<template>
  <a-modal
    :width="750"
    :visible="visible"
    :maskClosable="false"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '30px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="商户编号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="商户ID">{{ form.merchantId }}</a-descriptions-item>
      <a-descriptions-item label="级别名称">{{ form.gradeName }}</a-descriptions-item>
      <a-descriptions-item label="等级编号">
        <a-tag v-if="form.gradeNo === 'LV1'" color="pink">1级</a-tag>
        <a-tag v-else-if="form.gradeNo === 'LV2'" color="cyan">2级</a-tag>
        <a-tag v-else-if="form.gradeNo === 'LV3'" color="blue">3级</a-tag>
        <a-tag v-else-if="form.gradeNo === 'LV4'" color="purple">4级</a-tag>
        <a-tag v-else-if="form.gradeNo === 'LV5'" color="pink">5级</a-tag>
        <a-tag v-else-if="form.gradeNo === 'LV6'" color="cyan">6级</a-tag>
        <a-tag v-else-if="form.gradeNo === 'LV7'" color="blue">7级</a-tag>
        <a-tag v-else-if="form.gradeNo === 'LV8'" color="purple">8级</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="是否发放优惠券">
        <a-tag v-if="form.isIssuedCoupon === 1" color="success">是</a-tag>
        <a-tag v-if="form.isIssuedCoupon === 2" color="error">否</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="符合标准（金额）">{{ form.meetStandard }}</a-descriptions-item>
    </a-descriptions>
    <br />

    <a-descriptions :column="2">
      <a-descriptions-item label="创建人">{{ form.createUserId }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改人">{{ form.lastModifyUserId }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>

    <a-divider orientation="left" :orientationMargin="0" v-if="form.couponDTOList" dashed>优惠券列表</a-divider>
    <div v-for="({ couponName, couponCount }, key) in form.couponDTOList" :key="key">
      <a-descriptions :column="2">
        <a-descriptions-item label="优惠券名称">{{ couponName }}</a-descriptions-item>
        <a-descriptions-item label="优惠券数量">{{ couponCount }}</a-descriptions-item>
      </a-descriptions>
    </div>

    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>
      
      <script>
export default {
  name: 'MemberGradeDefinitionDetail',
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  data() {
    return {
      form: {}
    };
  },
  watch: {
    //监控，外面点击切换就换值
    detail() {
      this.form = Object.assign({}, this.detail);
    }
  },
  methods: {
    //更新显示值
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
      
    <style>
</style>