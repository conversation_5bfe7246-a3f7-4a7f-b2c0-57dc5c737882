<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
            </a-form-item>

            <a-form-item label="等级名称">
              <a-input v-model:value.trim="where.gradeName" placeholder="请输入等级名称" allow-clear />
            </a-form-item>

            <a-form-item label="等级编号">
              <a-select v-model:value="where.gradeNo" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option value="LV1">1级</a-select-option>
                <a-select-option value="LV2">2级</a-select-option>
                <a-select-option value="LV3">3级</a-select-option>
                <a-select-option value="LV4">4级</a-select-option>
                <a-select-option value="LV5">5级</a-select-option>
                <a-select-option value="LV6">6级</a-select-option>
                <a-select-option value="LV7">7级</a-select-option>
                <a-select-option value="LV8">8级</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="是否发放优惠券">
              <a-select v-model:value="where.isIssuedCoupon" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">是</a-select-option>
                <a-select-option :value="0">否</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'gradeNo'">
              <a-tag v-if="record.gradeNo === 'LV1'" color="pink">1级</a-tag>
              <a-tag v-else-if="record.gradeNo === 'LV2'" color="cyan">2级</a-tag>
              <a-tag v-else-if="record.gradeNo === 'LV3'" color="blue">3级</a-tag>
              <a-tag v-else-if="record.gradeNo === 'LV4'" color="purple">4级</a-tag>
              <a-tag v-else-if="record.gradeNo === 'LV5'" color="pink">5级</a-tag>
              <a-tag v-else-if="record.gradeNo === 'LV6'" color="cyan">6级</a-tag>
              <a-tag v-else-if="record.gradeNo === 'LV7'" color="blue">7级</a-tag>
              <a-tag v-else-if="record.gradeNo === 'LV8'" color="purple">8级</a-tag>
            </template>

            <template v-else-if="column.key === 'isIssuedCoupon'">
              <a-tag v-if="record.isIssuedCoupon === 1" color="success">是</a-tag>
              <a-tag v-else-if="record.isIssuedCoupon === 0" color="error">否</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <MemberGradeDefinitionDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>
    
    <script>
import { MemberGradeDefinitionApi } from '@/api/memberUserManage/MemberGradeDefinitionApi';
import MemberGradeDefinitionDetail from './member-grade-definition-detail.vue';

export default {
  name: 'MemberGradeDefinition',
  components: {
    MemberGradeDefinitionDetail
  },
  data() {
    return {
      // 表格搜索条件
      where: {},
      current: null,
      showDetail: false,
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '商户ID',
          dataIndex: 'merchantId',
          align: 'center'
        },
        {
          title: '级别名称',
          dataIndex: 'gradeName',
          align: 'center'
        },
        {
          title: '等级编号',
          dataIndex: 'gradeNo',
          key: 'gradeNo',
          align: 'center'
        },
        {
          title: '是否发放优惠券',
          dataIndex: 'isIssuedCoupon',
          key: 'isIssuedCoupon',
          align: 'center'
        },
        {
          title: '符合标准（金额）',
          dataIndex: 'meetStandard',
          align: 'center'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right',
          width: 180
        }
      ]
    };
  },
  methods: {
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where, orders }) {
      return MemberGradeDefinitionApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
    
  <style>
</style>