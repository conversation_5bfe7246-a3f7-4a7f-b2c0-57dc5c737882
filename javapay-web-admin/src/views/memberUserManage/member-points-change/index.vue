<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="会员编号">
              <a-input v-model:value.trim="where.memberNo" placeholder="请输入会员编号" allow-clear />
            </a-form-item>
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="记账标识">
              <a-select v-model:value="where.debitType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">加款+</a-select-option>
                <a-select-option :value="2">减款-</a-select-option>
                <a-select-option :value="3">不记账</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="积分变动来源">
              <a-select v-model:value="where.changeSource" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">会员注册</a-select-option>
                <a-select-option :value="2">购物获得</a-select-option>
                <a-select-option :value="3">活动获得（签到、点击浏览 等等）</a-select-option>
                <a-select-option :value="4">积分兑换</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'debitType'">
              <a-tag v-if="record.debitType === 1" color="pink">加款+</a-tag>
              <a-tag v-else-if="record.debitType === 2" color="cyan">减款-</a-tag>
              <a-tag v-else-if="record.debitType === 3" color="blue">不记账</a-tag>
            </template>

            <template v-if="column.key === 'changeSource'">
              <a-tag v-if="record.changeSource === 1" color="pink">会员注册</a-tag>
              <a-tag v-else-if="record.changeSource === 2" color="cyan">购物获得</a-tag>
              <a-tag v-else-if="record.changeSource === 3" color="blue">活动获得（签到、点击浏览 等等）</a-tag>
              <a-tag v-else-if="record.changeSource === 4" color="purple">积分兑换</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <MemberPointsChangeDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>
      
      <script>
import { MemberPointsChangeApi } from '@/api/memberUserManage/MemberPointsChangeApi';
import MemberPointsChangeDetail from './member-points-change-detail.vue';

export default {
  name: 'MemberGradeDefinition',
  components: {
    MemberPointsChangeDetail
  },
  data() {
    return {
      // 表格搜索条件
      where: {},
      current: null,
      showDetail: false,
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '商户ID',
          dataIndex: 'merchantId',
          align: 'center'
        },
        {
          title: '会员编号',
          dataIndex: 'memberNo',
          align: 'center'
        },
        {
          title: '会员ID',
          dataIndex: 'memberId',
          align: 'center'
        },
        {
          title: '记账标识',
          dataIndex: 'debitType',
          key: 'debitType',
          align: 'center'
        },
        {
          title: '变动来源ID',
          dataIndex: 'changeSourceId',
          align: 'center'
        },
        {
          title: '积分变动来源',
          dataIndex: 'changeSource',
          key: 'changeSource',
          align: 'center'
        },
        {
          title: '变动积分数量',
          dataIndex: 'changeQuantity',
          align: 'center'
        },
        {
          title: '现有积分数量',
          dataIndex: 'nowQuantity',
          align: 'center'
        },
        {
          title: '原有积分数量',
          dataIndex: 'origQuantity',
          align: 'center'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right',
          width: 180
        }
      ]
    };
  },
  methods: {
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where, orders }) {
      return MemberPointsChangeApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
      
    <style>
</style>