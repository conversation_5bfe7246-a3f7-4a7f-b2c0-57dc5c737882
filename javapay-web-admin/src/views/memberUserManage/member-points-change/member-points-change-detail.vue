<template>
  <a-modal
    :width="750"
    :visible="visible"
    :maskClosable="false"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '30px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="商户编号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="商户ID">{{ form.merchantId }}</a-descriptions-item>
      <a-descriptions-item label="会员编号">{{ form.memberNo }}</a-descriptions-item>
      <a-descriptions-item label="会员ID">{{ form.memberId }}</a-descriptions-item>
      <a-descriptions-item label="记账标识">
        <a-tag v-if="form.debitType === 1" color="pink">加款+</a-tag>
        <a-tag v-else-if="form.debitType === 2" color="cyan">减款-</a-tag>
        <a-tag v-else-if="form.debitType === 3" color="blue">不记账</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="变动来源ID">{{ form.changeSourceId }}</a-descriptions-item>
      <a-descriptions-item label="积分变动来源">
        <a-tag v-if="form.changeSource === 1" color="pink">会员注册</a-tag>
        <a-tag v-else-if="form.changeSource === 2" color="cyan">购物获得</a-tag>
        <a-tag v-else-if="form.changeSource === 3" color="blue">活动获得（签到、点击浏览 等等）</a-tag>
        <a-tag v-else-if="form.changeSource === 4" color="purple">积分兑换</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="原有积分数量">{{ form.origQuantity }}</a-descriptions-item>
      <a-descriptions-item label="现有积分数量">{{ form.nowQuantity }}</a-descriptions-item>
      <a-descriptions-item label="变动积分数量">{{ form.changeQuantity }}</a-descriptions-item>
    </a-descriptions>
    <br />

    <a-descriptions :column="2">
      <a-descriptions-item label="创建人">{{ form.createUserId }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改人">{{ form.lastModifyUserId }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>

    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>
        
        <script>
export default {
  name: 'MemberPointsChangeDetail',
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  data() {
    return {
      form: {}
    };
  },
  watch: {
    //监控，外面点击切换就换值
    detail() {
      this.form = Object.assign({}, this.detail);
    }
  },
  methods: {
    //更新显示值
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
        
      <style>
</style>