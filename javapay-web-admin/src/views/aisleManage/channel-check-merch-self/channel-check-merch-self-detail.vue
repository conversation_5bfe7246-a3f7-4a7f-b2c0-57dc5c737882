<template>
  <a-modal
    :width="880"
    :visible="visible"
    :maskClosable="false"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '30px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="支付通道">{{ filterChannelCode(form.channelCode) }}</a-descriptions-item>
      <a-descriptions-item label="商户编号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="通道商户编号">{{ form.chnMerchNo }}</a-descriptions-item>
      <a-descriptions-item label="直属代理商编号">{{ form.agentNo }}</a-descriptions-item>
      <a-descriptions-item label="权益订单号" :span="2">{{ form.payOrderNo }}</a-descriptions-item>
      <a-descriptions-item label="是否需要意愿核身">
        <a-tag v-if="form.isNeedIdentityVert === 0">否</a-tag>
        <a-tag v-else-if="form.isNeedIdentityVert === 1" color="cyan">是</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="意愿核身订单号">{{ form.checkOrderNo }}</a-descriptions-item>
      <a-descriptions-item label="意愿核身状态">
        <a-tag v-if="form.checkSelfStatus === 0" color="blue">已发起</a-tag>
        <a-tag v-else-if="form.checkSelfStatus === 1" color="green">成功</a-tag>
        <a-tag v-else-if="form.checkSelfStatus === 2" color="red">失败</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="意愿核身完成时间">{{ form.checkFinishTime }}</a-descriptions-item>
      <a-descriptions-item label="意愿核身通道返回信息" :span="2">{{ form.chnRespDesc }}</a-descriptions-item>
      <a-descriptions-item label="意愿核身h5链接地址" :span="2">{{ form.checkSelfUrl }}</a-descriptions-item>
      <a-descriptions-item label="应收金额">{{ form.originPayAmt }}</a-descriptions-item>
      <a-descriptions-item label="实收金额">{{ form.actualPayAmt }}</a-descriptions-item>
      <a-descriptions-item label="支付状态">
        <a-tag v-if="form.payStatus === 0">初始化订单</a-tag>
        <a-tag v-else-if="form.payStatus === 1" color="orange">未支付</a-tag>
        <a-tag v-else-if="form.payStatus === 2" color="green">已支付</a-tag>
        <a-tag v-else-if="form.payStatus === 3" color="red">支付失败</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="支付时间">{{ form.payTime }}</a-descriptions-item>
      <a-descriptions-item label="押金活动金额">{{ form.serviceAmount }}</a-descriptions-item>
      <a-descriptions-item label="终端编号">{{ form.terminalNo }}</a-descriptions-item>
      <a-descriptions-item label="终端SN">{{ form.terminalSn }}</a-descriptions-item>
      <a-descriptions-item label="有效开始时间">{{ form.validBeginTime }}</a-descriptions-item>
      <a-descriptions-item label="有效结束时间">{{ form.validEndTime }}</a-descriptions-item>
      <a-descriptions-item label="有效状态">
        <a-tag v-if="form.validStatus === 0" color="pink">无效</a-tag>
        <a-tag v-else-if="form.validStatus === 1" color="cyan">有效</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="客户端类型">
        <a-tag v-if="form.clientType === 2" color="blue">浏览器</a-tag>
        <a-tag v-else-if="form.clientType === 1" color="cyan">APP</a-tag>
      </a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
export default {
  props: {
    visible: Boolean,
    detail: Object,
    channelCodes: Array
  },
  emits: ['update:visible'],
  data() {
    return {
      form: {}
    };
  },
  watch: {
    detail() {
      this.form = Object.assign({}, this.detail);
    }
  },
  methods: {
    filterChannelCode(channelCode) {
      const item = this.channelCodes.find(i => i.channelCode === channelCode);
      return item?.channelName || channelCode;
    },
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
