<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="通道商户编号">
              <a-input v-model:value.trim="where.chnMerchNo" placeholder="请输入通道商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="权益订单号">
              <a-input v-model:value.trim="where.payOrderNo" placeholder="请输入权益订单号" allow-clear />
            </a-form-item>
            <a-form-item label="意愿核身订单号">
              <a-input v-model:value.trim="where.checkOrderNo" placeholder="请输入意愿核身订单号" allow-clear />
            </a-form-item>
            <a-form-item label="归属大区" v-if="hasPurview(['0'])">
              <a-input v-model:value.trim="where.regionNo" placeholder="请输入归属大区" allow-clear />
            </a-form-item>
            <a-form-item label="归属运营中心" v-if="hasPurview(['0', '1'])">
              <a-input v-model:value.trim="where.branchNo" placeholder="请输入归属运营中心" allow-clear />
            </a-form-item>
            <a-form-item label="归属一级代理" v-if="hasPurview(['0', '1', '2'])">
              <a-input v-model:value.trim="where.agentNo" placeholder="请输入归属一级代理" allow-clear />
            </a-form-item>
            <a-form-item label="归属直属代理" v-if="hasPurview(['0', '1', '2' ,'3'])">
              <a-input v-model:value.trim="where.directAgentNo" placeholder="请输入归属直属代理" allow-clear />
            </a-form-item>
            <a-form-item label="支付通道">
              <a-select v-model:value="where.channelCode" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
                  {{ channelName }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="意愿核身状态">
              <a-select v-model:value="where.checkSelfStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">已发起</a-select-option>
                <a-select-option :value="1">成功</a-select-option>
                <a-select-option :value="2">失败</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="开始日期">
              <a-date-picker v-model:value="where.searchBeginTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item label="结束日期">
              <a-date-picker v-model:value="where.searchEndTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'checkSelfStatus'">
              <a-tag v-if="record.checkSelfStatus === 0" color="blue">已发起</a-tag>
              <a-tag v-else-if="record.checkSelfStatus === 1" color="green">成功</a-tag>
              <a-tag v-else-if="record.checkSelfStatus === 2" color="red">失败</a-tag>
            </template>
            <template v-else-if="column.key === 'payStatus'">
              <a-tag v-if="record.payStatus === 0">初始化订单</a-tag>
              <a-tag v-else-if="record.payStatus === 1" color="orange">未支付</a-tag>
              <a-tag v-else-if="record.payStatus === 2" color="green">已支付</a-tag>
              <a-tag v-else-if="record.payStatus === 3" color="red">支付失败</a-tag>
            </template>
            <template v-if="column.key === 'validStatus'">
              <a-tag v-if="record.validStatus === 0" color="pink">无效</a-tag>
              <a-tag v-else-if="record.validStatus === 1" color="cyan">有效</a-tag>
            </template>
            <template v-if="column.key === 'isNeedIdentityVert'">
              <a-tag v-if="record.isNeedIdentityVert === 0">否</a-tag>
              <a-tag v-else-if="record.isNeedIdentityVert === 1" color="cyan">是</a-tag>
            </template>
            <template v-if="column.key === 'clientType'">
              <a-tag v-if="record.clientType === 2" color="blue">浏览器</a-tag>
              <a-tag v-else-if="record.clientType === 1" color="cyan">APP</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <ChannelCheckMerchSelfDetail v-model:visible="showDetail" :detail="current" :channelCodes="channelCodes" />
  </div>
</template>

<script>
import ChannelCheckMerchSelfDetail from './channel-check-merch-self-detail.vue';
import { ChannelCheckMerchSelfApi } from '@/api/aisleManage/ChannelCheckMerchSelfApi';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import { hasPurview } from '@/utils/permission';

export default {
  name: 'ChannelCheckMerchSelf',
  components: { ChannelCheckMerchSelfDetail },
  data() {
    return {
      channelCodes: [],
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '支付通道',
          dataIndex: 'channelCode',
          align: 'center',
          customRender: ({ text }) => {
            const item = this.channelCodes.find(c => c.channelCode === text);
            return item?.channelName || '--';
          }
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '通道商户编号',
          dataIndex: 'chnMerchNo',
          align: 'center'
        },
        {
          title: '直属代理商编号',
          dataIndex: 'agentNo',
          align: 'center'
        },
        {
          title: '权益订单号',
          dataIndex: 'payOrderNo',
          align: 'center'
        },
        {
          title: '是否需要意愿核身',
          dataIndex: 'isNeedIdentityVert',
          key: 'isNeedIdentityVert',
          align: 'center'
        },
        {
          title: '意愿核身订单号',
          dataIndex: 'checkOrderNo',
          align: 'center'
        },
        {
          title: '意愿核身状态',
          dataIndex: 'checkSelfStatus',
          key: 'checkSelfStatus',
          align: 'center'
        },
        // {
        //   title: '意愿核身h5链接地址',
        //   dataIndex: 'checkSelfUrl',
        //   width: 200
        // },
        {
          title: '意愿核身通道返回信息',
          dataIndex: 'chnRespDesc',
          width: 200
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          align:'center'
        },
        {
          title: '意愿核身完成时间',
          dataIndex: 'checkFinishTime',
          align: 'center'
        },
        {
          title: '应收金额',
          dataIndex: 'originPayAmt',
          align: 'center'
        },
        {
          title: '实收金额',
          dataIndex: 'actualPayAmt',
          align: 'center'
        },
        {
          title: '押金活动金额',
          dataIndex: 'serviceAmount',
          align: 'center'
        },
        {
          title: '支付状态',
          dataIndex: 'payStatus',
          key: 'payStatus',
          align: 'center'
        },
        {
          title: '支付时间',
          dataIndex: 'payTime',
          align: 'center'
        },
        {
          title: '终端编号',
          dataIndex: 'terminalNo',
          align: 'center'
        },
        {
          title: '终端SN',
          dataIndex: 'terminalSn',
          align: 'center'
        },
        {
          title: '有效开始时间',
          dataIndex: 'validBeginTime',
          align: 'center'
        },
        {
          title: '有效结束时间',
          dataIndex: 'validEndTime',
          align: 'center'
        },
        {
          title: '有效状态',
          dataIndex: 'validStatus',
          key: 'validStatus',
          align: 'center'
        },
        {
          title: '客户端类型',
          dataIndex: 'clientType',
          key: 'clientType',
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right',
          width: 100
        }
      ],
      // 表格搜索条件
      where: {},
      showDetail: false,
      current: null
    };
  },
  async mounted() {
    const data = await ChannelManageApi.list({ validStatus: 1 });
    this.channelCodes = data || [];
  },
  methods: {
    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    datasource({ page, limit, where, orders }) {
      return ChannelCheckMerchSelfApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    },
    hasPurview
  }
};
</script>
