<template>
  <div class="ele-body">
    <!-- 搜索框内容 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="通道商户编号">
              <a-input v-model:value.trim="where.chnMerchNo" placeholder="请输入通道商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="通道商户简称">
              <a-input v-model:value.trim="where.chnMerchName" placeholder="请输入通道商户简称" allow-clear />
            </a-form-item>
            <a-form-item label="通道商户名称">
              <a-input v-model:value.trim="where.chnMerchFullName" placeholder="请输入通道商户名称" allow-clear />
            </a-form-item>
            <a-form-item label="银联交易商户号">
              <a-input v-model:value.trim="where.unionTransChnMerchNo" placeholder="请输入银联交易商户号" allow-clear />
            </a-form-item>
            <a-form-item label="固定商户号">
              <a-input v-model:value.trim="where.fixedChnMerchNo" placeholder="请输入固定商户号" allow-clear />
            </a-form-item>
            <a-form-item label="虚拟终端SN">
              <a-input v-model:value.trim="where.virtualTermSn" placeholder="请输入虚拟终端SN" allow-clear />
            </a-form-item>
            <a-form-item label="支付通道">
              <a-select v-model:value="where.channelCode" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="item.channelCode" v-for="item in channelCodes" :key="item.id">{{
                  item.channelName
                }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="商户类型">
              <a-select v-model:value="where.isMicro" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">企业商户</a-select-option>
                <a-select-option :value="1">个人商户</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="通道商户类型">
              <a-select v-model:value="where.chnMerchType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">小微商户</a-select-option>
                <a-select-option :value="2">固定商户</a-select-option>
                <a-select-option :value="3">标准商户</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="结算类型">
              <a-select v-model:value="where.accountType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option value="G">对公</a-select-option>
                <a-select-option value="S">对私</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="是否法人结算">
              <a-select v-model:value="where.isLegalSettle" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">是</a-select-option>
                <a-select-option :value="0">否</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="结算方式">
              <a-select v-model:value="where.settleMethod" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">D0结算</a-select-option>
                <a-select-option :value="2">T1结算</a-select-option>
                <a-select-option :value="3">D1结算</a-select-option>
                <a-select-option :value="4">定时结算</a-select-option>
              </a-select>
            </a-form-item>
            <!-- <a-form-item label="D0结算类型">
              <a-select v-model:value="where.d0SettleType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">秒到</a-select-option>
                <a-select-option :value="1">合并到账</a-select-option>
              </a-select>
            </a-form-item> -->
            <a-form-item label="有效状态">
              <a-select v-model:value="where.validStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">有效</a-select-option>
                <a-select-option :value="0">无效</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="支付宝开通状态">
              <a-select v-model:value="where.alipayOpenStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="-1">限制开通</a-select-option>
                <a-select-option :value="0">未开通</a-select-option>
                <a-select-option :value="1">已开通</a-select-option>
                <a-select-option :value="2">申请中</a-select-option>
                <a-select-option :value="3">开通失败</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="支付宝开通状态(大额)">
              <a-select v-model:value="where.alipayIndustryOpenStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">未开通</a-select-option>
                <a-select-option :value="1">已开通</a-select-option>
                <a-select-option :value="2">申请中</a-select-option>
                <a-select-option :value="3">开通失败</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="微信开通状态">
              <a-select v-model:value="where.wechatOpenStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="-1">限制开通</a-select-option>
                <a-select-option :value="0">未开通</a-select-option>
                <a-select-option :value="1">已开通</a-select-option>
                <a-select-option :value="2">申请中</a-select-option>
                <a-select-option :value="3">开通失败</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="云闪付开通状态">
              <a-select v-model:value="where.unionpayOpenStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="-1">限制开通</a-select-option>
                <a-select-option :value="0">未开通</a-select-option>
                <a-select-option :value="1">已开通</a-select-option>
                <a-select-option :value="2">申请中</a-select-option>
                <a-select-option :value="3">开通失败</a-select-option>
                <a-select-option :value="4">待选择结算卡</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="EPOS开通状态">
              <a-select v-model:value="where.eposOpenStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="-1">限制开通</a-select-option>
                <a-select-option :value="0">未开通</a-select-option>
                <a-select-option :value="1">已开通</a-select-option>
                <a-select-option :value="2">申请中</a-select-option>
                <a-select-option :value="3">开通失败</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="支付宝认证状态">
              <a-select v-model:value="where.alipayAuthStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option value="1">已认证</a-select-option>
                <a-select-option value="0">未认证</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="微信认证状态">
              <a-select v-model:value="where.wechatAuthStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option value="1">已认证</a-select-option>
                <a-select-option value="0">未认证</a-select-option>
              </a-select>
            </a-form-item>
            <!-- <a-form-item label="银联开通状态">
              <a-select v-model:value="where.unionOpenStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="-1">限制开通</a-select-option>
                <a-select-option :value="0">未开通</a-select-option>
                <a-select-option :value="1">已开通</a-select-option>
                <a-select-option :value="2">申请中</a-select-option>
                <a-select-option :value="3">开通失败</a-select-option>
              </a-select>
            </a-form-item> -->
            <!-- <a-form-item label="网联开通状态">
              <a-select v-model:value="where.netOpenStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="-1">限制开通</a-select-option>
                <a-select-option :value="0">未开通</a-select-option>
                <a-select-option :value="1">已开通</a-select-option>
                <a-select-option :value="2">申请中</a-select-option>
                <a-select-option :value="3">开通失败</a-select-option>
              </a-select>
            </a-form-item> -->

            <a-form-item label="银联交易结算周期">
              <a-select v-model:value="where.unionpaySettlePeriod" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">D0结算</a-select-option>
                <a-select-option :value="2">T1结算</a-select-option>
                <a-select-option :value="3">D1结算</a-select-option>
                <a-select-option :value="4">定时结算</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格内容 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'settleMethod'">
              <a-badge v-if="record.settleMethod === 1" color="pink" text="D0结算" />
              <a-badge v-else-if="record.settleMethod === 2" color="blue" text="T1结算" />
              <a-badge v-else-if="record.settleMethod === 3" color="cyan" text="D1结算" />
              <a-badge v-else-if="record.settleMethod === 4" color="purple" text="定时结算" />
            </template>

            <template v-else-if="column.key === 'unionpaySettlePeriod'">
              <a-badge v-if="record.unionpaySettlePeriod === 1" color="pink" text="D0结算" />
              <a-badge v-else-if="record.unionpaySettlePeriod === 2" color="blue" text="T1结算" />
              <a-badge v-else-if="record.unionpaySettlePeriod === 3" color="cyan" text="D1结算" />
              <a-badge v-else-if="record.unionpaySettlePeriod === 4" color="purple" text="定时结算" />
            </template>

            <template v-if="column.key === 'validStatus'">
              <a-tag v-if="record.validStatus === 1" color="success">有效</a-tag>
              <a-tag v-else-if="record.validStatus === 0" color="error">无效</a-tag>
            </template>

            <template v-if="column.key === 'isMicro'">
              <a-tag v-if="record.isMicro === 0" color="cyan">企业商户</a-tag>
              <a-tag v-else-if="record.isMicro === 1" color="blue">个人商户</a-tag>
            </template>

            <template v-if="column.key === 'chnMerchType'">
              <a-tag v-if="record.chnMerchType === 1" color="cyan">小微商户</a-tag>
              <a-tag v-else-if="record.chnMerchType === 2" color="blue">固定商户</a-tag>
              <a-tag v-else-if="record.chnMerchType === 3" color="purple">标准商户</a-tag>
            </template>

            <template v-if="column.key === 'accountType'">
              <a-tag v-if="record.accountType === 'G'" color="cyan">对公</a-tag>
              <a-tag v-else-if="record.accountType === 'S'" color="blue">对私</a-tag>
            </template>

            <template v-if="column.key === 'isLegalSettle'">
              <a-tag color="success" v-if="record.isLegalSettle === 1">是</a-tag>
              <a-tag v-else>否</a-tag>
            </template>

            <template v-if="column.key === 'alipayOpenStatus'">
              <a-tag v-if="record.alipayOpenStatus === -1" color="warning">限制开通</a-tag>
              <a-tag v-else-if="record.alipayOpenStatus === 0">未开通</a-tag>
              <a-tag v-else-if="record.alipayOpenStatus === 1" color="success">已开通</a-tag>
              <a-tag v-else-if="record.alipayOpenStatus === 2" color="processing">申请中</a-tag>
              <a-tag v-else-if="record.alipayOpenStatus === 3" color="error">开通失败</a-tag>
            </template>

            <template v-if="column.key === 'wechatOpenStatus'">
              <a-tag v-if="record.wechatOpenStatus === -1" color="warning">限制开通</a-tag>
              <a-tag v-else-if="record.wechatOpenStatus === 0">未开通</a-tag>
              <a-tag v-else-if="record.wechatOpenStatus === 1" color="success">已开通</a-tag>
              <a-tag v-else-if="record.wechatOpenStatus === 2" color="processing">申请中</a-tag>
              <a-tag v-else-if="record.wechatOpenStatus === 3" color="error">开通失败</a-tag>
            </template>

            <template v-if="column.key === 'unionpayOpenStatus'">
              <a-tag v-if="record.unionpayOpenStatus === -1" color="warning">限制开通</a-tag>
              <a-tag v-else-if="record.unionpayOpenStatus === 0">未开通</a-tag>
              <a-tag v-else-if="record.unionpayOpenStatus === 1" color="success">已开通</a-tag>
              <a-tag v-else-if="record.unionpayOpenStatus === 2" color="processing">申请中</a-tag>
              <a-tag v-else-if="record.unionpayOpenStatus === 3" color="error">开通失败</a-tag>
              <a-tag v-else-if="record.unionpayOpenStatus === 4" color="processing">待选择结算卡</a-tag>
            </template>

            <template v-if="column.key === 'eposOpenStatus'">
              <a-tag v-if="record.eposOpenStatus === -1" color="warning">限制开通</a-tag>
              <a-tag v-else-if="record.eposOpenStatus === 0">未开通</a-tag>
              <a-tag v-else-if="record.eposOpenStatus === 1" color="success">已开通</a-tag>
              <a-tag v-else-if="record.eposOpenStatus === 2" color="processing">申请中</a-tag>
              <a-tag v-else-if="record.eposOpenStatus === 3" color="error">开通失败</a-tag>
            </template>

            <template v-if="column.key === 'alipayAuthStatus'">
              <a-tag v-if="record.alipayAuthStatus === '1'" color="success">已认证</a-tag>
              <a-tag v-else-if="record.alipayAuthStatus === '0'">未认证</a-tag>
            </template>

            <template v-if="column.key === 'wechatAuthStatus'">
              <a-tag v-if="record.wechatAuthStatus === '1'" color="success">已认证</a-tag>
              <a-tag v-else-if="record.wechatAuthStatus === '0'">未认证</a-tag>
            </template>

            <template v-if="column.key === 'unionOpenStatus'">
              <a-tag v-if="record.unionOpenStatus === -1" color="warning">限制开通</a-tag>
              <a-tag v-else-if="record.unionOpenStatus === 0">未开通</a-tag>
              <a-tag v-else-if="record.unionOpenStatus === 1" color="success">已开通</a-tag>
              <a-tag v-else-if="record.unionOpenStatus === 2" color="processing">申请中</a-tag>
              <a-tag v-else-if="record.unionOpenStatus === 3" color="error">开通失败</a-tag>
            </template>

            <template v-if="column.key === 'netOpenStatus'">
              <a-tag v-if="record.netOpenStatus === -1" color="warning">限制开通</a-tag>
              <a-tag v-else-if="record.netOpenStatus === 0">未开通</a-tag>
              <a-tag v-else-if="record.netOpenStatus === 1" color="success">已开通</a-tag>
              <a-tag v-else-if="record.netOpenStatus === 2" color="processing">申请中</a-tag>
              <a-tag v-else-if="record.netOpenStatus === 3" color="error">开通失败</a-tag>
            </template>

            <template v-if="column.key === 'alipayIndustryOpenStatus'">
              <a-tag v-if="record.alipayIndustryOpenStatus === 0">未开通</a-tag>
              <a-tag v-else-if="record.alipayIndustryOpenStatus === 1" color="success">已开通</a-tag>
              <a-tag v-else-if="record.alipayIndustryOpenStatus === 2" color="processing">申请中</a-tag>
              <a-tag v-else-if="record.alipayIndustryOpenStatus === 3" color="error">开通失败</a-tag>
            </template>

            <template v-if="column.key === 'completeFirstTrans'">
              <a-tag v-if="record.completeFirstTrans === 0">未完成</a-tag>
              <a-tag v-else-if="record.completeFirstTrans === 1" color="success">已完成</a-tag>
            </template>

            <template v-if="column.key === 'd0SettleType'">
              <a-tag v-if="record.d0SettleType === 0" color="blue">秒到</a-tag>
              <a-tag v-else-if="record.d0SettleType === 1" color="purple">合并到账</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <!-- <a @click="payMethodOpenConfig(record)">支付方式配置</a> -->
                <a @click="handleDetail(record)">详情</a>
                <a-divider type="vertical" />
                <a @click="queryBankcardDetail(record)">结算账户详情</a>
                <a-divider type="vertical" />
                <a @click="transCumulateDetail(record)">交易累计详情</a>
                <a-divider type="vertical" />
                <a-dropdown>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item v-if="record.isMicro === 1 && record.validStatus === 1" @click="updateAddress(record)">
                        修改地址
                      </a-menu-item>
                      <a-menu-item v-if="record.channelCode === '1001' && record.settleMethod === 4" @click="changeSettleTime(record)">
                        修改定时结算时间
                      </a-menu-item>
                      <a-menu-item v-if="hasPurview('0')" @click="changeOpenStatus(record)">修改开通状态</a-menu-item>
                      <a-menu-item @click="handleSupplyImages(record)">补充图片</a-menu-item>
                    </a-menu>
                  </template>
                  <a class="ele-text-danger">操作 <DownOutlined /></a>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <ChannelMerchManageDetail v-if="showDetail" v-model:visible="showDetail" :detail="current" :channelCodes="channelCodes" />

    <PayMethodConfig v-if="showEdit" v-model:visible="showEdit" :data="current" @done="reload" />

    <ChangeSettletime v-if="showEditSettleTime" v-model:visible="showEditSettleTime" :data="current" @done="reload" />

    <BankcardDetail v-if="showBankcardDetail" v-model:visible="showBankcardDetail" :data="current" />

    <!-- 交易累计详情 -->
    <TransCumulateDetail
      v-if="showTransCumulateDetail"
      v-model:visible="showTransCumulateDetail"
      :detail="current"
      :channelCodes="channelCodes"
    />

    <CloseFuYouChannel v-if="showCloseFuYouChannel" v-model:visible="showCloseFuYouChannel" :data="current" @done="reload" />

    <UpdateAddress
      v-if="showUpdateAddress"
      v-model:visible="showUpdateAddress"
      :data="current"
      @done="reload"
      :channelCodes="channelCodes"
    />

    <ChangeOpenStatus v-if="showChangeOpenStatus" v-model:visible="showChangeOpenStatus" :data="current" @done="reload" />

    <SupplyImages v-if="showSupplyImages" v-model:visible="showSupplyImages" :data="current" @done="reload" />
  </div>
</template>

<script>
import { ChannelMerchManageApi } from '@/api/aisleManage/ChannelMerchManageApi';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import PayMethodConfig from './pay-method-config.vue';
import ChangeSettletime from './change-settletime.vue';
import BankcardDetail from './bankcard-detail.vue';
import CloseFuYouChannel from './close-fuyou-channel.vue';
import { hasPurview } from '@/utils/permission';
import TransCumulateDetail from './trans-cumulate-detail.vue';
import ChannelMerchManageDetail from './channel-merch-manage-detail.vue';
import UpdateAddress from './update-address.vue';
import ChangeOpenStatus from './change-open-status.vue';
import SupplyImages from './supply-images.vue';

export default {
  name: 'ChannelMerchManage',
  components: {
    TransCumulateDetail,
    PayMethodConfig,
    BankcardDetail,
    ChangeSettletime,
    CloseFuYouChannel,
    ChannelMerchManageDetail,
    UpdateAddress,
    ChangeOpenStatus,
    SupplyImages
  },
  data() {
    return {
      //表格查询条件
      where: {},
      //展示编辑页面传的参数
      current: null,
      //是否显示详情
      showDetail: false,
      showTransCumulateDetail: false,
      // 是否显示支付方式配置页面
      showEdit: false,
      showBankcardDetail: false,
      showEditSettleTime: false,
      showCloseFuYouChannel: false, //是否展示关闭富有商户页面
      showChangeOpenStatus: false,
      showSupplyImages: false,
      hasPurview,
      //表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center'
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '通道商户名称',
          dataIndex: 'chnMerchFullName',
          align: 'center'
        },
        {
          title: '商户类型',
          dataIndex: 'isMicro',
          key: 'isMicro',
          align: 'center'
        },
        {
          title: '通道商户类型',
          dataIndex: 'chnMerchType',
          key: 'chnMerchType',
          align: 'center'
        },
        {
          title: '支付通道',
          dataIndex: 'channelCode',
          align: 'center',
          customRender: ({ text }) => {
            const item = this.channelCodes.find(c => c.channelCode === text);
            return item?.channelName || '--';
          }
        },
        {
          title: '渠道商户号',
          dataIndex: 'chnMerchNo',
          align: 'center'
        },
        {
          title: '渠道商户简称',
          dataIndex: 'chnMerchName',
          align: 'center'
        },
        {
          title: '银联交易商户号',
          dataIndex: 'unionTransChnMerchNo',
          align: 'center'
        },
        {
          title: '固定商户号',
          dataIndex: 'fixedChnMerchNo',
          align: 'center'
        },
        {
          title: '虚拟终端SN',
          dataIndex: 'virtualTermSn',
          align: 'center'
        },
        {
          title: '银联开通状态',
          dataIndex: 'unionpayOpenStatus',
          key: 'unionpayOpenStatus',
          align: 'center'
        },
        {
          title: '银联开通说明',
          dataIndex: 'unionMessage',
          width: 180
        },
        {
          title: '支付宝开通状态',
          dataIndex: 'alipayOpenStatus',
          key: 'alipayOpenStatus',
          align: 'center'
        },
        {
          title: '支付宝开通说明',
          dataIndex: 'alipayMessage',
          width: 180
        },
        {
          title: '支付宝开通状态(大额)',
          dataIndex: 'alipayIndustryOpenStatus',
          key: 'alipayIndustryOpenStatus',
          align: 'center'
        },
        {
          title: '支付宝开通说明(大额)',
          dataIndex: 'alipayIndustryOpenReason',
          width: 180
        },
        {
          title: '微信开通状态',
          dataIndex: 'wechatOpenStatus',
          key: 'wechatOpenStatus',
          align: 'center'
        },
        {
          title: '微信开通说明',
          dataIndex: 'wechatMessage',
          width: 180
        },
        {
          title: '是否完成首笔交易',
          dataIndex: 'completeFirstTrans',
          key: 'completeFirstTrans',
          align: 'center'
        },
        {
          title: '微信子商户号',
          dataIndex: 'wechatMerchId',
          align: 'center'
        },
        {
          title: '支付宝子商户号',
          dataIndex: 'alipayMerchId',
          align: 'center'
        },
        {
          title: '结算类型',
          dataIndex: 'accountType',
          key: 'accountType',
          align: 'center'
        },
        {
          title: '是否法人结算',
          dataIndex: 'isLegalSettle',
          key: 'isLegalSettle',
          align: 'center'
        },
        {
          title: '结算方式',
          dataIndex: 'settleMethod',
          key: 'settleMethod',
          align: 'center'
        },
        {
          title: '定时结算时间',
          dataIndex: 'settleTime',
          align: 'center'
        },
        {
          title: '银联交易结算周期',
          dataIndex: 'unionpaySettlePeriod',
          key: 'unionpaySettlePeriod',
          align: 'center'
        },
        {
          title: 'D0结算类型',
          dataIndex: 'd0SettleType',
          key: 'd0SettleType',
          align: 'center'
        },
        {
          title: '有效状态',
          dataIndex: 'validStatus',
          key: 'validStatus',
          align: 'center'
        },

        // {
        //   title: 'EPOS开通状态',
        //   dataIndex: 'eposOpenStatus',
        //   key: 'eposOpenStatus',
        //   align: 'center'
        // },
        {
          title: '支付宝认证状态',
          dataIndex: 'alipayAuthStatus',
          key: 'alipayAuthStatus',
          align: 'center'
        },
        {
          title: '微认证状态',
          dataIndex: 'wechatAuthStatus',
          key: 'wechatAuthStatus',
          align: 'center'
        },
        // {
        //   title: '银联开通状态',
        //   dataIndex: 'unionOpenStatus',
        //   key: 'unionOpenStatus',
        //   align: 'center'
        // },
        // {
        //   title: '银联开通原因',
        //   dataIndex: 'unionOpenReason',
        //   key: 'unionOpenReason',
        //   align: 'center'
        // },
        // {
        //   title: '网联开通状态',
        //   dataIndex: 'netOpenStatus',
        //   key: 'netOpenStatus',
        //   align: 'center'
        // },
        // {
        //   title: '网联开通原因',
        //   dataIndex: 'netOpenReason',
        //   key: 'netOpenReason',
        //   align: 'center'
        // },
        {
          title: '聚合钱包UUID',
          dataIndex: 'unionWalletUuid',
          align: 'center'
        },
        // {
        //   title: '二维码钱包UUID',
        //   dataIndex: 'qrcodeWalletUuid',
        //   align: 'center'
        // },
        // {
        //   title: 'POS钱包UUID',
        //   dataIndex: 'posWalletUuid',
        //   align: 'center'
        // },
        // {
        //   title: 'EPOS钱包UUID',
        //   dataIndex: 'eposWalletUuid',
        //   align: 'center'
        // },
        {
          title: '报备来源',
          dataIndex: 'reportSource',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          align: 'center',
          width: 330
        }
      ],
      channelCodes: [],
      showUpdateAddress: false
    };
  },
  mounted() {
    this.getChannelCodes();
  },
  methods: {
    async handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    //查询方法
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    updateAddress(record) {
      this.current = record;
      this.showUpdateAddress = true;
    },

    handleSupplyImages(record) {
      this.current = record;
      this.showSupplyImages = true;
    },

    //重置
    reset() {
      this.where = {}; //清空查询条件
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    payMethodOpenConfig(row) {
      this.current = row;
      this.showEdit = true;
    },

    async getChannelCodes() {
      const data = await ChannelManageApi.list({ validStatus: 1 });
      this.channelCodes = data || [];
    },

    changeSettleTime(row) {
      this.current = { id: row.id, settleTime: row.settleTime };
      this.showEditSettleTime = true;
    },

    //交易累计详情
    transCumulateDetail(row) {
      this.current = row;
      this.showTransCumulateDetail = true;
    },

    //关闭富有商户
    closeChlMerch(row) {
      this.current = row;
      this.showCloseFuYouChannel = true;
    },

    changeOpenStatus(row) {
      this.current = row;
      this.showChangeOpenStatus = true;
    },

    async queryBankcardDetail({ bankCardId }) {
      const data = (await ChannelMerchManageApi.settleBankCardDetail({ bankCardId })) || {};
      this.current = data.merchantBankCard ? [data.merchantBankCard] : [];
      this.showBankcardDetail = true;
    },

    //获取数据方法
    datasource({ page, limit, where }) {
      return ChannelMerchManageApi.getChannelMerchManagePages({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>
