<template>
  <a-modal
    :width="880" :visible="visible" title="交易累计详情" :body-style="{ paddingBottom: '20px' }" :mask-closable="false"
    @update:visible="updateVisible">
    <a-descriptions :column="2">
      <a-descriptions-item label="通道商户ID">{{ form.chnMerchId }}</a-descriptions-item>
      <a-descriptions-item label="通道商户编号">{{ form.chnMerchNo }}</a-descriptions-item>
      <a-descriptions-item label="EPOS日累计金额">{{ form.eposDayCumulate }}</a-descriptions-item>
      <a-descriptions-item label="微信日累计金额">{{ form.wechatDayCumulate }}</a-descriptions-item>
      <a-descriptions-item label="支付宝日累计金额">{{ form.alipayDayCumulate }}</a-descriptions-item>
      <a-descriptions-item label="银联日累计金额">{{ form.unionpayDayCumulate }}</a-descriptions-item>

      <a-descriptions-item label="POS手机Pay贷记卡日累计金额">{{ form.posNfcCreditDayCumulate }}</a-descriptions-item>
      <a-descriptions-item label="POS手机Pay借记卡日累计金额">{{ form.posNfcDebitDayCumulate }}</a-descriptions-item>
      <a-descriptions-item label="POS芯片贷记卡日累计金额">{{ form.posIcCreditDayCumulate }}</a-descriptions-item>
      <a-descriptions-item label="POS芯片借记卡日累计金额">{{ form.posIcDebitDayCumulate }}</a-descriptions-item>
      <a-descriptions-item label="POS磁条贷记卡日累计金额">{{ form.posCiCreditDayCumulate }}</a-descriptions-item>
      <a-descriptions-item label="POS磁条借记卡日累计金额">{{ form.posCiDebitDayCumulate }}</a-descriptions-item>

      <a-descriptions-item label="通道名称">
        <template v-for="({ channelCode, channelName }, key) in channelCodes" :key="key">
          <a-badge v-if="form.channelCode === channelCode" color="purple" :text="channelName" />
        </template>
      </a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';
import { ChannelMerchManageApi } from '@/api/aisleManage/ChannelMerchManageApi';

export default {
  name: 'TransCumulateDetail',
  props: {
    visible: Boolean,
    detail: Object,
    channelCodes: Array
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(async () => {
      if (props.detail) {
        data.form = await ChannelMerchManageApi.transCumulateDetail({ chnMerchId: props.detail.id });
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
