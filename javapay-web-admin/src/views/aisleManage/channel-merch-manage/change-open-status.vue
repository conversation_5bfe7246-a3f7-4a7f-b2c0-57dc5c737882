<template>
  <a-modal
    :width="500"
    :visible="visible"
    :confirm-loading="loading"
    title="修改开通状态"
    :body-style="{ paddingBottom: '8px' }"
    :mask-closable="false"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules" layout="vertical">
      <a-form-item label="支付宝开通状态 " name="alipayOpenStatus">
        <a-select v-model:value="form.alipayOpenStatus" style="width: 100%" placeholder="请选择">
          <a-select-option :value="-1">限制开通</a-select-option>
          <a-select-option :value="0">未开通</a-select-option>
          <a-select-option :value="1">已开通</a-select-option>
          <a-select-option :value="2">申请中</a-select-option>
          <a-select-option :value="3">开通失败</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="微信开通状态 " name="wechatOpenStatus">
        <a-select v-model:value="form.wechatOpenStatus" style="width: 100%" placeholder="请选择">
          <a-select-option :value="-1">限制开通</a-select-option>
          <a-select-option :value="0">未开通</a-select-option>
          <a-select-option :value="1">已开通</a-select-option>
          <a-select-option :value="2">申请中</a-select-option>
          <a-select-option :value="3">开通失败</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="云闪付开通状态 " name="unionpayOpenStatus">
        <a-select v-model:value="form.unionpayOpenStatus" style="width: 100%" placeholder="请选择">
          <a-select-option :value="-1">限制开通</a-select-option>
          <a-select-option :value="0">未开通</a-select-option>
          <a-select-option :value="1">已开通</a-select-option>
          <a-select-option :value="2">申请中</a-select-option>
          <a-select-option :value="3">开通失败</a-select-option>
          <a-select-option :value="4">待选择结算卡</a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { ChannelMerchManageApi } from '@/api/aisleManage/ChannelMerchManageApi';

export default {
  props: {
    visible: Boolean,
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {},
      // 表单验证规则
      rules: {
        alipayOpenStatus: [{ required: true, message: '请选择' }],
        wechatOpenStatus: [{ required: true, message: '请选择' }],
        unionpayOpenStatus: [{ required: true, message: '请选择' }]
      },
      // 提交状态
      loading: false
    };
  },
  mounted() {
    this.form = Object.assign({}, this.data);
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      ChannelMerchManageApi.editOpenStatus({
        id: this.form.id,
        alipayOpenStatus: this.form.alipayOpenStatus,
        wechatOpenStatus: this.form.wechatOpenStatus,
        unionpayOpenStatus: this.form.unionpayOpenStatus
      })
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
