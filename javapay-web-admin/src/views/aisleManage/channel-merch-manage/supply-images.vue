<template>
  <a-modal
    :width="800"
    :visible="visible"
    :confirm-loading="loading"
    :title="`商户图片补充 (${form.chnMerchFullName})`"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" layout="vertical">
      <!-- 文件信息 -->
      <a-row :gutter="[16, 16]">
        <template v-for="(item, key) in fileList" :key="key">
          <a-col :span="6">
            <a-form-item :label="`${item.label}`">
              <a-upload
                v-model:file-list="item.fileData"
                accept=".png, .jpg, .jpeg"
                :max-count="1"
                list-type="picture-card"
                :before-upload="file => handleSelectFile(file, item)"
                @preview="handlePreviewFile"
              >
                <div v-if="!item.fileData?.length">
                  <plus-outlined />
                  <div style="margin-top: 8px">Upload</div>
                </div>
              </a-upload>
            </a-form-item>
          </a-col>
        </template>
      </a-row>
      <!-- 预览图片 -->
      <a-image
        :style="{ display: 'none' }"
        :src="previewImage"
        :preview="{ visible: previewVisible, onVisibleChange: setPreviewVisible }"
      />
    </a-form>
  </a-modal>
</template>

<script>
import { Upload, message } from 'ant-design-vue';
import { compressorImageSpecifySize } from '@/utils/image-compressor-util';
import { RiskyTransNoticeApi } from '@/api/transactionManage/RiskyTransNoticeApi';
import { ChannelMerchManageApi } from '@/api/aisleManage/ChannelMerchManageApi';
import { FIXED_CHANNEL_CODE } from '@/config/setting';

export default {
  props: {
    visible: Boolean,
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      form: {},
      // 预览图片
      previewImage: '',
      previewVisible: false,
      // 提交loading
      loading: false,
      // 文件列表
      fileList: [
        {
          label: '身份证正面',
          fileType: 1,
          fileData: []
        },
        {
          label: '身份证背面',
          fileType: 2,
          fileData: []
        },
        {
          label: '经营场所照片',
          fileType: 12,
          fileData: []
        },
        {
          label: '营业执照',
          fileType: 7,
          fileData: []
        },
        {
          label: '开户许可证',
          fileType: 16,
          fileData: []
        },
        {
          label: '门头照',
          fileType: 11,
          fileData: []
        },
        {
          label: '银行卡正面照片',
          fileType: 3,
          fileData: []
        },
        {
          label: '手持身份证照片',
          fileType: 6,
          fileData: []
        },
        {
          label: '收银台照片',
          fileType: 13,
          fileData: []
        }
      ]
    };
  },
  mounted() {
    this.form = Object.assign({}, this.data);
  },
  methods: {
    /**
     * 提交表单
     */
    async save() {
      // 校验文件
      await this.validateFileList();

      // 修改加载框为正在加载
      this.loading = true;

      // 上传图片
      await this.uploadImages().catch(() => {
        this.loading = false;
        return Promise.reject('Error: 图片上传失败');
      });

      const params = {
        chnMerchNo: this.form.chnMerchNo,
        imageJsonList: this.form.imageJsonList
      };
      ChannelMerchManageApi.supplyFileUpload(params)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    /**
     * 上传图片
     */
    async uploadImages() {
      const fileListHasVal = this.fileList.filter(i => i.fileData?.length);
      const noChangeFiles = [];
      const changeedFiles = [];
      fileListHasVal.forEach(i => {
        i.fileData.forEach(j => {
          if (/^(https?:)/.test(j.url)) {
            noChangeFiles.push({
              id: j.id,
              imageType: i.fileType,
              imagePath: j.url
            });
          } else {
            changeedFiles.push({
              fileType: i.fileType,
              suffixType: j.suffixType,
              fileData: j.url
            });
          }
        });
      });

      let imageJsonList = [];
      if (changeedFiles.length) {
        const data = await RiskyTransNoticeApi.uploadImages({
          channelCode: FIXED_CHANNEL_CODE,
          fileDTOList: changeedFiles,
          merchantNo: this.form.merchantNo
        });
        imageJsonList = data.imageJsonList;
      }

      this.form.imageJsonList = [...noChangeFiles, ...imageJsonList];
    },

    /**
     * 选中文件
     * @param {*} file
     * @param {*} item 当前图片项
     */
    handleSelectFile(file, item) {
      compressorImageSpecifySize(file).then(({ url }) => {
        item.fileData = [{ url, suffixType: 'png' }];
      });
      return Upload.LIST_IGNORE;
    },

    /**
     * 校验图片是否上传
     */
    validateFileList() {
      return new Promise((resolve, reject) => {
        if (this.fileList.some(f => !!f.fileData?.length)) {
          resolve();
        } else {
          message.warn('没有图片需要上传');
          reject();
        }
      });
    },

    handlePreviewFile(file) {
      this.previewImage = file?.url;
      this.setPreviewVisible(true);
    },

    setPreviewVisible(visible) {
      this.previewVisible = visible;
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
