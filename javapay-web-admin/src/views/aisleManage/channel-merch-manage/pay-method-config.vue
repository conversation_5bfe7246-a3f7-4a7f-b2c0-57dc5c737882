<template>
  <a-modal
    :width="1000"
    :visible="visible"
    :confirm-loading="loading"
    :title="'支付方式配置'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 7 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 17 }, sm: { span: 24 } }"
    >
      <a-row :gutter="16">
        <!-- 支付宝支付 -->
        <a-divider orientation="left" :orientationMargin="40" dashed>支付宝支付</a-divider>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="支付宝主扫" name="aliScanMerchSwitch">
            <a-radio-group name="aliScanMerchSwitch" v-model:value="form.aliScanMerchSwitch">
              <a-radio :value="1">开启</a-radio>
              <a-radio :value="0">关闭</a-radio>
            </a-radio-group>
          </a-form-item>

          <a-form-item label="支付宝生活号" name="aliLifeSwitch">
            <a-radio-group name="aliLifeSwitch" v-model:value="form.aliLifeSwitch">
              <a-radio :value="1">开启</a-radio>
              <a-radio :value="0">关闭</a-radio>
            </a-radio-group>
          </a-form-item>

          <a-form-item label="支付宝第三方应用" name="aliThirdSwitch">
            <a-radio-group name="aliThirdSwitch " v-model:value="form.aliThirdSwitch">
              <a-radio :value="1">开启</a-radio>
              <a-radio :value="0">关闭</a-radio>
            </a-radio-group>
          </a-form-item>

          
        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="支付宝被扫" name="aliScanPayerSwitch">
            <a-radio-group name="aliScanPayerSwitch" v-model:value="form.aliScanPayerSwitch">
              <a-radio :value="1">开启</a-radio>
              <a-radio :value="0">关闭</a-radio>
            </a-radio-group>
          </a-form-item>

          <a-form-item label="支付宝小程序" name="aliAppletSwitch">
            <a-radio-group name="aliAppletSwitch" v-model:value="form.aliAppletSwitch">
              <a-radio :value="1">开启</a-radio>
              <a-radio :value="0">关闭</a-radio>
            </a-radio-group>
          </a-form-item>

          <a-form-item label="支付宝APP" name="aliAppSwitch">
            <a-radio-group name="aliAppSwitch " v-model:value="form.aliAppSwitch">
              <a-radio :value="1">开启</a-radio>
              <a-radio :value="0">关闭</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>

        <!-- 微信支付 -->
        <a-divider orientation="left" :orientationMargin="40" dashed>微信支付</a-divider>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="微信主扫" name="wxScanMerchSwitch">
            <a-radio-group name="wxScanMerchSwitch" v-model:value="form.wxScanMerchSwitch">
              <a-radio :value="1">开启</a-radio>
              <a-radio :value="0">关闭</a-radio>
            </a-radio-group>
          </a-form-item>

          <a-form-item label="微信公众号" name="wxGzhSwitch">
            <a-radio-group name="wxGzhSwitch" v-model:value="form.wxGzhSwitch">
              <a-radio :value="1">开启</a-radio>
              <a-radio :value="0">关闭</a-radio>
            </a-radio-group>
          </a-form-item>

          <a-form-item label="微信APP" name="wxAppSwitch">
            <a-radio-group name="wxAppSwitch " v-model:value="form.wxAppSwitch">
              <a-radio :value="1">开启</a-radio>
              <a-radio :value="0">关闭</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="微信被扫" name="wxScanPayerSwitch">
            <a-radio-group name="wxScanPayerSwitch" v-model:value="form.wxScanPayerSwitch">
              <a-radio :value="1">开启</a-radio>
              <a-radio :value="0">关闭</a-radio>
            </a-radio-group>
          </a-form-item>

          <a-form-item label="微信小程序" name="wxAppletSwitch">
            <a-radio-group name="wxAppletSwitch" v-model:value="form.wxAppletSwitch">
              <a-radio :value="1">开启</a-radio>
              <a-radio :value="0">关闭</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>

        <!-- 银联扫码支付 -->
        <a-divider orientation="left" :orientationMargin="40" dashed>银联扫码支付</a-divider>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="银联主扫" name="unionScanMerchSwitch">
            <a-radio-group name="unionScanMerchSwitch" v-model:value="form.unionScanMerchSwitch">
              <a-radio :value="1">开启</a-radio>
              <a-radio :value="0">关闭</a-radio>
            </a-radio-group>
          </a-form-item>

          <a-form-item label="银联JS支付" name="unionJsPaySwitch">
            <a-radio-group name="unionJsPaySwitch" v-model:value="form.unionJsPaySwitch">
              <a-radio :value="1">开启</a-radio>
              <a-radio :value="0">关闭</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="银联被扫" name="unionScanPayerSwitch">
            <a-radio-group name="unionScanPayerSwitch " v-model:value="form.unionScanPayerSwitch">
              <a-radio :value="1">开启</a-radio>
              <a-radio :value="0">关闭</a-radio>
            </a-radio-group>
          </a-form-item>

          <a-form-item label="云闪付小程序" name="unionAppletSwitch">
            <a-radio-group name="unionAppletSwitch" v-model:value="form.unionAppletSwitch">
              <a-radio :value="1">开启</a-radio>
              <a-radio :value="0">关闭</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>

        <!-- EPOS支付 -->
        <a-divider orientation="left" :orientationMargin="40" dashed>EPOS支付</a-divider>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="EPOS支付" name="eposPaySwitch">
            <a-radio-group name="eposPaySwitch " v-model:value="form.eposPaySwitch">
              <a-radio :value="1">开启</a-radio>
              <a-radio :value="0">关闭</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script>
import { ChannelMerchManageApi } from '@/api/aisleManage/ChannelMerchManageApi';
import { message } from 'ant-design-vue';
export default {
  name: 'PayMethodConfig',
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {},
      rules: {
        aliScanMerchSwitch: [{ required: true, message: '请选择支付宝主扫' }],
        aliLifeSwitch: [{ required: true, message: '请选择支付宝生活号' }],
        aliAppSwitch: [{ required: true, message: '请选择支付宝APP' }],
        aliScanPayerSwitch: [{ required: true, message: '请选择支付宝被扫' }],
        aliAppletSwitch: [{ required: true, message: '请选择支付宝小程序' }],

        wxScanMerchSwitch: [{ required: true, message: '请选择微信主扫' }],
        wxGzhSwitch: [{ required: true, message: '请选择微信公众号' }],
        wxAppSwitch: [{ required: true, message: '请选择微信APP' }],
        wxScanPayerSwitch: [{ required: true, message: '请选择微信被扫' }],
        wxAppletSwitch: [{ required: true, message: '请选择微信小程序' }],

        unionScanMerchSwitch: [{ required: true, message: '请选择银联主扫' }],
        unionJsPaySwitch: [{ required: true, message: '请选择银联JS支付' }],
        unionScanPayerSwitch: [{ required: true, message: '请选择银联被扫' }],
        unionAppletSwitch: [{ required: true, message: '请选择云闪付小程序' }],

        eposPaySwitch: [{ required: true, message: '请选择EPOS支付' }]
      },
      // 提交状态
      loading: false
    };
  },
  mounted() {
    if (this.data) {
      const transAllowFlagInfo = this.data.transAllowFlagInfo ? JSON.parse(this.data.transAllowFlagInfo) : {};
      this.form = Object.assign({}, transAllowFlagInfo);
    }
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      const params = {
        payMethodSwitchInfoDTO: this.form,
        id: this.data.id
      };

      ChannelMerchManageApi.payMethodSwitchConfig(params)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },
   
    /**
     * 更新编辑界面的弹框是否显示
     *
     * @param value true-显示，false-隐藏
     * <AUTHOR>
     * @date 2022/11/03 17:53
     */
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>

<style></style>
