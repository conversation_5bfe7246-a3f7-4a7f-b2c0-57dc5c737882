<template>
  <a-modal
    :width="680"
    :visible="visible"
    :confirm-loading="loading"
    title="修改地址"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules" :label-col="{ style: { width: '100px' } }">
      <a-row :gutter="24">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="商户编号">
            <a-input v-model:value="form.merchantNo" placeholder="商户编号" disabled />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="通道商户编号">
            <a-input v-model:value="form.chnMerchNo" placeholder="通道商户编号" disabled />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="通道名称">
            <a-select v-model:value="form.channelCode" style="width: 100%" placeholder="请选择" disabled>
              <a-select-option :value="item.channelCode" v-for="item in channelCodes" :key="item.id">{{
                item.channelName
              }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <div style="margin-bottom: 22px">
        <a-divider />
      </div>
      <a-row :gutter="24">
        <a-col :md="24" :sm="24" :xs="24">
          <a-form-item label="选择省市区" name="city">
            <a-cascader
              v-model:value="officeAreaValue"
              :options="officeRegionsData"
              :load-data="options => loadAreaData(options, 3)"
              :allow-clear="false"
              placeholder="请选择"
              @change="selectedOfficeAreaValue"
            />
          </a-form-item>
        </a-col>
        <a-col :md="24" :sm="24" :xs="24">
          <a-form-item label="详细地址" name="addrDetail">
            <a-textarea v-model:value="form.addrDetail" placeholder="请输入详细地址" :auto-size="{ minRows: 1, maxRows: 6 }" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { ChannelMerchManageApi } from '@/api/aisleManage/ChannelMerchManageApi';
import { AreaApi } from '@/api/base/AreaApi';
import { deepCopy } from '@/utils/util';

export default {
  props: {
    visible: Boolean,
    data: Object,
    channelCodes: Array
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {
        merchantNo: '', // 商户编号 必填
        channelCode: '', // 通道编号 必填
        chnMerchNo: '', // 通道商户编号 必填

        province: '', // 省编码 必填
        city: '', // 市编码 必填
        district: '', // 区县编码 必填
        addrDetail: '' // 详情地址 必填
      },
      officeAreaValue: [],
      officeRegionsData: [],
      // 表单验证规则
      rules: {
        city: [{ required: true, message: '请选择省市区' }],
        addrDetail: [{ required: true, message: '请输入详细地址' }]
      },
      // 提交状态
      loading: false
    };
  },
  created() {
    if (this.data) {
      const {
        merchantNo,
        chnMerchNo: chnMerchNo,
        channelCode,
        provinceCode: province,
        cityCode: city,
        countryCode: district,
        licenseAddr: addrDetail
      } = this.data;
      Object.keys(this.form).forEach(key => {
        this.form[key] = eval(key);
      });

      if (city) {
        this.officeAreaValue = [province, city, district];
      }
      this.loadAreaData();
    }
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      ChannelMerchManageApi.uptAddress(this.form)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    async loadAreaData(selectedOptions, totalLevel = 3) {
      const targetOption = selectedOptions ? selectedOptions[selectedOptions.length - 1] : { level: 1, code: '' };
      const { level, code } = targetOption;
      const data = await AreaApi.list({ level: level + 1, status: 1, parentCode: code });
      const filterData = data.map(d => {
        return { label: d.areaName, value: d.areaCode, code: d.areaCode, isLeaf: level > totalLevel - 1, level: d.level };
      });
      if (level === 1) {
        this.officeRegionsData = deepCopy(filterData);
      } else {
        targetOption.children = filterData;
      }

      if (!selectedOptions) {
        const officeItem = this.officeRegionsData.find(r => r.value === this.form.province);
        if (officeItem) {
          await this.loadAreaData([officeItem]);
          const citem = officeItem.children.find(i => i.value === this.form.city);
          citem && this.loadAreaData([citem]);
        }
      }
    },

    selectedOfficeAreaValue(value) {
      [this.form.province, this.form.city, this.form.district] = value || [];
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
