<template>
  <a-modal
    :width="1000"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="商户编号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="通道商户名称">{{ form.chnMerchFullName }}</a-descriptions-item>
      <a-descriptions-item label="渠道商户号">{{ form.chnMerchNo }}</a-descriptions-item>
      <a-descriptions-item label="渠道商户简称">{{ form.chnMerchName }}</a-descriptions-item>
      <a-descriptions-item label="银联交易商户号">{{ form.unionTransChnMerchNo }}</a-descriptions-item>
      <a-descriptions-item label="固定商户号">{{ form.fixedChnMerchNo }}</a-descriptions-item>
      <a-descriptions-item label="虚拟终端SN">{{ form.virtualTermSn }}</a-descriptions-item>
      <a-descriptions-item label="支付通道">
        <template v-for="({ channelCode, channelName }, key) in channelCodes" :key="key">
          <a-badge v-if="form.channelCode === channelCode" color="purple" :text="channelName" />
        </template>
      </a-descriptions-item>
      <a-descriptions-item label="商户类型">
        <a-tag v-if="form.isMicro === 0" color="cyan">企业商户</a-tag>
        <a-tag v-else-if="form.isMicro === 1" color="blue">个人商户</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="通道商户类型">
        <a-tag v-if="form.chnMerchType === 1" color="cyan">小微商户</a-tag>
        <a-tag v-else-if="form.chnMerchType === 2" color="blue">固定商户</a-tag>
        <a-tag v-else-if="form.chnMerchType === 3" color="purple">标准商户</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="企业名称">{{ form.licenseName }}</a-descriptions-item>
      <a-descriptions-item label="企业注册地址" :span="2">{{ form.licenseAddr }}</a-descriptions-item>
      <a-descriptions-item label="法人姓名">{{ form.legalName }}</a-descriptions-item>
      <a-descriptions-item label="法人手机号">{{ form.legalTelMask }}</a-descriptions-item>
      <a-descriptions-item label="营业执照号">{{ form.licenseNo }}</a-descriptions-item>
      <a-descriptions-item label="营业执照有效期">{{
        form.licenseStartDate ? `${form.licenseStartDate}-${form.licenseEndDate}` : '--'
      }}</a-descriptions-item>
      <a-descriptions-item label="结算类型">
        <a-tag v-if="form.accountType === 'G'" color="cyan">对公</a-tag>
        <a-tag v-else-if="form.accountType === 'S'" color="blue">对私</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="是否法人结算">
        <a-tag color="success" v-if="form.isLegalSettle === 1">是</a-tag>
        <a-tag v-else>否</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="结算方式">
        <a-badge v-if="form.settleMethod === 1" color="pink" text="D0结算" />
        <a-badge v-else-if="form.settleMethod === 2" color="blue" text="T1结算" />
        <a-badge v-else-if="form.settleMethod === 3" color="cyan" text="D1结算" />
        <a-badge v-else-if="form.settleMethod === 4" color="purple" text="定时结算" />
      </a-descriptions-item>
      <a-descriptions-item label="定时结算时间">{{ form.settleTime }}</a-descriptions-item>
      <a-descriptions-item label="银联交易结算周期">
        <a-badge v-if="form.unionpaySettlePeriod === 1" color="pink" text="D0结算" />
        <a-badge v-else-if="form.unionpaySettlePeriod === 2" color="blue" text="T1结算" />
        <a-badge v-else-if="form.unionpaySettlePeriod === 3" color="cyan" text="D1结算" />
        <a-badge v-else-if="form.unionpaySettlePeriod === 4" color="purple" text="定时结算" />
      </a-descriptions-item>
      <a-descriptions-item label="银联开通状态">
        <a-tag v-if="form.unionpayOpenStatus === -1" color="warning">限制开通</a-tag>
        <a-tag v-else-if="form.unionpayOpenStatus === 0">未开通</a-tag>
        <a-tag v-else-if="form.unionpayOpenStatus === 1" color="success">已开通</a-tag>
        <a-tag v-else-if="form.unionpayOpenStatus === 2" color="processing">申请中</a-tag>
        <a-tag v-else-if="form.unionpayOpenStatus === 3" color="error">开通失败</a-tag>
        <a-tag v-else-if="form.unionpayOpenStatus === 4" color="processing">待选择结算卡</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="银联开通说明">{{ form.unionMessage }}</a-descriptions-item>
      <a-descriptions-item label="支付宝开通状态">
        <a-tag v-if="form.alipayOpenStatus === -1" color="warning">限制开通</a-tag>
        <a-tag v-else-if="form.alipayOpenStatus === 0">未开通</a-tag>
        <a-tag v-else-if="form.alipayOpenStatus === 1" color="success">已开通</a-tag>
        <a-tag v-else-if="form.alipayOpenStatus === 2" color="processing">申请中</a-tag>
        <a-tag v-else-if="form.alipayOpenStatus === 3" color="error">开通失败</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="支付宝开通说明">{{ form.alipayMessage }}</a-descriptions-item>
      <a-descriptions-item label="支付宝开通状态(大额)">
        <a-tag v-if="form.alipayIndustryOpenStatus === -1" color="warning">限制开通</a-tag>
        <a-tag v-else-if="form.alipayIndustryOpenStatus === 0">未开通</a-tag>
        <a-tag v-else-if="form.alipayIndustryOpenStatus === 1" color="success">已开通</a-tag>
        <a-tag v-else-if="form.alipayIndustryOpenStatus === 2" color="processing">申请中</a-tag>
        <a-tag v-else-if="form.alipayIndustryOpenStatus === 3" color="error">开通失败</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="支付宝开通说明(大额)">{{ form.alipayIndustryOpenReason }}</a-descriptions-item>
      <a-descriptions-item label="微信开通状态">
        <a-tag v-if="form.wechatOpenStatus === -1" color="warning">限制开通</a-tag>
        <a-tag v-else-if="form.wechatOpenStatus === 0">未开通</a-tag>
        <a-tag v-else-if="form.wechatOpenStatus === 1" color="success">已开通</a-tag>
        <a-tag v-else-if="form.wechatOpenStatus === 2" color="processing">申请中</a-tag>
        <a-tag v-else-if="form.wechatOpenStatus === 3" color="error">开通失败</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="微信开通说明">{{ form.wechatMessage }}</a-descriptions-item>
      <a-descriptions-item label="是否完成首笔交易">
        <a-tag v-if="form.completeFirstTrans === 0">未完成</a-tag>
        <a-tag v-else-if="form.completeFirstTrans === 1" color="success">已完成</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="微信子商户号">{{ form.wechatMerchId }}</a-descriptions-item>
      <a-descriptions-item label="支付宝子商户号">{{ form.alipayMerchId }}</a-descriptions-item>
      <a-descriptions-item label="支付宝认证状态">
        <a-tag v-if="form.alipayAuthStatus === '1'" color="success">已认证</a-tag>
        <a-tag v-else-if="form.alipayAuthStatus === '0'">未认证</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="微认证状态">
        <a-tag v-if="form.wechatAuthStatus === '1'" color="success">已认证</a-tag>
        <a-tag v-else-if="form.wechatAuthStatus === '0'">未认证</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="聚合钱包UUID">{{ form.unionWalletUuid }}</a-descriptions-item>
      <a-descriptions-item label="有效状态">
        <a-tag v-if="form.validStatus === 1" color="success">有效</a-tag>
        <a-tag v-else-if="form.validStatus === 0" color="error">无效</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="D0结算类型">
        <a-tag v-if="form.d0SettleType === 0" color="blue">秒到</a-tag>
        <a-tag v-else-if="form.d0SettleType === 1" color="purple">合并到账</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="报备来源">{{ form.reportSource }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

export default {
  props: {
    visible: Boolean,
    detail: Object,
    channelCodes: Array
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
