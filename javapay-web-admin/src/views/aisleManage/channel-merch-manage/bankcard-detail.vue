<template>
  <a-modal :width="1000" :visible="visible" title="结算账户详情" :body-style="{ paddingBottom: '20px' }" :mask-closable="false" @update:visible="updateVisible">
    <ele-pro-table ref="table" row-key="id" :need-page="false" :datasource="datasource" :columns="columns" :scroll="{ x: 'max-content' }">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'cardType'">
          <a-tag v-if="record.cardType === 1" color="pink">借记卡</a-tag>
          <a-tag v-else-if="record.cardType === 2" color="blue">贷记卡</a-tag>
          <a-tag v-else-if="record.cardType === 3" color="cyan">准贷记卡</a-tag>
          <a-tag v-else-if="record.cardType === 4" color="purple">预付费卡</a-tag>
        </template>
        <template v-else-if="column.key === 'accountType'">
          <a-tag v-if="record.accountType === 'G'" color="orange">对公</a-tag>
          <a-tag v-else-if="record.accountType === 'S'" color="cyan">对私</a-tag>
        </template>
      </template>
    </ele-pro-table>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>
<script>
export default {
  props: {
    visible: Boolean,
    data: Array
  },
  emits: ['update:visible'],
  data() {
    return {
      datasource: [],
      columns: [
        {
          title: '银行户名',
          dataIndex: 'bankAccountName',
          align: 'center'
        },
        {
          title: '银行账号',
          dataIndex: 'bankAccountNoMask'
        },
        {
          title: '银行名称',
          dataIndex: 'bankBranch'
        },
        {
          title: '账户类型',
          dataIndex: 'accountType',
          key: 'accountType',
          align: 'center'
        },
        {
          title: '联行号',
          dataIndex: 'bankChannelNo'
        },
        {
          title: '卡类型',
          dataIndex: 'cardType',
          key: 'cardType',
          align: 'center'
        },
        {
          title: '银行预留手机号',
          dataIndex: 'mobileMask',
          align: 'center'
        }
      ]
    };
  },
  created() {
    this.datasource = Object.assign([], this.data);
  },
  methods: {
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
