<template>
  <a-modal
    :width="888"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新增'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules" layout="vertical">
      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="支付通道" name="channelCode">
            <a-select v-model:value="form.channelCode" style="width: 100%" placeholder="请选择">
              <a-select-option :value="item.channelCode" v-for="item in channelCodes" :key="item.id"
              >{{ item.channelName }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="通道用户编号" name="chnUserNo">
            <a-input v-model:value="form.chnUserNo" placeholder="通道用户编号" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="平台用户编号" name="userNo">
            <a-input v-model:value="form.userNo" placeholder="平台用户编号" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="认证状态" name="authStatus">
            <a-select v-model:value="form.authStatus" style="width: 100%" placeholder="请选择" allow-clear>
              <a-select-option :value="0">待认证</a-select-option>
              <a-select-option :value="1">认证待审核</a-select-option>
              <a-select-option :value="2">审核不通过</a-select-option>
              <a-select-option :value="3">认证通过</a-select-option>
              <a-select-option :value="4">入网成功</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="认证描述" name="authMessage">
            <a-textarea v-model:value="form.authMessage" placeholder="请输入认证描述" :auto-size="{ minRows: 1, maxRows: 5 }" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="有效状态" name="validStatus">
            <a-select v-model:value="form.validStatus" style="width: 100%" placeholder="请选择" allow-clear>
              <a-select-option :value="1">有效</a-select-option>
              <a-select-option :value="0">无效</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { ChannelUserApi } from '@/api/aisleManage/ChannelUserApi';

export default {
  props: {
    visible: Boolean,
    data: Object,
    channelCodes: Array
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {
        validStatus: 1
      },
      // 表单验证规则
      rules: {
        chnUserNo: [{ required: true, message: '请输入通道用户编号' }],
        userNo: [{ required: true, message: '请输入平台用户编号' }],
        authStatus: [{ required: true, message: '请选择' }],
        authMessage: [{ required: true, message: '请输入认证描述' }],
        channelCode: [{ required: true, message: '请选择' }],
        validStatus: [{ required: true, message: '请选择' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false
    };
  },
  created() {
    if (this.data) {
      this.isUpdate = true;
      this.form = Object.assign(this.form, this.data);
    }
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      // 执行编辑或修改方法
      if (this.isUpdate) {
        result = ChannelUserApi.edit(this.form);
      } else {
        result = ChannelUserApi.add(this.form);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
