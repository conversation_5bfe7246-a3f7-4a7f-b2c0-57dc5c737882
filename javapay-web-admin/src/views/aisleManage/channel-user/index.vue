<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="平台用户编号">
              <a-input v-model:value.trim="where.userNo" placeholder="请输入平台用户编号" allow-clear />
            </a-form-item>
            <a-form-item label="通道用户编号">
              <a-input v-model:value.trim="where.chnUserNo" placeholder="请输入通道用户编号" allow-clear />
            </a-form-item>
            <a-form-item label="支付通道">
              <a-select v-model:value="where.channelCode" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
                  {{ channelName }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="认证状态">
              <a-select v-model:value="where.authStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">待认证</a-select-option>
                <a-select-option :value="1">认证待审核</a-select-option>
                <a-select-option :value="2">审核不通过</a-select-option>
                <a-select-option :value="3">认证通过</a-select-option>
                <a-select-option :value="4">入网成功</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="有效状态">
              <a-select v-model:value="where.validStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">有效</a-select-option>
                <a-select-option :value="0">无效</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'validStatus'">
              <a-tag v-if="record.validStatus === 0" color="pink">无效</a-tag>
              <a-tag v-else-if="record.validStatus === 1" color="cyan">有效</a-tag>
            </template>

            <template v-else-if="column.key === 'authStatus'">
              <a-tag v-if="record.authStatus === 0">待认证</a-tag>
              <a-tag v-else-if="record.authStatus === 1" color="cyan">认证待审核</a-tag>
              <a-tag v-else-if="record.authStatus === 2" color="red">审核不通过</a-tag>
              <a-tag v-else-if="record.authStatus === 3" color="green">认证通过</a-tag>
              <a-tag v-else-if="record.authStatus === 4" color="blue">入网成功</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleEdit(record)">修改</a>
                <a-divider type="vertical" />
                <a-popconfirm title="确定要删除此记录吗？" @confirm="remove(record)">
                  <a class="ele-text-danger">删除</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <ChannelUserEdit v-if="showEdit" v-model:visible="showEdit" :data="current" :channel-codes="channelCodes" @done="reload" />
  </div>
</template>

<script>
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import ChannelUserEdit from './modules/ChannelUserEdit.vue';
import { message } from 'ant-design-vue';
import { ChannelUserApi } from '@/api/aisleManage/ChannelUserApi';

export default {
  name: 'ChannelUser',
  components: {
    ChannelUserEdit
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '支付通道',
          dataIndex: 'channelCode',
          align: 'center',
          customRender: ({ text }) => {
            const item = this.channelCodes.find(c => c.channelCode === text);
            return item?.channelName || '--';
          }
        },
        {
          title: '通道用户编号',
          dataIndex: 'chnUserNo',
          align: 'center'
        },
        {
          title: '平台用户编号',
          dataIndex: 'userNo',
          align: 'center'
        },
        {
          title: '认证状态',
          dataIndex: 'authStatus',
          key: 'authStatus',
          align: 'center'
        },
        {
          title: '认证描述',
          dataIndex: 'authMessage',
          width: 200
        },
        {
          title: '有效状态',
          dataIndex: 'validStatus',
          key: 'validStatus',
          align: 'center',
          width: 120
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          align: 'center'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime',
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          width: 150,
          fixed: 'right'
        }
      ],
      // 表格搜索条件
      where: {},
      channelCodes: [],
      showEdit: false,
      current: null
    };
  },
  mounted() {
    this.getChannelCodes();
  },
  methods: {
    async remove(row) {
      const result = await ChannelUserApi.delete({ id: row.id });
      message.success(result.message);
      this.reload();
    },

    handleEdit(record) {
      this.current = record;
      this.showEdit = true;
    },

    async getChannelCodes() {
      const data = await ChannelManageApi.list({ validStatus: 1 });
      this.channelCodes = data || [];
    },

    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    datasource({ page, limit, where }) {
      return ChannelUserApi.findPage({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>
