<template>
  <a-modal
    :width="900"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新增'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @cancel="cancel"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules">
      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="支付方式" name="payMethod">
            <a-select v-model:value="form.payMethod" placeholder="请选择" :disabled="isUpdate" allow-clear>
              <a-select-option :value="1">云闪付</a-select-option>
              <a-select-option :value="2">微信支付</a-select-option>
              <a-select-option :value="3">支付宝支付</a-select-option>
              <a-select-option :value="4">EPOS支付</a-select-option>
              <a-select-option :value="5">POS刷卡</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="代理商编号" name="agentNo">
            <a-input v-model:value="form.agentNo" placeholder="请输入代理商编号" :disabled="isUpdate" allow-clear />
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="支付通道" name="channelCode">
            <a-select v-model:value="form.channelCode" class="ele-fluid" placeholder="请选择" allow-clear :disabled="isUpdate">
              <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">{{
                channelName
              }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>

    <br />

    <div class="block-interval channel-bottom">
      <a-button type="primary" @click="addRow">添加</a-button>

      <a-form v-for="(item, index) in paramsArr" :key="index" :rules="paramsRules" :model="item" layout="inline" class="channel-margin">
        <a-row>
          <a-form-item label="参数名称" name="parKey">
            <a-input style="width: 280px" v-model:value.trim="item.parKey" placeholder="请输入参数名称" allow-clear />
          </a-form-item>
          <a-form-item label="参数值" name="parValue">
            <a-input style="width: 280px" v-model:value.trim="item.parValue" placeholder="请输入参数值" allow-clear />
          </a-form-item>
          <a-form-item class="ele-text-center">
            <a-button type="danger" @click="deleteRow(index)">删除</a-button>
          </a-form-item>
        </a-row>
      </a-form>
    </div>
  </a-modal>
</template>
  
  <script>
import { PayParameterManageApi } from '@/api/aisleManage/PayParameterManageApi';
import { message } from 'ant-design-vue';

export default {
  name: 'PayParameterEdit',
  props: {
    visible: Boolean,
    showData: Object,
    channelCodes: Array
  },
  emits: ['update:visible', 'done'],

  data() {
    return {
      //表单数据
      form: {},
      //是否是修改
      isUpdate: false,
      //提交状态
      loading: false,
      //通道编号是否可以编辑
      isChannelCodeInputDisabled: true,
      //接收键值对数组
      paramsArr: [],
      //表单规则
      rules: {
        payMethod: [{ required: true, message: '请选择支付方式' }]
      },

      //键值对表格规则
      paramsRules: {
        parKey: [{ required: true, message: '请输入参数名称' }],
        parValue: [{ required: true, message: '请输入参数值' }]
      }
    };
  },

  watch: {
    showData() {
      //有就是编辑
      if (this.showData) {
        this.isUpdate = true;
        this.form = Object.assign({}, this.showData);
        //编辑不能修改
        this.isChannelCodeInputDisabled = true;
        //首先将数组初始化
        this.paramsArr = [];
        //将json串转换为obj对象
        const obj = JSON.parse(this.showData.payParams);
        //将obj数据转换成[{parkey:'',parValue:''}]这种形式
        Object.keys(obj).forEach(item => {
          this.paramsArr.push({
            parKey: item,
            parValue: obj[item]
          });
        });
      } else {
        //没有就是新增
        this.isUpdate = false;
        this.form = {};
        //新增可以修改
        this.isChannelCodeInputDisabled = false;
        //没有就将数组清空
        this.paramsArr = [];
      }
      if (this.$refs.form) {
        this.$refs.form.clearValidate();
      }
    }
  },

  methods: {
    //传值更新父组件的值
    updateVisible(value) {
      this.$emit('update:visible', value);
    },
    addRow() {
      this.paramsArr.push({
        parKey: '',
        parValue: ''
      });
    },
    //删除键值对
    deleteRow(index) {
      this.paramsArr.splice(index, 1);
    },
    //取消事件
    cancel() {
      this.form = Object.assign({}, this.showData);
      this.$refs.form.clearValidate();
      this.paramsArr = [];
      //将json串转换为obj对象
      const obj = JSON.parse(this.showData.payParams);
      //将obj数据转换成[{parkey:'',parValue:''}]这种形式
      Object.keys(obj).forEach(item => {
        this.paramsArr.push({
          parKey: item,
          parValue: obj[item]
        });
      });
      //关闭需要传值返回
      this.$emit('update:visible', false);
    },
    //新增或者保存
    async save() {
      await this.$refs.form.validate();
      this.loading = true;
      //创建新字典型数据用于接收 param数组值
      const paramdict = {};
      //将数组提取然后转换成json串
      this.paramsArr.forEach(item => {
        if (item.parKey && item.parKey !== 0 && item.parValue && item.parValue !== 0) {
          paramdict[item.parKey] = item.parValue;
        }
      });
      this.form.payParams = paramdict;

      const id = this.form.id;
      const payMethod = this.form.payMethod;
      const payParamsObj = this.form.payParams;

      let result = null;
      if (this.isUpdate) {
        //更新事件
        result = PayParameterManageApi.edit({ 
          id, 
          channelCode: this.form.channelCode,
          agentNo: this.form.agentNo,
          payParamsDTO: payParamsObj 
        });
      } else {
        //新增事件
        result = PayParameterManageApi.add({ 
          payMethod,
          channelCode: this.form.channelCode,
          agentNo: this.form.agentNo,
          payParamsDTO: payParamsObj 
        });
      }

      result
        .then(res => {
          this.loading = false;
          message.success(res.message);

          if (!this.isUpdate) {
            this.form = {};
          }
          //请求成功，删除键值对的数据和页面
          this.paramsArr = [];
          //关闭弹框，传值给父组件
          this.updateVisible(false);
          //操作完成，提示父组件刷新页面
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    }
  }
};
</script>
      
  <style>
.channel-margin {
  margin-top: 20px;
}

.channel-bottom {
  margin-bottom: 50px;
}
</style>
  