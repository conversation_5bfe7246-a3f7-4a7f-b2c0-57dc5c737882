<template>
  <a-modal
    :width="600"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '30px' }"
    @update:visible="updateVisivle"
    :footer="null"
  >
    <a-descriptions :column="1">
      <a-descriptions-item label="支付方式">
        <a-tag v-if="form.payMethod === 1" color="pink">云闪付</a-tag>
        <a-tag v-else-if="form.payMethod === 2" color="cyan">微信支付</a-tag>
        <a-tag v-else-if="form.payMethod === 3" color="blue">支付宝支付</a-tag>
        <a-tag v-else-if="form.payMethod === 4" color="purple">EPOS支付</a-tag>
        <a-tag v-else-if="form.payMethod === 5" color="pink">POS刷卡</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="通道名称">
        <template v-for="({ channelCode, channelName }, key) in channelCodes" :key="key">
          <a-badge v-if="form.channelCode === channelCode" color="purple" :text="channelName" />
        </template>
      </a-descriptions-item>

      <a-descriptions-item label="代理商编号">{{ form.agentNo }}</a-descriptions-item>

    </a-descriptions>

    <div v-for="(value, key, index) in JSON.parse(form.payParams)" :key="index">
      <a-descriptions :column="2">
        <a-descriptions-item label="  参数名称">{{ key }}</a-descriptions-item>
        <a-descriptions-item label="参数值">{{ value }}</a-descriptions-item>
      </a-descriptions>
    </div>
  </a-modal>
</template>
  
  <script>
export default {
  name: 'PayParameterDetail',
  props: {
    visible: Boolean,
    detail: Object,
    channelCodes: Array
  },
  emits: ['update:visible'],
  data() {
    return {
      form: {}
    };
  },
  watch: {
    //父组件值改变则重新赋值
    detail() {
      this.form = Object.assign({}, this.detail);
    }
  },
  methods: {
    updateVisivle(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
  
  <style>
</style>
  