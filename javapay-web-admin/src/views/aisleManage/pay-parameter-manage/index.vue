<template>
  <div class="ele-body">
    <!-- 搜索框内容 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="支付方式">
              <a-select v-model:value="where.payMethod" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">云闪付</a-select-option>
                <a-select-option :value="2">微信支付</a-select-option>
                <a-select-option :value="3">支付宝支付</a-select-option>
                <a-select-option :value="4">EPOS支付</a-select-option>
                <a-select-option :value="5">POS刷卡</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格内容 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- 表格上方的操作按钮 -->
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>

          <!-- 表体的操作 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'payMethod'">
              <a-tag v-if="record.payMethod === 1" color="pink">云闪付</a-tag>
              <a-tag v-else-if="record.payMethod === 2" color="cyan">微信支付</a-tag>
              <a-tag v-else-if="record.payMethod === 3" color="blue">支付宝支付</a-tag>
              <a-tag v-else-if="record.payMethod === 4" color="purple">EPOS支付</a-tag>
              <a-tag v-else-if="record.payMethod === 5" color="cyan">POS刷卡</a-tag>
            </template>
            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleEdit(record)">修改</a>
                <a-divider type="vertical" />
                <a @click="handleShowDetail(record)">详情</a>
                <a-divider type="vertical" />
                <a-popconfirm title="确定要删除此行数据吗？" @confirm="remove(record)">
                  <a class="ele-text-danger">删除</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 新增编辑 -->
    <PayParameterEdit v-model:visible="showEdit" :showData="current" :channelCodes="channelCodes" @done="reload" />

    <!-- 详情页面 -->
    <PayParameterDetail v-model:visible="showDetail" :detail="current" :channelCodes="channelCodes" />
  </div>
</template>

<script>
import { PayParameterManageApi } from '@/api/aisleManage/PayParameterManageApi';
import PayParameterEdit from './pay-parameter-edit.vue';
import PayParameterDetail from './pay-parameter-detail.vue'
import { message } from 'ant-design-vue';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';

export default {
  name: 'PlatformParameterManage',
  components: {
    PayParameterEdit,
    PayParameterDetail
  },
  data() {
    return {
      //表格查询条件
      where: {},
      //展示编辑页面传的参数
      current: null,
      //是否展示新增或者是编辑页面
      showEdit: false,
      //是否展示详情页面
      showDetail: false,
      channelCodes: [],

      //表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center'
        },
        {
          title: '支付方式',
          dataIndex: 'payMethod',
          key: 'payMethod',
          align: 'center'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center'
        }
      ]
    };
  },
  async mounted() {
    const data = await ChannelManageApi.list({ validStatus: 1 });
    this.channelCodes = data || [];
  },
  methods: {
    //查询方法
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    //重置
    reset() {
      this.where = {}; //清空查询条件
      /**
       * 为啥都清空了还要添加
       */
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    //新建或者编辑
    handleEdit(row) {
      this.showEdit = true;
      this.current = row;
    },

    //展示详情
    handleShowDetail(row) {
      this.showDetail = true;
      this.current = row;
    },

    async remove(row) {
      const result = await PayParameterManageApi.delete({ id: row.id });
      message.success(result.message);
      this.reload();
    },

    //获取数据方法
    datasource({ page, limit, where }) {
      return PayParameterManageApi.getPlatformPayChannelPages({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>

<style>
</style>