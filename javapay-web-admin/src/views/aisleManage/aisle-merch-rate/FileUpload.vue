<template>
  <a-modal :width="500" :visible="visible" title="批量修改费率文件上传" :mask-closable="false" @update:visible="updateVisible">
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 5 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 19 }, sm: { span: 24 } }"
    >
      <a-form-item label="支付通道" name="channelCode">
        <a-select v-model:value="form.channelCode" class="ele-fluid" placeholder="请选择">
          <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">{{
            channelName
          }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="费率类型" name="rateType">
        <a-select v-model:value="form.rateType" class="ele-fluid" placeholder="请选择">
          <a-select-option :value="1">线下收单费率</a-select-option>
          <a-select-option :value="3">EPOS费率</a-select-option>
        </a-select>
      </a-form-item>
    </a-form>

    <!-- 自定义页脚按钮 -->
    <template #footer>
      <a-space>
        <a-button key="back" @click="updateVisible(false)">取消</a-button>
        <a-upload
          name="file"
          accept=".xlsx"
          :maxCount="1"
          :action="FileUploadUrl"
          :headers="headers"
          :before-upload="beforeUpload"
          @change="afterUpload"
          :showUploadList="false"
        >
          <a-button type="primary" :disabled="!(form.channelCode && form.rateType)">
            <template #icon>
              <CloudUploadOutlined />
            </template>
            <span>上传文件(.xlsx)</span>
          </a-button>
        </a-upload>
      </a-space>
    </template>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { getToken } from '@/utils/token-util';

export default {
  props: {
    visible: Boolean,
    channelCodes: Array
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {
        channelCode:'1009', // 默认电银
        rateType: 1,
      },
      // 上传文件头
      headers: {
        Authorization: getToken()
      }
    };
  },
  computed: {
    FileUploadUrl() {
      const query = `?channelCode=${this.form.channelCode}&rateType=${this.form.rateType}`;
      return `/api/channelMerchantRate/upload${query}`;
    }
  },
  methods: {
    beforeUpload(file) {
      const isLt10M = file.size / 1024 / 1024 <= 10;
      if (!isLt10M) {
        message.error('上传文件不能超过10MB');
      }
      return isLt10M;
    },
    afterUpload({ file }) {
      if (!file.response) return;

      if (file.response.code === '00000') {
        message.success('上传成功');
        this.updateVisible(false);
        this.$emit('done');
      } else {
        message.error(file.response.message || '上传失败');
      }
    },
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
