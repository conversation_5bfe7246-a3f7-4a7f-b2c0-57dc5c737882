<template>
  <a-modal
    :width="760"
    :visible="visible"
    :confirm-loading="loading"
    title="编辑"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :label-col="{ style: { width: '110px' } }">
      <a-row :gutter="24">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="支付通道" name="channelCode" :rules="[{ required: true, message: '请选择支付通道' }]">
            <a-select v-model:value="form.channelCode" class="ele-fluid" placeholder="请选择" disabled>
              <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
                {{ channelName }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="费率类型">
            <a-select v-model:value="form.rateType" class="ele-fluid" placeholder="请选择" disabled>
              <a-select-option :value="1">线下收单费率</a-select-option>
              <a-select-option :value="2">刷卡费率</a-select-option>
              <a-select-option :value="3">EPOS费率</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="支持的银行" name="bankCodes" v-if="form.rateType === 3">
        <a-select
          v-model:value="form.bankCodes"
          mode="multiple"
          :filter-option="filterBank"
          showSearch
          style="width: 100%"
          placeholder="无数据"
          disabled
        >
          <a-select-option v-for="({ bankName, typeCode }, key) in bankList" :key="key" :value="typeCode">{{ bankName }} </a-select-option>
        </a-select>
      </a-form-item>

      <a-form
        ref="rateForm"
        :model="form"
        :label-col="{ md: { span: 17 }, sm: { span: 24 } }"
        :wrapper-col="{ md: { span: 7 }, sm: { span: 24 } }"
      >
        <RateTypeRateModule :rate-item="form" />
      </a-form>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { AisleMerchRateApi } from '@/api/aisleManage/AisleMerchRateApi';
import RateTypeRateModule from '../../business-team/_components/RateTypeRateModule.vue';
import { deepCopy } from '@/utils/util';

const rateReg = /^\d+(\.\d{1,3})?$/;
const amountReg = /^\d+(\.\d{1})?$/;
const feeMaxReg = /^(0|[1-9][0-9]*)$/;
export default {
  components: {
    RateTypeRateModule
  },
  props: {
    visible: Boolean,
    data: Object,
    channelCodes: Array,
    bankList: Array // 银行列表
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      form: {
        rateInfoDTO: {},
        bankCodes: []
      },
      rateModuleFormRules: {
        rateValidate: [
          { required: true, message: '必填项' },
          { pattern: rateReg, message: '正数且小数点后最多三位', trigger: 'blur' },
          {
            validator: async (rule, value) => {
              const dotAfterValue = value.toString().split('.')[1];
              if (dotAfterValue?.length === 3) {
                if (['0', '5'].includes([...dotAfterValue].pop())) {
                  return Promise.resolve();
                }
                return Promise.reject('小数点后第三位必须为0或5');
              } else {
                return Promise.resolve();
              }
            },
            trigger: 'blur'
          }
        ],
        feeMax: [
          { required: true, message: '必填项' },
          { pattern: feeMaxReg, message: '整数', trigger: 'blur' }
        ],
        amountValidate: [
          { required: true, message: '必填项' },
          { pattern: amountReg, message: '正数且小数点后最多一位', trigger: 'blur' }
        ]
      },
      // 提交状态
      loading: false
    };
  },
  created() {
    if (this.data) {
      this.form = Object.assign({}, this.data);

      const { rateInfo } = this.data;
      if (rateInfo) {
        const formatRateInfo = JSON.parse(rateInfo);
        this.form.rateInfoDTO = formatRateInfo || {};
      }
      delete this.form['rateInfo'];

      this.form.bankCodes = this.form.bankCodes ? this.form.bankCodes.split(',') : [];
    }

    this.form.rateRatio = 100;
    this.form.isSame = 1;
    const rateFields = ['withdrawRate', 'withdrawSingleFee'];
    rateFields.forEach(f => (this.form.rateInfoDTO[f] = 0));
  },
  methods: {
    filterBank(input, option) {
      const bank = this.bankList.find(bank => bank.typeCode === option.value);
      if (bank) {
        return bank.bankName.includes(input);
      }
      return false;
    },

    async save() {
      // 校验表单
      await this.$refs.rateForm.validate();

      // 修改加载框为正在加载
      this.loading = true;

      const submitParams = deepCopy(this.form);
      submitParams.bankCodes = submitParams.bankCodes.join(',');
      submitParams.rateInfoDTO.rateType = submitParams.rateType;

      AisleMerchRateApi.edit(submitParams)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示修改成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
