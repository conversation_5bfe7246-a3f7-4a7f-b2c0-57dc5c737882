<template>
  <a-modal
    :width="760"
    :visible="visible"
    :confirm-loading="loading"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
  >
    <a-form ref="form" :model="form" class="ele-form-detail" :label-col="{ style: { width: '110px' } }">
      <a-row :gutter="24">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="支付通道" name="channelCode" :rules="[{ required: true, message: '请选择支付通道' }]">
            <a-select v-model:value="form.channelCode" class="ele-fluid" placeholder="请选择" allow-clear :disabled="true">
              <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
                {{ channelName }}
              </a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="有效状态" name="validStatus" required>
            <a-switch :checkedValue="1" :un-checked-value="0" v-model:checked="form.validStatus" :disabled="isDisabled">
              <template #checkedChildren><check-outlined /></template>
              <template #unCheckedChildren><close-outlined /></template>
            </a-switch>
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="费率类型" name="rateType" :rules="[{ required: true, message: '请选择费率类型' }]">
            <a-select v-model:value="form.rateType" class="ele-fluid" placeholder="请选择" :disabled="true" allow-clear>
              <a-select-option :value="1">线下收单费率</a-select-option>
              <a-select-option :value="2">刷卡费率</a-select-option>
              <a-select-option :value="3">EPOS费率</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="支持的银行" name="bankCodes" v-if="form.rateType === 3">
        <a-select
          v-model:value="form.bankCodes"
          mode="multiple"
          :filter-option="filterBank"
          showSearch
          style="width: 100%"
          placeholder="请选择"
          :disabled="isDisabled"
        >
          <a-select-option v-for="({ bankName, typeCode }, key) in bankList" :key="key" :value="typeCode">{{ bankName }} </a-select-option>
        </a-select>
      </a-form-item>

      <a-form
        ref="rateForm"
        :model="form"
        :label-col="{ md: { span: 17 }, sm: { span: 24 } }"
        :wrapper-col="{ md: { span: 7 }, sm: { span: 24 } }"
      >
        <RateTypeRateModule :rate-item="form" :disabled="isDisabled" />
      </a-form>
    </a-form>

    <template #footer v-if="isDisabled">
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

  <script>
import RateTypeRateModule from '../../business-team/_components/RateTypeRateModule.vue';

export default {
  name: 'AisleMerchRateDetail',
  components: {
    RateTypeRateModule
  },
  props: {
    visible: Boolean,
    detail: Object,
    channelCodes: Array,
    bankList: Array // 银行列表
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      isDisabled: true,
      // 表单数据
      form: {
        validStatus: 1,
        rateInfoDTO: {},
        bankCodes: [],
        rateRatio: ''
      },
      // 提交状态
      loading: false,
      templateNos: [],
      //是否展示银行编码选择
      showBankCodeList: false
    };
  },
  watch: {
    detail() {
      this.form = Object.assign({}, this.detail);

      const { rateInfo } = this.detail;
      if (rateInfo) {
        const formatRateInfo = JSON.parse(rateInfo);
        this.form.rateInfoDTO = formatRateInfo || {};
      }
      delete this.form['rateInfo'];

      //有数据的话先将字符串转换成数组类型显示
      this.form.bankCodes = this.form.bankCodes ? this.form.bankCodes.split(',') : [];

      this.form.rateRatio = 100;
      const rateFields = ['withdrawRate', 'withdrawSingleFee'];
      rateFields.forEach(f => (this.form.rateInfoDTO[f] = 0));
    }
  },
  methods: {
    //对银行选择 名称的模糊查询
    filterBank(input, option) {
      const bank = this.bankList.find(bank => bank.typeCode === option.value);
      if (bank) {
        return bank.bankName.toLowerCase().includes(input.toLowerCase());
      }
      return false;
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
