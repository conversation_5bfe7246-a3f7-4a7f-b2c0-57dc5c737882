<template>
  <div class="ele-body">
    <a-spin :spinning="spinning" tip="下载中, 请稍候...">
      <!-- 搜索框内容 -->
      <div class="block-interval">
        <a-card :bordered="false">
          <a-form layout="inline" :model="where">
            <a-row :gutter="[0, 16]">
              <a-form-item label="商户编号">
                <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
              </a-form-item>

              <a-form-item label="通道商户编号">
                <a-input v-model:value.trim="where.chnMerchNo" placeholder="请输入通道商户编号" allow-clear />
              </a-form-item>

              <a-form-item label="版本号">
                <a-input v-model:value.trim="where.version" placeholder="请输入版本号" allow-clear />
              </a-form-item>

              <a-form-item label="费率编号">
                <a-input v-model:value.trim="where.templateNo" placeholder="请输入费率编号" allow-clear />
              </a-form-item>

              <a-form-item label="政策编号">
                <a-input v-model:value.trim="where.policyNo" placeholder="请输入政策编号" allow-clear />
              </a-form-item>

              <a-form-item label="通道名称">
                <a-select v-model:value="where.channelCode" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
                    {{ channelName }}
                  </a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item label="有效状态">
                <a-select v-model:value="where.validStatus" style="width: 205px" placeholder="请选择" allow-clear>
                  <a-select-option value="0">无效</a-select-option>
                  <a-select-option value="1">有效</a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item label="费率类型">
                <a-select v-model:value="where.rateType" style="width: 200px" placeholder="请选择" allow-clear>
                  <a-select-option :value="1">线下收单费率</a-select-option>
                  <a-select-option :value="3">EPOS费率</a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item class="ele-text-center">
                <a-space>
                  <a-button type="primary" @click="reload">查询</a-button>
                  <a-button @click="reset">重置</a-button>
                </a-space>
              </a-form-item>
            </a-row>
          </a-form>
        </a-card>
      </div>

      <!-- 表格内容 -->
      <div>
        <a-card :bordered="false" class="table-height">
          <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
            <template #toolbar>
              <a-space>
                <a-button @click="showFileDownload = true">
                  <template #icon>
                    <download-outlined />
                  </template>
                  <span>费率文件记录下载</span>
                </a-button>
                <a-button @click="showFileUpload = true">
                  <template #icon>
                    <upload-outlined />
                  </template>
                  <span>批量修改费率文件上传</span>
                </a-button>
              </a-space>
            </template>

            <!-- 表体的操作 -->
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'validStatus'">
                <a-tag v-if="record.validStatus === 0" color="pink">无效</a-tag>
                <a-tag v-else-if="record.validStatus === 1" color="cyan">有效</a-tag>
              </template>

              <template v-else-if="column.key === 'rateType'">
                <a-tag color="pink" v-if="record.rateType === 1">线下收单费率</a-tag>
                <a-tag color="cyan" v-else-if="record.rateType === 2">刷卡费率</a-tag>
                <a-tag color="blue" v-else-if="record.rateType === 3">EPOS费率</a-tag>
              </template>

              <!-- table操作栏按钮 -->
              <template v-else-if="column.key === 'action'">
                <a-space>
                  <a @click="handleDetail(record)">详情</a>
                  <a-divider type="vertical" />
                  <a @click="handleEdit(record)">编辑</a>
                </a-space>
              </template>
            </template>
          </ele-pro-table>
        </a-card>
      </div>

      <!-- 详情 -->
      <AisleMerchRateDetail v-model:visible="showDetail" :detail="current" :channelCodes="channelCodes" :bankList="bankList" />

      <!-- 修改费率模板信息 -->
      <AisleMerchRateEdit
        v-if="showEdit"
        v-model:visible="showEdit"
        :data="current"
        :channelCodes="channelCodes"
        :bankList="bankList"
        @done="reload"
      />

      <FileDownload v-if="showFileDownload" v-model:visible="showFileDownload" :channelCodes="channelCodes" />

      <FileUpload v-if="showFileUpload" v-model:visible="showFileUpload" :channelCodes="channelCodes" @done="reload" />
    </a-spin>
  </div>
</template>

<script>
import { AisleMerchRateApi } from '@/api/aisleManage/AisleMerchRateApi';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import AisleMerchRateDetail from './aisle-merch-rate-detail.vue';
import AisleMerchRateEdit from './AisleMerchRateEdit.vue';
import FileDownload from './FileDownload.vue';
import FileUpload from './FileUpload.vue';
import { BankCodeManageApi } from '@/api/base/BankCodeManageApi';

export default {
  name: 'AisleMerchRate',
  components: {
    AisleMerchRateDetail,
    AisleMerchRateEdit,
    FileDownload,
    FileUpload
  },
  data() {
    return {
      spinning: false,
      //表格查询条件
      where: {},
      //展示编辑页面传的参数
      current: null,
      //是否展示详情页面
      showDetail: false,
      showEdit: false,
      //通道数组
      channelCodes: [],
      //银行列表
      bankList: [],
      showFileDownload: false,
      showFileUpload: false,
      //表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center'
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '通道商户编号',
          dataIndex: 'chnMerchNo',
          align: 'center'
        },
        {
          title: '通道名称',
          dataIndex: 'channelCode',
          align: 'center',
          customRender: ({ text }) => {
            const item = this.channelCodes.find(c => c.channelCode === text);
            return item?.channelName || '--';
          }
        },
        {
          title: '费率类型',
          dataIndex: 'rateType',
          key: 'rateType',
          align: 'center'
        },
        {
          title: '费率编号',
          dataIndex: 'templateNo',
          align: 'center'
        },
        {
          title: '政策编号',
          dataIndex: 'policyNo',
          align: 'center'
        },

        {
          title: '版本号',
          dataIndex: 'version',
          align: 'center'
        },
        {
          title: '有效状态',
          dataIndex: 'validStatus',
          key: 'validStatus',
          align: 'center'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '修改人',
          dataIndex: 'lastModifyUserId'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 160,
          align: 'center'
        }
      ]
    };
  },
  async mounted() {
    this.getChannelCodes();
    this.getBankList();
  },
  methods: {
    //查询方法
    reload() {
      this.$refs.table.reload({ page: 1 });
    },
    //重置
    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    handleEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    async getChannelCodes() {
      const data = await ChannelManageApi.list({ validStatus: 1 });
      this.channelCodes = data || [];
    },

    async getBankList() {
      const data = await BankCodeManageApi.list();
      this.bankList = data || [];
    },

    //获取数据方法
    datasource({ page, limit, where }) {
      return AisleMerchRateApi.findPage({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>
