<template>
  <a-modal
    :width="800"
    :visible="visible"
    title="详情"
    :body-style="{ paddingBottom: '20px' }"
    :mask-closable="false"
    @update:visible="updateVisible"
    :footer="null"
  >
    <a-descriptions bordered :column="2">
      <a-descriptions-item label="商户ID">{{ form.merchantId }}</a-descriptions-item>
      <a-descriptions-item label="商户编号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="账户类型">
        <a-tag v-if="form.accountType === 'G'" color="pink">对公</a-tag>
        <a-tag v-else-if="form.accountType === 'S'" color="cyan">对私</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="渠道商户编号">{{ form.chnMerchNo }}</a-descriptions-item>
      <a-descriptions-item label="渠道商户名称">{{ form.chnMerchName }}</a-descriptions-item>
      <a-descriptions-item label="渠道编码">{{ form.channelCode }}</a-descriptions-item>

      <a-descriptions-item label="通道名称">{{ form.channelName }}</a-descriptions-item>
      <a-descriptions-item label="通道编号">{{ form.channelCode }}</a-descriptions-item>
      <a-descriptions-item label="通道结算卡变更状态">
        <a-tag v-if="form.changeChannelStatus === 0" color="pink">未变更</a-tag>
        <a-tag v-else-if="form.changeChannelStatus === 1" color="cyan">通道变更成功</a-tag>
        <a-tag v-else-if="form.changeChannelStatus === 2" color="blue">通道变更驳回</a-tag>
        <a-tag v-else-if="form.changeChannelStatus === 3" color="purple">通道审核中</a-tag>
        <a-tag v-else-if="form.changeChannelStatus === 4" color="pink">提交成功,待上传图片后等待通知处理</a-tag>
        <a-tag v-else-if="form.changeChannelStatus === 5" color="cyan">提交成功,待上传图片后即成功</a-tag>
        <a-tag v-else-if="form.changeChannelStatus === 6" color="blue">审核中,待处理</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="账户UUID">{{ form.accountUuid }}</a-descriptions-item>
      <a-descriptions-item label="账户结算卡变更状态">
        <a-tag v-if="form.changeAccountStatus === 0" color="pink">未变更</a-tag>
        <a-tag v-else-if="form.changeAccountStatus === 1" color="cyan">变更账户成功</a-tag>
        <a-tag v-else-if="form.changeAccountStatus === 2" color="blue">通道账户失败</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="变更来源">
        <a-tag v-if="form.changeSourceType === 1" color="pink">主动变更</a-tag>
        <a-tag v-else-if="form.changeSourceType === 2" color="cyan">变更通知</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="是否法人结算">
        <a-tag v-if="form.legalFlag === '0'" color="pink">否</a-tag>
        <a-tag v-else-if="form.legalFlag === '1'" color="cyan">是</a-tag>
      </a-descriptions-item>

      <a-descriptions-item label="变更后结算卡id">{{ form.afterBankCardId }}</a-descriptions-item>
      <a-descriptions-item label="变更后银行账号">{{ form.bankAccountNoMask }}</a-descriptions-item>
      <a-descriptions-item label="变更后卡类型">
        <a-tag v-if="form.cardType === 1" color="pink">借记卡</a-tag>
        <a-tag v-else-if="form.cardType === 2" color="cyan">贷记卡</a-tag>
        <a-tag v-else-if="form.cardType === 3" color="blue">准贷记卡</a-tag>
        <a-tag v-else-if="form.cardType === 4" color="purple">预付费卡</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="变更后银行户名">{{ form.bankAccountName }}</a-descriptions-item>
      <a-descriptions-item label="变更后银行预留身份证号">{{ form.idCardNoMask }}</a-descriptions-item>
      <a-descriptions-item label="变更后银行预留手机号">{{ form.mobileMask }}</a-descriptions-item>
      <a-descriptions-item label="变更后开户行省编码">{{ form.province }}</a-descriptions-item>

      <a-descriptions-item label="变更后开户行市编码">{{ form.city }}</a-descriptions-item>
      <a-descriptions-item label="变更后联行号">{{ form.bankChannelNo }}</a-descriptions-item>
      <a-descriptions-item label="变更后银行名称">{{ form.bankName }}</a-descriptions-item>
      <a-descriptions-item label="变更后银行行别代码">{{ form.typeCode }}</a-descriptions-item>
      <a-descriptions-item label="变更后开户支行名称">{{ form.bankBranch }}</a-descriptions-item>
      <a-descriptions-item label="变更前结算卡id">{{ form.beforeBankCardId }}</a-descriptions-item>

      <a-descriptions-item label="证件到期日">{{ form.certEndDate }}</a-descriptions-item>
      <a-descriptions-item label="通道结算卡变更结果描述">{{ form.channelRespMsg }}</a-descriptions-item>
      <a-descriptions-item label="账户结算卡变更结果描述">{{ form.accountRespMsg }}</a-descriptions-item>

      <a-descriptions-item label="创建人">{{ form.createUserId }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改人">{{ form.lastModifyUserId }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>
  </a-modal>
</template>

<script>
export default {
  name: 'AisleMerchPaymentcardChangeDetail',
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  data() {
    return {
      // 表单数据
      form: Object.assign({}, this.detail),
      // 表单验证规则
      rules: {
        mcc: [{ required: true, message: '请输入MCC码' }],
        mccName: [{ required: true, message: '请输入MCC名称' }],
        parentMcc: [{ required: true, message: '请输入父级MCC码' }],
        mccLevel: [{ required: true, message: '请选择' }],
        mccType: [{ required: true, message: '请选择' }],
        status: [{ required: true, message: '请选择' }],
        consumeType: [{ required: true, message: '请选择' }],
        franchiseType: [{ required: true, message: '请选择' }],
        freeType: [{ required: true, message: '请选择' }],
        isGeneral: [{ required: true, message: '请选择' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false
    };
  },
  watch: {
    detail() {
      if (this.detail) {
        this.form = Object.assign({}, this.detail);
        this.isUpdate = true;
      } else {
        this.form = {};
        this.isUpdate = false;
      }
    }
  },
  methods: {
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>

<style></style>
