<template>
  <div class="ele-body">
    <!-- 搜索框内容 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户名称" allow-clear />
            </a-form-item>

            <a-form-item label="渠道商户编号">
              <a-input v-model:value.trim="where.chnMerchNo" placeholder="请输入渠道商户编号" allow-clear />
            </a-form-item>

            <a-form-item label="渠道商户名称">
              <a-input v-model:value.trim="where.chnMerchName" placeholder="请输入商户名称" allow-clear />
            </a-form-item>

            <a-form-item label="通道编号">
              <a-input v-model:value.trim="where.channelCode" placeholder="请输入通道编号" allow-clear />
            </a-form-item>

            <a-form-item label="通道名称">
              <a-input v-model:value.trim="where.channelName" placeholder="请输入通道名称" allow-clear />
            </a-form-item>

            <a-form-item label="变更后开户支行名称">
              <a-input v-model:value.trim="where.bankBranch" placeholder="请输入变更后开户支行名称" allow-clear />
            </a-form-item>

            <a-form-item label="变更后开户行省编码">
              <a-input v-model:value.trim="where.province" placeholder="请输入变更后开户行省编码" allow-clear />
            </a-form-item>

            <a-form-item label="是否法人结算">
              <a-select v-model:value="where.legalFlag" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">否</a-select-option>
                <a-select-option :value="1">是</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="变更来源">
              <a-select v-model:value="where.changeSourceType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">主动变更</a-select-option>
                <a-select-option :value="2">变更通知</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="账户结算卡变更状态">
              <a-select v-model:value="where.changeAccountStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">未变更</a-select-option>
                <a-select-option :value="1">变更账户成功</a-select-option>
                <a-select-option :value="2">变更账户失败</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="通道结算卡变更状态">
              <a-select v-model:value="where.changeChannelStatus" style="width: 260px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">未变更</a-select-option>
                <a-select-option :value="1">通道变更成功</a-select-option>
                <a-select-option :value="2">通道变更驳回</a-select-option>
                <a-select-option :value="3">通道审核中</a-select-option>
                <a-select-option :value="4">提交成功,待上传图片后等待通知处理</a-select-option>
                <a-select-option :value="5">提交成功,待上传图片后即成功</a-select-option>
                <a-select-option :value="6">审核中,待处理</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格内容 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- 表体的操作 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'accountType'">
              <a-tag v-if="record.accountType === 'S'" color="pink">对私</a-tag>
              <a-tag v-else-if="record.accountType === 'G'" color="cyan">对公</a-tag>
            </template>

            <template v-if="column.key === 'changeChannelStatus'">
              <a-tag v-if="record.changeChannelStatus === 0" color="pink">未变更</a-tag>
              <a-tag v-else-if="record.changeChannelStatus === 1" color="blue">通道变更成功</a-tag>
              <a-tag v-else-if="record.changeChannelStatus === 2" color="cyan">通道变更驳回</a-tag>
              <a-tag v-else-if="record.changeChannelStatus === 3" color="purple">通道审核中</a-tag>
              <a-tag v-else-if="record.changeChannelStatus === 4" color="pink">提交成功,待上传图片后等待通知处理</a-tag>
              <a-tag v-else-if="record.changeChannelStatus === 5" color="blue">提交成功,待上传图片后即成功</a-tag>
              <a-tag v-else-if="record.changeChannelStatus === 6" color="cyan">审核中,待处理</a-tag>
            </template>

            <template v-if="column.key === 'changeAccountStatus'">
              <a-tag v-if="record.changeAccountStatus === 0" color="pink">未变更</a-tag>
              <a-tag v-else-if="record.changeAccountStatus === 1" color="blue">变更账户成功</a-tag>
              <a-tag v-else-if="record.changeAccountStatus === 2" color="cyan">变更账户失败</a-tag>
            </template>

            <template v-if="column.key === 'legalFlag'">
              <a-tag v-if="record.legalFlag === '0'" color="pink">否</a-tag>
              <a-tag v-else-if="record.legalFlag === '1'" color="cyan">是</a-tag>
            </template>

            <template v-if="column.key === 'changeSourceType'">
              <a-tag v-if="record.changeSourceType === 1" color="pink">主动变更</a-tag>
              <a-tag v-else-if="record.changeSourceType === 2" color="cyan">变更通知</a-tag>
            </template>

            <template v-if="column.key === 'cardType'">
              <a-tag v-if="record.cardType === 1" color="pink">借记卡</a-tag>
              <a-tag v-if="record.cardType === 2" color="blue">贷记卡</a-tag>
              <a-tag v-if="record.cardType === 3" color="cyan">准贷记卡</a-tag>
              <a-tag v-if="record.cardType === 4" color="purple">预付费卡</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-popconfirm title="确定要同步账户结算卡变更？" @confirm="handleEditAccountSettleCard(record)">
                  <a
                    v-show="record.changeAccountStatus === 2 && record.changeChannelStatus === 1">修改结算卡</a>
                </a-popconfirm>

                <a-divider v-show="record.changeAccountStatus === 2 && record.changeChannelStatus === 1" type="vertical" />
                <a @click="handleShowDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情页面 -->
    <AisleMerchPaymentcardChangeDetail v-model:visible="showDetail" :detail="current" />

  </div>
</template>

<script>
import { AisleMerchPaymentCardChangeApi } from '@/api/aisleManage/AisleMerchPaymentCardChangeApi';
import { message } from 'ant-design-vue';
import AisleMerchPaymentcardChangeDetail from './AisleMerchPaymentcardChangeDetail.vue';

export default {
  name: 'AisleMerchPaymentCardChange',
  components: {
    AisleMerchPaymentcardChangeDetail
  },
  data() {
    return {
      //表格查询条件
      where: {},
      //展示编辑页面传的参数
      current: null,
      //是否展示详情页面
      showDetail: false,

      //表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center'
        },
        {
          title: '商户ID',
          dataIndex: 'merchantId',
          align: 'center'
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '账户类型',
          dataIndex: 'accountType',
          key: 'accountType',
          align: 'center'
        },
        {
          title: '渠道商户编号',
          dataIndex: 'chnMerchNo',
          align: 'center'
        },
        {
          title: '渠道商户名称',
          dataIndex: 'chnMerchName',
          align: 'center'
        },
        {
          title: '渠道编码',
          dataIndex: 'channelCode',
          align: 'center'
        },
        {
          title: '通道名称',
          dataIndex: 'channelName',
          align: 'center'
        },
        {
          title: '通道编号',
          dataIndex: 'channelCode',
          align: 'center'
        },
        {
          title: '通道结算卡变更状态',
          dataIndex: 'changeChannelStatus',
          key: 'changeChannelStatus',
          align: 'center'
        },
        {
          title: '账户UUID',
          dataIndex: 'accountUuid',
          align: 'center'
        },

        {
          title: '账户结算卡变更状态',
          dataIndex: 'changeAccountStatus',
          key: 'changeAccountStatus',
          align: 'center'
        },
        {
          title: '变更来源',
          dataIndex: 'changeSourceType',
          key: 'changeSourceType',
          align: 'center'
        },
        {
          title: '是否法人结算',
          dataIndex: 'legalFlag',
          key: 'legalFlag',
          align: 'center'
        },
        {
          title: '变更后结算卡id',
          dataIndex: 'afterBankCardId',
          align: 'center'
        },
        {
          title: '变更后银行账号',
          dataIndex: 'bankAccountNoMask',
          align: 'center'
        },
        {
          title: '变更后卡类型',
          dataIndex: 'cardType',
          key: 'cardType',
          align: 'center'
        },
        {
          title: '变更后银行户名',
          dataIndex: 'bankAccountName',
          align: 'center'
        },
        {
          title: '变更后银行预留身份证号',
          dataIndex: 'idCardNoMask',
          align: 'center'
        },
        {
          title: '变更后银行预留手机号',
          dataIndex: 'mobileMask',
          align: 'center'
        },
        {
          title: '变更后开户行省编码',
          dataIndex: 'province',
          align: 'center'
        },
        {
          title: '变更后开户行市编码',
          dataIndex: 'city',
          align: 'center'
        },
        {
          title: '变更后联行号',
          dataIndex: 'bankChannelNo',
          align: 'center'
        },
        {
          title: '变更后银行名称',
          dataIndex: 'bankName',
          align: 'center'
        },
        {
          title: '变更后银行行别代码',
          dataIndex: 'typeCode',
          align: 'center'
        },
        {
          title: '变更后开户支行名称',
          dataIndex: 'bankBranch',
          align: 'center'
        },
        {
          title: '变更前结算卡id',
          dataIndex: 'beforeBankCardId',
          align: 'center'
        },
        {
          title: '证件到期日',
          dataIndex: 'certEndDate',
          align: 'center'
        },
        {
          title: '通道结算卡变更结果描述',
          dataIndex: 'channelRespMsg',
          align: 'center'
        },
        {
          title: '账户结算卡变更结果描述',
          dataIndex: 'accountRespMsg',
          align: 'center'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 150,
          align: 'center'
        }
      ]
    };
  },
  methods: {
    //查询方法
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    //重置
    reset() {
      this.where = {}; //清空查询条件

      this.$refs.table.reload({ page: 1, where: this.where });
    },

    //同步账户结算卡变更
    async handleEditAccountSettleCard(row) {
      // 修改加载框为正在加载
      this.loading = true;

      let result = AisleMerchPaymentCardChangeApi.editAccountSettleCard({ id : row.id });
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);
        })
        .catch(() => {
          this.loading = false;
        });
    },

    //展示详情
    handleShowDetail(row) {
      this.showDetail = true;
      this.current = row;
    },

    //获取数据方法
    datasource({ page, limit, where }) {
      return AisleMerchPaymentCardChangeApi.getMerchPaymentCardPage({
        ...where,
        pageNo: page,
        pageSize: limit
      });
    }
  }
};
</script>

<style></style>
