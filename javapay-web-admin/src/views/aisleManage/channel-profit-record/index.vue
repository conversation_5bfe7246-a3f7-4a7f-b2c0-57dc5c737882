<template>
  <div class="ele-body">
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="借贷标识">
              <a-select v-model:value="where.debitType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">加款</a-select-option>
                <a-select-option :value="2">减款</a-select-option>
                <a-select-option :value="3">不计</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="资金类型">
              <a-select v-model:value="where.recordType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option value="CREATE_AMOUNT">创建账户</a-select-option>
                <a-select-option value="ADJUST">调账</a-select-option>
                <a-select-option value="D_PROFIT_DEBIT">日结分润出账</a-select-option>
                <a-select-option value="D_PROFIT_CREDIT">日结分润入账</a-select-option>
                <a-select-option value="RECHARGE">充值</a-select-option>
                <a-select-option value="PAYMENTGOODS">货款</a-select-option>
                <a-select-option value="BACK_ACCOUNT">退回账户</a-select-option>
                <a-select-option value="SERVICEFEE">服务费</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="开始日期">
              <a-date-picker v-model:value="where.searchBeginTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item label="结束日期">
              <a-date-picker v-model:value="where.searchEndTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'debitType'">
              <a-tag v-if="record.debitType === 1" color="green">加款</a-tag>
              <a-tag v-else-if="record.debitType === 2" color="red">减款</a-tag>
              <a-tag v-else-if="record.debitType === 3">不记账</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <ChannelProfiRecordDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { ChannelProfiRecordApi } from '@/api/aisleManage/ChannelProfiRecordApi';
import ChannelProfiRecordDetail from './channel-profit-record-detail.vue';
import dayjs from 'dayjs';

export default {
  name: 'ChannelProfiRecord',
  components: {
    ChannelProfiRecordDetail
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          align: 'center',
          width: 80,
          fixed: 'left'
        },
        {
          title: '机构编号',
          dataIndex: 'agentNo',
          align: 'center'
        },
        {
          title: '资金类型',
          dataIndex: 'recordTypeDesc',
          align: 'center'
        },
        {
          title: '金额',
          dataIndex: 'amount',
          align: 'center'
        },
        {
          title: '借贷标识',
          dataIndex: 'debitType',
          key: 'debitType',
          align: 'center'
        },
        {
          title: '入账时间',
          dataIndex: 'accountTime',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 120,
          align: 'center'
        }
      ],
      // 表格搜索条件
      where: {
        searchBeginTime: dayjs().format('YYYY-MM-DD'),
        searchEndTime: dayjs().format('YYYY-MM-DD')
      },
      // 当前编辑数据
      current: null,
      // 是否显示编辑弹窗
      showDetail: false,
      channelCodes: []
    };
  },
  methods: {
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {
        searchBeginTime: dayjs().format('YYYY-MM-DD'),
        searchEndTime: dayjs().format('YYYY-MM-DD')
      };
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where, orders }) {
      return ChannelProfiRecordApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
