<template>
  <a-modal :width="800" :visible="visible" title="详情" :body-style="{ paddingBottom: '8px' }" @update:visible="updateVisible">
    <a-descriptions :column="2" bordered>
      <a-descriptions-item label="机构编号">{{ form.agentNo }}</a-descriptions-item>
      <a-descriptions-item label="资金类型">{{ form.recordTypeDesc }}</a-descriptions-item>
      <a-descriptions-item label="金额">{{ form.amount }}</a-descriptions-item>
      <a-descriptions-item label="借贷标识">{{ form.debitType }}</a-descriptions-item>
      <a-descriptions-item label="入账时间">{{ form.accountTime }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
