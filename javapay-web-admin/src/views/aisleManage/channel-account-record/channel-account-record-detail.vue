<template>
  <a-modal :width="800" :visible="visible" title="详情" :body-style="{ paddingBottom: '8px' }" @update:visible="updateVisible">
    <a-descriptions :column="2" bordered>
      <a-descriptions-item label="流水号">{{ form.flowNo }}</a-descriptions-item>
      <a-descriptions-item label="账户号">{{ form.accountNo }}</a-descriptions-item>
      <a-descriptions-item label="资金方向">
        <a-tag v-if="form.direction === 'INIT'" color="blue">初始化(创建)</a-tag>
        <a-tag v-else-if="form.direction === 'UNCHANGE'">不变</a-tag>
        <a-tag v-else-if="form.direction === 'INCREMENT'" color="green">资金变动方向增加</a-tag>
        <a-tag v-else-if="form.direction === 'DECREASE'" color="red">资金变动方向减少</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="金额">{{ form.amount }}</a-descriptions-item>
      <a-descriptions-item label="资金类型">
        <a-tag v-if="form.recordType === 'CREATE_AMOUNT'">创建账户</a-tag>
        <a-tag v-else-if="form.recordType === 'ADJUST'">调账</a-tag>
        <a-tag v-else-if="form.recordType === 'D_PROFIT_DEBIT'">日结分润出账</a-tag>
        <a-tag v-else-if="form.recordType === 'D_PROFIT_CREDIT'">日结分润入账</a-tag>
        <a-tag v-else-if="form.recordType === 'RECHARGE'">充值</a-tag>
        <a-tag v-else-if="form.recordType === 'PAYMENTGOODS'">货款</a-tag>
        <a-tag v-else-if="form.recordType === 'BACK_ACCOUNT'">退回账户</a-tag>
        <a-tag v-else-if="form.recordType === 'SERVICEFEE'">服务费</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="请求类型">
        <a-tag v-if="form.requestType === 'CREATE_AMOUNT'" color="blue">创建帐户</a-tag>
        <a-tag v-else-if="form.requestType === 'CREDIT'" color="green">资金入账</a-tag>
        <a-tag v-else-if="form.requestType === 'DEBIT'" color="pink">资金出账</a-tag>
        <a-tag v-else-if="form.requestType === 'FROZEN'" color="red">资金冻结</a-tag>
        <a-tag v-else-if="form.requestType === 'UNFROZEN'" color="purple">资金解冻,出账</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="当前可用余额">{{ form.avaliableBalance }}</a-descriptions-item>
      <a-descriptions-item label="发生后可用余额">{{ form.afterAvailableBalance }}</a-descriptions-item>
      <a-descriptions-item label="实际余额">{{ form.balance }}</a-descriptions-item>
      <a-descriptions-item label="发生后实际余额">{{ form.afterBalance }}</a-descriptions-item>
      <a-descriptions-item label="冻结金额">{{ form.frozenQuota }}</a-descriptions-item>
      <a-descriptions-item label="交易发生后冻结金额">{{ form.afterFrozenQuota }}</a-descriptions-item>
      <a-descriptions-item label="备注">{{ form.remark }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
