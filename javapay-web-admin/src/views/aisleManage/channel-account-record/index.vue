<template>
  <div class="ele-body">
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="开始日期">
              <a-date-picker v-model:value="where.searchBeginTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item label="结束日期">
              <a-date-picker v-model:value="where.searchEndTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #toolbar>
            <a-descriptions
              :title="`余额信息${summary.updateTime ? '(更新时间: ' + summary.updateTime + ')' : ''}`"
              :column="4"
              size="small"
            >
              <a-descriptions-item label="当前可用余额(元)">{{ summary.availableBalance }}</a-descriptions-item>
              <a-descriptions-item label="实际余额(元)">{{ summary.balance }}</a-descriptions-item>
              <a-descriptions-item label="冻结金额(元)">{{ summary.frozenAmount }}</a-descriptions-item>
            </a-descriptions>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'direction'">
              <a-tag v-if="record.direction === 'INIT'" color="blue">初始化(创建)</a-tag>
              <a-tag v-else-if="record.direction === 'UNCHANGE'">不变</a-tag>
              <a-tag v-else-if="record.direction === 'INCREMENT'" color="green">资金变动方向增加</a-tag>
              <a-tag v-else-if="record.direction === 'DECREASE'" color="red">资金变动方向减少</a-tag>
            </template>

            <template v-else-if="column.key === 'requestType'">
              <a-tag v-if="record.requestType === 'CREATE_AMOUNT'" color="blue">创建帐户</a-tag>
              <a-tag v-else-if="record.requestType === 'CREDIT'" color="green">资金入账</a-tag>
              <a-tag v-else-if="record.requestType === 'DEBIT'" color="pink">资金出账</a-tag>
              <a-tag v-else-if="record.requestType === 'FROZEN'" color="red">资金冻结</a-tag>
              <a-tag v-else-if="record.requestType === 'UNFROZEN'" color="purple">资金解冻,出账</a-tag>
            </template>

            <template v-else-if="column.key === 'recordType'">
              <a-tag v-if="record.recordType === 'CREATE_AMOUNT'">创建账户</a-tag>
              <a-tag v-else-if="record.recordType === 'ADJUST'">调账</a-tag>
              <a-tag v-else-if="record.recordType === 'D_PROFIT_DEBIT'">日结分润出账</a-tag>
              <a-tag v-else-if="record.recordType === 'D_PROFIT_CREDIT'">日结分润入账</a-tag>
              <a-tag v-else-if="record.recordType === 'RECHARGE'">充值</a-tag>
              <a-tag v-else-if="record.recordType === 'PAYMENTGOODS'">货款</a-tag>
              <a-tag v-else-if="record.recordType === 'BACK_ACCOUNT'">退回账户</a-tag>
              <a-tag v-else-if="record.recordType === 'SERVICEFEE'">服务费</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <ChannelAccountRecordDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { ChannelAccountRecordApi } from '@/api/aisleManage/ChannelAccountRecordApi';
import ChannelAccountRecordDetail from './channel-account-record-detail.vue';
import dayjs from 'dayjs';

export default {
  name: 'ChannelAccountRecord',
  components: {
    ChannelAccountRecordDetail
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          align: 'center',
          width: 80,
          fixed: 'left'
        },
        {
          title: '流水号',
          dataIndex: 'flowNo',
          align: 'center'
        },
        {
          title: '账户号',
          dataIndex: 'accountNo',
          align: 'center'
        },
        {
          title: '资金方向',
          dataIndex: 'direction',
          key: 'direction',
          align: 'center'
        },
        {
          title: '金额',
          dataIndex: 'amount',
          align: 'center'
        },
        {
          title: '资金类型',
          dataIndex: 'recordType',
          key: 'recordType',
          align: 'center'
        },
        {
          title: '请求类型',
          dataIndex: 'requestType',
          key: 'requestType',
          align: 'center'
        },
        {
          title: '当前可用余额',
          dataIndex: 'avaliableBalance',
          align: 'center'
        },
        {
          title: '发生后可用余额',
          dataIndex: 'afterAvailableBalance',
          align: 'center'
        },
        {
          title: '实际余额',
          dataIndex: 'balance',
          align: 'center'
        },
        {
          title: '发生后实际余额',
          dataIndex: 'afterBalance',
          align: 'center'
        },
        {
          title: '冻结金额',
          dataIndex: 'frozenQuota',
          align: 'center'
        },
        {
          title: '交易发生后冻结金额',
          dataIndex: 'afterFrozenQuota',
          align: 'center'
        },
        {
          title: '备注',
          dataIndex: 'remark',
          width: 200
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 120,
          align: 'center'
        }
      ],
      // 表格搜索条件
      where: {
        searchBeginTime: dayjs().format('YYYY-MM-DD'),
        searchEndTime: dayjs().format('YYYY-MM-DD')
      },
      // 当前编辑数据
      current: null,
      // 是否显示编辑弹窗
      showDetail: false,
      channelCodes: [],
      summary: {}
    };
  },
  mounted() {
    this.getSummaryData();
  },
  methods: {
    reload() {
      this.$refs.table.reload({ page: 1 });
      this.getSummaryData();
    },

    reset() {
      this.where = {
        searchBeginTime: dayjs().format('YYYY-MM-DD'),
        searchEndTime: dayjs().format('YYYY-MM-DD')
      };
      this.$refs.table.reload({ page: 1, where: this.where });
      this.getSummaryData();
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    async getSummaryData() {
      this.summary = (await ChannelAccountRecordApi.walletAmtQuery(this.where)) || {};
    },

    datasource({ page, limit, where, orders }) {
      return ChannelAccountRecordApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
