<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="变更序号">
              <a-input v-model:value.trim="where.changeBatchNo" placeholder="请输入变更序号" allow-clear />
            </a-form-item>
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="通道商户编号">
              <a-input v-model:value.trim="where.chnMerchNo" placeholder="请输入通道商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="费率编号">
              <a-input v-model:value.trim="where.templateNo" placeholder="请输入费率编号" allow-clear />
            </a-form-item>
            <a-form-item label="通道名称">
              <a-select v-model:value="where.channelCode" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
                  {{ channelName }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="费率类型">
              <a-select v-model:value="where.rateType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">线下收单费率</a-select-option>
                <a-select-option :value="3">EPOS费率</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="平台变更状态">
              <a-select v-model:value="where.changeStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">是</a-select-option>
                <a-select-option :value="0">否</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="通道变更状态">
              <a-select v-model:value="where.chnChangeStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">待修改</a-select-option>
                <a-select-option :value="2">审核中</a-select-option>
                <a-select-option :value="3">修改成功</a-select-option>
                <a-select-option :value="4">修改失败</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #toolbar>
            <a-space>
              <a-button @click="showCancelEditByBatchNo = true">
                <template #icon>
                  <edit-outlined />
                </template>
                <span>取消修改</span>
              </a-button>
            </a-space>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'changeStatus'">
              <a-tag color="success" v-if="record.changeStatus === 1">是</a-tag>
              <a-tag v-else>否</a-tag>
            </template>

            <template v-else-if="column.key === 'chnChangeStatus'">
              <a-tag color="warning" v-if="record.chnChangeStatus === 1">待修改</a-tag>
              <a-tag color="processing" v-else-if="record.chnChangeStatus === 2">审核中</a-tag>
              <a-tag color="success" v-else-if="record.chnChangeStatus === 3">修改成功</a-tag>
              <a-tag color="error" v-else-if="record.chnChangeStatus === 4">修改失败</a-tag>
            </template>

            <template v-else-if="column.key === 'rateType'">
              <a-tag color="pink" v-if="record.rateType === 1">线下收单费率</a-tag>
              <a-tag color="cyan" v-else-if="record.rateType === 2">刷卡费率</a-tag>
              <a-tag color="blue" v-else-if="record.rateType === 3">EPOS费率</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
                <a v-if="record.chnChangeStatus === 1" @click="handleCancelEdit(record)">取消修改</a>
                <!-- <a-divider type="vertical" />
                <a @click="handleEdit(record)">修改</a> -->
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 修改 -->
    <AisleMerchRateChangeEdit v-if="showEdit" v-model:visible="showEdit" :data="current" @done="reload" />

    <!-- 取消修改 -->
    <CancelEditByBatchNo v-if="showCancelEditByBatchNo" v-model:visible="showCancelEditByBatchNo" @done="reload" />

    <!-- 详情 -->
    <AisleMerchRateChangeDetail v-model:visible="showDetail" :detail="current" :bankList="bankList" />
  </div>
</template>

<script>
import { AisleMerchRateChangeApi } from '@/api/aisleManage/AisleMerchRateChangeApi';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import { RateTemplateApi } from '@/api/businessTeam/rate-template/RateTemplateApi';
import AisleMerchRateChangeEdit from './AisleMerchRateChangeEdit.vue';
import CancelEditByBatchNo from './CancelEditByBatchNo.vue';
import AisleMerchRateChangeDetail from './AisleMerchRateChangeDetail.vue';
import { BankCodeManageApi } from '@/api/base/BankCodeManageApi';
import { message, Modal } from 'ant-design-vue';
import { createVNode } from 'vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';

export default {
  name: 'AisleMerchRateChange',
  components: {
    AisleMerchRateChangeEdit,
    AisleMerchRateChangeDetail,
    CancelEditByBatchNo
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '通道商户编号',
          dataIndex: 'chnMerchNo',
          align: 'center'
        },
        {
          title: '通道名称',
          dataIndex: 'channelCode',
          align: 'center',
          customRender: ({ text }) => {
            const item = this.channelCodes.find(c => c.channelCode === text);
            return item?.channelName || '--';
          }
        },
        {
          title: '费率编号',
          dataIndex: 'templateNo',
          align: 'center'
        },
        {
          title: '费率类型',
          dataIndex: 'rateType',
          key: 'rateType',
          align: 'center'
        },
        {
          title: '政策编号',
          dataIndex: 'policyNo',
          align: 'center'
        },
        {
          title: '通道商户费率ID',
          dataIndex: 'chnMerchRateId',
          align: 'center'
        },
        {
          title: '变更序号',
          dataIndex: 'changeBatchNo',
          align: 'center'
        },
        {
          title: '通道变更流水号',
          dataIndex: 'chnChangeFlowNo',
          align: 'center'
        },
        {
          title: '平台变更状态',
          dataIndex: 'changeStatus',
          key: 'changeStatus',
          align: 'center'
        },
        {
          title: '平台变更描述',
          dataIndex: 'changeDesc',
          width: 200
        },
        {
          title: '通道变更状态',
          dataIndex: 'chnChangeStatus',
          key: 'chnChangeStatus',
          align: 'center'
        },
        {
          title: '通道变更描述',
          dataIndex: 'chnChangeDesc',
          width: 200
        },
        {
          title: '版本号',
          dataIndex: 'version',
          align: 'center'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '修改人',
          dataIndex: 'lastModifyUserId'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          width: 160,
          align: 'center',
          fixed: 'right'
        }
      ],
      // 表格搜索条件
      where: {},
      current: null,
      showEdit: false,
      showDetail: false,
      showCancelEditByBatchNo: false,
      channelCodes: [],
      templateNos: [],
      bankList: []
    };
  },
  mounted() {
    this.getChannelCodes();
    this.getBankList();
  },
  methods: {
    async getTemplateNos() {
      const data = await RateTemplateApi.list({ validStatus: 1 });
      this.templateNos = data;
    },

    async getChannelCodes() {
      const data = await ChannelManageApi.list({ validStatus: 1 });
      this.channelCodes = data || [];
    },

    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleCancelEdit(row) {
      Modal.confirm({
        title: '提示',
        content: '确定要取消修改此记录吗?',
        icon: createVNode(ExclamationCircleOutlined),
        maskClosable: true,
        onOk: async () => {
          const result = await AisleMerchRateChangeApi.edit({ id: row.id});
          message.success(result.message);
          this.reload();
        }
      });
    },

    handleEdit(row) {
      this.current = row || {};
      this.showEdit = true;
    },

    handleDetail(row) {
      this.current = row || {};
      const item = this.channelCodes.find(c => c.channelCode === row.channelCode);
      this.current.channelName = item?.channelName;
      this.showDetail = true;
    },

    async getBankList() {
      const data = await BankCodeManageApi.list();
      this.bankList = data || [];
    },

    async datasource({ page, limit, where }) {
      const tableData = await AisleMerchRateChangeApi.findPage({ ...where, pageNo: page, pageSize: limit });
      return tableData || [];
    }
  }
};
</script>
