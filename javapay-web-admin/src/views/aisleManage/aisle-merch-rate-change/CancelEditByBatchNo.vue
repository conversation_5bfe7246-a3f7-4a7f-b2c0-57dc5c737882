<template>
  <a-modal
    :width="600"
    :visible="visible"
    :confirm-loading="loading"
    title="取消修改"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 4 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 20 }, sm: { span: 24 } }"
    >
      <a-form-item label="变更序号:" name="changeBatchNo">
        <a-input v-model:value="form.changeBatchNo" placeholder="请输入变更序号" allow-clear />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { AisleMerchRateChangeApi } from '@/api/aisleManage/AisleMerchRateChangeApi';
import { message } from 'ant-design-vue';
export default {
  props: {
    // 弹窗是否打开
    visible: Boolean
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      form: {},
      rules: {
        changeBatchNo: [{ required: true, message: '请输入变更序号' }]
      },
      // 提交状态
      loading: false
    };
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;
      let result = AisleMerchRateChangeApi.edit(this.form);
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示编辑成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    /**
     * 更新编辑界面的弹框是否显示
     *
     * @param value true-显示，false-隐藏
     * <AUTHOR>
     * @date 2022/11/03 17:53
     */
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>

<style></style>
