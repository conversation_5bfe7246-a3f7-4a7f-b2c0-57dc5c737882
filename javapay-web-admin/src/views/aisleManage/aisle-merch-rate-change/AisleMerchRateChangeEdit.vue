<template>
  <a-modal
    :width="600"
    :visible="visible"
    :confirm-loading="loading"
    title="修改"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :label-col="{ style: { width: '110px' } }">
      <a-form-item label="变更序号">
        <a-input :value="form.changeBatchNo" disabled />
      </a-form-item>
      <a-form-item label="平台变更状态" name="changeStatus" :rules="[{ required: true, message: '请选择平台变更状态' }]">
        <a-select v-model:value="form.changeStatus" class="ele-fluid" placeholder="请选择">
          <a-select-option :value="1">是</a-select-option>
          <a-select-option :value="0">否</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="平台变更描述" name="changeDesc">
        <a-textarea v-model:value="form.changeDesc" placeholder="请输入平台变更描述" :auto-size="{ minRows: 2, maxRows: 5 }" />
      </a-form-item>
      <a-form-item label="通道变更状态" name="chnChangeStatus" :rules="[{ required: true, message: '请选择通道变更状态' }]">
        <a-select v-model:value="form.chnChangeStatus" class="ele-fluid" placeholder="请选择">
          <a-select-option :value="1">待修改</a-select-option>
          <a-select-option :value="2">审核中</a-select-option>
          <a-select-option :value="3">修改成功</a-select-option>
          <a-select-option :value="4">修改失败</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="通道变更描述" name="chnChangeDesc">
        <a-textarea v-model:value="form.chnChangeDesc" placeholder="请输入通道变更描述" :auto-size="{ minRows: 2, maxRows: 5 }" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { AisleMerchRateChangeApi } from '@/api/aisleManage/AisleMerchRateChangeApi';

export default {
  props: {
    visible: Boolean,
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {},
      // 提交状态
      loading: false
    };
  },
  created() {
    if (this.data) {
      this.form = Object.assign({}, this.data);
    }
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      AisleMerchRateChangeApi.edit(this.form)
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示修改成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
