<template>
  <a-modal
    title="详情"
    :width="1000"
    :visible="visible"
    :body-style="{ paddingBottom: '8px' }"
    :mask-closable="false"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="商户编号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="通道商户编号">{{ form.chnMerchNo }}</a-descriptions-item>
      <a-descriptions-item label="通道名称">{{ form.channelName }}</a-descriptions-item>
      <a-descriptions-item label="费率编号">{{ form.templateNo }}</a-descriptions-item>
      <a-descriptions-item label="费率类型">
        <a-tag color="pink" v-if="form.rateType === 1">线下收单费率</a-tag>
        <a-tag color="cyan" v-else-if="form.rateType === 2">刷卡费率</a-tag>
        <a-tag color="blue" v-else-if="form.rateType === 3">EPOS费率</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="政策编号">{{ form.policyNo }}</a-descriptions-item>
      <a-descriptions-item label="平台变更状态">
        <a-tag color="success" v-if="form.changeStatus === 1">是</a-tag>
        <a-tag v-else>否</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="平台变更描述">{{ form.changeDesc }}</a-descriptions-item>
      <a-descriptions-item label="通道变更状态">
        <a-tag color="warning" v-if="form.chnChangeStatus === 1">待修改</a-tag>
        <a-tag color="processing" v-else-if="form.chnChangeStatus === 2">审核中</a-tag>
        <a-tag color="success" v-else-if="form.chnChangeStatus === 3">修改成功</a-tag>
        <a-tag color="error" v-else-if="form.chnChangeStatus === 4">修改失败</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="通道变更描述">{{ form.chnChangeDesc }}</a-descriptions-item>
      <a-descriptions-item label="通道商户费率ID">{{ form.chnMerchRateId }}</a-descriptions-item>
      <a-descriptions-item label="通道变更流水号">{{ form.chnChangeFlowNo }}</a-descriptions-item>
      <a-descriptions-item label="变更序号">{{ form.changeBatchNo }}</a-descriptions-item>
      <a-descriptions-item label="版本号">{{ form.version }}</a-descriptions-item>
      <a-descriptions-item label="创建人">{{ form.createUserId }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="修改人">{{ form.lastModifyUserId }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>
    <a-collapse :active-key="['1']">
      <a-collapse-panel key="1" header="费率变更详情 (左侧为新内容, 右侧为旧内容)">
        <a-form :label-col="{ md: { span: 6 }, sm: { span: 24 } }" :wrapper-col="{ md: { span: 18 }, sm: { span: 24 } }" :colon="false">
          <a-form-item
            :class="{
              'info-changed': isInfoChanged(form.rateInfo[infoKey], form.oldRateInfo[infoKey]),
              'hide-label': infoKey === 'divider'
            }"
            v-for="({ label, infoKey, formItemType }, keyi) in rateInfoMap[form.rateInfo.rateType || 1]"
            :key="keyi"
            :label="label"
          >
            <template v-if="!formItemType && infoKey !== 'divider'">
              <a-input v-for="(item, keyc) in [form.rateInfo, form.oldRateInfo]" :key="keyc" :value="item[infoKey]" readonly />
            </template>
            <template v-if="formItemType === 'select'">
              <a-select
                v-for="(item, keyc) in [form.rateInfo, form.oldRateInfo]"
                :key="keyc"
                :value="item[infoKey] ? item[infoKey].split(',') : []"
                mode="multiple"
                style="width: 100%"
                placeholder=""
                disabled
              >
                <a-select-option v-for="({ bankName, typeCode }, keys) in bankList" :key="keys" :value="typeCode"
                >{{ bankName }}
                </a-select-option>
              </a-select>
            </template>
            <a-divider v-if="infoKey === 'divider'" style="height: 100%" orientation="left" orientationMargin="0" dashed>{{
              label
            }}</a-divider>
          </a-form-item>
        </a-form>
      </a-collapse-panel>
    </a-collapse>

    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

// 扫码相关的字段
const allScanRateFieldsMap = [
  { field: 'creditRate', name: '银联标准贷记卡费率(%)' },
  { field: 'debitRate', name: '银联标准借记卡费率(%)' },
  { field: 'debitFeeMax', name: '银联标准借记卡手续费封顶(元)' },
  { field: 'nfcCreditRate', name: '银联云闪付贷记卡费率(%)' },
  { field: 'nfcDebitRate', name: '银联云闪付借记卡费率(%)' },
  { field: 'aliPayRate', name: '支付宝扫码费率(%)' },
  { field: 'aliPayLargeRate', name: '支付宝大额费率(%)' },
  { field: 'wechatRate', name: '微信扫码费率(%)' },
  { field: 'creditQrD0Rate', name: '贷记卡D0附加费率(%)' },
  { field: 'creditQrD0SingleFee', name: '贷记卡D0附加单笔(元)' },
  { field: 'debitQrD0Rate', name: '借记卡D0附加费率(%)' },
  { field: 'debitQrD0SingleFee', name: '借记卡D0附加单笔(元)' }
];

export default {
  props: {
    visible: Boolean,
    detail: Object,
    bankList: Array
  },
  emits: ['update:visible'],
  setup(props, context) {
    // 费率信息
    const rateInfoMap = {
      1: [],
      2: [
        { label: 'POS收单交易', infoKey: 'divider' },
        { label: '银联标准贷记卡费率(%)', infoKey: 'creditRate' },
        { label: '银联标准借记卡费率(%)', infoKey: 'debitRate' },
        { label: '银联标准借记卡手续费封顶值(元)', infoKey: 'debitFeeMax' },
        { label: '银联云闪付贷记卡费率(%)', infoKey: 'nfcCreditRate' },
        { label: '银联云闪付借记卡费率(%)', infoKey: 'nfcDebitRate' },
        { label: '刷卡贷记卡D0附加费率(%)', infoKey: 'creditD0Rate' },
        { label: '刷卡借记卡D0附加费率(%)', infoKey: 'debitD0Rate' },
        { label: '刷卡贷记卡D0附加单笔费用(元)', infoKey: 'creditD0SingleFee' },
        { label: '刷卡借记卡D0附加单笔费用(元)', infoKey: 'debitD0SingleFee' }
      ],
      3: [
        { label: '无卡交易', infoKey: 'divider' },
        { label: '无卡贷记卡费率(%)', infoKey: 'creditEposRate' },
        { label: '无卡贷记卡D0费率(%)', infoKey: 'creditEposD0Rate' },
        { label: '无卡借记卡D0费率(%)', infoKey: 'debitEposD0Rate' },
        { label: '无卡借记卡费率(%)', infoKey: 'debitEposRate' },
        { label: '无卡贷记卡D0附加单笔费用(元)', infoKey: 'creditEposD0SingleFee' },
        { label: '无卡借记卡D0附加单笔费用(元)', infoKey: 'debitEposD0SingleFee' },
        { label: '支持的银行', infoKey: 'bankCodes', formItemType: 'select' }
      ]
    };

    // 设置表单默认值
    function formDefaults() {
      return {
        rateInfo: {},
        oldRateInfo: {}
      };
    }
    const data = reactive({
      form: formDefaults()
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = formDefaults();

        Object.keys(data.form).forEach(k => {
          data.form[k] = props.detail[k]
            ? typeof props.detail[k] === 'string'
              ? JSON.parse(props.detail[k])
              : props.detail[k]
            : data.form[k];
        });

        data.form = Object.assign(props.detail, data.form);

        data.form.rateInfo.bankCodes = data.form.bankCodes;
        data.form.oldRateInfo.bankCodes = data.form.oldBankCodes;

        data.form.rateInfo.rateType = data.form.rateType;

        const rateKeys = Object.keys(data.form.rateInfo || {});
        const targetScanRateList = allScanRateFieldsMap.filter(item => rateKeys.includes(item.field));
        const scanRateList = targetScanRateList.map(item => {
          return {
            label: item.name,
            infoKey: item.field
          };
        });
        rateInfoMap[1] = [{ label: '线下收单', infoKey: 'divider' }, ...scanRateList];
      }
    });

    const isInfoChanged = (current, old) => {
      if ([current, old].every(i => i !== 0 && !i)) return false;
      return current !== old;
    };

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible,
      rateInfoMap,
      isInfoChanged
    };
  }
};
</script>

<style lang="less" scoped>
:deep(.ant-form-item-label) {
  text-align: center;
  background-color: #f1f3f4;
}
.info-changed {
  :deep(.ant-form-item-label) {
    background-color: #fa541cc9;
  }
}
.hide-label {
  :deep(.ant-form-item-label) {
    display: none;
  }
  .ant-divider-horizontal.ant-divider-with-text {
    margin: 0;
  }
}
:deep(.ant-row) {
  align-items: flex-start;
  .ant-form-item-control-input-content {
    display: flex;
    .ant-input,
    .ant-select-selector {
      border-radius: 0;
    }
  }
}
:deep(.ant-select-disabled.ant-select:not(.ant-select-customize-input)) .ant-select-selector {
  background-color: transparent;
}
</style>
