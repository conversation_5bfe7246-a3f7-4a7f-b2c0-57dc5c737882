<template>
  <a-modal
    :width="1000"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新增'"
    :body-style="{ paddingBottom: '8px' }"
    :mask-closable="false"
    @update:visible="updateVisible"
    @cancel="cancel"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 7 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 17 }, sm: { span: 24 } }"
    >
      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="支付通道" name="channelCode">
            <a-select v-model:value="form.channelCode" class="ele-fluid" placeholder="请选择" allow-clear :disabled="isUpdate">
              <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">{{
                channelName
              }}</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="MCC编码" name="mcc">
            <a-input v-model:value="form.mcc" placeholder="请输入MCC编码" allow-clear />
          </a-form-item>
          <a-form-item label="MCC大类" name="mccBigClass">
            <a-input v-model:value="form.mccBigClass" placeholder="请输入MCC大类" allow-clear />
          </a-form-item>
          <a-form-item label="是否常用" name="frequentlyUsed">
            <a-select v-model:value="form.frequentlyUsed" style="width: 100%" placeholder="请选择" allow-clear>
              <a-select-option :value="1">是</a-select-option>
              <a-select-option :value="0">否</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="编码类型" name="codeType">
            <a-input v-model:value="form.codeType" placeholder="请输入编码类型" allow-clear />
          </a-form-item>
          <a-form-item label="商户类别与描述" name="mccName">
            <a-input v-model:value="form.mccName" placeholder="请输入商户类别与描述" allow-clear />
          </a-form-item>
          <a-form-item label="MCC小类" name="mccSmallClass">
            <a-input v-model:value="form.mccSmallClass" placeholder="请输入MCC小类" allow-clear />
          </a-form-item>
          <a-form-item label="状态" name="status">
            <a-select v-model:value="form.status" style="width: 100%" placeholder="请选择" allow-clear>
              <a-select-option :value="1">有效</a-select-option>
              <a-select-option :value="0">无效</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { AisleMccApi } from '@/api/aisleManage/AisleMccApi';

function formDefaults() {
  return {
    status: 1
  };
}

export default {
  name: 'AisleMccCodeEdit',
  props: {
    visible: Boolean,
    data: Object,
    channelCodes: Array
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: Object.assign({}, this.data),
      // 表单验证规则
      rules: {
        channelCode: [{ required: true, message: '请选择通道' }],
        codeType: [{ required: true, message: '请输入编码类型' }],
        mcc: [{ required: true, message: '请输入MCC编码' }],
        mccBigClass: [{ required: true, message: '请输入MCC大类' }],
        mccName: [{ required: true, message: '请输入商户类别与描述' }],
        mccSmallClass: [{ required: true, message: '请输入MCC小类' }],
        status: [{ required: true, message: '请选择状态' }],
        frequentlyUsed: [{ required: true, message: '请选择是否常用' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false
    };
  },
  watch: {
    data() {
      if (this.data) {
        this.form = Object.assign({}, this.data);
        this.isUpdate = true;
      } else {
        this.form = formDefaults();
        this.isUpdate = false;
      }
      if (this.$refs.form) {
        this.$refs.form.clearValidate();
      }
    }
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      // 执行编辑或修改方法
      if (this.isUpdate) {
        result = AisleMccApi.edit(this.form);
      } else {
        result = AisleMccApi.add(this.form);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 如果是新增，则form表单置空
          if (!this.isUpdate) {
            this.form = formDefaults();
          }

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    cancel() {
      this.form = Object.assign(formDefaults(), this.data);
      this.$refs.form.clearValidate();
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
