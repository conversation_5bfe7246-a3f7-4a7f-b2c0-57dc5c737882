<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="MCC编码">
              <a-input v-model:value.trim="where.mcc" placeholder="请输入MCC编码" allow-clear />
            </a-form-item>
            <a-form-item label="MCC大类">
              <a-input v-model:value.trim="where.mccBigClass" placeholder="请输入MCC大类" allow-clear />
            </a-form-item>
            <a-form-item label="MCC小类">
              <a-input v-model:value.trim="where.mccSmallClass" placeholder="请输入MCC小类" allow-clear />
            </a-form-item>
            <a-form-item label="商户类别与描述">
              <a-input v-model:value.trim="where.mccName" placeholder="请输入商户类别与描述" allow-clear />
            </a-form-item>
            <a-form-item label="支付通道">
              <a-select v-model:value="where.channelCode" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
                  {{ channelName }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="编码类型">
              <a-input v-model:value.trim="where.codeType" placeholder="请输入编码类型" allow-clear />
            </a-form-item>
            <a-form-item label="是否常用">
              <a-select v-model:value="where.frequentlyUsed" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">是</a-select-option>
                <a-select-option :value="0">否</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="有效状态">
              <a-select v-model:value="where.status" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">有效</a-select-option>
                <a-select-option :value="0">无效</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table
          ref="table"
          row-key="id"
          :datasource="datasource"
          :columns="columns"
          :where="where"
          v-model:selection="selection"
          :scroll="{ x: 'max-content' }"
        >
          <!-- table上边工具栏 -->
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
              <a-button danger @click="removeBatch">
                <template #icon>
                  <delete-outlined />
                </template>
                <span>删除</span>
              </a-button>
            </a-space>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'frequentlyUsed'">
              <a-tag v-if="record.frequentlyUsed === 1">是</a-tag>
              <a-tag v-else>否</a-tag>
            </template>

            <template v-if="column.key === 'status'">
              <a-tag color="success" v-if="record.status === 1">有效</a-tag>
              <a-tag color="error" v-else>无效</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleEdit(record)">修改</a>
                <a-divider type="vertical" />
                <a-popconfirm title="确定要删除此行数据吗？" @confirm="remove(record)">
                  <a class="ele-text-danger">删除</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 编辑 -->
    <AisleMccEdit v-model:visible="showEdit" :data="current" :channelCodes="channelCodes" @done="reload" />
  </div>
</template>

<script>
import { message, Modal } from 'ant-design-vue';
import { createVNode } from 'vue';
import { AisleMccApi } from '@/api/aisleManage/AisleMccApi';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import AisleMccEdit from './AisleMccEdit.vue';
import { PlusOutlined, DeleteOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue';

export default {
  name: 'AisleMccCode',
  components: {
    AisleMccEdit,
    PlusOutlined,
    DeleteOutlined
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '支付通道',
          dataIndex: 'channelCode',
          align: 'center',
          customRender: ({ text }) => {
            const item = this.channelCodes.find(c => c.channelCode === text);
            return item?.channelName || '--';
          }
        },
        {
          title: '编码类型',
          dataIndex: 'codeType',
          align: 'center',
          width: 120
        },
        {
          title: 'MCC编码',
          dataIndex: 'mcc',
          align: 'center',
          width: 120
        },
        {
          title: 'MCC大类',
          dataIndex: 'mccBigClass',
          align: 'center'
        },
        {
          title: '商户类别与描述',
          dataIndex: 'mccName'
        },
        {
          title: 'MCC小类',
          dataIndex: 'mccSmallClass',
          align: 'center'
        },
        {
          title: '是否常用',
          dataIndex: 'frequentlyUsed',
          key: 'frequentlyUsed',
          align: 'center',
          width: 120
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          align: 'center',
          width: 120
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right',
          width: 150
        }
      ],
      // 表格搜索条件
      where: {},
      selection: [],
      current: null,
      showEdit: false,
      channelCodes: []
    };
  },
  async mounted() {
    const data = await ChannelManageApi.list({ validStatus: 1 });
    this.channelCodes = data || [];
  },
  methods: {
    reload() {
      this.selection = [];
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.selection = [];
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    async remove(row) {
      const result = await AisleMccApi.delete({ id: row.id });
      message.success(result.message);
      this.reload();
    },

    removeBatch() {
      if (!this.selection.length) {
        message.error('请至少选择一条数据');
        return;
      }
      Modal.confirm({
        title: '提示',
        content: '确定要删除选中的数据吗?',
        icon: createVNode(ExclamationCircleOutlined),
        maskClosable: true,
        onOk: async () => {
          let params = this.selection.map(d => d.id);
          const result = await AisleMccApi.batchDelete({ ids: params });
          message.success(result.message);
          this.reload();
        }
      });
    },

    datasource({ page, limit, where, orders }) {
      return AisleMccApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
