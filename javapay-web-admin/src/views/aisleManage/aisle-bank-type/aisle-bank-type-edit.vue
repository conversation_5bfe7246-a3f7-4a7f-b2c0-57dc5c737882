<template>
  <a-modal
    :width="550"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新增'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 4 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 20 }, sm: { span: 24 } }"
    >
      <a-form-item label="支付通道" name="channelCode">
        <a-select v-model:value="form.channelCode" class="ele-fluid" placeholder="请选择" allow-clear :disabled="isUpdate">
          <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
            {{ channelName }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="总行编码" name="bankCode">
        <a-input v-model:value="form.bankCode" placeholder="请输入总行编码" allow-clear />
      </a-form-item>
      <a-form-item label="总行名称" name="bankName">
        <a-input v-model:value="form.bankName" placeholder="请输入总行名称" allow-clear />
      </a-form-item>
      <a-form-item label="总行简称" name="bankShort">
        <a-input v-model:value="form.bankShort" placeholder="请输入总行简称" allow-clear />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { AisleBankTypeApi } from '@/api/aisleManage/AisleBankTypeApi';

export default {
  props: {
    visible: Boolean,
    data: Object,
    channelCodes: Array
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {},
      // 表单验证规则
      rules: {
        bankCode: [{ required: true, message: '请输入总行编码' }],
        bankName: [{ required: true, message: '请输入总行名称' }],
        bankShort: [{ required: true, message: '请输入总行简称' }],
        channelCode: [{ required: true, message: '请选择支付通道' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false
    };
  },
  created() {
    if (this.data) {
      this.form = Object.assign({}, this.data);
      this.isUpdate = true;
    }
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      // 执行编辑或修改方法
      if (this.isUpdate) {
        result = AisleBankTypeApi.edit(this.form);
      } else {
        result = AisleBankTypeApi.add(this.form);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
