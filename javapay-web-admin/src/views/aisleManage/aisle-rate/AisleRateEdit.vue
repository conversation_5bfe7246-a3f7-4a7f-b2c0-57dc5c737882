<template>
  <a-modal
    :width="760"
    :visible="visible"
    :confirm-loading="loading"
    :title="modalTitle"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :class="{ 'ele-form-detail': optType === 2 }" :label-col="{ style: { width: '110px' } }">
      <a-row :gutter="24">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="支付通道" name="channelCode" :rules="[{ required: true, message: '请选择支付通道' }]">
            <a-select
              v-model:value="form.channelCode"
              class="ele-fluid"
              placeholder="请选择"
              allow-clear
              :disabled="optType !== 0"
              @change="onChannelChange"
            >
              <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
                {{ channelName }}
              </a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="有效状态" name="validStatus" required>
            <a-switch :checkedValue="1" :un-checked-value="0" v-model:checked="form.validStatus" :disabled="isDisabled">
              <template #checkedChildren><check-outlined /></template>
              <template #unCheckedChildren><close-outlined /></template>
            </a-switch>
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="费率类型" name="rateType" :rules="[{ required: true, message: '请选择费率类型' }]">
            <a-select
              v-model:value="form.rateType"
              class="ele-fluid"
              placeholder="请选择"
              :disabled="optType !== 0"
              allow-clear
              @change="onChangeRateType"
            >
              <a-select-option :value="1">扫码费率</a-select-option>
              <a-select-option :value="2">刷卡费率</a-select-option>
              <a-select-option :value="3">EPOS费率</a-select-option>
              <a-select-option :value="4">机构出款费率</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <div v-if="!isDisabled && form.rateType === 1">
        <a-divider orientation="left" orientation-margin="0px"> 请选择扫码费率字段 </a-divider>
        <a-transfer
          class="rate-transfer"
          v-model:target-keys="targetRateKeys"
          :data-source="rateTransferSource"
          :one-way="true"
          :titles="['未选中', '选中']"
          :render="item => item.title"
          :disabled="false"
          @change="handleChange"
        />
      </div>

      <a-form-item label="支持的银行" name="bankCodes" v-if="form.rateType === 3">
        <a-select
          v-model:value="form.bankCodes"
          mode="multiple"
          :filter-option="filterBank"
          showSearch
          style="width: 100%"
          placeholder="请选择"
          :disabled="isDisabled"
        >
          <a-select-option v-for="({ bankName, typeCode }, key) in bankList" :key="key" :value="typeCode">{{ bankName }} </a-select-option>
        </a-select>
      </a-form-item>

      <a-form
        ref="rateForm"
        :model="form"
        :label-col="{ md: { span: 17 }, sm: { span: 24 } }"
        :wrapper-col="{ md: { span: 7 }, sm: { span: 24 } }"
      >
        <RateTypeRateModule :rate-item="form" :disabled="isDisabled" />
      </a-form>
    </a-form>

    <template #footer v-if="isDisabled">
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>

  <!-- 商户选择弹窗 -->
  <BankCodeSelect v-if="showBankCodeList" v-model:visible="showBankCodeList" @done="setBankCodeInfo" />
</template>

<script>
import { message } from 'ant-design-vue';
import { AisleRateApi } from '@/api/aisleManage/AisleRateApi';
import RateTypeRateModule from '../../business-team/_components/RateTypeRateModule.vue';
import { deepCopy } from '@/utils/util';
import BankCodeSelect from './BankCodeSelect.vue';
import { cloneDeep } from 'lodash-es';

// 扫码相关的字段
const allScanRateField = [
  { field: 'creditRate', name: '银联标准贷记卡费率(%)', group_id: 1, single: 1 },
  { field: 'debitRate', name: '银联标准借记卡费率(%)', group_id: 1 },
  { field: 'debitFeeMax', name: '银联标准借记卡手续费封顶(元)', group_id: 1 },
  { field: 'nfcCreditRate', name: '银联云闪付贷记卡费率(%)', group_id: 2 },
  { field: 'nfcDebitRate', name: '银联云闪付借记卡费率(%)', group_id: 2 },
  { field: 'aliPayRate', name: '支付宝扫码费率(%)', group_id: 3 },
  { field: 'aliPayLargeRate', name: '支付宝大额费率(%)', group_id: 3 },
  { field: 'wechatRate', name: '微信扫码费率(%)', group_id: 3 },
  { field: 'creditQrD0Rate', name: '贷记卡D0附加费率(%)', group_id: 4 },
  { field: 'creditQrD0SingleFee', name: '贷记卡D0附加单笔(元)', group_id: 4 },
  { field: 'debitQrD0Rate', name: '借记卡D0附加费率(%)', group_id: 4 },
  { field: 'debitQrD0SingleFee', name: '借记卡D0附加单笔(元)', group_id: 4 }
];

const rateTransferSourceMap = [];
allScanRateField.forEach(item => {
  rateTransferSourceMap.push({
    key: item.field,
    title: item.name,
    disabled: false,
    valueType: item.field.endsWith('Rate') ? 'rate' : 'amount',
    group_id: item.group_id,
    single: item.single
  });
});

export default {
  name: 'AisleRateEdit',
  components: {
    RateTypeRateModule,
    BankCodeSelect
  },
  props: {
    visible: Boolean,
    data: Object,
    channelCodes: Array,
    optType: Number, // 0 添加; 1 编辑; 2 详情
    bankList: Array // 银行列表
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      rateTransferSource: rateTransferSourceMap,
      targetRateKeys: [],
      modalTitle: titleArr[this.optType],
      isDisabled: this.optType === 2,
      // 表单数据
      form: {
        validStatus: 1,
        rateInfoDTO: {},
        bankCodes: []
      },
      // 提交状态
      loading: false,
      templateNos: [],
      //是否展示银行编码选择
      showBankCodeList: false
    };
  },
  created() {
    if (this.data) {
      this.form = Object.assign({}, this.data);

      const { rateInfo } = this.data;
      if (rateInfo) {
        const formatRateInfo = JSON.parse(rateInfo);
        this.form.rateInfoDTO = formatRateInfo || {};

        if (this.form.rateType === 1) {
          this.targetRateKeys = Object.keys(this.form.rateInfoDTO).filter(f => f !== 'rateType');
        }
      }
      delete this.form['rateInfo'];

      //有数据的话先将字符串转换成数组类型显示
      this.form.bankCodes = this.form.bankCodes ? this.form.bankCodes.split(',') : [];
    }

    this.form.rateRatio = 100;
    this.form.isSame = 1;
    const rateFields = ['withdrawRate', 'withdrawSingleFee'];
    rateFields.forEach(f => (this.form.rateInfoDTO[f] = 0));
  },
  methods: {
    handleChange() {
      const excludeFields = ['withdrawRate', 'withdrawSingleFee', 'rateType'];
      const allFields = [...excludeFields, ...this.targetRateKeys];
      const rateInfoDTO = {};
      allFields.forEach(f => {
        rateInfoDTO[f] = this.form.rateInfoDTO[f];
      });
      this.form = Object.assign({}, this.form, { rateInfoDTO });
    },

    onChannelChange() {
      this.form.templateNo = null;
      this.form.rateInfoDTO = {};
      this.updateKey();
    },

    updateKey() {
      const oldData = cloneDeep(this.form);
      this.form = {};
      this.form = Object.assign({}, oldData);
      this.targetRateKeys = [];
    },

    onChangeRateType() {
      if (this.optType === 0) {
        this.form.rateInfoDTO = {};
        this.updateKey();
      }
    },

    //对银行选择 名称的模糊查询
    filterBank(input, option) {
      const bank = this.bankList.find(bank => bank.typeCode === option.value);
      if (bank) {
        return bank.bankName.toLowerCase().includes(input.toLowerCase());
      }
      return false;
    },

    async save() {
      // 校验表单
      await this.$refs.form.validate();
      await this.$refs.rateForm.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      //深拷贝之后，下面的提交数据都用 submitParams 来处理
      const submitParams = deepCopy(this.form);

      submitParams.bankCodes = submitParams.bankCodes.join(',');

      submitParams.rateInfoDTO.rateType = submitParams.rateType;
      if (submitParams.rateType === 3) {
        //如果类型为3，无卡费率，需要把银行卡信息加入
        submitParams.rateInfoDTO.bankCodes = submitParams.bankCodes;
      }
      if (this.optType) {
        result = AisleRateApi.edit(submitParams);
      } else {
        result = AisleRateApi.add(submitParams);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示修改成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};

// 弹框标题
const titleArr = ['添加', '修改', '详情'];
</script>

<style scoped>
::v-deep(.rate-transfer .ant-transfer-list) {
  flex: 1;
  height: 250px;
}

::v-deep(.rate-transfer .ant-transfer-operation) {
  flex-shrink: 0;
}
</style>
