<template>
  <a-modal
    :width="1500"
    :visible="visible"
    title="商户选择"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @cancel="cancel"
    @ok="save"
  >
    <div class="ele-body">
      <!-- 搜索框内容 -->
      <div class="block-interval">
        <a-card :bordered="false">
          <a-form layout="inline" :model="where">
            <a-row :gutter="[0, 16]">
              <a-form-item label="银行名称">
                <a-input v-model:value.trim="where.bankName" placeholder="请输入银行名称" allow-clear />
              </a-form-item>

              <a-form-item label="银行行别代码">
                <a-input v-model:value.trim="where.typeCode" placeholder="请输入银行行别代码" allow-clear />
              </a-form-item>

              <a-form-item class="ele-text-center">
                <a-space>
                  <a-button type="primary" @click="reload">查询</a-button>
                  <a-button @click="reset">重置</a-button>
                </a-space>
              </a-form-item>
            </a-row>
          </a-form>
        </a-card>
      </div>

      <!-- 表格内容 -->
      <div>
        <a-card :bordered="false" class="table-height">
          <ele-pro-table
            ref="table"
            row-key="id"
            :datasource="datasource"
            :columns="columns"
            :where="where"
            v-model:selection="selectedRows"
            :scroll="{ x: 'max-content' }"
          />
        </a-card>
      </div>
    </div>
  </a-modal>
</template>
  
  <script>
import { BankCodeManageApi } from '@/api/base/BankCodeManageApi';
export default {
  name: 'BankCodeSelect',
  props: {
    // 弹窗是否打开
    visible: Boolean
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      //表格查询条件
      where: {},
      //选择数据数组
      selectedRows: [],

      //表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center'
        },
        {
          title: '银行名称',
          dataIndex: 'bankName',
          align: 'center'
        },
        {
          title: '银行行别代码',
          dataIndex: 'typeCode',
          align: 'center'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          align: 'center'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId',
          align: 'center'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime',
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 150,
          align: 'center'
        }
      ]
    };
  },
  methods: {
    save() {
      this.$emit('done', this.selectedRows);
      //关闭弹框
      this.updateVisible(false);

      this.where = {}; //清空查询条件
      this.selectedRows = [];
    },
    cancel() {
      this.where = {}; //清空查询条件
      this.selectedRows = [];
    },
    //重置
    reset() {
      this.where = {}; //清空查询条件
      this.selectedRows = [];
      /**
       * 为啥都清空了还要添加
       */
      this.$refs.table.reload({ page: 1, where: this.where });
    },
    //查询方法
    reload() {
      this.selectedRows = [];
      this.$refs.table.reload({ page: 1 });
    },
    /**
     * 更新编辑界面的弹框是否显示
     *
     * @param value true-显示，false-隐藏
     * <AUTHOR>
     * @date 2022/11/03 17:53
     */
    updateVisible(value) {
      this.$emit('update:visible', value);
    },

    datasource({ page, limit, where, orders }) {
      return BankCodeManageApi.getBankCodePages({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
  
  <style>
</style>