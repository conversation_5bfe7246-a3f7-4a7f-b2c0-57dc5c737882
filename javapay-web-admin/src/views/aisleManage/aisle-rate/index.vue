<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="支付通道">
              <a-select v-model:value="where.channelCode" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
                  {{ channelName }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="费率模板">
              <a-select v-model:value="where.templateNo" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option v-for="({ templateNo }, key) in templateNos" :key="key" :value="templateNo">{{
                  templateNo
                }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="费率类型">
              <a-select v-model:value="where.rateType" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">扫码费率</a-select-option>
                <a-select-option :value="2">刷卡费率</a-select-option>
                <a-select-option :value="3">EPOS费率</a-select-option>
                <a-select-option :value="4">机构出款费率</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="有效状态">
              <a-select v-model:value="where.validStatus" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">有效</a-select-option>
                <a-select-option :value="0">无效</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- table上边工具栏 -->
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="handleAdd()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'validStatus'">
              <a-tag color="success" v-if="record.validStatus === 1">有效</a-tag>
              <a-tag color="error" v-else>无效</a-tag>
            </template>

            <template v-else-if="column.key === 'rateType'">
              <a-tag color="pink" v-if="record.rateType === 1">扫码费率</a-tag>
              <a-tag color="cyan" v-else-if="record.rateType === 2">刷卡费率</a-tag>
              <a-tag color="blue" v-else-if="record.rateType === 3">EPOS费率</a-tag>
              <a-tag color="blue" v-else-if="record.rateType === 4">机构出款费率</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleEdit(record.id, 2)">详情</a>
                <a-divider type="vertical" />
                <a @click="handleEdit(record.id, 1)">修改</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 修改费率模板信息 -->
    <AisleRateEdit
      v-if="showEdit"
      v-model:visible="showEdit"
      :data="current"
      :opt-type="optType"
      :channelCodes="channelCodes"
      :bankList="bankList"
      @done="reload"
    />
  </div>
</template>

<script>
import { AisleRateApi } from '@/api/aisleManage/AisleRateApi';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import { RateTemplateApi } from '@/api/businessTeam/rate-template/RateTemplateApi';
import AisleRateEdit from './AisleRateEdit.vue';
import { BankCodeManageApi } from '@/api/base/BankCodeManageApi';

export default {
  name: 'AisleRate',
  components: {
    AisleRateEdit
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '费率编号',
          dataIndex: 'templateNo',
          align: 'center'
        },
        {
          title: '支付通道',
          dataIndex: 'channelCode',
          align: 'center',
          customRender: ({ text }) => {
            const item = this.channelCodes.find(c => c.channelCode === text);
            return item?.channelName || '--';
          }
        },
        {
          title: '费率类型',
          dataIndex: 'rateType',
          key: 'rateType',
          align: 'center'
        },
        {
          title: '有效状态',
          dataIndex: 'validStatus',
          key: 'validStatus',
          width: 100,
          align: 'center'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '修改人',
          dataIndex: 'lastModifyUserId'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          width: 140,
          align: 'center',
          fixed: 'right'
        }
      ],
      // 表格搜索条件
      where: {},
      current: null,
      showEdit: false,
      channelCodes: [],
      templateNos: [],
      bankList: [],
      optType: null
    };
  },
  mounted() {
    this.getChannelCodes();
    this.getTemplateNos();
    this.getBankList();
  },
  methods: {
    async getTemplateNos() {
      const data = await RateTemplateApi.list({ validStatus: 1 });
      this.templateNos = data;
    },

    async getChannelCodes() {
      const data = await ChannelManageApi.list({ validStatus: 1 });
      this.channelCodes = data || [];
    },

    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleAdd() {
      this.optType = 0;
      this.current = null;
      this.showEdit = true;
    },

    async handleEdit(id, optType) {
      const data = await AisleRateApi.detail({ id });
      this.current = data || {};
      this.optType = optType;
      this.showEdit = true;
    },

    async getBankList() {
      const data = await BankCodeManageApi.list();
      this.bankList = data || [];
    },

    async datasource({ page, limit, where }) {
      const tableData = await AisleRateApi.findPage({ ...where, pageNo: page, pageSize: limit });
      return tableData || [];
    }
  }
};
</script>
