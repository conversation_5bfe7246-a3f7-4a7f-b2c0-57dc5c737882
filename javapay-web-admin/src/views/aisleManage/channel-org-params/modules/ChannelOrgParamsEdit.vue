<template>
  <a-modal
    :width="888"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新增'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules" layout="vertical">
      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="支付通道" name="channelCode">
            <a-select v-model:value="form.channelCode" style="width: 100%" placeholder="请选择">
              <a-select-option :value="item.channelCode" v-for="item in channelCodes" :key="item.id"
              >{{ item.channelName }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="机构编号" name="orgCode">
            <a-input v-model:value="form.orgCode" placeholder="机构编号" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="通道机构编号" name="chnOrgCode">
            <a-input v-model:value="form.chnOrgCode" placeholder="通道机构编号" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="通道机构密钥" name="chnOrgKey">
            <a-input v-model:value="form.chnOrgKey" placeholder="通道机构密钥" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="拓展参数1" name="extParams1">
            <a-input v-model:value="form.extParams1" placeholder="拓展参数1" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="拓展参数2" name="extParams2">
            <a-input v-model:value="form.extParams2" placeholder="拓展参数2" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="有效状态" name="validStatus">
            <a-select v-model:value="form.validStatus" style="width: 100%" placeholder="请选择" allow-clear>
              <a-select-option :value="1">有效</a-select-option>
              <a-select-option :value="0">无效</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { ChannelOrgParamsApi } from '@/api/aisleManage/ChannelOrgParamsApi';

export default {
  props: {
    visible: Boolean,
    data: Object,
    channelCodes: Array
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {
        channelCode: null, // 通道编号【必须】"
        validStatus: 1
      },
      // 表单验证规则
      rules: {
        chnOrgCode: [{ required: true, message: '请输入通道机构编号' }],
        chnOrgKey: [{ required: true, message: '请输入通道机构密钥' }],
        orgCode: [{ required: true, message: '请输入机构编号' }],
        extParams1: [{ required: true, message: '请输入拓展参数1' }],
        extParams2: [{ required: true, message: '请输入拓展参数2' }],
        channelCode: [{ required: true, message: '请选择' }],
        validStatus: [{ required: true, message: '请选择' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false
    };
  },
  created() {
    if (this.data) {
      this.isUpdate = true;
      this.form = Object.assign(this.form, this.data);
    }
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      // 执行编辑或修改方法
      if (this.isUpdate) {
        result = ChannelOrgParamsApi.edit(this.form);
      } else {
        result = ChannelOrgParamsApi.add(this.form);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
