<template>
  <a-modal
    :width="800"
    :visible="visible"
    :confirm-loading="loading"
    title="修改银联凭条打印名称"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @cancel="cancel"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 7 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 17 }, sm: { span: 24 } }"
    >
      <a-form-item label="银联凭条打印名称:" name="printName" style="width: 500px">
        <a-input v-model:value="form.printName" placeholder="请输入银联凭条打印名称" allow-clear />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { ChannelBusinessOrderApi } from '@/api/aisleManage/ChannelBusinessOrderApi';
import { message } from 'ant-design-vue';
export default {
  name: 'YLPrintNameEdit',
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    detail: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      form: Object.assign({}, this.detail),
      rules: {
        printName: [{ required: true, message: '请输入银联凭条打印名称' }]
      },
      // 提交状态
      loading: false
    };
  },
  watch: {
    detail() {
      this.form = Object.assign({}, this.detail)
    }
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;
      let result = ChannelBusinessOrderApi.editYLPrintName({ id: this.form.id, printName: this.form.printName });
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示编辑成功
          message.success(result.message);

          this.form = {};

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    cancel() {
      this.form = Object.assign({}, this.detail);
      this.$refs.form.clearValidate();
    },

    /**
     * 更新编辑界面的弹框是否显示
     *
     * @param value true-显示，false-隐藏
     * <AUTHOR>
     * @date 2022/11/03 17:53
     */
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>

<style>
</style>