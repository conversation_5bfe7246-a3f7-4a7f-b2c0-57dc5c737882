<template>
  <a-modal :width="880" :visible="visible" title="详情" :body-style="{ paddingBottom: '20px' }" :mask-closable="false" @update:visible="updateVisible">
    <a-descriptions bordered :column="2">
      <a-descriptions-item label="通道名称">
        <template v-for="({ channelCode, channelName }, key) in channelCodes" :key="key">
          <a-badge v-if="form.channelCode === channelCode" color="purple" :text="channelName" />
        </template>
      </a-descriptions-item>
      <a-descriptions-item label="通道商户编号">{{ form.chnMerchNo }}</a-descriptions-item>
      <a-descriptions-item label="业务类型">{{ form.businessType }}</a-descriptions-item>
      <a-descriptions-item label="通道业务类型">{{ form.channelBusinessType }}</a-descriptions-item>
      <a-descriptions-item label="通道订单号">{{ form.channelOrderNo }}</a-descriptions-item>
      <a-descriptions-item label="订单状态">
        <a-tag v-if="form.orderStatus === 0">未开通</a-tag>
        <a-tag v-else-if="form.orderStatus === 1" color="success">已开通</a-tag>
        <a-tag v-else-if="form.orderStatus === 2" color="processing">审核中</a-tag>
        <a-tag v-else-if="form.orderStatus === 3" color="error">审核失败</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="通道响应码" :span="2">{{ form.channelRespCode || '--' }}</a-descriptions-item>
      <a-descriptions-item label="通道响应消息" :span="2">{{ form.channelRespMsg || '--' }}</a-descriptions-item>
      <a-descriptions-item label="商户号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="银联商户号">{{ form.unionMerchNo }}</a-descriptions-item>
      <a-descriptions-item label="银联凭条打印名称">{{ form.printName }}</a-descriptions-item>
      <a-descriptions-item label="创建人">{{ form.createUserId }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改人">{{ form.lastModifyUserId }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

export default {
  name: 'ChannelBusinessOrderDetail',
  props: {
    visible: Boolean,
    detail: Object,
    channelCodes: Array
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
