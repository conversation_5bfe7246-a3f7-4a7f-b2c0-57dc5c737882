<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="业务类型">
              <a-input v-model:value.trim="where.businessType" placeholder="请输入业务类型" allow-clear />
            </a-form-item>
            <a-form-item label="通道业务类型">
              <a-input v-model:value.trim="where.channelBusinessType" placeholder="请输入通道业务类型" allow-clear />
            </a-form-item>
            <a-form-item label="通道商户编号">
              <a-input v-model:value.trim="where.chnMerchNo" placeholder="请输入通道商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="通道订单号">
              <a-input v-model:value.trim="where.channelOrderNo" placeholder="请输入通道订单号" allow-clear />
            </a-form-item>
            <a-form-item label="商户号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户号" allow-clear />
            </a-form-item>
            <a-form-item label="通道名称">
              <a-select v-model:value="where.channelCode" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
                  {{ channelName }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="订单状态">
              <a-select v-model:value="where.orderStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">未开通</a-select-option>
                <a-select-option :value="1">已开通</a-select-option>
                <a-select-option :value="2">审核中</a-select-option>
                <a-select-option :value="3">审核失败</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table
          ref="table"
          row-key="id"
          :datasource="datasource"
          :columns="columns"
          :where="where"
          v-model:selection="selection"
          :scroll="{ x: 'max-content' }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'orderStatus'">
              <a-tag v-if="record.orderStatus === 0">未开通</a-tag>
              <a-tag v-else-if="record.orderStatus === 1" color="success">已开通</a-tag>
              <a-tag v-else-if="record.orderStatus === 2" color="processing">审核中</a-tag>
              <a-tag v-else-if="record.orderStatus === 3" color="error">审核失败</a-tag>
            </template>
            <!-- table操作栏按钮 -->
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
                <a-divider v-if="record.orderStatus === 3" type="vertical" />
                <a v-if="record.orderStatus === 3" @click="editYLPrintName(record)">修改银联凭条打印名称</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <ChannelBusinessOrderDetail v-model:visible="showDetail" :detail="current" :channelCodes="channelCodes" />

    <!-- 编辑银联凭条名称 -->
    <YLPrintNameEdit v-model:visible="showEditYLPrintName" :detail="current" @done="reload" />
  </div>
</template>

<script>
import { ChannelBusinessOrderApi } from '@/api/aisleManage/ChannelBusinessOrderApi';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import ChannelBusinessOrderDetail from './ChannelBusinessOrderDetail.vue';
import YLPrintNameEdit from './YLPrintNameEdit.vue'

export default {
  name: 'ChannelBusinessOrder',
  components: {
    ChannelBusinessOrderDetail,
    YLPrintNameEdit
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '通道名称',
          dataIndex: 'channelCode',
          align: 'center',
          customRender: ({ text }) => {
            const item = this.channelCodes.find(c => c.channelCode === text);
            return item?.channelName || '--';
          }
        },
        {
          title: '通道商户编号',
          dataIndex: 'chnMerchNo',
          align: 'center'
        },
        {
          title: '业务类型',
          dataIndex: 'businessType',
          align: 'center'
        },
        {
          title: '通道业务类型',
          dataIndex: 'channelBusinessType',
          align: 'center'
        },

        {
          title: '通道订单号',
          dataIndex: 'channelOrderNo',
          align: 'center'
        },
        {
          title: '订单状态',
          dataIndex: 'orderStatus',
          key: 'orderStatus',
          align: 'center'
        },
        {
          title: '通道响应码',
          dataIndex: 'channelRespCode',
          align: 'center',
          customRender: ({ text }) => text || '--'
        },
        {
          title: '通道响应消息',
          dataIndex: 'channelRespMsg',
          width: 200,
          customRender: ({ text }) => text || '--'
        },
        {
          title: '商户号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '银联商户号',
          dataIndex: 'unionMerchNo',
          align: 'center'
        },
        {
          title: '银联凭条打印名称',
          dataIndex: 'printName',
          align: 'center'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right',
          width: 250
        }
      ],
      // 表格搜索条件
      where: {},
      selection: [],
      current: null,
      showDetail: false,
      showEditYLPrintName: false, //是否显示编辑银联凭条名称
      channelCodes: []
    };
  },
  async mounted() {
    const data = await ChannelManageApi.list({ validStatus: 1 });
    this.channelCodes = data || [];
  },
  methods: {
    reload() {
      this.selection = [];
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.selection = [];
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    editYLPrintName(row) {
      this.current = row;
      this.showEditYLPrintName = true;
    },

    datasource({ page, limit, where, orders }) {
      return ChannelBusinessOrderApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
