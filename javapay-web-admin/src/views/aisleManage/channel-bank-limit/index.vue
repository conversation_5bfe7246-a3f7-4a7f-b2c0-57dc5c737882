<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="银行编码">
              <a-input v-model:value.trim="where.bankCode" placeholder="请输入银行编码" allow-clear />
            </a-form-item>
            <a-form-item label="银行名称">
              <a-input v-model:value.trim="where.bankName" placeholder="请输入银行名称" allow-clear />
            </a-form-item>
            <a-form-item label="通道编号">
              <a-select v-model:value="where.channelCode" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option v-for="item in channelList" :key="item.id" :value="item.channelCode">{{ item.channelName }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="状态">
              <a-select v-model:value="where.validStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">有效</a-select-option>
                <a-select-option :value="0">无效</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table
          ref="table"
          row-key="id"
          :datasource="datasource"
          :columns="columns"
          :where="where"
          :scroll="{ x: 'max-content' }"
        >
          <!-- table上边工具栏 -->
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="handleEdit()" v-if="!hasPurview(['1', '2', '3'])">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'validStatus'">
              <a-tag color="success" v-if="record.validStatus === 1">有效</a-tag>
              <a-tag color="error" v-else>无效</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleEdit(record)">修改</a>
                <a-divider type="vertical" />
                <a-popconfirm title="确定要删除吗？" @confirm="remove(record)">
                  <a class="ele-text-danger">删除</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 编辑 -->
    <ChannelBankLimitEdit v-model:visible="showEdit" :data="current" :bankList="bankList" :channelList="channelList" @done="reload" />
  </div>
</template>

<script>
import { message } from 'ant-design-vue';
import { ChannelBankLimitApi } from '@/api/aisleManage/ChannelBankLimitApi';
import ChannelBankLimitEdit from './channel-bank-limit-edit.vue';
import { BankCodeManageApi } from '@/api/base/BankCodeManageApi';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import { hasPurview } from '@/utils/permission';

export default {
  name: 'ChannelBankLimit',
  components: { ChannelBankLimitEdit },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '银行编码',
          dataIndex: 'bankCode'
        },
        {
          title: '银行名称',
          dataIndex: 'bankName'
        },
        {
          title: '通道编号',
          dataIndex: 'channelCode'
        },
        {
          title: '通道名称',
          dataIndex: 'channelName'
        },
        {
          title: '银行单日最大限额(元)',
          dataIndex: 'dayMaxLimit'
        },
        {
          title: '银行单笔最大限额(元)',
          dataIndex: 'onceMaxLimit'
        },
        {
          title: '有效状态',
          dataIndex: 'validStatus',
          key: 'validStatus'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right',
          width: 180,
          hideCol: hasPurview(['1', '2', '3']),
        }
      ].filter(i => !i.hideCol),
      // 表格搜索条件
      where: {},
      current: null,
      showDetail: false,
      showEdit: false,
      bankList: [],
      channelList: [],
      hasPurview
    };
  },
  mounted() {
    this.getBankList();
    this.getChannelList();
  },
  methods: {
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    async remove(row) {
      const result = await ChannelBankLimitApi.delete({ id: row.id });
      message.success(result.message);
      this.reload();
    },

    datasource({ page, limit, where, orders }) {
      return ChannelBankLimitApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    },

    async getBankList() {
      const data = await BankCodeManageApi.list();
      this.bankList = data || [];
    },

    async getChannelList() {
      const data = await ChannelManageApi.list({ validStatus: 1 });
      this.channelList = data || [];
    },
  }
};
</script>
