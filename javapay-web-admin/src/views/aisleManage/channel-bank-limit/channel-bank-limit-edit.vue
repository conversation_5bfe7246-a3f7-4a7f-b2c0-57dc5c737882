<template>
  <a-modal
    :width="1050"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新增'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @cancel="cancel"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 8 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 16 }, sm: { span: 24 } }"
    >
      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="银行名称" name="bankCode">
            <a-select
              v-model:value="form.bankCode"
              mode="default"
              :filter-option="filterBank"
              showSearch
              style="width: 100%"
              placeholder="请选择"
            >
              <a-select-option v-for="({ bankName, typeCode }, key) in bankList" :key="key" :value="typeCode"
              >{{ bankName }}
              </a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="银行单日最大限额(元)" name="dayMaxLimit">
            <a-input v-model:value="form.dayMaxLimit" placeholder="请输入银行单日最大限额(元)" allow-clear />
          </a-form-item>

          <a-form-item label="有效状态" name="validStatus">
            <a-select v-model:value="form.validStatus" style="width: 100%" placeholder="请选择" allow-clear>
              <a-select-option :value="1">有效</a-select-option>
              <a-select-option :value="0">无效</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="通道名称" name="channelCode">
            <a-select
              v-model:value="form.channelCode"
              mode="default"
              :filter-option="filterChannel"
              showSearch
              style="width: 100%"
              placeholder="请选择"
            >
              <a-select-option v-for="({ channelName, channelCode }, key) in channelList" :key="key" :value="channelCode"
              >{{ channelName }}
              </a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="银行单笔最大限额(元)" name="onceMaxLimit">
            <a-input v-model:value="form.onceMaxLimit" placeholder="请输入银行单笔最大限额(元)" allow-clear />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { ChannelBankLimitApi } from '@/api/aisleManage/ChannelBankLimitApi';

export default {
  name: 'ChannelBankLimitEdit',
  props: {
    visible: Boolean,
    data: Object,
    bankList: Array,
    channelList: Array
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: Object.assign({}, this.data),
      // 表单验证规则
      rules: {
        bankCode: [{ required: true, message: '请输入银行编码' }],
        bankName: [{ required: true, message: '请输入银行名称' }],
        channelCode: [{ required: true, message: '请输入通道编号' }],
        channelName: [{ required: true, message: '请输入通道名称' }],
        dayMaxLimit: [{ required: true, pattern: /^[0-9]*$/, message: '请输入有效金额' }],
        onceMaxLimit: [{ required: true, pattern: /^[0-9]*$/, message: '请输入有效金额' }],
        validStatus: [{ required: true, message: '请选择有效状态' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false
    };
  },
  watch: {
    data() {
      if (this.data) {
        this.form = Object.assign({}, this.data);
        this.isUpdate = true;
      } else {
        this.form = {};
        this.isUpdate = false;
      }
      if (this.$refs.form) {
        this.$refs.form.clearValidate();
      }
    }
  },
  methods: {
    //对银行选择 名称的模糊查询
    filterBank(input, option) {
      const bank = this.bankList.find(bank => bank.typeCode === option.value);
      if (bank) {
        return bank.bankName.toLowerCase().includes(input.toLowerCase());
      }
      return false;
    },

    //对选择 名称的模糊查询
    filterChannel(input, option) {
      const channelItem = this.channelList.find(item => item.channelCode === option.value);
      if (channelItem) {
        return channelItem.channelName.toLowerCase().includes(input.toLowerCase());
      }
      return false;
    },

    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      //匹配数据
      const foundBank = this.bankList.find(item => item.typeCode === this.form.bankCode);
      if (foundBank) {
        this.form.bankName = foundBank.bankName;
      }
      const foundChannel = this.channelList.find(item => item.channelCode === this.form.channelCode);
      if (foundChannel) {
        this.form.channelName = foundChannel.channelName;
      }

      // 执行编辑或修改方法
      if (this.isUpdate) {
        result = ChannelBankLimitApi.edit(this.form);
      } else {
        result = ChannelBankLimitApi.add(this.form);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 如果是新增，则form表单置空
          if (!this.isUpdate) {
            this.form = {};
          }

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    cancel() {
      this.form = Object.assign({}, this.data);
      this.$refs.form.clearValidate();
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
