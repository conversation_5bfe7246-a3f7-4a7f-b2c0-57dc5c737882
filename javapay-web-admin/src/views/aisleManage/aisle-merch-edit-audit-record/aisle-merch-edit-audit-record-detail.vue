<template>
  <a-modal
    title="详情"
    :width="1000"
    :visible="visible"
    :body-style="{ paddingBottom: '8px' }"
    :mask-closable="false"
    @update:visible="updateVisible"
  >
    <a-alert message="左侧为新内容, 右侧为旧内容" show-icon type="info" style="margin-bottom: 24px" />
    <a-form :label-col="{ md: { span: 6 }, sm: { span: 24 } }" :wrapper-col="{ md: { span: 18 }, sm: { span: 24 } }" :colon="false">
      <a-tabs v-model:activeKey="activeKey" type="card">
        <!-- 基本信息 -->
        <a-tab-pane key="1" tab="基本信息">
          <template v-for="({ label, infoKey, formItemType, isHide }, key) in baseInfoMap" :key="key">
            <a-form-item
              v-if="!isHide"
              :class="{ 'info-changed': isInfoChanged(form['newBaseInfo'][infoKey], form['oldBaseInfo'][infoKey]) }"
              :label="label"
            >
              <template v-for="i in ['newBaseInfo', 'oldBaseInfo']" :key="i">
                <a-input v-if="!formItemType" :value="form[i][infoKey]" readonly />
                <a-textarea
                  v-else-if="formItemType === 'textarea'"
                  :value="form[i][infoKey]"
                  :auto-size="{ minRows: 2, maxRows: 5 }"
                  readonly
                />
                <a-select v-else-if="formItemType === 'select'" :value="form[i][infoKey]" class="ele-fluid" disabled>
                  <template v-if="infoKey === 'channelCode'">
                    <!-- 支付通道 -->
                    <a-select-option v-for="(item, key) in channelCodes" :value="item.channelCode" :key="key">
                      {{ item.channelName }}
                    </a-select-option>
                  </template>
                  <template v-else-if="infoKey === 'isMicro'">
                    <!-- 商户类型 -->
                    <a-select-option :value="0">企业商户</a-select-option>
                    <a-select-option :value="1">个人商户</a-select-option>
                  </template>
                  <template v-else-if="infoKey === 'accountType'">
                    <!-- 账户类型 -->
                    <a-select-option value="G">对公</a-select-option>
                    <a-select-option value="S">对私</a-select-option>
                  </template>
                  <template v-else-if="infoKey === 'isLegalSettle'">
                    <!-- 是否法人结算 -->
                    <a-select-option :value="1">是</a-select-option>
                    <a-select-option :value="0">否</a-select-option>
                  </template>
                </a-select>
              </template>
            </a-form-item>
          </template>
        </a-tab-pane>
        <!-- 图片信息 -->
        <a-tab-pane key="3" tab="图片信息">
          <template v-for="(f, keyF) in fileList" :key="keyF">
            <a-form-item v-if="!f.isHide" :label="f.label" :class="{ 'info-changed': isImgChanged(f.imageType) }">
              <template v-for="k in ['newImageInfo', 'oldImageInfo']" :key="k">
                <template v-for="(c, keyC) in form[k] || []" :key="keyC">
                  <a-upload
                    v-if="f.imageType === c.imageType"
                    :file-list="[{ url: c.imagePath }]"
                    list-type="picture-card"
                    @preview="() => handlePreview(c.imagePath)"
                    disabled
                  />
                </template>
              </template>
            </a-form-item>
          </template>
          <!-- 预览图片 -->
          <a-image
            :style="{ display: 'none' }"
            :src="previewImage"
            :preview="{ visible: previewVisible, onVisibleChange: setPreviewVisible }"
          />
        </a-tab-pane>
      </a-tabs>
    </a-form>

    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, ref, toRefs, watchEffect } from 'vue';
import { imageTypeEnum } from '@/config/enumerate';
import { deepCopy } from '@/utils/util';

export default {
  props: {
    visible: Boolean,
    detail: Object,
    channelCodes: Array
  },
  emits: ['update:visible'],
  setup(props, context) {
    // 基本信息
    const baseInfoMap = ref([
      { label: '商户简称', infoKey: 'shortName' },
      { label: '商户编号', infoKey: 'merchantNo' },
      { label: '通道商户号', infoKey: 'chnMerchNo' },
      { label: '商户类型', infoKey: 'isMicro', formItemType: 'select' },
      { label: '通道名称', infoKey: 'channelCode', formItemType: 'select' },
      { label: '账户类型', infoKey: 'accountType', formItemType: 'select' },
      { label: '法人姓名', infoKey: 'legalName' },
      { label: '法人手机号', infoKey: 'legalTelMask' },
      { label: '是否法人结算', infoKey: 'isLegalSettle', formItemType: 'select' },

      { label: '营业执照全称', infoKey: 'licenseName' },
      { label: '营业执照号', infoKey: 'licenseNo' },
      { label: '营业执照有效期开始', infoKey: 'licenseStartDate' },
      { label: '营业执照有效期结束', infoKey: 'licenseEndDate' },
      { label: '注册地址', infoKey: 'licenseAddr', formItemType: 'textarea' }
    ]);

    // 设置表单默认值
    function formDefaults() {
      return {
        newBaseInfo: {},
        oldBaseInfo: {},
        newImageInfo: [],
        oldImageInfo: []
      };
    }
    const data = reactive({
      form: formDefaults(),
      activeKey: '1',
      previewVisible: false,
      previewTitle: '',
      previewImage: '',
      loading: false,
      fileList: []
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.activeKey = '1';

        const detail = deepCopy(props.detail);

        detail.modifyDataAfter = JSON.parse(detail.modifyDataAfter) || {};
        detail.modifyDataBefore = JSON.parse(detail.modifyDataBefore) || {};

        Object.assign(data.form, formDefaults(), {
          newBaseInfo: detail.modifyDataAfter,
          oldBaseInfo: detail.modifyDataBefore,
          newImageInfo: detail.modifyDataAfter.imageJsonList || [],
          oldImageInfo: detail.modifyDataBefore.imageJsonList || []
        });

        const imageTypes = imageTypeEnum.map(i => i.value);
        data.form.newImageInfo.forEach(i => {
          if (imageTypes.includes(i.imageType)) {
            const item = imageTypeEnum.find(e => e.value === i.imageType);
            i.label = item?.label;
          } else {
            i.label = `图片类型-${i.imageType}`;
          }
        });

        data.fileList = [...data.form.newImageInfo];
      }
    });

    const handlePreview = imagePath => {
      data.previewImage = imagePath;
      setPreviewVisible(true);
    };

    const setPreviewVisible = visible => {
      data.previewVisible = visible;
    };

    const isInfoChanged = (current, old) => {
      if ([current, old].every(i => i !== 0 && !i)) return false;
      return current !== old;
    };

    const isImgChanged = imageType => {
      const [currentItem, oldItem] = ['newImageInfo', 'oldImageInfo'].map(i => {
        const item = data.form[i].find(c => c.imageType === imageType);
        return item || {};
      });

      return isInfoChanged(currentItem.imagePath, oldItem.imagePath);
    };

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible,
      handlePreview,
      setPreviewVisible,
      isImgChanged,
      isInfoChanged,
      baseInfoMap
    };
  }
};
</script>

<style lang="less" scoped>
:deep(.ant-form-item-label) {
  text-align: center;
  background-color: #f1f3f4;
}
.info-changed {
  :deep(.ant-form-item-label) {
    background-color: #fa541cc9;
  }
}
.hide-label {
  :deep(.ant-form-item-label) {
    display: none;
  }
  .ant-divider-horizontal.ant-divider-with-text {
    margin: 0;
  }
}
:deep(.ant-row) {
  align-items: flex-start;
  .ant-form-item-control-input-content {
    display: flex;
    .ant-input,
    .ant-select-selector {
      border-radius: 0;
    }
  }
}
:deep(.ant-select-disabled.ant-select:not(.ant-select-customize-input)) .ant-select-selector {
  background-color: transparent;
}
</style>
