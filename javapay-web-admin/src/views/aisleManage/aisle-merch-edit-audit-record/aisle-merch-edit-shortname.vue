<template>
  <a-modal
    title="详情"
    :width="1000"
    :visible="visible"
    :body-style="{ paddingBottom: '8px' }"
    :mask-closable="false"
    @update:visible="updateVisible"
  >
    <a-alert message="左侧为新内容, 右侧为旧内容" show-icon type="info" style="margin-bottom: 24px" />
    <a-form :label-col="{ md: { span: 6 }, sm: { span: 24 } }" :wrapper-col="{ md: { span: 18 }, sm: { span: 24 } }" :colon="false">
      <template v-for="({ label, infoKey, formItemType, isHide }, key) in baseInfoMap" :key="key">
        <a-form-item
          v-if="!isHide"
          :class="{ 'info-changed': isInfoChanged(form['newBaseInfo'][infoKey], form['oldBaseInfo'][infoKey]) }"
          :label="label"
        >
          <template v-for="i in ['newBaseInfo', 'oldBaseInfo']" :key="i">
            <a-input v-if="!formItemType" :value="form[i][infoKey]" readonly />
            <a-textarea
              v-else-if="formItemType === 'textarea'"
              :value="form[i][infoKey]"
              :auto-size="{ minRows: 2, maxRows: 5 }"
              readonly
            />
            <a-select v-else-if="formItemType === 'select'" :value="form[i][infoKey]" class="ele-fluid" disabled>
              <template v-if="infoKey === 'channelCode'">
                <!-- 支付通道 -->
                <a-select-option v-for="(item, key) in channelCodes" :value="item.channelCode" :key="key">
                  {{ item.channelName }}
                </a-select-option>
              </template>
              <template v-else-if="infoKey === 'isMicro'">
                <!-- 商户类型 -->
                <a-select-option :value="0">企业商户</a-select-option>
                <a-select-option :value="1">个人商户</a-select-option>
              </template>
              <template v-else-if="infoKey === 'accountType'">
                <!-- 账户类型 -->
                <a-select-option value="G">对公</a-select-option>
                <a-select-option value="S">对私</a-select-option>
              </template>
              <template v-else-if="infoKey === 'isLegalSettle'">
                <!-- 是否法人结算 -->
                <a-select-option :value="1">是</a-select-option>
                <a-select-option :value="0">否</a-select-option>
              </template>
            </a-select>
          </template>
        </a-form-item>
      </template>
    </a-form>

    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, ref, toRefs, watchEffect } from 'vue';
import { deepCopy } from '@/utils/util';

export default {
  props: {
    visible: Boolean,
    detail: Object,
    channelCodes: Array
  },
  emits: ['update:visible'],
  setup(props, context) {
    // 基本信息
    const baseInfoMap = ref([
      { label: '商户简称', infoKey: 'shortName' },
      // { label: '商户编号', infoKey: 'merchantNo' },
      // { label: '通道商户号', infoKey: 'chnMerchNo' },
      // { label: '商户类型', infoKey: 'isMicro', formItemType: 'select' },
      // { label: '通道名称', infoKey: 'channelCode', formItemType: 'select' },
      // { label: '账户类型', infoKey: 'accountType', formItemType: 'select' },
      // { label: '是否法人结算', infoKey: 'isLegalSettle', formItemType: 'select' },
    ]);

    // 设置表单默认值
    function formDefaults() {
      return {
        newBaseInfo: {},
        oldBaseInfo: {},
      };
    }
    const data = reactive({
      form: formDefaults(),
    });

    const watch = watchEffect(() => {
      if (props.detail) {

        const detail = deepCopy(props.detail);

        detail.modifyDataAfter = JSON.parse(detail.modifyDataAfter) || {};
        detail.modifyDataBefore = JSON.parse(detail.modifyDataBefore) || {};

        Object.assign(data.form, formDefaults(), {
          newBaseInfo: detail.modifyDataAfter,
          oldBaseInfo: detail.modifyDataBefore,
        });
      }
    });

    const isInfoChanged = (current, old) => {
      if ([current, old].every(i => i !== 0 && !i)) return false;
      return current !== old;
    };

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible,
      isInfoChanged,
      baseInfoMap
    };
  }
};
</script>

<style lang="less" scoped>
:deep(.ant-form-item-label) {
  text-align: center;
  background-color: #f1f3f4;
}
.info-changed {
  :deep(.ant-form-item-label) {
    background-color: #fa541cc9;
  }
}
.hide-label {
  :deep(.ant-form-item-label) {
    display: none;
  }
  .ant-divider-horizontal.ant-divider-with-text {
    margin: 0;
  }
}
:deep(.ant-row) {
  align-items: flex-start;
  .ant-form-item-control-input-content {
    display: flex;
    .ant-input,
    .ant-select-selector {
      border-radius: 0;
    }
  }
}
:deep(.ant-select-disabled.ant-select:not(.ant-select-customize-input)) .ant-select-selector {
  background-color: transparent;
}
</style>
