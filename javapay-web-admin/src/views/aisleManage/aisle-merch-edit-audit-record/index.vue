<template>
  <div class="ele-body">
    <!-- 搜索框内容 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
            </a-form-item>

            <a-form-item label="通道名称">
              <a-select v-model:value="where.channelCode" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option v-for="({ channelName, channelCode }, key) in channelCodes" :key="key" :value="channelCode">
                  {{ channelName }}
                </a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="审核状态">
              <a-select v-model:value="where.auditStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option value="0">待审核</a-select-option>
                <a-select-option value="1">审核中</a-select-option>
                <a-select-option value="2">审核成功</a-select-option>
                <a-select-option value="3">审核失败</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="修改类型">
              <a-select v-model:value="where.modifyType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">修改商户简称</a-select-option>
                <a-select-option :value="2">转企业报备</a-select-option>
                <a-select-option :value="3">修改商户地址</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="请求流水">
              <a-input v-model:value.trim="where.requestId" placeholder="请输入请求流水" allow-clear />
            </a-form-item>

            <a-form-item label="渠道商户编号">
              <a-input v-model:value.trim="where.chnMerchNo" placeholder="请输入渠道商户编号" allow-clear />
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格内容 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: '1000' }">
          <!-- 表体的操作 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'auditStatus'">
              <a-tag v-if="record.auditStatus === 0" color="pink">待审核</a-tag>
              <a-tag v-else-if="record.auditStatus === 1" color="cyan">审核中</a-tag>
              <a-tag v-else-if="record.auditStatus === 2" color="blue">审核成功</a-tag>
              <a-tag v-else-if="record.auditStatus === 3" color="purple">审核失败</a-tag>
            </template>
            <template v-if="column.key === 'modifyType'">
              <a-tag v-if="record.modifyType === 1" color="cyan">修改商户简称</a-tag>
              <a-tag v-else-if="record.modifyType === 2" color="blue">转企业报备</a-tag>
              <a-tag v-else-if="record.modifyType === 3" color="orange">修改商户地址</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <AisleMerchEditAuditRecordDetail v-model:visible="showDetail" :detail="current" :channelCodes="channelCodes" />

    <AisleMerchEditShortName v-model:visible="showShortName" :detail="current" :channelCodes="channelCodes" />

    <AisleMerchEditAddress v-model:visible="showAddress" :detail="current" />
  </div>
</template>

<script>
import { AisleMerchEditAuditRecordApi } from '@/api/aisleManage/AisleMerchEditAuditRecordApi';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import AisleMerchEditAuditRecordDetail from './aisle-merch-edit-audit-record-detail.vue';
import AisleMerchEditShortName from './aisle-merch-edit-shortname.vue';
import AisleMerchEditAddress from './aisle-merch-edit-address.vue';

export default {
  name: 'AisleMerchEditAuditRecord',
  components: {
    AisleMerchEditAuditRecordDetail,
    AisleMerchEditShortName,
    AisleMerchEditAddress
  },
  data() {
    return {
      //表格查询条件
      where: {},
      //展示编辑页面传的参数
      current: null,
      //是否展示详情页面
      showDetail: false,
      showShortName: false,
      showAddress: false,
      //通道数组
      channelCodes: [],
      //表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center'
        },
        {
          title: '商户名称',
          dataIndex: 'merchantName',
          width: 160
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo',
          align: 'center',
          width: 160
        },
        {
          title: '渠道商户编号',
          dataIndex: 'chnMerchNo',
          align: 'center',
          width: 160
        },
        {
          title: '通道名称',
          dataIndex: 'channelCode',
          align: 'center',
          width: 160,
          customRender: ({ text }) => {
            const item = this.channelCodes.find(c => c.channelCode === text);
            return item?.channelName || '--';
          }
        },
        {
          title: '修改类型',
          dataIndex: 'modifyType',
          key: 'modifyType',
          align: 'center',
          width: 160
        },
        {
          title: '审核备注',
          dataIndex: 'auditRemark',
          width: 300,
          ellipsis: true
        },
        {
          title: '审核状态',
          dataIndex: 'auditStatus',
          key: 'auditStatus',
          align: 'center',
          width: 160
        },
        {
          title: '请求流水',
          dataIndex: 'requestId',
          align: 'center',
          width: 160
        },
        {
          title: '创建人',
          dataIndex: 'createUserId',
          width: 190,
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          width: 190,
          align: 'center'
        },
        {
          title: '修改人',
          dataIndex: 'lastModifyUserId',
          width: 190,
          align: 'center'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime',
          width: 190,
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 120,
          align: 'center'
        }
      ]
    };
  },
  async mounted() {
    const data = await ChannelManageApi.list({ validStatus: 1 });
    this.channelCodes = data || [];
  },
  methods: {
    //查询方法
    reload() {
      this.$refs.table.reload({ page: 1 });
    },
    //重置
    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;

      switch (row.modifyType) {
        case 1:
          this.showShortName = true;
          break;
        case 2:
          this.showDetail = true;
          break;
        case 3:
          this.showAddress = true;
          break;
      }
    },

    //获取数据方法
    datasource({ page, limit, where }) {
      return AisleMerchEditAuditRecordApi.findPage({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>
