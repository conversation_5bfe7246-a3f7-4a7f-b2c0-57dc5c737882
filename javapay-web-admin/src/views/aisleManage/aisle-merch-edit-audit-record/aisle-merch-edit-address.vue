<template>
  <a-modal
    title="详情"
    :width="1000"
    :visible="visible"
    :body-style="{ paddingBottom: '8px' }"
    :mask-closable="false"
    @update:visible="updateVisible"
  >
    <a-alert message="左侧为新内容, 右侧为旧内容" show-icon type="info" style="margin-bottom: 24px" />
    <a-form :label-col="{ md: { span: 6 }, sm: { span: 24 } }" :wrapper-col="{ md: { span: 18 }, sm: { span: 24 } }" :colon="false">
      <template v-for="({ label, infoKey, formItemType, isHide }, key) in baseInfoMap" :key="key">
        <a-form-item
          v-if="!isHide"
          :class="{ 'info-changed': isInfoChanged(form['newBaseInfo'][infoKey], form['oldBaseInfo'][infoKey]) }"
          :label="label"
        >
          <template v-for="i in ['newBaseInfo', 'oldBaseInfo']" :key="i">
            <a-input v-if="!formItemType" :value="form[i][infoKey]" readonly />
            <a-textarea
              v-else-if="formItemType === 'textarea'"
              :value="form[i][infoKey]"
              :auto-size="{ minRows: 2, maxRows: 5 }"
              readonly
            />
          </template>
        </a-form-item>
      </template>
    </a-form>

    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, ref, toRefs, watchEffect } from 'vue';
import { deepCopy } from '@/utils/util';
import { AreaApi } from '@/api/base/AreaApi';

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    // 基本信息
    const baseInfoMap = ref([
      { label: '省', infoKey: 'province' },
      { label: '市', infoKey: 'city' },
      { label: '区', infoKey: 'district' },
      { label: '详细地址', infoKey: 'addrDetail', formItemType: 'textarea' }
    ]);

    // 设置表单默认值
    function formDefaults() {
      return {
        newBaseInfo: {},
        oldBaseInfo: {}
      };
    }
    const data = reactive({
      form: formDefaults()
    });


    const watch = watchEffect(() => {
      if (props.detail) {
        const detail = deepCopy(props.detail);

        detail.modifyDataAfter = JSON.parse(detail.modifyDataAfter) || {};
        detail.modifyDataBefore = JSON.parse(detail.modifyDataBefore) || {};

        Object.assign(data.form, formDefaults(), {
          newBaseInfo: detail.modifyDataAfter,
          oldBaseInfo: detail.modifyDataBefore
        });

        loadAreaData([]);
      }
    });

    const isInfoChanged = (current, old) => {
      if ([current, old].every(i => i !== 0 && !i)) return false;
      return current !== old;
    };

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    const loadAreaData = async ([option]) => {
      const targetOption = option ? option : { level: 1, value: '' };
      const { level, value } = targetOption;

      const res = await AreaApi.list({ level: level + 1, status: 1, parentCode: value });
      const formatData = res.map(d => {
        return { label: d.areaName, value: d.areaCode, level: d.level };
      });

      if (level === 1) {
        data.regionsData = deepCopy(formatData);
        data.oldRegionsData = deepCopy(formatData);
      } else {
        targetOption.children = formatData;
      }

      if (!option) {
        const { newBaseInfo, oldBaseInfo } = data.form;

        if (newBaseInfo.city) {
          const item = data.regionsData.find(r => r.value === newBaseInfo.province);
          if (item) {
            newBaseInfo.province = item.label;
            await loadAreaData([item]);
            const citem = item.children.find(i => i.value === newBaseInfo.city);
            if (citem) {
              newBaseInfo.city = citem.label;
              await loadAreaData([citem]);
              const ditem = citem.children.find(i => i.value === newBaseInfo.district);
              if (ditem) {
                newBaseInfo.district = ditem.label;
              }
            }
          }
        }
        if (oldBaseInfo.city) {
          const item = data.oldRegionsData.find(r => r.value === oldBaseInfo.province);
          if (item) {
            oldBaseInfo.province = item.label;
            await loadAreaData([item]);
            const citem = item.children.find(i => i.value === oldBaseInfo.city);
            if (citem) {
              oldBaseInfo.city = citem.label;
              await loadAreaData([citem]);
              const ditem = citem.children.find(i => i.value === oldBaseInfo.district);
              if (ditem) {
                oldBaseInfo.district = ditem.label;
              }
            }
          }
        }
      }
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible,
      isInfoChanged,
      baseInfoMap
    };
  }
};
</script>

<style lang="less" scoped>
:deep(.ant-form-item-label) {
  text-align: center;
  background-color: #f1f3f4;
}
.info-changed {
  :deep(.ant-form-item-label) {
    background-color: #fa541cc9;
  }
}
.hide-label {
  :deep(.ant-form-item-label) {
    display: none;
  }
  .ant-divider-horizontal.ant-divider-with-text {
    margin: 0;
  }
}
:deep(.ant-row) {
  align-items: flex-start;
  .ant-form-item-control-input-content {
    display: flex;
    .ant-input,
    .ant-select-selector {
      border-radius: 0;
    }
  }
}
:deep(.ant-select-disabled.ant-select:not(.ant-select-customize-input)) .ant-select-selector {
  background-color: transparent;
}
</style>
