<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="MCC编码">
              <a-input v-model:value.trim="where.mcc" placeholder="请输入MCC编码" allow-clear />
            </a-form-item>
            <a-form-item label="MCC名称">
              <a-input v-model:value.trim="where.mccName" placeholder="请输入MCC名称" allow-clear />
            </a-form-item>
            <a-form-item label="MCC大类">
              <a-input v-model:value.trim="where.mccBigClass" placeholder="请输入MCC大类" allow-clear />
            </a-form-item>
            <a-form-item label="MCC小类">
              <a-input v-model:value.trim="where.mccSmallClass" placeholder="请输入MCC小类" allow-clear />
            </a-form-item>
            <a-form-item label="编码类型">
              <a-select v-model:value="where.codeType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option value="UNION">银联</a-select-option>
                <a-select-option value="WECHAT">微信</a-select-option>
                <a-select-option value="ALIPAY">支付宝</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- table上边工具栏 -->
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'codeType'">
              <a-tag color="red" v-if="record.codeType === 'UNION'">银联</a-tag>
              <a-tag color="success" v-else-if="record.codeType === 'WECHAT'">微信</a-tag>
              <a-tag color="processing" v-else-if="record.codeType === 'ALIPAY'">支付宝</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
                <a-divider type="vertical" />
                <a @click="handleEdit(record)">修改</a>
                <a-divider type="vertical" />
                <a-popconfirm title="确定要删除此行数据吗？" @confirm="remove(record)">
                  <a class="ele-text-danger">删除</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 编辑 -->
    <AisleMccRelationEdit v-model:visible="showEdit" :data="current" @done="reload" />

    <!-- 详情 -->
    <AisleMccRelationDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { message } from 'ant-design-vue';
import { AisleMccRelationApi } from '@/api/aisleManage/AisleMccRelationApi';
import AisleMccRelationEdit from './AisleMccRelationEdit.vue';
import AisleMccRelationDetail from './AisleMccRelationDetail.vue';

export default {
  name: 'AisleMccRelation',
  components: {
    AisleMccRelationEdit,
    AisleMccRelationDetail
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: 'MCC编码',
          dataIndex: 'mcc',
          align: 'center'
        },
        {
          title: 'MCC名称',
          dataIndex: 'mccName'
        },
        {
          title: 'MCC大类',
          dataIndex: 'mccBigClass'
        },
        {
          title: 'MCC小类',
          dataIndex: 'mccSmallClass'
        },
        {
          title: '编码类型',
          key: 'codeType',
          dataIndex: 'codeType',
          align: 'center',
          width: 120
        },
        // {
        //   title: '创建人',
        //   dataIndex: 'createUserId'
        // },
        // {
        //   title: '创建时间',
        //   dataIndex: 'createTime'
        // },
        // {
        //   title: '最后修改人',
        //   dataIndex: 'lastModifyUserId'
        // },
        // {
        //   title: '最后修改时间',
        //   dataIndex: 'lastModifyTime'
        // },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right',
          width: 180
        }
      ],
      // 表格搜索条件
      where: {},
      current: null,
      showEdit: false,
      showDetail: false
    };
  },
  methods: {
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    async remove(row) {
      const result = await AisleMccRelationApi.delete({ id: row.id });
      message.success(result.message);
      this.reload();
    },

    datasource({ page, limit, where, orders }) {
      return AisleMccRelationApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
