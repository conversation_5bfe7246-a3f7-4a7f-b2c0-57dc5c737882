<template>
  <a-modal
    :width="750"
    :visible="visible"
    :maskClosable="false"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '30px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="MCC编码">{{ form.mcc }}</a-descriptions-item>
      <a-descriptions-item label="MCC名称">{{ form.mccName }}</a-descriptions-item>
      <a-descriptions-item label="MCC大类">{{ form.mccBigClass }}</a-descriptions-item>
      <a-descriptions-item label="MCC小类">{{ form.mccSmallClass }}</a-descriptions-item>
      <a-descriptions-item label="编码类型" :span="2">
        <a-tag color="red" v-if="form.codeType === 'UNION'">银联</a-tag>
        <a-tag color="success" v-else-if="form.codeType === 'WECHAT'">微信</a-tag>
        <a-tag color="processing" v-else-if="form.codeType === 'ALIPAY'">支付宝</a-tag>
      </a-descriptions-item>
    </a-descriptions>

    <a-descriptions title="易生" :column="2">
      <a-descriptions-item label="MCC编码">{{ form.easyMcc }}</a-descriptions-item>
      <a-descriptions-item label="MCC名称">{{ form.easyMccName }}</a-descriptions-item>
    </a-descriptions>
    <a-descriptions title="易票联" :column="2">
      <a-descriptions-item label="MCC编码">{{ form.efpsMcc }}</a-descriptions-item>
      <a-descriptions-item label="MCC名称">{{ form.efpsMccName }}</a-descriptions-item>
    </a-descriptions>
    <a-descriptions title="富友" :column="2">
      <a-descriptions-item label="MCC编码">{{ form.fuiouMcc }}</a-descriptions-item>
      <a-descriptions-item label="MCC名称">{{ form.fuiouMccName }}</a-descriptions-item>
    </a-descriptions>
    <a-descriptions title="快付通" :column="2">
      <a-descriptions-item label="MCC编码">{{ form.kftMcc }}</a-descriptions-item>
      <a-descriptions-item label="MCC名称">{{ form.kftMccName }}</a-descriptions-item>
    </a-descriptions>
    <a-descriptions title="厦门易生" :column="2">
      <a-descriptions-item label="MCC编码">{{ form.xmeasyMcc }}</a-descriptions-item>
      <a-descriptions-item label="MCC名称">{{ form.xmeasyMccName }}</a-descriptions-item>
    </a-descriptions>

    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
export default {
  props: {
    visible: Boolean,
    detail: Object,
  },
  emits: ['update:visible'],
  data() {
    return {
      form: {}
    };
  },
  watch: {
    detail() {
      this.form = Object.assign({}, this.detail);
    }
  },
  methods: {
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
