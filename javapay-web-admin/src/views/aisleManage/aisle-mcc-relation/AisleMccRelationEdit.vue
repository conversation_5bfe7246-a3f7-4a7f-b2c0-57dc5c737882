<template>
  <a-modal
    :width="1000"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新增'"
    :body-style="{ paddingBottom: '8px' }"
    :mask-closable="false"
    @update:visible="updateVisible"
    @cancel="cancel"
    @ok="save"
  >
    <a-form
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 7 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 17 }, sm: { span: 24 } }"
    >
      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="MCC编码" name="mcc">
            <a-input v-model:value="form.mcc" placeholder="请输入MCC编码" allow-clear />
          </a-form-item>
          <a-form-item label="MCC名称" name="mccName">
            <a-input v-model:value="form.mccName" placeholder="请输入MCC名称" allow-clear />
          </a-form-item>
          <a-form-item label="编码类型" name="codeType">
            <a-select v-model:value="form.codeType" style="width: 100%" placeholder="请选择" allow-clear>
              <a-select-option value="UNION">银联</a-select-option>
              <a-select-option value="WECHAT">微信</a-select-option>
              <a-select-option value="ALIPAY">支付宝</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="MCC大类" name="mccBigClass">
            <a-input v-model:value="form.mccBigClass" placeholder="请输入MCC大类" allow-clear />
          </a-form-item>
          <a-form-item label="MCC小类" name="mccSmallClass">
            <a-input v-model:value="form.mccSmallClass" placeholder="请输入MCC小类" allow-clear />
          </a-form-item>
        </a-col>
      </a-row>

      <a-divider orientation="left" dashed>易生</a-divider>
      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="MCC编码" name="easyMcc">
            <a-input v-model:value="form.easyMcc" placeholder="请输入MCC编码" allow-clear />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="MCC名称" name="easyMccName">
            <a-input v-model:value="form.easyMccName" placeholder="请输入MCC名称" allow-clear />
          </a-form-item>
        </a-col>
      </a-row>
      <a-divider orientation="left" dashed>易票联</a-divider>
      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="MCC编码" name="efpsMcc">
            <a-input v-model:value="form.efpsMcc" placeholder="请输入MCC编码" allow-clear />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="MCC名称" name="efpsMccName">
            <a-input v-model:value="form.efpsMccName" placeholder="请输入MCC名称" allow-clear />
          </a-form-item>
        </a-col>
      </a-row>
      <a-divider orientation="left" dashed>富友</a-divider>
      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="MCC编码" name="fuiouMcc">
            <a-input v-model:value="form.fuiouMcc" placeholder="请输入MCC编码" allow-clear />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="MCC名称" name="fuiouMccName">
            <a-input v-model:value="form.fuiouMccName" placeholder="请输入MCC名称" allow-clear />
          </a-form-item>
        </a-col>
      </a-row>
      <a-divider orientation="left" dashed>快付通</a-divider>
      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="MCC编码" name="kftMcc">
            <a-input v-model:value="form.kftMcc" placeholder="请输入MCC编码" allow-clear />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="MCC名称" name="kftMccName">
            <a-input v-model:value="form.kftMccName" placeholder="请输入MCC名称" allow-clear />
          </a-form-item>
        </a-col>
      </a-row>
      <a-divider orientation="left" dashed>厦门易生</a-divider>
      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="MCC编码" name="xmeasyMcc">
            <a-input v-model:value="form.xmeasyMcc" placeholder="请输入MCC编码" allow-clear />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="MCC名称" name="xmeasyMccName">
            <a-input v-model:value="form.xmeasyMccName" placeholder="请输入MCC名称" allow-clear />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { AisleMccRelationApi } from '@/api/aisleManage/AisleMccRelationApi';

function formDefaults() {
  return {};
}

export default {
  props: {
    visible: Boolean,
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      form: Object.assign({}, this.data),
      rules: {
        codeType: [{ required: true, message: '请选择编码类型' }],
        mcc: [{ required: true, message: '请输入MCC编码' }],
        mccName: [{ required: true, message: '请输入MCC名称' }],
        mccBigClass: [{ required: false, message: '请输入MCC大类' }],
        mccSmallClass: [{ required: true, message: '请输入MCC小类' }],
        easyMcc: [{ required: true, message: '请输入MCC编码' }],
        easyMccName: [{ required: true, message: '请输入MCC名称' }],
        efpsMcc: [{ required: true, message: '请输入MCC编码' }],
        efpsMccName: [{ required: true, message: '请输入MCC名称' }],
        fuiouMcc: [{ required: true, message: '请输入MCC编码' }],
        fuiouMccName: [{ required: true, message: '请输入MCC名称' }],
        kftMcc: [{ required: true, message: '请输入MCC编码' }],
        kftMccName: [{ required: true, message: '请输入MCC名称' }],
        xmeasyMcc: [{ required: true, message: '请输入MCC编码' }],
        xmeasyMccName: [{ required: true, message: '请输入MCC名称' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false
    };
  },
  watch: {
    data() {
      if (this.data) {
        this.form = Object.assign({}, this.data);
        this.isUpdate = true;
      } else {
        this.form = formDefaults();
        this.isUpdate = false;
      }
      if (this.$refs.form) {
        this.$refs.form.clearValidate();
      }
    }
  },
  methods: {
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      // 执行编辑或修改方法
      if (this.isUpdate) {
        result = AisleMccRelationApi.edit(this.form);
      } else {
        result = AisleMccRelationApi.add(this.form);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 如果是新增，则form表单置空
          if (!this.isUpdate) {
            this.form = formDefaults();
          }

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    cancel() {
      this.form = Object.assign(formDefaults(), this.data);
      this.$refs.form.clearValidate();
    },

    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
