<template>
  <a-modal
    :width="1000"
    :visible="visible"
    title="交易参数信息"
    :mask-closable="false"
    :body-style="{ paddingBottom: '30px' }"
    @update:visible="updateVisivle"
  >
    <a-descriptions :column="3">
      <a-descriptions-item label="主密钥">{{ form.masterKey }}</a-descriptions-item>
      <a-descriptions-item label="主密钥索引">{{ form.masterOnLmk }}</a-descriptions-item>
      <a-descriptions-item label="主密钥校验值">{{ form.masterCheckValue }}</a-descriptions-item>
      <a-descriptions-item label="MAC密钥">{{ form.macKey }}</a-descriptions-item>
      <a-descriptions-item label="MAC密钥索引">{{ form.macOnLmk }}</a-descriptions-item>
      <a-descriptions-item label="MAC密钥校验值">{{ form.macCheckValue }}</a-descriptions-item>
      <a-descriptions-item label="PIN密钥">{{ form.pinKey }}</a-descriptions-item>
      <a-descriptions-item label="PIN密钥索引">{{ form.pinOnLmk }}</a-descriptions-item>
      <a-descriptions-item label="PIN密钥校验值">{{ form.pinCheckValue }}</a-descriptions-item>
      <a-descriptions-item label="磁道密钥">{{ form.trackKey }}</a-descriptions-item>
      <a-descriptions-item label="磁道密钥索引">{{ form.trackOnLmk }}</a-descriptions-item>
      <a-descriptions-item label="磁道密钥校验值">{{ form.trackCheckValue }}</a-descriptions-item>
      <a-descriptions-item label="签到日期">{{ form.signinDate || '尚未签到' }}</a-descriptions-item>
    </a-descriptions>

    <template #footer>
      <a-button @click="updateVisivle(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  data() {
    return {
      form: {},
    };
  },
  mounted() {
    this.form = Object.assign({}, this.detail);
  },
  methods: {
    updateVisivle(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
