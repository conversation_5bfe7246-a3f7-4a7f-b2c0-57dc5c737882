<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="代理商编号">
              <a-input v-model:value.trim="where.agentNo" placeholder="请输入代理商编号" allow-clear />
            </a-form-item>
            <a-form-item label="渠道商户号">
              <a-input v-model:value.trim="where.chnMerchNo" placeholder="请输入渠道商户号" allow-clear />
            </a-form-item>
            <a-form-item label="商户号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户号" allow-clear />
            </a-form-item>
            <a-form-item label="终端SN号">
              <a-input v-model:value.trim="where.termSn" placeholder="请输入终端SN号" allow-clear />
            </a-form-item>
            <a-form-item label="终端编号">
              <a-input v-model:value.trim="where.terminalNo" placeholder="请输入终端编号" allow-clear />
            </a-form-item>
            <a-form-item label="支付通道">
              <a-select v-model:value="where.channelCode" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option
                  v-for="({ channelName, channelCode }, key) in channelCodes" :key="key"
                  :value="channelCode">
                  {{ channelName }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="银联终端绑定状态">
              <a-select v-model:value="where.unionSnBindStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">未绑定</a-select-option>
                <a-select-option :value="1">已绑定</a-select-option>
                <a-select-option :value="2">绑定失败</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="绑机时间(开始)">
              <a-date-picker v-model:value="where.searchBeginTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item label="绑机时间(结束)">
              <a-date-picker v-model:value="where.searchEndTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table
          ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where"
          :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'unionSnBindStatus'">
              <a-tag v-if="record.unionSnBindStatus === 0">未绑定</a-tag>
              <a-tag color="success" v-else-if="record.unionSnBindStatus === 1">已绑定</a-tag>
              <a-tag color="error" v-else-if="record.unionSnBindStatus === 2">绑定失败</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleShowDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <AisleMerchTerminalDetail
      v-if="showDetail" v-model:visible="showDetail" :detail="current"
      :channelCodes="channelCodes" />

    <TransParamsInfo v-if="showTransParamsInfo" v-model:visible="showTransParamsInfo" :detail="current" />
    <ChannelTransParameter v-if="showChannelTransParameter" v-model:visible="showChannelTransParameter" :detail="current" />
  </div>
</template>

<script>
import { AisleMerchTerminalApi } from '@/api/aisleManage/AisleMerchTerminalApi';
import { ChannelManageApi } from '@/api/trandingChannel/channel-manage/ChannelManageApi';
import AisleMerchTerminalDetail from './AisleMerchTerminalDetail.vue';
import TransParamsInfo from './TransParamsInfo.vue';
import ChannelTransParameter from './ChannelTransParameter.vue';
import { message } from 'ant-design-vue';
import { ChannelMerchTermTransApi } from '@/api/aisleManage/ChannelMerchTermTransApi';

export default {
  name: 'AisleMerchTerminal',
  components: { AisleMerchTerminalDetail, TransParamsInfo, ChannelTransParameter},
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '支付通道',
          dataIndex: 'channelCode',
          align: 'center',
          customRender: ({ text }) => {
            const item = this.channelCodes.find(c => c.channelCode === text);
            return item?.channelName || '--';
          }
        },
        {
          title: '终端SN号',
          dataIndex: 'termSn',
          align: 'center'
        },
        {
          title: '终端编号',
          dataIndex: 'terminalNo',
          align: 'center'
        },
        {
          title: '代理商编号',
          dataIndex: 'agentNo',
          align: 'center'
        },
        {
          title: '绑机时间',
          dataIndex: 'bindTime',
          align: 'center'
        },
        {
          title: '通道商户名称',
          dataIndex: 'chnMerchFullName',
          align: 'center'
        },
        {
          title: '通道商户简称',
          dataIndex: 'chlMerchShortName',
          align: 'center'
        },
        {
          title: '渠道商户号',
          dataIndex: 'chnMerchNo',
          align: 'center'
        },
        {
          title: '商户号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '终端绑定状态',
          dataIndex: 'unionSnBindStatus',
          key: 'unionSnBindStatus',
          align: 'center'
        },
        {
          title: '终端绑定状态备注',
          dataIndex: 'remarkMessage',
          align:'center'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          width: 120,
          fixed: 'right'
        }
      ],
      // 表格搜索条件
      where: {},
      current: null,
      showDetail: false,
      showTransParamsInfo: false,
      showChannelTransParameter: false,
      channelCodes: []
    };
  },
  mounted() {
    this.getChannelCodes();
  },
  methods: {
    async reReport(row) {
      const result = await AisleMerchTerminalApi.reReportUnionTerminalSn({ id: row.id });
      message.success(result.message);
      this.reload();
    },

    async handleTransParam(row) {
      const data = await AisleMerchTerminalApi.terminalTransInfo({ terminalId: row.id });
      this.current = data;
      this.showTransParamsInfo = true;
    },

    async handleChannelTransParameter(row) {
      const data = await ChannelMerchTermTransApi.getDetailByTerminalSn({ terminalSn: row.termSn});
      this.current = data;
      this.showChannelTransParameter = true;
    },

    //展示详情
    handleShowDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    async getChannelCodes() {
      const data = await ChannelManageApi.list({ validStatus: 1 });
      this.channelCodes = data || [];
    },

    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    datasource({ page, limit, where }) {
      return AisleMerchTerminalApi.findPage({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>
