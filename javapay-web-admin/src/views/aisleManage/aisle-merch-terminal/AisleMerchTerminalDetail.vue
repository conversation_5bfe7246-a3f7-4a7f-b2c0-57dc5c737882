<template>
  <a-modal
    :width="1000" :visible="visible" title="详情" :mask-closable="false" :body-style="{ paddingBottom: '30px' }"
    @update:visible="updateVisivle">
    <a-descriptions :column="2">
      <a-descriptions-item label="支付通道" :span="2">{{ form.channelName }}</a-descriptions-item>
      <a-descriptions-item label="终端SN号">{{ form.termSn }}</a-descriptions-item>
      <a-descriptions-item label="终端编号">{{ form.terminalNo }}</a-descriptions-item>
      <a-descriptions-item label="代理商编号">{{ form.agentNo }}</a-descriptions-item>
      <a-descriptions-item label="绑机时间" :span="2">{{ form.bindTime }}</a-descriptions-item>
      <a-descriptions-item label="通道商户名称">{{ form.chnMerchFullName }}</a-descriptions-item>
      <a-descriptions-item label="通道商户简称">{{ form.chlMerchShortName }}</a-descriptions-item>
      <a-descriptions-item label="渠道商户Id">{{ form.chlMerchantId }}</a-descriptions-item>
      <a-descriptions-item label="渠道商户号">{{ form.chnMerchNo }}</a-descriptions-item>
      <a-descriptions-item label="商户Id">{{ form.merchantId }}</a-descriptions-item>
      <a-descriptions-item label="商户号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="交易设备终端主密钥" :span="2">{{ form.tradeTradeMainKey }}</a-descriptions-item>
      <a-descriptions-item label="终端绑定状态">
        <a-tag v-if="form.unionSnBindStatus === 0">未绑定</a-tag>
        <a-tag color="success" v-else-if="form.unionSnBindStatus === 1">已绑定</a-tag>
        <a-tag color="error" v-else-if="form.unionSnBindStatus === 2">绑定失败</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="终端绑定状态备注">{{ form.remarkMessage }}</a-descriptions-item>
    </a-descriptions>

    <template #footer>
      <a-button @click="updateVisivle(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
export default {
  props: {
    visible: Boolean,
    detail: Object,
    channelCodes: Array
  },
  emits: ['update:visible'],
  data() {
    return {
      form: {}
    };
  },
  mounted() {
    this.form = Object.assign({}, this.detail);
    const channelItem = this.channelCodes.find(i => i.channelCode === this.form.channelCode);
    this.form.channelName = channelItem?.channelName;
  },
  methods: {
    updateVisivle(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
