<template>
  <a-modal :width="800" :visible="visible" title="详情" :body-style="{ paddingBottom: '8px' }" @update:visible="updateVisible">
    <a-descriptions :column="2" bordered>
      <a-descriptions-item label="任务名称">{{ form.taskName }}</a-descriptions-item>
      <a-descriptions-item label="机构编号">{{ form.orgNo }}</a-descriptions-item>
      <a-descriptions-item label="关联用户编号">{{ form.connectUserNo }}</a-descriptions-item>
      <a-descriptions-item label="交易笔数">{{ form.transNums }}</a-descriptions-item>
      <a-descriptions-item label="标签ID">{{ form.tagId }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
