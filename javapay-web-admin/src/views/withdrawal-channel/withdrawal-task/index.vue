<template>
  <div class="ele-body">
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="任务名称">
              <a-input v-model:value.trim="where.taskName" placeholder="任务名称" allow-clear />
            </a-form-item>
            <a-form-item label="机构编号">
              <a-input v-model:value.trim="where.orgNo" placeholder="机构编号" allow-clear />
            </a-form-item>
            <a-form-item label="关联用户编号">
              <a-input v-model:value.trim="where.connectUserNo" placeholder="关联用户编号" allow-clear />
            </a-form-item>
            <a-form-item label="任务开始时间">
              <a-date-picker v-model:value="where.taskStartTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item label="任务截止时间">
              <a-date-picker v-model:value="where.taskEndTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <!-- table操作栏按钮 -->
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <WithdrawalTaskDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { WithdrawalTaskApi } from '@/api/withdrawal-channel/WithdrawalTaskApi';
import WithdrawalTaskDetail from './withdrawal-task-detail.vue';

export default {
  name: 'WithdrawalChannelTask',
  components: {
    WithdrawalTaskDetail
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          align: 'center',
          width: 80,
          fixed: 'left'
        },
        {
          title: '任务ID',
          dataIndex: 'taskId',
          align: 'center'
        },
        {
          title: '任务名称',
          dataIndex: 'taskName',
          align: 'center'
        },
        {
          title: '机构编号',
          dataIndex: 'orgNo',
          align: 'center'
        },
        {
          title: '关联用户编号',
          dataIndex: 'connectUserNo',
          align: 'center'
        },
        {
          title: '交易笔数',
          dataIndex: 'transNums',
          align: 'center'
        },
        {
          title: '标签ID',
          dataIndex: 'tagId',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 120,
          align: 'center'
        }
      ],
      // 表格搜索条件
      where: {},
      // 当前编辑数据
      current: null,
      // 是否显示编辑弹窗
      showDetail: false
    };
  },
  methods: {
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where, orders }) {
      return WithdrawalTaskApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
