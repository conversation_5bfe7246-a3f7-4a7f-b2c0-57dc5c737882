<template>
  <div class="ele-body">
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="机构编号">
              <a-input v-model:value.trim="where.orgNo" placeholder="机构编号" allow-clear />
            </a-form-item>
            <a-form-item label="机构名称">
              <a-input v-model:value.trim="where.orgName" placeholder="机构名称" allow-clear />
            </a-form-item>
            <a-form-item label="签约主体名称">
              <a-input v-model:value.trim="where.signOrgName" placeholder="签约主体名称" allow-clear />
            </a-form-item>
            <a-form-item label="开发者编号">
              <a-input v-model:value.trim="where.clientKey" placeholder="开发者编号" allow-clear />
            </a-form-item>
            <a-form-item label="发票类目">
              <a-input v-model:value.trim="where.invoiceContent" placeholder="发票类目" allow-clear />
            </a-form-item>
            <a-form-item label="是否平台出款">
              <a-select v-model:value="where.isPlatform" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">是</a-select-option>
                <a-select-option :value="0">否</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- table上边工具栏 -->
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'isPlatform'">
              <a-tag v-if="record.isPlatform === 0">否</a-tag>
              <a-tag v-else-if="record.isPlatform === 1" color="blue">是</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
                <a-divider type="vertical" />
                <a @click="handleEdit(record)">修改</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 编辑 -->
    <OrgConfigEdit v-model:visible="showEdit" v-if="showEdit" :data="current" @done="reload" />

    <!-- 详情 -->
    <OrgConfigDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { OrgConfigApi } from '@/api/withdrawal-channel/OrgConfigApi';
import OrgConfigDetail from './org-config-detail.vue';
import OrgConfigEdit from './org-config-edit.vue';

export default {
  name: 'OrgConfig',
  components: {
    OrgConfigDetail,
    OrgConfigEdit
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          align: 'center',
          width: 80,
          fixed: 'left'
        },
        {
          title: '机构编号',
          dataIndex: 'orgNo',
          align: 'center'
        },
        {
          title: '机构名称',
          dataIndex: 'orgName',
          align: 'center'
        },
        {
          title: '签约主体名称',
          dataIndex: 'signOrgName',
          align: 'center'
        },
        {
          title: '税率(%)',
          dataIndex: 'taxRate',
          align: 'center'
        },
        {
          title: '税源地描述',
          dataIndex: 'taxLocationDesc',
          align: 'center'
        },
        {
          title: '是否平台出款',
          dataIndex: 'isPlatform',
          key: 'isPlatform',
          align: 'center'
        },
        {
          title: '开发者编号(client_key)',
          dataIndex: 'clientKey',
          align: 'center'
        },
        {
          title: '签名密钥(sign_key)',
          dataIndex: 'signKey',
          align: 'center'
        },
        {
          title: '资质方ID(tax_agent_parent_id)',
          dataIndex: 'taxAgentParentId',
          align: 'center'
        },
        {
          title: '委托方ID(tax_agent_location_id)',
          dataIndex: 'taxAgentLocationId',
          align: 'center'
        },
        {
          title: '发票类目',
          dataIndex: 'invoiceContent',
          align: 'center'
        },
        {
          title: '任务描述',
          dataIndex: 'taskDesc',
          align: 'center'
        },
        {
          title: '任务地点',
          dataIndex: 'taskLocation',
          align: 'center'
        },
        {
          title: '任务验收标准',
          dataIndex: 'acceptanceStandard',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 180,
          align: 'center'
        }
      ],
      // 表格搜索条件
      where: {},
      // 当前编辑数据
      current: null,
      showDetail: false,
      showEdit: false
    };
  },
  methods: {
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    handleEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    datasource({ page, limit, where, orders }) {
      return OrgConfigApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
