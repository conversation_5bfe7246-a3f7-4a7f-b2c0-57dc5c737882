<template>
  <a-modal :width="800" :visible="visible" title="详情" :body-style="{ paddingBottom: '8px' }" @update:visible="updateVisible">
    <a-descriptions :column="2">
      <a-descriptions-item label="机构编号">{{ form.orgNo }}</a-descriptions-item>
      <a-descriptions-item label="机构名称">{{ form.orgName }}</a-descriptions-item>
      <a-descriptions-item label="税源地描述">{{ form.taxLocationDesc }}</a-descriptions-item>
      <a-descriptions-item label="签约主体名称">{{ form.signOrgName }}</a-descriptions-item>
      <a-descriptions-item label="开发者编号(client_key)">{{ form.clientKey }}</a-descriptions-item>
      <a-descriptions-item label="签名密钥(sign_key)">{{ form.signKey }}</a-descriptions-item>
      <a-descriptions-item label="资质方ID(tax_agent_parent_id)">{{ form.taxAgentParentId }}</a-descriptions-item>
      <a-descriptions-item label="委托方ID(tax_agent_location_id)">{{ form.taxAgentLocationId }}</a-descriptions-item>
      <a-descriptions-item label="是否平台出款">
        <a-tag v-if="form.isPlatform === 0">否</a-tag>
        <a-tag v-else-if="form.isPlatform === 1" color="blue">是</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="发票类目">{{ form.invoiceContent }}</a-descriptions-item>
      <a-descriptions-item label="税率(%)">{{ form.taxRate }}</a-descriptions-item>
      <a-descriptions-item label="任务描述">{{ form.taskDesc }}</a-descriptions-item>
      <a-descriptions-item label="任务地点">{{ form.taskLocation }}</a-descriptions-item>
      <a-descriptions-item label="任务验收标准" :span="2">{{ form.acceptanceStandard }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
