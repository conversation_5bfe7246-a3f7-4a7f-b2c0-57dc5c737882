<template>
  <a-modal
    :width="1000"
    :visible="visible"
    title="选择代理商"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <div class="ele-body">
      <!-- 搜索框内容 -->
      <div class="block-interval">
        <a-card :bordered="false">
          <a-form layout="inline" :model="where">
            <a-row :gutter="[0, 16]">
              <a-form-item label="代理商名称">
                <a-input v-model:value.trim="where.agentName" placeholder="请输入代理商名称" allow-clear />
              </a-form-item>
              <a-form-item label="代理商简称">
                <a-input v-model:value.trim="where.agentSname" placeholder="请输入代理商简称" allow-clear />
              </a-form-item>
              <a-form-item label="归属大区编号" v-if="hasPurview(['0'])">
                <a-input v-model:value.trim="where.regionNo" placeholder="请输入归属大区编号" allow-clear />
              </a-form-item>
              <a-form-item label="归属运营中心编号" v-if="hasPurview(['0', '1'])">
                <a-input v-model:value.trim="where.branchNo" placeholder="请输入归属运营中心编号" allow-clear />
              </a-form-item>
              <a-form-item label="子代分润出款方式">
                <a-select v-model:value="where.subAgentSettleChannelWay" style="width: 205px" placeholder="请选择" allow-clear>
                  <!-- <a-select-option :value="1">平台税筹账户</a-select-option>
                  <a-select-option :value="2">一代税筹账户</a-select-option> -->
                  <a-select-option :value="3">展业平台</a-select-option>
                  <a-select-option :value="1">平台出款</a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item class="ele-text-center">
                <a-space>
                  <a-button type="primary" @click="reload">查询</a-button>
                  <a-button @click="reset">重置</a-button>
                </a-space>
              </a-form-item>
            </a-row>
          </a-form>
        </a-card>
      </div>

      <!-- 表格内容 -->
      <div>
        <a-card :bordered="false" class="table-height">
          <ele-pro-table
            ref="table"
            row-key="id"
            :datasource="datasource"
            :columns="columns"
            :where="where"
            v-model:current="selectedRow"
            :scroll="{ x: 'max-content' }"
          >
            <!-- 表体的操作 -->
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'remitType'">
                <a-tag v-if="record.remitType === 1" color="cyan">平台清算</a-tag>
                <a-tag v-else-if="record.remitType === 2" color="pink">自行提现</a-tag>
                <span v-else>--</span>
              </template>

              <template v-else-if="column.key === 'settleMode'">
                <a-tag v-if="record.settleMode === 1" color="cyan">日结</a-tag>
                <a-tag v-else-if="record.settleMode === 2" color="blue">月结</a-tag>
                <span v-else>--</span>
              </template>

              <template v-else-if="column.key === 'subAgentSettleChannelWay'">
                <a-tag v-if="record.subAgentSettleChannelWay === 3" color="orange">展业平台</a-tag>
                <a-tag v-else-if="record.subAgentSettleChannelWay === 1" color="blue">平台出款</a-tag>
              </template>

              <template v-else-if="column.key === 'agentStatus'">
                <a-tag v-if="record.agentStatus?.checkStatus === 3" color="success">
                  <template #icon> <check-circle-outlined /> </template>正常</a-tag
                >
                <a-tag v-else-if="record.agentStatus?.checkStatus === 4" color="processing">
                  <template #icon> <sync-outlined :spin="true" /> </template> 审核中</a-tag
                >
                <a-tag v-else-if="record.agentStatus?.checkStatus === 5" color="error">
                  <template #icon> <close-circle-outlined /> </template> 驳回待编辑</a-tag
                >
              </template>
            </template>
          </ele-pro-table>
        </a-card>
      </div>
    </div>
  </a-modal>
</template>

<script>
import { AgentCenterApi } from '@/api/businessTeam/agent-center/AgentCenterApi';
import { hasPurview } from '@/utils/permission';

export default {
  props: {
    visible: Boolean
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      //表格查询条件
      where: {
        orgType: 1
      },
      //选择数据
      selectedRow: null,
      // 表格列配置
      columns: [
        {
          title: '代理商编号',
          dataIndex: 'agentNo',
          align: 'center',
          fixed: 'left'
        },
        {
          title: '代理商名称',
          dataIndex: 'agentName'
        },
        {
          title: '代理商简称',
          dataIndex: 'agentSname'
        },
        {
          title: '归属大区编号',
          dataIndex: 'regionNo',
          align: 'center',
          hideCol: !hasPurview(['0'])
        },
        {
          title: '归属运营中心编号',
          dataIndex: 'branchNo',
          align: 'center',
          hideCol: !hasPurview(['0', '1'])
        },
        {
          title: '法人姓名',
          dataIndex: 'legalName',
          align: 'center'
        },
        {
          title: '法人电话号码',
          dataIndex: 'legalTelMask',
          align: 'center'
        },
        {
          title: '联系人姓名',
          dataIndex: 'contactsName',
          align: 'center'
        },
        {
          title: '联系人电话号码',
          dataIndex: 'contactsTelMask',
          align: 'center'
        },
        {
          title: '清算类型',
          dataIndex: 'remitType',
          key: 'remitType',
          align: 'center'
        },
        {
          title: '结算方式',
          dataIndex: 'settleMode',
          key: 'settleMode',
          align: 'center'
        },
        {
          title: '子代分润出款方式',
          dataIndex: 'subAgentSettleChannelWay',
          key: 'subAgentSettleChannelWay',
          align: 'center'
        }
      ].filter(i => !i.hideCol)
    };
  },
  methods: {
    save() {
      this.$emit('done', this.selectedRow);
      this.updateVisible(false);
    },
    reset() {
      this.where = { orgType: 1 };
      this.selectedRow = null;
      this.$refs.table.reload({ page: 1, where: this.where });
    },
    reload() {
      this.selectedRow = null;
      this.$refs.table.reload({ page: 1 });
    },
    hasPurview,
    updateVisible(value) {
      this.$emit('update:visible', value);
    },
    datasource({ page, limit, where }) {
      return AgentCenterApi.findPage({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>
