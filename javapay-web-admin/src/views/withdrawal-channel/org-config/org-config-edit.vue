<template>
  <a-modal
    :width="1000"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新增'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules" :labelCol="{ style: { width: '120px' } }">
      <a-row :gutter="24">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="机构编号" name="orgNo">
            <a-input v-model:value="form.orgNo" placeholder="请输入机构编号" allow-clear :disabled="isUpdate">
              <template #addonAfter v-if="!isUpdate && hasPurview(['0','1','2'])">
                <span @click="showSelectOrg = true">选择</span>
              </template>
            </a-input>
          </a-form-item>
          <a-form-item label="签名密钥" name="signKey">
            <a-input v-model:value="form.signKey" placeholder="请输入签名密钥(sign_key)" allow-clear :disabled="isUpdate" />
          </a-form-item>
          <a-form-item label="开发者编号" name="clientKey">
            <a-input v-model:value="form.clientKey" placeholder="请输入开发者编号(client_key)" allow-clear :disabled="isUpdate" />
          </a-form-item>
          <a-form-item label="是否平台出款" name="isPlatform">
            <a-select v-model:value="form.isPlatform" style="width: 100%" placeholder="请选择" :disabled="isUpdate">
              <a-select-option :value="1">是</a-select-option>
              <a-select-option :value="0">否</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="任务地点" name="taskLocation">
            <a-input v-model:value="form.taskLocation" placeholder="请输入任务地点" allow-clear />
          </a-form-item>
          <a-form-item label="任务验收标准" name="acceptanceStandard">
            <a-textarea
              v-model:value="form.acceptanceStandard"
              placeholder="请输入任务验收标准"
              :auto-size="{ minRows: 1, maxRows: 3 }"
              allow-clear
            />
          </a-form-item>
          <a-form-item label="委托方ID" name="taxAgentLocationId">
            <a-input v-model:value="form.taxAgentLocationId" placeholder="请输入委托方ID(tax_agent_location_id)" allow-clear :disabled="isUpdate" />
          </a-form-item>
          <a-form-item label="税源地描述" name="taxLocationDesc">
            <a-textarea
              v-model:value="form.taxLocationDesc"
              :auto-size="{ minRows: 1, maxRows: 3 }"
              placeholder="请输入税源地描述"
              allow-clear
            />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="机构名称" name="orgName">
            <a-input v-model:value="form.orgName" placeholder="请输入机构名称" allow-clear />
          </a-form-item>

          <a-form-item label="签约主体名称" name="signOrgName">
            <a-input v-model:value="form.signOrgName" placeholder="请输入签约主体名称" allow-clear />
          </a-form-item>
          <a-form-item label="发票类目" name="invoiceContent">
            <a-input v-model:value="form.invoiceContent" placeholder="请输入发票类目" allow-clear />
          </a-form-item>
          <a-form-item />
          <a-form-item label="任务描述" name="taskDesc">
            <a-textarea v-model:value="form.taskDesc" :auto-size="{ minRows: 1, maxRows: 3 }" placeholder="请输入任务描述" allow-clear />
          </a-form-item>
          <a-form-item />
          <a-form-item label="资质方ID" name="taxAgentParentId">
            <a-input v-model:value="form.taxAgentParentId" placeholder="请输入资质方ID(tax_agent_parent_id)" allow-clear :disabled="isUpdate" />
          </a-form-item>
          <a-form-item label="税率(%)" name="taxRate">
            <a-input v-model:value="form.taxRate" placeholder="请输入税率(%)" allow-clear />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>

    <!-- 选择代理商 -->
    <AgentSelect v-model:visible="showSelectOrg" v-if="showSelectOrg" @done="handleSelectOrg" />
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { OrgConfigApi } from '@/api/withdrawal-channel/OrgConfigApi';
import AgentSelect from './agent-select.vue';
import { hasPurview } from '@/utils/permission';

export default {
  components: { AgentSelect },
  props: {
    visible: Boolean,
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {
        isPlatform: 0
      },
      // 表单验证规则
      rules: {
        orgName: [{ required: true, message: '请输入机构名称' }],
        orgNo: [{ required: true, message: '请输入机构编号' }],
        signKey: [{ required: true, message: '请输入签名密钥' }],
        signOrgName: [{ required: true, message: '请输入签约主体名称' }],
        clientKey: [{ required: true, message: '请输入开发者编号' }],
        // invoiceContent: [{ required: true, message: '请输入发票类目' }], // 非必输
        isPlatform: [{ required: true, message: '请选择' }],
        taskDesc: [{ required: true, message: '请输入任务描述' }],
        taskLocation: [{ required: true, message: '请输入任务地点' }],
        acceptanceStandard: [{ required: true, message: '请输入任务验收标准' }],
        taxAgentLocationId: [{ required: true, message: '请输入委托方ID' }],
        taxAgentParentId: [{ required: true, message: '请输入资质方ID' }],
        taxLocationDesc: [{ required: true, message: '请输入税源地描述' }],
        taxRate: [{ required: true, message: '请输入税率(%)' }]
      },
      loading: false,
      isUpdate: false,
      showSelectOrg: false
    };
  },
  mounted() {
    if (this.data) {
      this.isUpdate = true;
      this.form = Object.assign({}, this.data);
    }
  },
  methods: {
    hasPurview,
    handleSelectOrg(row) {
      const { agentName, agentNo } = row || {};
      this.form.orgNo = agentNo;
      this.form.orgName = this.form.signOrgName = agentName;
    },
    async save() {
      // 校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;

      // 执行编辑或修改方法
      if (this.isUpdate) {
        result = OrgConfigApi.edit(this.form);
      } else {
        result = OrgConfigApi.add(this.form);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
