<template>
  <div class="ele-body">
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="机构编号">
              <a-input v-model:value.trim="where.orgNo" placeholder="机构编号" allow-clear />
            </a-form-item>
            <a-form-item label="真实姓名">
              <a-input v-model:value.trim="where.realName" placeholder="真实姓名" allow-clear />
            </a-form-item>
            <a-form-item label="请求流水号">
              <a-input v-model:value.trim="where.requestId" placeholder="请求流水号" allow-clear />
            </a-form-item>
            <a-form-item label="用户编号">
              <a-input v-model:value.trim="where.userNo" placeholder="用户编号" allow-clear />
            </a-form-item>
            <a-form-item label="用户类型">
              <a-select v-model:value="where.userType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">大区</a-select-option>
                <a-select-option :value="2">运营中心</a-select-option>
                <a-select-option :value="3">代理商</a-select-option>
                <a-select-option :value="5">子级代理商</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="处理状态">
              <a-select v-model:value="where.processState" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">初始化</a-select-option>
                <a-select-option :value="1">成功</a-select-option>
                <a-select-option :value="2">失败</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="处理类型">
              <a-select v-model:value="where.processType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">签约</a-select-option>
                <a-select-option :value="1">认证</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'processType'">
              <a-tag v-if="record.processType === 0" color="yellow">签约</a-tag>
              <a-tag v-else-if="record.processType === 1" color="blue">认证</a-tag>
            </template>

            <template v-else-if="column.key === 'processState'">
              <a-tag v-if="record.processState === 0">初始化</a-tag>
              <a-tag v-else-if="record.processState === 1" color="green">成功</a-tag>
              <a-tag v-else-if="record.processState === 2" color="red">失败</a-tag>
            </template>

            <template v-else-if="column.key === 'userType'">
              <a-badge v-if="record.userType === 1" color="pink" text="大区" />
              <a-badge v-else-if="record.userType === 2" color="blue" text="运营中心" />
              <a-badge v-else-if="record.userType === 3" color="cyan" text="代理商" />
              <a-badge v-else-if="record.userType === 5" color="purple" text="子级代理商" />
            </template>

            <!-- table操作栏按钮 -->
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <UserSignDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { UserSignApi } from '@/api/withdrawal-channel/UserSignApi';
import UserSignDetail from './user-sign-detail.vue';

export default {
  name: 'UserSign',
  components: {
    UserSignDetail
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          align: 'center',
          width: 80,
          fixed: 'left'
        },
        {
          title: '机构编号',
          dataIndex: 'orgNo',
          align: 'center'
        },
        {
          title: '真实姓名',
          dataIndex: 'realName',
          align: 'center'
        },
        {
          title: '证件号码',
          dataIndex: 'idCardMask',
          align: 'center'
        },
        {
          title: '预留手机号',
          dataIndex: 'mobileMask',
          align: 'center'
        },
        {
          title: '处理类型',
          dataIndex: 'processType',
          key: 'processType',
          align: 'center'
        },
        {
          title: '处理状态',
          dataIndex: 'processState',
          key: 'processState',
          align: 'center'
        },
        {
          title: '失败原因',
          dataIndex: 'failReason'
        },
        {
          title: '请求流水号',
          dataIndex: 'requestId',
          align: 'center'
        },
        {
          title: '用户类型',
          dataIndex: 'userType',
          key: 'userType',
          align: 'center'
        },
        {
          title: '用户编号',
          dataIndex: 'userNo',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 120,
          align: 'center'
        }
      ],
      // 表格搜索条件
      where: {},
      // 当前编辑数据
      current: null,
      // 是否显示编辑弹窗
      showDetail: false
    };
  },
  methods: {
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where, orders }) {
      return UserSignApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
