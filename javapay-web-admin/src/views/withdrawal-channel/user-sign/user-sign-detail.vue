<template>
  <a-modal :width="800" :visible="visible" title="详情" :body-style="{ paddingBottom: '8px' }" @update:visible="updateVisible">
    <a-descriptions :column="2">
      <a-descriptions-item label="机构编号">{{ form.orgNo }}</a-descriptions-item>
      <a-descriptions-item label="真实姓名">{{ form.realName }}</a-descriptions-item>
      <a-descriptions-item label="证件号码">{{ form.idCardMask }}</a-descriptions-item>
      <a-descriptions-item label="预留手机号">{{ form.mobileMask }}</a-descriptions-item>
      <a-descriptions-item label="处理类型">
        <a-tag v-if="form.processType === 0" color="yellow">签约</a-tag>
        <a-tag v-else-if="form.processType === 1" color="blue">认证</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="处理状态">
        <a-tag v-if="form.processState === 0">初始化</a-tag>
        <a-tag v-else-if="form.processState === 1" color="green">成功</a-tag>
        <a-tag v-else-if="form.processState === 2" color="red">失败</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="失败原因" :span="2">{{ form.failReason }}</a-descriptions-item>
      <a-descriptions-item label="请求流水号" :span="2">{{ form.requestId }}</a-descriptions-item>
      <a-descriptions-item label="用户类型">
        <a-badge v-if="form.userType === 1" color="pink" text="大区" />
        <a-badge v-else-if="form.userType === 2" color="blue" text="运营中心" />
        <a-badge v-else-if="form.userType === 3" color="cyan" text="代理商" />
        <a-badge v-else-if="form.userType === 5" color="cyan" text="子级代理商" />
      </a-descriptions-item>
      <a-descriptions-item label="用户编号">{{ form.userNo }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
