<template>
  <a-modal :width="800" :visible="visible" title="详情" :body-style="{ paddingBottom: '8px' }" @update:visible="updateVisible">
    <a-descriptions :column="2" bordered>
      <a-descriptions-item label="机构编号">{{ form.orgNo }}</a-descriptions-item>
      <a-descriptions-item label="机构名称">{{ form.orgName }}</a-descriptions-item>
      <a-descriptions-item label="清算通道">{{ form.remitChannelName }}</a-descriptions-item>
      <a-descriptions-item label="通道机构编号">{{ form.chnOrgNo }}</a-descriptions-item>
      <a-descriptions-item label="充值金额">{{ form.rechargeAmt }}</a-descriptions-item>
      <a-descriptions-item label="到账金额">{{ form.settleAmt }}</a-descriptions-item>
      <a-descriptions-item label="交易流水号">{{ form.flowNo }}</a-descriptions-item>
      <a-descriptions-item label="订单完成时间">{{ form.completeTime }}</a-descriptions-item>
      <a-descriptions-item label="打款备注" :span="2">{{ form.remark }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
