<template>
  <div class="ele-body">
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="机构名称">
              <a-input v-model:value.trim="where.orgName" placeholder="机构名称" allow-clear />
            </a-form-item>
            <a-form-item label="机构编号">
              <a-input v-model:value.trim="where.orgNo" placeholder="机构编号" allow-clear />
            </a-form-item>
            <a-form-item label="交易流水号">
              <a-input v-model:value.trim="where.flowNo" placeholder="交易流水号" allow-clear />
            </a-form-item>
            <a-form-item label="清算通道">
              <a-select v-model:value="where.remitChannelCode" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option v-for="({ channelName, channelNo }, key) in channelCodes" :key="key" :value="channelNo">
                  {{ channelName }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="开始日期">
              <a-date-picker v-model:value="where.searchBeginTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item label="结束日期">
              <a-date-picker v-model:value="where.searchEndTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <!-- table操作栏按钮 -->
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <RechargeOrderDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { RechargeOrderApi } from '@/api/withdrawal-channel/RechargeOrderApi';
import RechargeOrderDetail from './recharge-order-detail.vue';
import { RemitChannelApi } from '@/api/account/remit-channel/RemitChannelApi';

export default {
  name: 'DLGRechargeOrder',
  components: {
    RechargeOrderDetail
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          align: 'center',
          width: 80,
          fixed: 'left'
        },
        {
          title: '机构编号',
          dataIndex: 'orgNo',
          align: 'center'
        },
        {
          title: '机构名称',
          dataIndex: 'orgName',
          align: 'center'
        },
        {
          title: '清算通道',
          dataIndex: 'remitChannelCode',
          align: 'center',
          customRender: ({ text, record }) => {
            const item = this.channelCodes.find(c => c.channelNo === text);
            record.remitChannelName = item?.channelName || '--';
            return item?.channelName || '--';
          }
        },
        {
          title: '通道机构编号',
          dataIndex: 'chnOrgNo',
          align: 'center'
        },
        {
          title: '充值金额',
          dataIndex: 'rechargeAmt',
          align: 'center'
        },
        {
          title: '到账金额',
          dataIndex: 'settleAmt',
          align: 'center'
        },
        {
          title: '交易流水号',
          dataIndex: 'flowNo',
          align: 'center'
        },
        {
          title: '订单完成时间',
          dataIndex: 'completeTime',
          align: 'center'
        },
        {
          title: '打款备注',
          dataIndex: 'remark',
          width: 200
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 120,
          align: 'center'
        }
      ],
      // 表格搜索条件
      where: {},
      // 当前编辑数据
      current: null,
      // 是否显示编辑弹窗
      showDetail: false,
      channelCodes: []
    };
  },
  async mounted() {
    const data = await RemitChannelApi.findAll({ validStatus: 1 });
    this.channelCodes = data || [];
  },
  methods: {
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where, orders }) {
      return RechargeOrderApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
