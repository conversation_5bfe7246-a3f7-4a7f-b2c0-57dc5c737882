<template>
  <a-modal
    :width="800"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新增'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @cancel="cancel"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules">
      <a-form-item label="商户编号" name="merchantNo">
        <a-input
          v-model:value="form.merchantNo"
          placeholder="请输入商户编号"
          @click="merchNoSelectMethod"
          :readonly="true"
          :disabled="isUpdate"
          allow-clear
        />
      </a-form-item>
      <a-form-item label="商户类型" name="merchantGrade" :rules="[{ required: (form.riskGrade !== undefined && form.riskGrade !== null), message: '请选择商户类型' }]">
        <a-select v-model:value="form.merchantGrade" style="width: 100%" placeholder="请选择" :disabled="isUpdate" allow-clear>
          <a-select-option value="A">企业商户</a-select-option>
          <a-select-option value="B">个体工商户</a-select-option>
          <a-select-option value="C">小微商户</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="风控等级">
        <a-select v-model:value="form.riskGrade" style="width: 100%" placeholder="请选择" :disabled="isUpdate" allow-clear>
          <a-select-option v-for="i in 10" :key="i" :value="i">{{ i }}级</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="触发行为类型" name="triggerActType">
        <a-select v-model:value="form.triggerActType" placeholder="请选择触发行为类型" style="width: 100%" allow-clear>
          <a-select-option :value="0">不处理</a-select-option>
          <a-select-option :value="1">上调风险等级</a-select-option>
          <a-select-option :value="2">身份证加入黑名单</a-select-option>
          <a-select-option :value="3">手机号加入黑名单</a-select-option>
          <a-select-option :value="4">付款用户加入黑名单</a-select-option>
          <a-select-option :value="5">拒绝交易</a-select-option>
          <a-select-option :value="6">关闭交易</a-select-option>
          <a-select-option :value="7">禁止出款</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="交易卡张数上限" name="cardCountLimit">
        <a-input v-model:value="form.cardCountLimit" placeholder="请输入交易卡张数上限" allow-clear />
      </a-form-item>
    </a-form>
  </a-modal>

  <!-- 商户选择弹窗 -->
  <MerchSelect v-if="showMerchList" v-model:visible="showMerchList" @done="setMerchInfo" />
</template>

<script>
import { message } from 'ant-design-vue';
import MerchSelect from '../transaction-limit-manage/merch-select.vue';
import { PayBankCardNumberOverRiskManageApi } from '@/api/riskManage/PayBankCardNumberOverRiskManageApi';

export default {
  name: 'PayBankCardNumberOverRiskEdit',
  components: {
    MerchSelect
  },
  props: {
    visible: Boolean,
    showData: Object
  },
  emits: ['update:visible', 'done'],

  data() {
    return {
      //表单数据
      form: {},
      //是否是修改
      isUpdate: false,
      //提交状态
      loading: false,
      //是否展示商户选择弹框页面
      showMerchList: false,
      //表单规则
      rules: {
        triggerActType: [{ required: true, message: '请选择触发行为类型' }],
        cardCountLimit: [{ required: true, message: '请输入交易卡张数上限' }]
      }
    };
  },
  watch: {
    showData() {
      //有值就是编辑
      if (this.showData) {
        this.isUpdate = true;
        this.form = Object.assign({}, this.showData);
      } else {
        this.isUpdate = false;
        this.form = {};
      }

      if (this.$refs.form) {
        this.$refs.form.clearValidate();
      }
    },
    //将次数转换为数字类型
    'form.countLimit': function (newVal) {
      this.form.countLimit = parseFloat(newVal);
    }
  },
  methods: {
    //点击商户编号，弹出商户列表页面
    merchNoSelectMethod() {
      this.showMerchList = true;
    },
    //从商户列表返回，配置相关数据
    setMerchInfo(merchInfo) {
      this.form.merchantNo = merchInfo.merchantNo;
      this.form.merchantGrade = merchInfo.grade;
      this.form.riskGrade = merchInfo.riskGrade;
    },
    async save() {
      //校验表单
      await this.$refs.form.validate();

      // 修改加载框为正在加载
      this.loading = true;

      let result = null;
      if (this.isUpdate) {
        console.log('修改');
        //修改的
        result = PayBankCardNumberOverRiskManageApi.edit(this.form);
      } else {
        console.log('新增');
        //新增
        result = PayBankCardNumberOverRiskManageApi.add(this.form);
      }

      result
        .then(result => {
          // 移除加载框
          this.loading = false;
          // 提示添加成功
          message.success(result.message);

          // 如果是新增，则form表单置空
          if (!this.isUpdate) {
            this.form = {};
          }
          //关闭弹框
          this.updateVisible(false);

          //触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    cancel() {
      this.form = Object.assign({}, this.showData);
      this.$refs.form.clearValidate();
    },

    //更新编辑页面的弹框是否显示
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>

<style></style>
