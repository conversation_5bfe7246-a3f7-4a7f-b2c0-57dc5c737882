<template>
  <a-modal
    :width="600"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新建'"
    :mask-closable="false"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @cancel="cancel"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules">
      <a-form-item label="白名单类型" name="whiteType">
        <a-select v-model:value="form.whiteType" placeholder="请选择" style="width: 100%">
          <a-select-option :value="1">身份证号</a-select-option>
          <a-select-option :value="2">手机号码</a-select-option>
          <a-select-option :value="3">结算卡号</a-select-option>
          <a-select-option :value="4">商户编号</a-select-option>
          <a-select-option :value="5">地区编号</a-select-option>
          <a-select-option :value="6">付款用户唯一标识</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="白名单对象" name="whiteObject">
        <a-input v-model:value="form.whiteObject" placeholder="请输入白名单对象" allow-clear />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { RiskWhitelistManageApi } from '@/api/riskManage/RiskWhitelistManageApi';
import { message } from 'ant-design-vue';

export default {
  name: 'RiskWhitelistEdit',
  props: {
    //弹窗是否打开
    visible: Boolean,
    //修改回显的数据
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      //表单数据
      form: {},
      rules: {
        whiteObject: [{ required: true, message: '请输入白名单对象' }],
        whiteType: [{ required: true, message: '请选择白名单类型' }]
      },
      //提交状态
      loading: false,
      //是否是修改
      isUpdate: false
    };
  },
  watch: {
    data() {
      //有就是编辑
      if (this.data) {
        this.form = Object.assign({}, this.data);
        this.isUpdate = true;
      } else {
        //没有就是新增
        this.form = {};
        this.isUpdate = false;
      }
    }
  },
  methods: {
    /**
     * 更新编辑界面的弹框是否显示
     */
    updateVisible(value) {
      this.$emit('update:visible', value);
    },

    //取消事件
    cancel() {
      this.form = Object.assign({}, this.data);
      this.$refs.form.clearValidate();
    },

    //保存和编辑事件
    async save() {
      //校验表单
      await this.$refs.form.validate();
      //修改加载框为正在加载
      this.loading = true;

      let result = null;
      //判断执行编辑还是修改
      if (this.isUpdate) {
        //编辑
        result = RiskWhitelistManageApi.edit({ id: this.form.id, whiteType: this.form.whiteType, whiteObject: this.form.whiteObject });
      } else {
        //新增
        result = RiskWhitelistManageApi.add(this.form);
      }

      result
        .then(res => {
          this.loading = false;
          message.success(res.message);
          if (!this.isUpdate) {
            this.form = {};
          }
          //关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          //触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    }
  }
};
</script>

<style></style>
