<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="blick-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="黑名单对象">
              <a-input v-model:value.trim="where.blackObject" placeholder="请输入黑名单对象" allow-clear />
            </a-form-item>
            <a-form-item label="黑名单类型">
              <a-select v-model:value="where.blackType" style="width: 205px" placeholder="请选择" allow-ckear>
                <a-select-option :value="1">身份证号</a-select-option>
                <a-select-option :value="2">手机号码</a-select-option>
                <a-select-option :value="3">结算卡号</a-select-option>
                <a-select-option :value="4">商户编号</a-select-option>
                <a-select-option :value="5">地区编号</a-select-option>
                <a-select-option :value="6">付款用户唯一标识</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table
          ref="table"
          row-key="id"
          :datasource="datasource"
          :columns="columns"
          :where="where"
          v-model:selection="selection"
          :scroll="{ x: 'max-content' }"
        >
          <!-- table上边的工具栏 -->
          <template #toolbar>
            <div>
              <a-space>
                <a-button type="primary" @click="openEdit()">
                  <template #icon>
                    <plus-outlined />
                  </template>
                  <span>新建</span>
                </a-button>
              </a-space>
            </div>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'blackType'">
              <a-tag v-if="record.blackType === 1" color="pink">身份证号</a-tag>
              <a-tag v-if="record.blackType === 2" color="blue">手机号码</a-tag>
              <a-tag v-if="record.blackType === 3" color="cyan">结算卡号</a-tag>
              <a-tag v-if="record.blackType === 4" color="purple">商户编号</a-tag>
              <a-tag v-if="record.blackType === 5" color="red">地区编号</a-tag>
              <a-tag v-if="record.blackType === 6" color="orange">付款用户唯一标识</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="openEdit(record)">修改</a>
                <a-divider type="vertical" />
                <a @click="handleShowDetail(record)">详情</a>
                <a-divider type="vertical" />
                <a-popconfirm title="确定要删除此记录吗?" @confirm="remove(record)">
                  <a class="ele-text-danger">删除</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 编辑弹窗 -->
    <RiskBlacklistEdit v-model:visible="showEdit" :data="current" @done="reload" />

    <!-- 详情页面 -->
    <RiskBlacklistDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { RiskBlacklistManageApi } from '@/api/riskManage/RiskBlacklistManageApi';
import RiskBlacklistEdit from './risk-blacklist-edit.vue'
import RiskBlacklistDetail from './risk-blacklist-detail.vue'
import { message } from 'ant-design-vue';

export default {
  name: 'RiskBlacklistManage',
  components: {
    RiskBlacklistEdit,
    RiskBlacklistDetail
   },
  data() {
    return {
      // 表格搜索条件
      where: {},
      //表格选中的数据
      selection: [],
      //当前编辑数据
      current: null,
      //是否显示编辑弹窗
      showEdit: false,
      //是否展示详情弹窗
      showDetail: false,

      //表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '黑名单对象',
          dataIndex: 'blackObject',
          align: 'center'
        },
        {
          title: '黑名单类型',
          dataIndex: 'blackType',
          key: 'blackType',
          align: 'center'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          align: 'center'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId',
          align: 'center'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime',
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 200,
          align: 'center'
        }
      ]
    };
  },
  methods: {
    /**
     * 打开新增或编辑弹窗
     */
    openEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    /**
     * 搜索按钮
     */
    reload() {
      this.selection = [];
      this.$refs.table.reload({ page: 1 });
    },

    /**
     * 重置按钮
     */
    reset() {
      this.where = {};
      this.selection = [];
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    /**
     * 展示详情
     */
    handleShowDetail(row) {
      this.showDetail = true;
      this.current = row;
    },

    /**
     * 删除
     */
    async remove(row) {
      const result = await RiskBlacklistManageApi.delete({ id: row.id });
      message.success(result.message);
      this.reload();
    },

    datasource({ page, limit, where, orders }) {
      return RiskBlacklistManageApi.getRiskBlacklistPages({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>

<style>
</style>
