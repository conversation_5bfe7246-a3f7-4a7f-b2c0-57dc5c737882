<template>
  <a-modal
    :width="600"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '30px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="1">
      <a-descriptions-item label="黑名单对象">{{ form.blackObject }}</a-descriptions-item>
      <a-descriptions-item label="黑名单类型">
        <a-tag v-if="form.blackType === 1">身份证号</a-tag>
        <a-tag v-else-if="form.blackType === 2">手机号码</a-tag>
        <a-tag v-else-if="form.blackType === 3">结算卡号</a-tag>
        <a-tag v-else-if="form.blackType === 4">商户编号</a-tag>
        <a-tag v-else-if="form.blackType === 5">地区编号</a-tag>
        <a-tag v-else-if="form.blackType === 6">付款用户唯一标识</a-tag>
      </a-descriptions-item>
    </a-descriptions>

    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
export default {
  name: 'RiskBlacklistDetail',
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  data() {
    return {
      form: {}
    };
  },
  watch: {
    //父组件值该表则重新赋值
    detail() {
      this.form = Object.assign({}, this.detail);
    }
  },
  methods: {
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>

<style></style>
