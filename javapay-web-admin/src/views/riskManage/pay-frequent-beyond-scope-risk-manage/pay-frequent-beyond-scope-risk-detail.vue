<template>
  <a-modal
    :width="750"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
    :footer="null"
  >
    <a-descriptions :column="1">
      <a-descriptions-item label="商户编号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="商户类型">
        <a-tag v-if="form.merchantGrade == 'A'" color="cyan">企业商户</a-tag>
        <a-tag v-else-if="form.merchantGrade == 'B'" color="blue">个体工商户</a-tag>
        <a-tag v-else-if="form.merchantGrade == 'C'" color="purple">小微商户</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="风控模型类型">
        <a-tag v-if="form.riskModelType == 1" color="cyan">定位风控模型</a-tag>
        <a-tag v-else-if="form.riskModelType == 2" color="blue">IP风控模型</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="风控等级">{{ form.riskGrade }}</a-descriptions-item>
      <a-descriptions-item label="次数上限">{{ form.countLimit }}</a-descriptions-item>
      <a-descriptions-item label="时间范围">{{ form.timeRange + '分钟' }}</a-descriptions-item>
      <a-descriptions-item label="触发行为类型">
        <a-tag v-if="form.triggerActType === 0" color="pink">不处理</a-tag>
        <a-tag v-else-if="form.triggerActType === 1" color="cyan">上调风险等级</a-tag>
        <a-tag v-else-if="form.triggerActType === 2" color="blue">身份证加入黑名单</a-tag>
        <a-tag v-else-if="form.triggerActType === 3" color="purple">手机号加入黑名单</a-tag>
        <a-tag v-else-if="form.triggerActType === 4" color="orange">付款用户加入黑名单</a-tag>
        <a-tag v-else-if="form.triggerActType === 5" color="pink">拒绝交易</a-tag>
        <a-tag v-else-if="form.triggerActType === 6" color="cyan">关闭交易</a-tag>
        <a-tag v-else-if="form.triggerActType === 7" color="blue">禁止出款</a-tag>
      </a-descriptions-item>
    </a-descriptions>
  </a-modal>
</template>

<script>
export default {
  name: 'PayFrequentBeyondScopeRiskDetail',
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  data() {
    return {
      form: {}
    };
  },
  watch: {
    //父组件值改变则重新赋值
    detail() {
      this.form = Object.assign({}, this.detail);
    }
  },
  methods: {
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>

<style></style>
