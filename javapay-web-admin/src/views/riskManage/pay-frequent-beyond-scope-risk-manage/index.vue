<template>
  <div class="ele-body">
    <!-- 搜索框内容 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入渠道编码" allow-clear />
            </a-form-item>

            <a-form-item label="商户类型">
              <a-select v-model:value="where.merchantGrade" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option value="A">企业商户</a-select-option>
                <a-select-option value="B">个体工商户</a-select-option>
                <a-select-option value="C">小微商户</a-select-option>
              </a-select>
            </a-form-item>



            <a-form-item label="触发行为类型">
              <a-select v-model:value="where.triggerActType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">不处理</a-select-option>
                <a-select-option :value="1">上调风险等级</a-select-option>
                <a-select-option :value="2">身份证加入黑名单</a-select-option>
                <a-select-option :value="3">手机号加入黑名单</a-select-option>
                <a-select-option :value="4">付款用户加入黑名单</a-select-option>
                <a-select-option :value="5">拒绝交易</a-select-option>
                <a-select-option :value="6">关闭交易</a-select-option>
                <a-select-option :value="7">禁止出款</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="风控模型类型">
              <a-select v-model:value="where.riskModelType" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">定位风控模型</a-select-option>
                <a-select-option :value="2">IP风控模型</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="风控等级">
              <a-select v-model:value="where.riskGrade" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option v-for="i in 10" :key="i" :value="i">{{ i }}级</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格内容 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- 表格上方的操作按钮 -->
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>

          <!-- 表体的操作 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'triggerActType'">
              <a-tag v-if="record.triggerActType === 0" color="pink">不处理</a-tag>
              <a-tag v-else-if="record.triggerActType === 1" color="cyan">上调风险等级</a-tag>
              <a-tag v-else-if="record.triggerActType === 2" color="blue">身份证加入黑名单</a-tag>
              <a-tag v-else-if="record.triggerActType === 3" color="purple">手机号加入黑名单</a-tag>
              <a-tag v-else-if="record.triggerActType === 4" color="orange">付款用户加入黑名单</a-tag>
              <a-tag v-else-if="record.triggerActType === 5" color="pink">拒绝交易</a-tag>
              <a-tag v-else-if="record.triggerActType === 6" color="cyan">关闭交易</a-tag>
              <a-tag v-else-if="record.triggerActType === 7" color="blue">禁止出款</a-tag>
            </template>

            <template v-else-if="column.key === 'merchantGrade'">
              <a-tag v-if="record.merchantGrade == 'A'" color="cyan">企业商户</a-tag>
              <a-tag v-else-if="record.merchantGrade == 'B'" color="blue">个体工商户</a-tag>
              <a-tag v-else-if="record.merchantGrade == 'C'" color="purple">小微商户</a-tag>
            </template>

            <template v-else-if="column.key === 'riskModelType'">
              <a-tag v-if="record.riskModelType == 1" color="cyan">定位风控模型</a-tag>
              <a-tag v-else-if="record.riskModelType == 2" color="blue">IP风控模型</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleEdit(record)">修改</a>

                <a-divider type="vertical" />

                <a @click="handleDetail(record)">详情</a>

                <a-divider type="vertical" />

                <a-popconfirm title="确定要删除此记录吗？" @confirm="remove(record)">
                  <a class="ele-text-danger">删除</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 编辑 -->
    <PayFrequentBeyondScopeRiskEdit v-model:visible="showEdit" :showData="current" @done="reload" />
    <!-- 详情 -->
    <PayFrequentBeyondScopeRiskDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { PayFrequentBeyondScopeRiskManageApi } from '@/api/riskManage/PayFrequentBeyondScopeRiskManageApi';
import PayFrequentBeyondScopeRiskEdit from './pay-frequent-beyond-scope-risk-edit.vue';
import PayFrequentBeyondScopeRiskDetail from './pay-frequent-beyond-scope-risk-detail.vue';
import { message } from 'ant-design-vue';

export default {
  name: 'PayFrequentBeyondScopeRiskManage',
  components: {
    PayFrequentBeyondScopeRiskEdit,
    PayFrequentBeyondScopeRiskDetail
  },
  data() {
    return {
      //表格查询条件
      where: {},
      //展示编辑页面传的参数
      current: null,
      //是否展示新增或者是编辑页面
      showEdit: false,
      //是否展示详情页面
      showDetail: false,
      //表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center'
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '商户类型',
          dataIndex: 'merchantGrade',
          key: 'merchantGrade',
          align: 'center'
        },
        {
          title: '触发行为类型',
          dataIndex: 'triggerActType',
          key: 'triggerActType',
          align: 'center'
        },
        {
          title: '风控模型类型',
          dataIndex: 'riskModelType',
          key: 'riskModelType',
          align: 'center'
        },
        {
          title: '风控等级',
          dataIndex: 'riskGrade',
          align: 'center'
        },
        {
          title: '次数上限',
          dataIndex: 'countLimit',
          align: 'center'
        },
        {
          title: '时间范围',
          dataIndex: 'timeRange',
          align: 'center'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          align: 'center'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId',
          align: 'center'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime',
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          align: 'center'
        }
      ]
    };
  },
  methods: {
    //查询方法
    reload() {
      this.$refs.table.reload({ page: 1 });
    },
    //重置
    reset() {
      this.where = {}; //清空查询条件
      /**
       * 为啥都清空了还要添加
       */
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleEdit(row) {
      this.current = row;
      this.showEdit = true;
    },

    handleDetail(row) {
      console.log('详情' + row);
      this.current = row;
      this.showDetail = true;
    },

    async remove(row) {
      const result = await PayFrequentBeyondScopeRiskManageApi.delete({ id: row.id });
      message.success(result.message);
      this.reload();
    },

    //获取数据方法
    datasource({ page, limit, where }) {
      return PayFrequentBeyondScopeRiskManageApi.getGeofenceLimitPages({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>
