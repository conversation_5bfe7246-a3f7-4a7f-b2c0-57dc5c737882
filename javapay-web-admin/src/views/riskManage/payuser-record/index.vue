<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="用户手机号">
              <a-input v-model:value.trim="where.mobile" placeholder="请输入用户手机号" allow-clear />
            </a-form-item>
            <a-form-item label="支付宝用户唯一标识">
              <a-input v-model:value.trim="where.aliUserId" placeholder="请输入支付宝用户唯一标识" allow-clear />
            </a-form-item>
            <a-form-item label="微信用户唯一标识">
              <a-input v-model:value.trim="where.wxUserId" placeholder="请输入微信用户唯一标识" allow-clear />
            </a-form-item>
            <a-form-item label="云闪付用户唯一标识">
              <a-input v-model:value.trim="where.ysfUserId" placeholder="请输入云闪付用户唯一标识" allow-clear />
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, record }">
            <!-- table操作栏按钮 -->
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <PayuserRecordDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { PayuserRecordApi } from '@/api/riskManage/PayuserRecordApi';
import PayuserRecordDetail from './payuser-record-detail.vue';

export default {
  name: 'PayuserRecord',
  components: {
    PayuserRecordDetail
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '商户ID',
          dataIndex: 'merchantId',
          align: 'center'
        },
        {
          title: '用户手机号',
          dataIndex: 'mobileMask',
          align: 'center'
        },
        {
          title: '支付宝用户唯一标识',
          dataIndex: 'aliUserId',
          align: 'center'
        },
        {
          title: '微信用户唯一标识',
          dataIndex: 'wxUserId',
          align: 'center'
        },
        {
          title: '云闪付用户唯一标识',
          dataIndex: 'ysfUserId',
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center'
        }
      ],
      // 表格搜索条件
      where: {},
      current: null,
      showDetail: false
    };
  },
  methods: {
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where, orders }) {
      return PayuserRecordApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
