<template>
  <div class="ele-body">
    <!-- 搜索框内容 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户号" allow-clear />
            </a-form-item>

            <a-form-item label="手机号码">
              <a-input v-model:value.trim="where.phoneNumber" placeholder="请输入手机号码" allow-clear />
            </a-form-item>

            <a-form-item label="事件类型">
              <a-select v-model:value="where.eventType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">黑名单记录</a-select-option>
                <a-select-option :value="2">超出商圈范围</a-select-option>
                <a-select-option :value="3">使用虚拟定位</a-select-option>
                <a-select-option :value="4">付款卡数量超限</a-select-option>
                <a-select-option :value="5">付款用户数量超限</a-select-option>
                <a-select-option :value="6">卡类型频繁交易</a-select-option>
                <a-select-option :value="7">手机号频繁发送短信</a-select-option>
                <a-select-option :value="8">频繁超出商圈范围</a-select-option>
                <a-select-option :value="9">频繁尝试虚拟定位</a-select-option>
                <a-select-option :value="10">多人多次付款出现超出商圈</a-select-option>
                <a-select-option :value="11">商户不同地区频繁交易</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="事件来源">
              <a-select v-model:value="where.eventSource" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">商户入网</a-select-option>
                <a-select-option :value="2">商户交易</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="触发行为类型">
              <a-select v-model:value="where.triggerActType" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="0">不处理</a-select-option>
                <a-select-option :value="1">上调风险等级</a-select-option>
                <a-select-option :value="2">身份证加入黑名单</a-select-option>
                <a-select-option :value="3">手机号加入黑名单</a-select-option>
                <a-select-option :value="4">付款用户加入黑名单</a-select-option>
                <a-select-option :value="5">拒绝交易</a-select-option>
                <a-select-option :value="6">关闭交易</a-select-option>
                <a-select-option :value="7">禁止出款</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格内容 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- 表体的操作 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'eventType'">
              <a-tag v-if="record.eventType === 1" color="blue">黑名单记录</a-tag>
              <a-tag v-if="record.eventType === 2" color="cyan">超出商圈范围</a-tag>
              <a-tag v-if="record.eventType === 3" color="purple">使用虚拟定位</a-tag>
              <a-tag v-if="record.eventType === 4" color="red">付款卡数量超限</a-tag>
              <a-tag v-if="record.eventType === 5" color="orange">付款用户数量超限</a-tag>
              <a-tag v-if="record.eventType === 6" color="blue">卡类型频繁交易</a-tag>
              <a-tag v-if="record.eventType === 7" color="cyan">手机号频繁发送短信</a-tag>
              <a-tag v-if="record.eventType === 8" color="purple">频繁超出商圈范围</a-tag>
              <a-tag v-if="record.eventType === 9" color="red">频繁尝试虚拟定位</a-tag>
              <a-tag v-if="record.eventType === 10" color="orange">多人多次付款出现超出商圈</a-tag>
              <a-tag v-if="record.eventType === 11" color="blue">商户不同地区频繁交易</a-tag>
            </template>

            <template v-else-if="column.key === 'eventSource'">
              <a-tag v-if="record.eventSource === 1" color="blue">商户入网</a-tag>
              <a-tag v-if="record.eventSource === 2" color="cyan">商户交易</a-tag>
            </template>

            <template v-else-if="column.key === 'triggerActType'">
              <a-tag v-if="record.triggerActType === 0" color="pink">不处理</a-tag>
              <a-tag v-if="record.triggerActType === 1" color="blue">上调风控等级</a-tag>
              <a-tag v-if="record.triggerActType === 2" color="cyan">身份证加入黑名单</a-tag>
              <a-tag v-if="record.triggerActType === 3" color="purple">手机号加入黑名单</a-tag>
              <a-tag v-if="record.triggerActType === 4" color="red">付款用户加入黑名单</a-tag>
              <a-tag v-if="record.triggerActType === 5" color="pink">拒绝交易</a-tag>
              <a-tag v-if="record.triggerActType === 6" color="blue">关闭交易</a-tag>
              <a-tag v-if="record.triggerActType === 7" color="cyan">禁止出款</a-tag>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>
  </div>
</template>

<script>
import { RiskEventRecordManageApi } from '@/api/riskManage/RiskEventRecordManageApi';

export default {
  name: 'RiskEventRecordManage',
  components: {},
  data() {
    return {
      //表格查询条件
      where: {},
      //表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center'
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '风控ID',
          dataIndex: 'riskId',
          align: 'center'
        },
        {
          title: '黑名单ID',
          dataIndex: 'blackId',
          align: 'center'
        },
        {
          title: '交易流水单号',
          dataIndex: 'transFlow',
          align: 'center'
        },
        {
          title: '关联的事件ID',
          dataIndex: 'eventId',
          align: 'center'
        },
        {
          title: '证件号码',
          dataIndex: 'certNo',
          align: 'center'
        },
        {
          title: '用户唯一标识',
          dataIndex: 'payuserOpenId',
          align: 'center'
        },
        {
          title: '手机号码',
          dataIndex: 'phoneNumber',
          align: 'center'
        },
        {
          title: '事件类型',
          dataIndex: 'eventType',
          key: 'eventType',
          align: 'center'
        },
        {
          title: '事件来源',
          dataIndex: 'eventSource',
          key: 'eventSource',
          align: 'center'
        },
        {
          title: '触发行为类型',
          dataIndex: 'triggerActType',
          key: 'triggerActType',
          align: 'center'
        },
        {
          title: '原因说明',
          dataIndex: 'rejectReason',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          align: 'center'
        }
      ]
    };
  },
  methods: {
    //查询方法
    reload() {
      this.$refs.table.reload({ page: 1 });
    },
    //重置
    reset() {
      this.where = {}; //清空查询条件
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    //获取数据方法
    datasource({ page, limit, where }) {
      return RiskEventRecordManageApi.getRiskEventRecordManagePages({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>
