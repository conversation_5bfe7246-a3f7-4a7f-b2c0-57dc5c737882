<template>
  <div class="ele-body">
    <!-- 搜索内容 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
            </a-form-item>

            <a-form-item label="商户等级">
              <a-select v-model:value="where.merchantGrade" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option value="A">企业商户</a-select-option>
                <a-select-option value="B">个体工商户</a-select-option>
                <a-select-option value="C">小微商户</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="风控等级">
              <a-select v-model:value="where.riskGrade" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">1级</a-select-option>
                <a-select-option :value="2">2级</a-select-option>
                <a-select-option :value="3">3级</a-select-option>
                <a-select-option :value="4">4级</a-select-option>
                <a-select-option :value="5">5级</a-select-option>
                <a-select-option :value="6">6级</a-select-option>
                <a-select-option :value="7">7级</a-select-option>
                <a-select-option :value="8">8级</a-select-option>
                <a-select-option :value="9">9级</a-select-option>
                <a-select-option :value="10">10级</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格内容 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- 表格上方的操作按钮 -->
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>
          <!-- 表格主体内容 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'merchantGrade'">
              <a-tag v-if="record.merchantGrade === 'A'" color="pink">企业商户</a-tag>
              <a-tag v-if="record.merchantGrade === 'B'" color="blue">个体工商户</a-tag>
              <a-tag v-if="record.merchantGrade === 'C'" color="cyan">小微商户</a-tag>
            </template>

            <template v-if="column.key === 'riskGrade'">
              <a-tag v-if="record.riskGrade === 1" color="pink">1级</a-tag>
              <a-tag v-if="record.riskGrade === 2" color="blue">2级</a-tag>
              <a-tag v-if="record.riskGrade === 3" color="cyan">3级</a-tag>
              <a-tag v-if="record.riskGrade === 4" color="pink">4级</a-tag>
              <a-tag v-if="record.riskGrade === 5" color="blue">5级</a-tag>
              <a-tag v-if="record.riskGrade === 6" color="cyan">6级</a-tag>
              <a-tag v-if="record.riskGrade === 7" color="pink">7级</a-tag>
              <a-tag v-if="record.riskGrade === 8" color="blue">8级</a-tag>
              <a-tag v-if="record.riskGrade === 9" color="cyan">9级</a-tag>
              <a-tag v-if="record.riskGrade === 10" color="pink">10级</a-tag>
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleEdit(record)">修改</a>
                <a-divider type="vertical" />
                <a @click="handleShowDetail(record)">详情</a>
                <a-divider type="vertical" />
                <a-popconfirm title="确定要删除此记录吗？" @confirm="remove(record)">
                  <a class="ele-text-danger">删除</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 新增编辑页面 -->
    <TransactionLimitManageEdit v-model:visible="showEdit" :showData="current" @done="reload" />

    <!-- 详情页面 -->
    <TransactionLimitManageDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { TransactionLimitManageApi } from '@/api/riskManage/TransactionLimitManageApi';
import TransactionLimitManageEdit from './transaction-limit-manage-edit.vue';
import TransactionLimitManageDetail from './transaction-limit-manage-detail.vue';
import { message } from 'ant-design-vue';

export default {
  name: 'TransactionLimitManage',
  components: {
    TransactionLimitManageEdit,
    TransactionLimitManageDetail
  },
  data() {
    return {
      // 表格查询条件
      where: {},
      //当前数据
      current: null,
      //是否展示新增或者编辑页面
      showEdit: false,
      //是否展示详情
      showDetail: false,

      //表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center'
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '商户等级',
          dataIndex: 'merchantGrade',
          key: 'merchantGrade',
          align: 'center'
        },
        {
          title: '风控等级',
          dataIndex: 'riskGrade',
          key: 'riskGrade',
          align: 'center'
        },
        {
          title: '银联扫码单笔限额',
          dataIndex: 'unionpayOnceLimit',
          align: 'center'
        },
        {
          title: '支付宝单笔限额',
          dataIndex: 'alipayOnceLimit',
          align: 'center'
        },
        {
          title: '微信单笔限额',
          dataIndex: 'wechatOnceLimit',
          align: 'center'
        },
        {
          title: 'EPOS单笔限额',
          dataIndex: 'eposOnceLimit',
          align: 'center'
        },
        {
          title: 'EPOS单笔自付限额',
          dataIndex: 'eposOnceSelfLimit',
          align: 'center'
        },
        {
          title: '银联扫码单日限额',
          dataIndex: 'unionpayDayLimit',
          align: 'center'
        },
        {
          title: '支付宝单日限额',
          dataIndex: 'alipayDayLimit',
          align: 'center'
        },
        {
          title: '微信单日限额',
          dataIndex: 'wechatDayLimit',
          align: 'center'
        },
        {
          title: 'EPOS单日限额',
          dataIndex: 'eposDayLimit',
          align: 'center'
        },
        {
          title: 'EPOS单日自付限额',
          dataIndex: 'eposDaySelfLimit',
          align: 'center'
        },
        {
          title: 'D0提现单日额度',
          dataIndex: 'd0DayWithdrawLimit',
          align: 'center'
        },
        {
          title: '扫码单日支付额度',
          dataIndex: 'qrcDayPayLimit',
          align: 'center'
        },
        {
          title: 'POS单日支付额度',
          dataIndex: 'posDayPayLimit',
          align: 'center'
        },
        {
          title: '银联扫码单月限额',
          dataIndex: 'unionpayMonthLimit',
          align: 'center'
        },
        {
          title: '支付宝单月限额',
          dataIndex: 'alipayMonthLimit',
          align: 'center'
        },
        {
          title: '微信单月限额',
          dataIndex: 'wechatMonthLimit',
          align: 'center'
        },
        {
          title: 'EPOS单月限额',
          dataIndex: 'eposMonthLimit',
          align: 'center'
        },
        {
          title: 'EPOS单月自付限额',
          dataIndex: 'eposMonthSelfLimit',
          align: 'center'
        },
        {
          title: '扫码单月支付额度',
          dataIndex: 'qrcMonthPayLimit',
          align: 'center'
        },
        {
          title: 'POS单月支付额度',
          dataIndex: 'posMonthPayLimit',
          align: 'center'
        },
        {
          title: 'POS手机Pay贷记卡单笔限额',
          dataIndex: 'posNfcCreditOnceLimit',
          align: 'center'
        },
        {
          title: 'POS手机Pay贷记卡单日限额',
          dataIndex: 'posNfcCreditDayLimit',
          align: 'center'
        },
        {
          title: 'POS手机Pay贷记卡单月限额',
          dataIndex: 'posNfcCreditMonthLimit',
          align: 'center'
        },
        {
          title: 'POS手机Pay借记卡单笔限额',
          dataIndex: 'posNfcDebitOnceLimit',
          align: 'center'
        },
        {
          title: 'POS手机Pay借记卡单日限额',
          dataIndex: 'posNfcDebitDayLimit',
          align: 'center'
        },
        {
          title: 'POS手机Pay借记卡单月限额',
          dataIndex: 'posNfcDebitMonthLimit',
          align: 'center'
        },
        {
          title: 'POS芯片贷记卡单笔限额',
          dataIndex: 'posIcCreditOnceLimit',
          align: 'center'
        },
        {
          title: 'POS芯片贷记卡单日限额',
          dataIndex: 'posIcCreditDayLimit',
          align: 'center'
        },
        {
          title: 'POS芯片贷记卡单月限额',
          dataIndex: 'posIcCreditMonthLimit',
          align: 'center'
        },
        {
          title: 'POS芯片借记卡单笔限额',
          dataIndex: 'posIcDebitOnceLimit',
          align: 'center'
        },
        {
          title: 'POS芯片借记卡单日限额',
          dataIndex: 'posIcDebitDayLimit',
          align: 'center'
        },
        {
          title: 'POS芯片借记卡单月限额',
          dataIndex: 'posIcDebitMonthLimit',
          align: 'center'
        },
        {
          title: 'POS磁条贷记卡单笔限额',
          dataIndex: 'posCiCreditOnceLimit',
          align: 'center'
        },
        {
          title: 'POS磁条贷记卡单日限额',
          dataIndex: 'posCiCreditDayLimit',
          align: 'center'
        },
        {
          title: 'POS磁条贷记卡单月限额',
          dataIndex: 'posCiCreditMonthLimit',
          align: 'center'
        },
        {
          title: 'POS磁条借记卡单笔限额',
          dataIndex: 'posCiDebitOnceLimit',
          align: 'center'
        },
        {
          title: 'POS磁条借记卡单日限额',
          dataIndex: 'posCiDebitDayLimit',
          align: 'center'
        },
        {
          title: 'POS磁条借记卡单月限额',
          dataIndex: 'posCiDebitMonthLimit',
          align: 'center'
        },

        {
          title: '交易时间范围',
          dataIndex: 'allowPayPeriod',
          align: 'center'
        },
        {
          title: '提现时间范围',
          dataIndex: 'allowWithdrawPeriod',
          align: 'center'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          align: 'center'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId',
          align: 'center'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime',
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 200,
          align: 'center'
        }
      ]
    };
  },
  methods: {
    //查询方法
    reload() {
      this.$refs.table.reload({ page: 1 });
    },

    //重置
    reset() {
      this.where = {}; //清空查询条件
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    //添加或者编辑
    handleEdit(row) {
      this.showEdit = true;
      this.current = row;
    },

    //查看详情
    handleShowDetail(row) {
      this.showDetail = true;
      this.current = row;
    },

    //删除指定行
    async remove(row) {
      const result = await TransactionLimitManageApi.delete({ id: row.id });
      message.success(result.message);
      this.reload();
    },

    datasource({ page, limit, where }) {
      return TransactionLimitManageApi.getTransactionLimitManagePages({ ...where, paegNo: page, pageSize: limit });
    }
  }
};
</script>

<style>
</style>
