<template>
  <a-modal
    :width="1100" :visible="visible" :maskClosable="false" :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新增'" :mask-closable="false" :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible" @cancel="cancel" @ok="save">
    <a-form
      ref="form" :model="form" :rules="rules" :label-col="{ md: { span: 10 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 17 }, sm: { span: 24 } }">
      <a-row :gutter="16">
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="商户编号" name="merchantNo">
            <a-input
              v-model:value="form.merchantNo" placeholder="请输入商户编号" @click="merchNoSelectMethod" :readonly="true"
              :disabled="isUpdate" allow-clear />
          </a-form-item>

          <a-form-item label="风控等级" name="riskGrade">
            <a-select
              v-model:value="form.riskGrade" placeholder="请选择风控等级" style="width: 100%" :disabled="isUpdate"
              allow-clear>
              <a-select-option :value="1">1级</a-select-option>
              <a-select-option :value="2">2级</a-select-option>
              <a-select-option :value="3">3级</a-select-option>
              <a-select-option :value="4">4级</a-select-option>
              <a-select-option :value="5">5级</a-select-option>
              <a-select-option :value="6">6级</a-select-option>
              <a-select-option :value="7">7级</a-select-option>
              <a-select-option :value="8">8级</a-select-option>
              <a-select-option :value="9">9级</a-select-option>
              <a-select-option :value="10">10级</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item
            label="商户类型" name="merchantGrade"
            :rules="[{ required: (form.riskGrade !== undefined && form.riskGrade !== null), message: '请选择商户类型' }]">
            <a-select
              v-model:value="form.merchantGrade" placeholder="请选择商户类型" style="width: 100%"
              :disabled="isUpdate || isMerchantGradeDisabled" allow-clear>
              <a-select-option value="A">企业商户</a-select-option>
              <a-select-option value="B">个体工商户</a-select-option>
              <a-select-option value="C">小微商户</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <!-- 单笔限额 -->
        <a-divider orientation="left" :orientationMargin="40" dashed>单笔限额</a-divider>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="银联扫码单笔限额" name="unionpayOnceLimit">
            <a-input v-model:value="form.unionpayOnceLimit" placeholder="请输入银联扫码单笔限额" allow-clear />
          </a-form-item>

          <a-form-item label="支付宝单笔限额" name="alipayOnceLimit">
            <a-input v-model:value="form.alipayOnceLimit" placeholder="请输入支付宝单笔限额" allow-clear />
          </a-form-item>

          <a-form-item label="EPOS单笔支付自限额" name="eposOnceSelfLimit">
            <a-input v-model:value="form.eposOnceSelfLimit" placeholder="请输入EPOS单笔支付自限额" allow-clear />
          </a-form-item>

          <a-form-item label="POS手机Pay贷记卡单笔限额" name="posNfcCreditOnceLimit">
            <a-input v-model:value="form.posNfcCreditOnceLimit" placeholder="请输入POS手机Pay贷记卡单笔限额" allow-clear />
          </a-form-item>
          <a-form-item label="POS芯片贷记卡单笔限额" name="posIcCreditOnceLimit">
            <a-input v-model:value="form.posIcCreditOnceLimit" placeholder="请输入POS芯片贷记卡单笔限额" allow-clear />
          </a-form-item>
          <a-form-item label="POS磁条贷记卡单笔限额" name="posCiCreditOnceLimit">
            <a-input v-model:value="form.posCiCreditOnceLimit" placeholder="请输入POS磁条贷记卡单笔限额" allow-clear />
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="微信单笔限额" name="wechatOnceLimit">
            <a-input v-model:value="form.wechatOnceLimit" placeholder="请输入微信单笔限额" allow-clear />
          </a-form-item>

          <a-form-item label="EPOS单笔支付限额" name="eposOnceLimit">
            <a-input v-model:value="form.eposOnceLimit" placeholder="请输入EPOS单笔限额" allow-clear />
          </a-form-item>

          <a-form-item />


          <a-form-item label="POS手机Pay借记卡单笔限额" name="posNfcDebitOnceLimit">
            <a-input v-model:value="form.posNfcDebitOnceLimit" placeholder="请输入POS手机Pay借记卡单笔限额" allow-clear />
          </a-form-item>
          <a-form-item label="POS芯片借记卡单笔限额" name="posIcDebitOnceLimit">
            <a-input v-model:value="form.posIcDebitOnceLimit" placeholder="请输入POS芯片借记卡单笔限额" allow-clear />
          </a-form-item>
          <a-form-item label="POS磁条借记卡单笔限额" name="posCiDebitOnceLimit">
            <a-input v-model:value="form.posCiDebitOnceLimit" placeholder="请输入POS磁条借记卡单笔限额" allow-clear />
          </a-form-item>
        </a-col>

        <!-- 单日限额 -->
        <a-divider orientation="left" :orientationMargin="40" dashed>单日限额</a-divider>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="银联扫码单日限额" name="unionpayDayLimit">
            <a-input v-model:value="form.unionpayDayLimit" placeholder="请输入微信单笔限额" allow-clear />
          </a-form-item>

          <a-form-item label="支付宝单日限额" name="alipayDayLimit">
            <a-input v-model:value="form.alipayDayLimit" placeholder="请输入微信单笔限额" allow-clear />
          </a-form-item>

          <a-form-item label="EPOS单日支付自额度" name="eposDaySelfLimit">
            <a-input v-model:value="form.eposDaySelfLimit" placeholder="请输入EPOS单日支付自额度" allow-clear />
          </a-form-item>

          <a-form-item label="POS手机Pay贷记卡单日限额" name="posNfcCreditDayLimit">
            <a-input v-model:value="form.posNfcCreditDayLimit" placeholder="请输入POS手机Pay贷记卡单日限额" allow-clear />
          </a-form-item>
          <a-form-item label="POS芯片贷记卡单日限额" name="posIcCreditDayLimit">
            <a-input v-model:value="form.posIcCreditDayLimit" placeholder="请输入POS芯片贷记卡单日限额" allow-clear />
          </a-form-item>
          <a-form-item label="POS磁条贷记卡单日限额" name="posCiCreditDayLimit">
            <a-input v-model:value="form.posCiCreditDayLimit" placeholder="请输入POS磁条贷记卡单日限额" allow-clear />
          </a-form-item>

          <a-form-item label="扫码单日支付额度" name="qrcDayPayLimit">
            <a-input v-model:value="form.qrcDayPayLimit" placeholder="请输入扫码单日支付额度" allow-clear />
          </a-form-item>

        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="微信单日限额" name="wechatDayLimit">
            <a-input v-model:value="form.wechatDayLimit" placeholder="请输入微信单笔限额" allow-clear />
          </a-form-item>

          <a-form-item label="EPOS单日限额" name="eposDayLimit">
            <a-input v-model:value="form.eposDayLimit" placeholder="请输入EPOS单笔限额" allow-clear />
          </a-form-item>

          <a-form-item label="D0提现单日限额" name="d0DayWithdrawLimit">
            <a-input v-model:value="form.d0DayWithdrawLimit" placeholder="请输入微信单笔限额" allow-clear />
          </a-form-item>

          <a-form-item label="POS手机Pay借记卡单日限额" name="posNfcDebitDayLimit">
            <a-input v-model:value="form.posNfcDebitDayLimit" placeholder="请输入POS手机Pay借记卡单日限额" allow-clear />
          </a-form-item>
          <a-form-item label="POS芯片借记卡单日限额" name="posIcDebitDayLimit">
            <a-input v-model:value="form.posIcDebitDayLimit" placeholder="请输入POS芯片借记卡单日限额" allow-clear />
          </a-form-item>
          <a-form-item label="POS磁条借记卡单日限额" name="posCiDebitDayLimit">
            <a-input v-model:value="form.posCiDebitDayLimit" placeholder="请输入POS磁条借记卡单日限额" allow-clear />
          </a-form-item>
          <a-form-item label="POS单日支付额度" name="posDayPayLimit">
            <a-input v-model:value="form.posDayPayLimit" placeholder="请输入POS单日支付额度" allow-clear />
          </a-form-item>
        </a-col>

        <!-- 单月限额 -->
        <a-divider orientation="left" :orientationMargin="40" dashed>单月限额</a-divider>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="银联扫码单月限额" name="unionpayMonthLimit">
            <a-input v-model:value="form.unionpayMonthLimit" placeholder="请输入银联扫码单月限额" allow-clear />
          </a-form-item>

          <a-form-item label="支付宝单月限额" name="alipayMonthLimit">
            <a-input v-model:value="form.alipayMonthLimit" placeholder="请输入支付宝单月限额" allow-clear />
          </a-form-item>

          <a-form-item label="EPOS单月支付额度" name="eposMonthSelfLimit">
            <a-input v-model:value="form.eposMonthSelfLimit" placeholder="请输入EPOS单月支付额度" allow-clear />
          </a-form-item>

          <a-form-item label="POS手机Pay贷记卡单月限额" name="posNfcCreditMonthLimit">
            <a-input v-model:value="form.posNfcCreditMonthLimit" placeholder="请输入POS手机Pay贷记卡单月限额" allow-clear />
          </a-form-item>
          <a-form-item label="POS芯片贷记卡单月限额" name="posIcCreditMonthLimit">
            <a-input v-model:value="form.posIcCreditMonthLimit" placeholder="请输入POS芯片贷记卡单月限额" allow-clear />
          </a-form-item>
          <a-form-item label="POS磁条贷记卡单月限额" name="posCiCreditMonthLimit">
            <a-input v-model:value="form.posCiCreditMonthLimit" placeholder="请输入POS磁条贷记卡单月限额" allow-clear />
          </a-form-item>
          <a-form-item label="扫码单月支付额度" name="qrcMonthPayLimit">
            <a-input v-model:value="form.qrcMonthPayLimit" placeholder="请输入扫码单月支付额度" allow-clear />
          </a-form-item>

        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="微信单月限额" name="wechatMonthLimit">
            <a-input v-model:value="form.wechatMonthLimit" placeholder="请输入微信单月限额" allow-clear />
          </a-form-item>

          <a-form-item label="EPOS单月限额" name="eposMonthLimit">
            <a-input v-model:value="form.eposMonthLimit" placeholder="请输入EPOS单月限额" allow-clear />
          </a-form-item>

          <a-form-item />

          <a-form-item label="POS手机Pay借记卡单月限额" name="posNfcDebitMonthLimit">
            <a-input v-model:value="form.posNfcDebitMonthLimit" placeholder="请输入POS手机Pay借记卡单月限额" allow-clear />
          </a-form-item>
          <a-form-item label="POS芯片借记卡单月限额" name="posIcDebitMonthLimit">
            <a-input v-model:value="form.posIcDebitMonthLimit" placeholder="请输入POS芯片借记卡单月限额" allow-clear />
          </a-form-item>
          <a-form-item label="POS磁条借记卡单月限额" name="posCiDebitMonthLimit">
            <a-input v-model:value="form.posCiDebitMonthLimit" placeholder="请输入POS磁条借记卡单月限额" allow-clear />
          </a-form-item>
          <a-form-item label="POS单月支付额度" name="posMonthPayLimit">
            <a-input v-model:value="form.posMonthPayLimit" placeholder="请输入POS单月支付额度" allow-clear />
          </a-form-item>
        </a-col>

        <!-- 交易/提现时间范围 -->
        <a-divider orientation="left" :orientationMargin="40" dashed>交易/提现时间范围</a-divider>
        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="交易时间范围" name="allowPayPeriod">
            <a-time-range-picker
              v-model:value="selectedPayTimeRange" @change="handlePayTimeChange" format="HH:mm"
              value-format="HH:mm" />
          </a-form-item>
        </a-col>

        <a-col :md="12" :sm="24" :xs="24">
          <a-form-item label="提现时间范围" name="allowWithdrawPeriod">
            <a-time-range-picker
              v-model:value="selectedWithdrawTimeRange" @change="handleWithdrawTimeChange"
              format="HH:mm" value-format="HH:mm" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>

  <!-- 商户选择弹窗 -->
  <MerchSelect v-if="showMerchList" v-model:visible="showMerchList" @done="setMerchInfo" />
</template>

<script>
import { TransactionLimitManageApi } from '@/api/riskManage/TransactionLimitManageApi';
import { message } from 'ant-design-vue';
import MerchSelect from './merch-select.vue';

export default {
  name: 'TransactionLimitManageEdit',
  components: {
    MerchSelect
  },
  props: {
    visible: Boolean,
    showData: Object
  },
  emits: ['update:visible', 'done'],
  data() {
    return {
      //表单数据
      form: {},
      //选择的允许支付时间段数组
      selectedPayTimeRange: [],
      //选择的允许提现支付时间段数组
      selectedWithdrawTimeRange: [],
      //是否是修改
      isUpdate: false,
      //提交状态
      loading: false,
      //是否展示商户列表
      showMerchList: false,
      //商户类型是否可编辑，从商户列表选择回来的就不能编辑
      isMerchantGradeDisabled: false,
      //表单规则
      rules: {
        merchantNo: [{ required: false, message: '请输入商户编号' }],

        unionpayOnceLimit: [{ required: true, message: '请输入银联扫码单笔限额' }],
        alipayOnceLimit: [{ required: true, message: '请输入支付宝单笔限额' }],
        wechatOnceLimit: [{ required: true, message: '请输入微信单笔限额' }],
        eposOnceLimit: [{ required: true, message: '请输入EPOS单笔限额' }],
        eposOnceSelfLimit: [{ required: true, message: '请输入POS单笔支付限额' }],

        unionpayDayLimit: [{ required: true, message: '请输入银联扫码单日额度' }],
        alipayDayLimit: [{ required: true, message: '请输入支付宝单日额度' }],
        wechatDayLimit: [{ required: true, message: '请输入微信单日额度' }],
        d0DayWithdrawLimit: [{ required: true, message: '请输入D0单日额度' }],
        eposDayLimit: [{ required: true, message: '请输入EPOS单日额度' }],
        eposDaySelfLimit: [{ required: true, message: '请输入EPOS单日自付额度' }],
        qrcDayPayLimit: [{ required: true, message: '请输入扫码单日支付额度' }],
        posDayPayLimit: [{ required: true, message: '请输入POS单日支付额度' }],

        unionpayMonthLimit: [{ required: true, message: '请输入银联扫码单月限额' }],
        alipayMonthLimit: [{ required: true, message: '请输入支付宝单月限额' }],
        wechatMonthLimit: [{ required: true, message: '请输入微信单月限额' }],
        eposMonthLimit: [{ required: true, message: '请输入EPOS单月限额' }],
        eposMonthSelfLimit: [{ required: true, message: '请输入EPOS单月自付限额' }],
        qrcMonthPayLimit: [{ required: true, message: '请输入扫码单月支付额度' }],
        posMonthPayLimit: [{ required: true, message: '请输入POS单月支付额度' }],

        posNfcCreditOnceLimit: [{ required: true, message: '请输入POS手机Pay贷记卡单笔限额' }],
        posNfcDebitOnceLimit: [{ required: true, message: '请输入POS手机Pay借记卡单笔限额' }],
        posIcCreditOnceLimit: [{ required: true, message: '请输入POS芯片贷记卡单笔限额' }],
        posIcDebitOnceLimit: [{ required: true, message: '请输入POS芯片借记卡单笔限额' }],
        posCiCreditOnceLimit: [{ required: true, message: '请输入POS磁条贷记卡单笔限额' }],
        posCiDebitOnceLimit: [{ required: true, message: '请输入POS磁条借记卡单笔限额' }],
        posNfcCreditDayLimit: [{ required: true, message: '请输入POS手机Pay贷记卡单日限额' }],
        posNfcDebitDayLimit: [{ required: true, message: '请输入POS手机Pay借记卡单日限额' }],
        posIcCreditDayLimit: [{ required: true, message: '请输入POS芯片贷记卡单日限额' }],
        posIcDebitDayLimit: [{ required: true, message: '请输入POS芯片借记卡单日限额' }],
        posCiCreditDayLimit: [{ required: true, message: '请输入POS磁条贷记卡单日限额' }],
        posCiDebitDayLimit: [{ required: true, message: '请输入POS磁条借记卡单日限额' }],
        posNfcCreditMonthLimit: [{ required: true, message: '请输入POS手机Pay贷记卡单月限额' }],
        posNfcDebitMonthLimit: [{ required: true, message: '请输入POS手机Pay借记卡单月限额' }],
        posIcCreditMonthLimit: [{ required: true, message: '请输入POS芯片贷记卡单月限额' }],
        posIcDebitMonthLimit: [{ required: true, message: '请输入POS芯片借记卡单月限额' }],
        posCiCreditMonthLimit: [{ required: true, message: '请输入POS磁条贷记卡单月限额' }],
        posCiDebitMonthLimit: [{ required: true, message: '请输入POS磁条借记卡单月限额' }],

        allowPayPeriod: [{ required: true, message: '请选择允许交易时间范围' }],
        allowWithdrawPeriod: [{ required: true, message: '请选择提现时间范围' }]
      }
    };
  },
  watch: {
    showData() {
      if (this.showData) {
        //有数据是编辑
        this.isUpdate = true;
        this.form = Object.assign({}, this.showData);

        let payTimeArr = this.form.allowPayPeriod.split('-');
        if (payTimeArr.length !== 0) {
          this.selectedPayTimeRange = payTimeArr;
        }

        let withdrawTimeArr = this.form.allowWithdrawPeriod.split('-');
        if (withdrawTimeArr.length !== 0) {
          this.selectedWithdrawTimeRange = withdrawTimeArr;
        }
      } else {
        //没有就是新增
        this.isUpdate = false;
        this.form = {};
        this.selectedPayTimeRange = [];
        this.selectedWithdrawTimeRange = [];
      }
    }
  },
  methods: {
    //传值更新父组件的值
    updateVisible(value) {
      this.$emit('update:visible', value);
    },

    merchNoSelectMethod() {
      this.showMerchList = true;
    },

    //从商户列表返回，配置相关数据
    setMerchInfo(merchInfo) {
      //选择的要让商户类型不能选择
      this.isMerchantGradeDisabled = true;
      this.form.merchantNo = merchInfo.merchantNo;
      this.form.merchantGrade = merchInfo.grade;
      this.form.riskGrade = merchInfo.riskGrade;

      //获取到相关数据之后，通过商户登记和风控等级去查询限额相关数据进行反显
      this.getTransactionLimitData({
        merchantGrade: merchInfo.grade
      });
    },

    async getTransactionLimitData(params) {
      // 修改加载框为正在加载
      this.loading = true;
      let result = null;
      result = TransactionLimitManageApi.getTransactionLimitManagePages(params);

      result
        .then(res => {
          // 移除加载框
          this.loading = false;

          //只把res.rows[0]里的值添加进去，并且不会覆盖this.form里面原有的属性和值
          if (this.form && typeof this.form === 'object' && res.rows[0] && typeof res.rows[0] === 'object') {
            const formKeys = Object.keys(this.form);
            for (const key in res.rows[0]) {
              if (!formKeys.includes(key)) {
                this.form[key] = res.rows[0][key];
              }
            }
          }

          let payTimeArr = this.form.allowPayPeriod.split('-');
          if (payTimeArr.length !== 0) {
            this.selectedPayTimeRange = payTimeArr;
          }

          let withdrawTimeArr = this.form.allowWithdrawPeriod.split('-');
          if (withdrawTimeArr.length != 0) {
            this.selectedWithdrawTimeRange = withdrawTimeArr;
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },

    /**
     * 保存和编辑
     */
    async save() {
      // 校验表单
      await this.$refs.form.validate();
      // 修改加载框为正在加载
      this.loading = true;

      let result = null;
      if (this.isUpdate) {
        // 修改
        result = TransactionLimitManageApi.edit(this.form);
      } else {
        //新增
        result = TransactionLimitManageApi.add(this.form);
      }

      result
        .then(res => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(res.message);
          // 如果是新增，则form表单置空
          if (!this.isUpdate) {
            this.form = {};
            this.selectedPayTimeRange = [];
            this.selectedWithdrawTimeRange = [];
          }
          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    cancel() {
      this.form = Object.assign({}, this.showData);
      this.$refs.form.clearValidate();
    },

    handlePayTimeChange(range) {
      const startTime = range[0]; //开始时间
      const endTime = range[1]; //结束时间
      this.form.allowPayPeriod = startTime + '-' + endTime;
    },

    handleWithdrawTimeChange(range) {
      const startTime = range[0]; //开始时间
      const endTime = range[1]; //结束时间
      this.form.allowWithdrawPeriod = startTime + '-' + endTime;
    }
  }
};
</script>

<style></style>
