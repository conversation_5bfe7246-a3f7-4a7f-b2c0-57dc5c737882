<template>
  <a-modal
    :width="750" :visible="visible" :maskClosable="false" title="详情" :mask-closable="false"
    :body-style="{ paddingBottom: '30px' }" @update:visible="updateVisible">
    <a-descriptions :column="2">
      <a-descriptions-item label="商户编码">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="商户等级">
        <a-tag v-if="form.merchantGrade === 'A'" color="success">企业商户</a-tag>
        <a-tag v-if="form.merchantGrade === 'B'" color="error">个体工商户</a-tag>
        <a-tag v-if="form.merchantGrade === 'C'" color="pink">小微商户</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="风控等级">
        <a-tag v-if="form.riskGrade === 1" color="success">1级</a-tag>
        <a-tag v-if="form.riskGrade === 2" color="error">2级</a-tag>
        <a-tag v-if="form.riskGrade === 3" color="pink">3级</a-tag>
        <a-tag v-if="form.riskGrade === 4" color="cyan">4级</a-tag>
        <a-tag v-if="form.riskGrade === 5" color="blue">5级</a-tag>
        <a-tag v-if="form.riskGrade === 6" color="purple">6级</a-tag>
        <a-tag v-if="form.riskGrade === 7" color="success">7级</a-tag>
        <a-tag v-if="form.riskGrade === 8" color="error">8级</a-tag>
        <a-tag v-if="form.riskGrade === 9" color="pink">9级</a-tag>
        <a-tag v-if="form.riskGrade === 10" color="cyan">10级</a-tag>
      </a-descriptions-item>
    </a-descriptions>

    <a-divider orientation="left" :orientationMargin="40" dashed>单笔限额</a-divider>
    <a-descriptions :column="2">
      <a-descriptions-item label="银联扫码单笔限额">{{ form.unionpayOnceLimit }}</a-descriptions-item>
      <a-descriptions-item label="微信单笔限额">{{ form.wechatOnceLimit }}</a-descriptions-item>
      <a-descriptions-item label="支付宝单笔限额">{{ form.alipayOnceLimit }}</a-descriptions-item>
      <a-descriptions-item label="EPOS单笔限额">{{ form.eposOnceLimit }}</a-descriptions-item>
      <a-descriptions-item label="EPOS单笔自付限额" :span="2">{{ form.eposOnceSelfLimit }}</a-descriptions-item>
      <a-descriptions-item label="POS手机Pay贷记卡单笔限额">{{ form.posNfcCreditOnceLimit }}</a-descriptions-item>
      <a-descriptions-item label="POS手机Pay借记卡单笔限额">{{ form.posNfcDebitOnceLimit }}</a-descriptions-item>
      <a-descriptions-item label="POS芯片贷记卡单笔限额">{{ form.posIcCreditOnceLimit }}</a-descriptions-item>
      <a-descriptions-item label="POS芯片借记卡单笔限额">{{ form.posIcDebitOnceLimit }}</a-descriptions-item>
      <a-descriptions-item label="POS磁条贷记卡单笔限额">{{ form.posCiCreditOnceLimit }}</a-descriptions-item>
      <a-descriptions-item label="POS磁条借记卡单笔限额">{{ form.posCiDebitOnceLimit }}</a-descriptions-item>
    </a-descriptions>

    <a-divider orientation="left" :orientationMargin="40" dashed>单日限额</a-divider>
    <a-descriptions :column="2">
      <a-descriptions-item label="银联扫码单日限额">{{ form.unionpayDayLimit }}</a-descriptions-item>
      <a-descriptions-item label="微信单日限额">{{ form.wechatDayLimit }}</a-descriptions-item>
      <a-descriptions-item label="支付宝单日限额">{{ form.alipayDayLimit }}</a-descriptions-item>
      <a-descriptions-item label="EPOS单日限额">{{ form.eposDayLimit }}</a-descriptions-item>
      <a-descriptions-item label="EPOS单日自付限额">{{ form.eposDaySelfLimit }}</a-descriptions-item>
      <a-descriptions-item label="D0提现单日额度">{{ form.d0DayWithdrawLimit }}</a-descriptions-item>
      <a-descriptions-item label="POS手机Pay贷记卡单日限额">{{ form.posNfcCreditDayLimit }}</a-descriptions-item>
      <a-descriptions-item label="POS手机Pay借记卡单日限额">{{ form.posNfcDebitDayLimit }}</a-descriptions-item>
      <a-descriptions-item label="POS芯片贷记卡单日限额">{{ form.posIcCreditDayLimit }}</a-descriptions-item>
      <a-descriptions-item label="POS芯片借记卡单日限额">{{ form.posIcDebitDayLimit }}</a-descriptions-item>
      <a-descriptions-item label="POS磁条贷记卡单日限额">{{ form.posCiCreditDayLimit }}</a-descriptions-item>
      <a-descriptions-item label="POS磁条借记卡单日限额">{{ form.posCiDebitDayLimit }}</a-descriptions-item>
      <a-descriptions-item label="扫码单日支付额度">{{ form.qrcDayPayLimit }}</a-descriptions-item>
      <a-descriptions-item label="POS单日支付额度">{{ form.posDayPayLimit }}</a-descriptions-item>
    </a-descriptions>

    <a-divider orientation="left" :orientationMargin="40" dashed>单月限额</a-divider>
    <a-descriptions :column="2">
      <a-descriptions-item label="银联扫码单月限额">{{ form.unionpayMonthLimit }}</a-descriptions-item>
      <a-descriptions-item label="微信单月限额">{{ form.wechatMonthLimit }}</a-descriptions-item>
      <a-descriptions-item label="支付宝单月限额">{{ form.alipayMonthLimit }}</a-descriptions-item>
      <a-descriptions-item label="EPOS单月限额">{{ form.eposMonthLimit }}</a-descriptions-item>
      <a-descriptions-item label="EPOS单月自付限额" :span="2">{{ form.eposMonthSelfLimit }}</a-descriptions-item>
      <a-descriptions-item label="POS手机Pay贷记卡单月限额">{{ form.posNfcCreditMonthLimit }}</a-descriptions-item>
      <a-descriptions-item label="POS手机Pay借记卡单月限额">{{ form.posNfcDebitMonthLimit }}</a-descriptions-item>
      <a-descriptions-item label="POS芯片贷记卡单月限额">{{ form.posIcCreditMonthLimit }}</a-descriptions-item>
      <a-descriptions-item label="POS芯片借记卡单月限额">{{ form.posIcDebitMonthLimit }}</a-descriptions-item>
      <a-descriptions-item label="POS磁条贷记卡单月限额">{{ form.posCiCreditMonthLimit }}</a-descriptions-item>
      <a-descriptions-item label="POS磁条借记卡单月限额">{{ form.posCiDebitMonthLimit }}</a-descriptions-item>
      <a-descriptions-item label="扫码单月支付额度">{{ form.qrcMonthPayLimit }}</a-descriptions-item>
      <a-descriptions-item label="POS单月支付额度">{{ form.posMonthPayLimit }}</a-descriptions-item>
    </a-descriptions>

    <a-divider orientation="left" :orientationMargin="40" dashed>交易/提现时间范围</a-divider>
    <a-descriptions :column="2">
      <a-descriptions-item label="交易时间范围">{{ form.allowPayPeriod }}</a-descriptions-item>
      <a-descriptions-item label="提现时间范围">{{ form.allowWithdrawPeriod }}</a-descriptions-item>
    </a-descriptions>

    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
export default {
  name: 'TransactionLimitManageDetail',
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  data() {
    return {
      form: {}
    };
  },
  watch: {
    //监控，外面点击切换就换值
    detail() {
      this.form = Object.assign({}, this.detail);
    }
  },
  methods: {
    //更新显示值
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>

<style></style>
