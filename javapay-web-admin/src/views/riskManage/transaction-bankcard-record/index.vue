<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="银行账户姓名">
              <a-input v-model:value.trim="where.bankAccountName" placeholder="请输入银行账户姓名" allow-clear />
            </a-form-item>
            <a-form-item label="银行账号">
              <a-input v-model:value.trim="where.bankAccountNoCipher" placeholder="请输入银行账号" allow-clear />
            </a-form-item>
            <a-form-item label="商户ID">
              <a-input v-model:value.trim="where.merchantId" placeholder="请输入商户ID" allow-clear />
            </a-form-item>
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
            </a-form-item>
            <a-form-item label="卡类型">
              <a-select v-model:value="where.cardType" style="width: 200px" placeholder="请选择" allow-clear>
                <a-select-option :value="1">借记卡</a-select-option>
                <a-select-option :value="2">贷记卡</a-select-option>
                <a-select-option :value="3">准贷记卡</a-select-option>
                <a-select-option :value="4">预付费卡</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table
          ref="table"
          row-key="id"
          :datasource="datasource"
          :columns="columns"
          :where="where"
          v-model:selection="selection"
          :scroll="{ x: 'max-content' }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'cardType'">
              <a-tag v-if="record.cardType === 1" color="pink">借记卡</a-tag>
              <a-tag v-else-if="record.cardType === 2" color="blue">贷记卡</a-tag>
              <a-tag v-else-if="record.cardType === 3" color="cyan">准贷记卡</a-tag>
              <a-tag v-else-if="record.cardType === 4" color="purple">预付费卡</a-tag>
            </template>
            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <TransactionBankcardRecordDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>

<script>
import { TransactionBankcardRecordApi } from '@/api/riskManage/TransactionBankcardRecordApi';
import TransactionBankcardRecordDetail from './transaction-bankcard-record-detail.vue';

export default {
  name: 'TransactionBankcardRecord',
  components: {
    TransactionBankcardRecordDetail
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '银行账号',
          dataIndex: 'bankAccountNoMask'
        },
        {
          title: '银行名称',
          dataIndex: 'bankName'
        },
        {
          title: '银行账户姓名',
          dataIndex: 'bankAccountName',
          align: 'center'
        },
        {
          title: '卡类型',
          key: 'cardType',
          dataIndex: 'cardType',
          align: 'center'
        },
        {
          title: '商户ID',
          dataIndex: 'merchantId',
          align: 'center'
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          width: 150,
          fixed: 'right'
        }
      ],
      // 表格搜索条件
      where: {},
      selection: [],
      current: null,
      showDetail: false
    };
  },
  methods: {
    reload() {
      this.selection = [];
      this.$refs.table.reload({ page: 1 });
    },

    reset() {
      this.where = {};
      this.selection = [];
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    datasource({ page, limit, where, orders }) {
      return TransactionBankcardRecordApi.findPage({ ...where, ...orders, pageNo: page, pageSize: limit });
    }
  }
};
</script>
