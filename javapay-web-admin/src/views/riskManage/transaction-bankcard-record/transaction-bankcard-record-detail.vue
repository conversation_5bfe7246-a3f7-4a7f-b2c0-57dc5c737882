<template>
  <a-modal :width="800" :visible="visible" title="详情" :mask-closable="false" :body-style="{ paddingBottom: '20px' }" @update:visible="updateVisible">
    <a-descriptions :column="2">
      <a-descriptions-item label="银行账号">{{ form.bankAccountNoMask }}</a-descriptions-item>
      <a-descriptions-item label="银行名称">{{ form.bankName }}</a-descriptions-item>
      <a-descriptions-item label="银行账户姓名">{{ form.bankAccountName }}</a-descriptions-item>
      <a-descriptions-item label="卡类型">
        <a-tag v-if="form.cardType === 1" color="pink">借记卡</a-tag>
        <a-tag v-else-if="form.cardType === 2" color="blue">贷记卡</a-tag>
        <a-tag v-else-if="form.cardType === 3" color="cyan">准贷记卡</a-tag>
        <a-tag v-else-if="form.cardType === 4" color="purple">预付费卡</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="商户ID">{{ form.merchantId }}</a-descriptions-item>
      <a-descriptions-item label="商户编号">{{ form.merchantNo }}</a-descriptions-item>
      <a-descriptions-item label="创建人">{{ form.createUserId }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改人">{{ form.lastModifyUserId }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';

export default {
  name: 'TransactionBankcardRecordDetail',
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {}
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
