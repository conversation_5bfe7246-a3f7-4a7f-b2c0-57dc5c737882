<!-- 新增和编辑弹窗 -->
<template>
  <a-modal
    :width="700"
    :visible="visible"
    :confirm-loading="loading"
    :title="isUpdate ? '修改' : '新建'"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @cancel="cancel"
    @ok="save"
  >
    <a-form ref="form" :model="form" :rules="rules">
      <a-form-item label="三方应用编号" name="thirdPlatNo">
        <a-input v-model:value="form.thirdPlatNo" placeholder="请输入三方应用编号" style="width: 280px" :disabled="isUpdate" allow-clear />
      </a-form-item>
      <a-form-item label="三方应用名称" name="thirdPlatName">
        <a-input v-model:value="form.thirdPlatName" placeholder="请输入三方应用名称" style="width: 280px" allow-clear />
      </a-form-item>

      <a-form-item label="有效状态" name="validStatus">
        <a-radio-group v-model:value="form.validStatus">
          <a-radio :value="1">有效</a-radio>
          <a-radio :value="0">无效</a-radio>
        </a-radio-group>
      </a-form-item>
    </a-form>

    <br />

    <div class="block-interval list-bottom">
      <a-button type="primary" @click="addRow">添加</a-button>

      <a-form v-for="(item, index) in paramsArr" :key="index" :rules="paramsRules" :model="item" layout="inline" class="list-margin">
        <a-row>
          <a-form-item label="IP白名单地址" name="ipAddr">
            <a-input style="width: 280px" v-model:value.trim="item.ipAddr" placeholder="请输入IP白名单地址" allow-clear />
          </a-form-item>
          <a-form-item class="ele-text-center">
            <a-button type="danger" @click="deleteRow(index)">删除</a-button>
          </a-form-item>
        </a-row>
      </a-form>
    </div>
  </a-modal>
</template>
  
  <script>
import { TripartiteApplicationManageApi } from '@/api/tripartiteApplication/TripartiteApplicationManageApi';
import { message } from 'ant-design-vue';

function formDefaults() {
  return {
    validStatus: 1
  };
}
export default {
  name: 'TripartiteApplicationEdit',
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: Object.assign({}, this.data),
      // 表单验证规则
      rules: {
        thirdPlatNo: [{ required: true, message: '请输入三方应用编号' }],
        thirdPlatName: [{ required: true, message: '请输入三方应用名称' }],
        validStatus: [{ required: true, message: '请选择有效状态' }]
      },
      //键值对表格规则
      paramsRules: {
        ipAddr: [{ required: true, message: '请输入IP白名单地址' }]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false,
      //接收键值对数组
      paramsArr: [
        {
          ipAddr: ''
        }
      ]
    };
  },
  watch: {
    data() {
      if (this.data) {
        this.form = Object.assign({}, this.data);

        this.paramsArr = this.form.ipWhiteList.split(',').map(item => {
          return { ipAddr: item };
        });

        this.isUpdate = true;
      } else {
        this.form = formDefaults();
        this.isUpdate = false;
      }
      if (this.$refs.form) {
        this.$refs.form.clearValidate();
      }
    }
  },
  methods: {
    addRow() {
      this.paramsArr.push({
        ipAddr: ''
      });
    },
    //删除键值对
    deleteRow(index) {
      this.paramsArr.splice(index, 1);
    },
    /**
     * 保存和编辑
     */
    async save() {
      // 校验表单
      await this.$refs.form.validate();
      // 修改加载框为正在加载
      this.loading = true;
      let result = null;

      this.form.ipWhiteList = this.paramsArr.map(item => item.ipAddr).join(',');
      // 执行编辑或修改
      if (this.isUpdate) {
        result = TripartiteApplicationManageApi.edit(this.form);
      } else {
        result = TripartiteApplicationManageApi.add(this.form);
      }
      result
        .then(result => {
          // 移除加载框
          this.loading = false;

          // 提示添加成功
          message.success(result.message);

          // 如果是新增，则form表单置空
          if (!this.isUpdate) {
            this.form = formDefaults();
          }

          // 关闭弹框，通过控制visible的值，传递给父组件
          this.updateVisible(false);

          // 触发父组件done事件
          this.$emit('done');
        })
        .catch(() => {
          this.loading = false;
        });
    },

    cancel() {
      this.form = Object.assign(formDefaults(), this.data);
      this.$refs.form.clearValidate();
    },

    /**
     * 更新编辑界面的弹框是否显示
     */
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>

<style>
.list-margin {
  margin-top: 20px;
}

.list-bottom {
  margin-bottom: 50px;
}
</style>
  