<template>
  <a-modal
    :width="600"
    :visible="visible"
    title="详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '30px' }"
    @update:visible="updateVisivle"
    :footer="null"
  >
    <a-descriptions :column="2">
      <a-descriptions-item label="三方应用名称">{{ form.thirdPlatName }}</a-descriptions-item>
      <a-descriptions-item label="三方应用编号">{{ form.thirdPlatNo }}</a-descriptions-item>
      <a-descriptions-item label="有效状态">
        <a-tag v-if="form.validStatus === 1" color="success">有效</a-tag>
        <a-tag v-else color="error">无效</a-tag>
      </a-descriptions-item>
    </a-descriptions>

    <div>
      <a-descriptions :column="2">
        <a-descriptions-item label="IP白名单地址" v-for="(item, index) in paramsArr" :key="index">{{ item.ipAddr }}</a-descriptions-item>
      </a-descriptions>
    </div>

    <br />

    <a-descriptions :column="2">
      <a-descriptions-item label="创建人">{{ form.createUserId }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="最后修改人">{{ form.lastModifyUserId }}</a-descriptions-item>
      <a-descriptions-item label="最后修改时间">{{ form.lastModifyTime }}</a-descriptions-item>
    </a-descriptions>
  </a-modal>
</template>
  
  <script>
export default {
  name: 'TripartiteApplicationDetail',
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  data() {
    return {
      form: {},
      paramsArr: []
    };
  },
  watch: {
    //父组件值改变则重新赋值
    detail() {
      this.form = Object.assign({}, this.detail);

      this.paramsArr = this.form.ipWhiteList.split(',').map(item => {
        return { ipAddr: item };
      });
    }
  },
  methods: {
    updateVisivle(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
  
  <style>
</style>
  