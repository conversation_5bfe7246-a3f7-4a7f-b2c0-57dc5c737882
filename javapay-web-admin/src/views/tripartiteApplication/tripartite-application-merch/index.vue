<template>
  <div class="ele-body">
    <!-- 搜索框内容 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row :gutter="[0, 16]">
            <a-form-item label="商户编号">
              <a-input v-model:value.trim="where.merchantNo" placeholder="请输入商户编号" allow-clear />
            </a-form-item>

            <a-form-item label="三方应用名称">
              <a-input v-model:value.trim="where.thirdPlatName" placeholder="请输入三方应用名称" allow-clear />
            </a-form-item>

            <a-form-item label="三方商户唯一标识">
              <a-input v-model:value.trim="where.thirdMerchantNo" placeholder="请输入三方商户唯一标识" allow-clear />
            </a-form-item>

            <a-form-item label="登录账号">
              <a-input v-model:value.trim="where.loginAccount" placeholder="请输入登录账号" allow-clear />
            </a-form-item>

            <a-form-item label="有效状态">
              <a-select v-model:value="where.validStatus" style="width: 205px" placeholder="请选择" allow-clear>
                <a-select-option value="1">有效</a-select-option>
                <a-select-option value="0">无效</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格内容 -->
    <div>
      <a-card :bordered="false" class="table-height">
        <ele-pro-table ref="table" row-key="id" :datasource="datasource" :columns="columns" :where="where" :scroll="{ x: 'max-content' }">
          <!-- 表格上方的操作按钮 -->
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="handleEdit()">
                <template #icon>
                  <plus-outlined />
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>

          <!-- 表体的操作 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'validStatus'">
              <a-switch :checked="record.validStatus === 1" @change="checked => editValidState(checked, record)" />
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleDetail(record)">详情</a>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 详情 -->
    <TripartiteApplicationMerchDetail v-model:visible="showDetail" :detail="current" />
  </div>
</template>
    
    <script>
import { TripartiteApplicationMerchApi } from '@/api/tripartiteApplication/TripartiteApplicationMerchApi';
import { message } from 'ant-design-vue';
import TripartiteApplicationMerchDetail from './tripartite-application-merch-detail.vue';

export default {
  name: 'TripartiteApplicationMerch',
  components: {
    TripartiteApplicationMerchDetail
  },
  data() {
    return {
      //表格查询条件
      where: {},
      //展示编辑页面传的参数
      current: null,
      //是否展示详情页面
      showDetail: false,
      //表格列配置
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          align: 'center'
        },
        {
          title: '商户编号',
          dataIndex: 'merchantNo',
          align: 'center'
        },
        {
          title: '商户登录账号(手机号)',
          dataIndex: 'loginAccount',
          align: 'center'
        },
        {
          title: '三方应用编号',
          dataIndex: 'thirdPlatNo',
          align: 'center'
        },
        {
          title: '三方商户唯一标识',
          dataIndex: 'thirdMerchantNo',
          align: 'center'
        },
        {
          title: '有效状态',
          dataIndex: 'validStatus',
          key: 'validStatus',
          width: 120,
          align: 'center'
        },
        {
          title: '创建人',
          dataIndex: 'createUserId',
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          align: 'center'
        },
        {
          title: '最后修改人',
          dataIndex: 'lastModifyUserId',
          align: 'center'
        },
        {
          title: '最后修改时间',
          dataIndex: 'lastModifyTime',
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 100,
          align: 'center'
        }
      ]
    };
  },
  methods: {
    //查询方法
    reload() {
      this.$refs.table.reload({ page: 1 });
    },
    //重置
    reset() {
      this.where = {};
      this.$refs.table.reload({ page: 1, where: this.where });
    },

    handleDetail(row) {
      this.current = row;
      this.showDetail = true;
    },

    async editValidState(checked, row) {
      const id = row.id;
      const validStatus = checked ? 1 : 0;
      const result = await TripartiteApplicationMerchApi.edit({ id, validStatus });
      message.success(result.message);
      row.validStatus = validStatus;
    },

    //获取数据方法
    datasource({ page, limit, where }) {
      return TripartiteApplicationMerchApi.findPage({ ...where, pageNo: page, pageSize: limit });
    }
  }
};
</script>
    