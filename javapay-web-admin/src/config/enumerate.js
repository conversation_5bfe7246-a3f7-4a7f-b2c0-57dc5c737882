// 证件类型
export const certTypeEnum = [
  { label: '居民身份证', value: '01' },
  { label: '军官证', value: '02' },
  { label: '护照', value: '03' },
  { label: '港澳居民来往内地通行证(回乡证)', value: '04' },
  { label: '台湾同胞来往内地通行证', value: '05' },
  { label: '警官证', value: '06' },
  { label: '士兵证', value: '07' },
  { label: '户口薄', value: '08' },
  { label: '临时身份证', value: '09' },
  { label: '外国人居留证', value: '10' },
  { label: '其他证件', value: '11' }
];

// 图片类型
export const imageTypeEnum = [
  { label: '身份证图片人像面', value: 1 },
  { label: '身份证图片国徽面', value: 2 },
  { label: '银行卡图片卡号面', value: 3 },
  { label: '银行卡图片背面', value: 4 },
  { label: '人脸正面照片', value: 5 },
  { label: '手持身份证头像面', value: 6 },
  { label: '营业执照照片', value: 7 },
  { label: '税务登记证照片', value: 8 },
  { label: '组织机构代码证照片', value: 9 },
  { label: '三方协议合同照片', value: 10 },
  { label: '门头照', value: 11 },
  { label: '店内环境照片', value: 12 },
  { label: '收银台照片', value: 13 },
  { label: '电子签名图像', value: 14 },
  { label: '协议照', value: 15 },
  { label: '开户许可证', value: 16 },
  { label: '开户意愿照片', value: 17 },
  { label: '代付证明图片', value: 18 },
  { label: '转账申请图片', value: 19 },
  { label: '清算授权书', value: 20 },
  { label: '门牌号', value: 21 },
  { label: '签购单', value: 22 },
];
