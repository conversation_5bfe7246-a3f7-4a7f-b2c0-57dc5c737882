import { message } from 'ant-design-vue';
import ImageCompressor from 'js-image-compressor';
import ImageCompressorSpecifySize from 'imagemin-web';

/**
 * 图片压缩
 * @param {*} file 文件
 */
export function compressorImage(file) {
  const fileSize = parseFloat(parseInt(file['size']) / 1024 / 1024).toFixed(2);
  // 压缩比
  const quality = fileSize < 2 ? 0.5 : 0.2;
  return new Promise((resolve, reject) => {
    // 配置项
    const options = {
      file,
      quality,
      loose: true,
      mimeType: 'image/jpeg',
      redressOrientation: true,
      convertSize: Infinity,

      // 压缩前回调
      beforeCompress: function (result) {
        console.log('压缩之前图片尺寸大小: ', result.size);
        console.log('mime 类型: ', result.type);
      },

      // 压缩成功回调
      success: function (result) {
        ImageCompressor.file2DataUrl(result, function (url) {
          // url base64值; mime 文件类型
          resolve({ url, mime: result.type });
        });
        console.log('压缩之后图片尺寸大小: ', result.size);
        console.log('mime 类型: ', result.type);
        console.log('实际压缩率： ', (((file.size - result.size) / file.size) * 100).toFixed(2) + '%');
      },

      // 发生错误
      error: function (err) {
        message.error('图片压缩失败, 请重试!');
        reject(err);
      }
    };
    new ImageCompressor(options);
  });
}

/**
 * 图片压缩到指定大小 默认200kb
 * @param {*} file 文件
 * @param {*} maxSize 压缩到指定尺寸
 */
export function compressorImageSpecifySize(file, { quality = 0.9, maxSize = 200, ...reset } = {}) {
  return new Promise((resolve, reject) => {
    // 配置项
    const options = {
      quality,
      maxSize,
      ...reset,

      // 成功回调
      success: function (blob) {
        fileToData(blob).then(function (dataURL) {
          resolve({ url: dataURL });
        });
      },

      // 错误回调
      fail: function (err) {
        reject(err);
      }
    };
    new ImageCompressorSpecifySize(file, options);
  });
}

/**
 * 文件File转 base64图片url
 * @param {[File | Blob]} file 文件File对象
 * @param {String} type 返回类型 ['arrayBuffer', 'dataURL']
 * @returns {Promise}
 */
export function fileToData(file, type = 'dataURL') {
  return new Promise((resolve, reject) => {
    let reader = new FileReader();
    reader.onload = function (e) {
      resolve(e.target.result);
    };
    reader.onloadend = function () {
      reader = null;
    };
    reader.onabort = function () {
      reject(new Error('Aborted to read the image with FileReader.'));
    };
    reader.onerror = function () {
      reject(new Error('Failed to read the image with FileReader.'));
    };
    if (type === 'arrayBuffer') {
      reader.readAsArrayBuffer(file);
    } else {
      reader.readAsDataURL(file);
    }
  });
}
