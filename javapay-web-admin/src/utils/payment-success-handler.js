import PublishApi from '@/api/system/notice/PublishApi';
import { message } from 'ant-design-vue';

/**
 * 支付成功处理工具类
 * 用于处理支付成功后的各种操作，包括发送通知消息
 * 
 * <AUTHOR> @date 2024/08/28
 */
export class PaymentSuccessHandler {
  
  /**
   * 处理支付成功事件
   * @param {Object} paymentData - 支付数据
   * @param {string} paymentData.userId - 用户ID
   * @param {string} paymentData.paymentId - 支付ID
   * @param {number} paymentData.amount - 支付金额
   * @param {string} paymentData.userAddress - 用户地址（可选）
   * @param {string} paymentData.productName - 产品名称（可选）
   * @param {Object} options - 配置选项
   * @param {boolean} options.sendNotification - 是否发送通知，默认true
   * @param {boolean} options.generateAddress - 是否生成专属地址，默认true
   * @param {string} options.customMessage - 自定义消息内容
   */
  static async handlePaymentSuccess(paymentData, options = {}) {
    const {
      sendNotification = true,
      generateAddress = true,
      customMessage = null
    } = options;

    try {
      // 1. 生成或获取用户专属地址
      let userAddress = paymentData.userAddress;
      if (generateAddress && !userAddress) {
        userAddress = await this.generateUserAddress(paymentData);
      }

      // 2. 发送支付成功通知消息
      if (sendNotification) {
        await this.sendPaymentSuccessNotification({
          userId: paymentData.userId,
          paymentId: paymentData.paymentId,
          amount: paymentData.amount,
          address: userAddress,
          productName: paymentData.productName,
          customMessage
        });
      }

      // 3. 记录支付成功日志
      this.logPaymentSuccess(paymentData, userAddress);

      return {
        success: true,
        address: userAddress,
        message: '支付成功处理完成'
      };

    } catch (error) {
      console.error('支付成功处理失败:', error);
      return {
        success: false,
        error: error.message,
        message: '支付成功处理失败'
      };
    }
  }

  /**
   * 发送支付成功通知消息
   * @param {Object} params - 通知参数
   */
  static async sendPaymentSuccessNotification(params) {
    const {
      userId,
      paymentId,
      amount,
      address,
      productName = '商品',
      customMessage
    } = params;

    // 构建通知内容
    let noticeContent = customMessage || this.buildNotificationContent({
      amount,
      paymentId,
      address,
      productName
    });

    try {
      const result = await PublishApi.sendPaymentSuccessNotice({
        userId,
        paymentId,
        address,
        amount,
        noticeContent
      });

      console.log('支付成功通知发送成功:', result);
      return result;
    } catch (error) {
      console.error('发送支付成功通知失败:', error);
      throw error;
    }
  }

  /**
   * 构建通知消息内容
   * @param {Object} params - 消息参数
   * @returns {string} 格式化的通知内容
   */
  static buildNotificationContent(params) {
    const { amount, paymentId, address, productName } = params;
    const currentTime = new Date().toLocaleString('zh-CN');

    return `🎉 支付成功通知

📦 商品：${productName}
💰 支付金额：¥${amount}
🔢 订单号：${paymentId}
⏰ 支付时间：${currentTime}

🏠 您的专属地址：
${address}

📱 请保存好您的地址信息，如有疑问请联系客服。

感谢您的使用！`;
  }

  /**
   * 生成用户专属地址
   * @param {Object} paymentData - 支付数据
   * @returns {string} 生成的地址
   */
  static async generateUserAddress(paymentData) {
    // 这里可以根据实际业务逻辑生成地址
    // 例如：调用第三方API、根据用户信息生成等
    
    const timestamp = Date.now();
    const randomStr = Math.random().toString(36).substring(2, 8);
    
    // 示例地址格式，实际应用中应该根据业务需求调整
    const address = `https://yourapp.com/user/${paymentData.userId}/access/${timestamp}${randomStr}`;
    
    return address;
  }

  /**
   * 批量处理支付成功事件
   * @param {Array} paymentList - 支付数据列表
   * @param {Object} options - 配置选项
   */
  static async batchHandlePaymentSuccess(paymentList, options = {}) {
    const results = [];
    
    for (const paymentData of paymentList) {
      try {
        const result = await this.handlePaymentSuccess(paymentData, options);
        results.push({
          paymentId: paymentData.paymentId,
          ...result
        });
      } catch (error) {
        results.push({
          paymentId: paymentData.paymentId,
          success: false,
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * 记录支付成功日志
   * @param {Object} paymentData - 支付数据
   * @param {string} address - 生成的地址
   */
  static logPaymentSuccess(paymentData, address) {
    const logData = {
      timestamp: new Date().toISOString(),
      userId: paymentData.userId,
      paymentId: paymentData.paymentId,
      amount: paymentData.amount,
      address: address,
      action: 'PAYMENT_SUCCESS_HANDLED'
    };

    console.log('支付成功处理日志:', logData);
    
    // 这里可以调用日志记录API
    // LogApi.recordPaymentSuccess(logData);
  }

  /**
   * 验证支付数据
   * @param {Object} paymentData - 支付数据
   * @returns {boolean} 验证结果
   */
  static validatePaymentData(paymentData) {
    const requiredFields = ['userId', 'paymentId', 'amount'];
    
    for (const field of requiredFields) {
      if (!paymentData[field]) {
        throw new Error(`缺少必要字段: ${field}`);
      }
    }

    if (paymentData.amount <= 0) {
      throw new Error('支付金额必须大于0');
    }

    return true;
  }

  /**
   * 发送实时消息到聊天框（如果页面有聊天组件）
   * @param {string} userId - 用户ID
   * @param {string} message - 消息内容
   * @param {string} address - 地址信息
   */
  static sendChatMessage(userId, message, address) {
    // 如果页面有聊天组件，可以通过事件总线或直接调用组件方法
    const chatMessage = {
      type: 'PAYMENT_SUCCESS',
      content: message,
      address: address,
      timestamp: Date.now(),
      userId: userId
    };

    // 发送到聊天框
    window.dispatchEvent(new CustomEvent('payment-success-chat', {
      detail: chatMessage
    }));

    // 或者通过Vue的事件总线
    // this.$eventBus.$emit('payment-success-chat', chatMessage);
  }
}

/**
 * 支付成功处理的快捷方法
 * @param {Object} paymentData - 支付数据
 * @param {string} customAddress - 自定义地址（可选）
 */
export const handleWhopPaymentSuccess = async (paymentData, customAddress = null) => {
  try {
    // 验证数据
    PaymentSuccessHandler.validatePaymentData(paymentData);

    // 如果提供了自定义地址，使用自定义地址
    if (customAddress) {
      paymentData.userAddress = customAddress;
    }

    // 处理支付成功
    const result = await PaymentSuccessHandler.handlePaymentSuccess(paymentData, {
      sendNotification: true,
      generateAddress: !customAddress // 如果有自定义地址就不生成
    });

    if (result.success) {
      message.success('支付成功，地址已发送到您的消息中心！');
      
      // 发送到聊天框（如果需要）
      PaymentSuccessHandler.sendChatMessage(
        paymentData.userId,
        '支付成功！您的专属地址已生成。',
        result.address
      );
    } else {
      message.error('支付成功处理失败：' + result.message);
    }

    return result;
  } catch (error) {
    console.error('Whop支付成功处理错误:', error);
    message.error('处理支付成功事件时发生错误');
    return { success: false, error: error.message };
  }
};
