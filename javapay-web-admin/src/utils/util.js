//深度克隆
export function deepCopy(o) {
  if (o instanceof Array) {
    let n = [];
    for (let i = 0; i < o.length; ++i) {
      n[i] = deepCopy(o[i]);
    }
    return n;
  } else if (o instanceof Object) {
    let n = {};
    for (let i in o) {
      n[i] = deepCopy(o[i]);
    }
    return n;
  } else {
    return o;
  }
}

/**
 * @description: 文件下载
 * @param {Blob} res blob对象
 * @param {string} filename 文件名称，包含文件后缀
 * @param {string} type 文件类型
 */
const ExcelBlobType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
export function download(res, filename, type = ExcelBlobType) {
  // 创建blob对象，解析流数据
  const blob = new Blob([res], {
    // 设置返回的文件类型, 默认为excel
    type: type
  });
  const a = document.createElement('a');
  const URL = window.URL || window.webkitURL;
  const herf = URL.createObjectURL(blob);
  a.href = herf;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  window.URL.revokeObjectURL(herf);
}
