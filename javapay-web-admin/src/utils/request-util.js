/**
 * 通用请求工具类封装
 *
 * <AUTHOR>
 * @date 2022/4/8 10:47
 */
import request from '@/utils/request';
import { getToken } from '@/utils/token-util';

export default class RequestUtil {
  /**
   * get请求
   *
   * <AUTHOR>
   * @date 2022/4/8 10:50
   */
  static async get(url, params) {
    if (params === undefined) {
      params = {};
    }
    let result = await request.get(url, { params });
    return result.data;
  }

  /**
   * get请求并获取数据
   *
   * <AUTHOR>
   * @date 2022/4/8 10:50
   */
  static async getAndLoadData(url, params) {
    let result = await this.get(url, params);
    return result.data;
  }

  /**
   * post请求
   *
   * <AUTHOR>
   * @date 2022/4/8 10:50
   */
  static async post(url, params) {
    if (params === undefined) {
      params = {};
    }
    let result = await request.post(url, params);
    return result.data;
  }

  /**
   * post请求并获取数据
   *
   * <AUTHOR>
   * @date 2022/4/8 10:50
   */
  static async postAndLoadData(url, params) {
    let result = await this.post(url, params);
    return result.data;
  }

  /**
   * 配置的方式请求
   *
   */
  static async requestByConfig(config) {
    if (!config?.url) throw new Error('-----requestByConfig方法 请求配置有误-----');
    let result = await request(config);
    return result.data;
  }

  /**
   * 配置的方式请求
   *
   */
  static async requestByConfigNoData(config) {
    if (!config?.url) throw new Error('-----requestByConfig方法 请求配置有误-----');
    let result = await request(config);
    return result
  }

  /**
   * 封装downLoad请求
   *
   * <AUTHOR>
   * @date 2021/4/2 16:13
   */
  static downLoad(url, params) {
    if (params === undefined) {
      params = {};
    }
    let paramUrl = '?';
    for (let field in params) {
      if (params[field]) {
        paramUrl = paramUrl + field + '=' + params[field] + '&';
      }
    }
    paramUrl = paramUrl.substring(0, paramUrl.length - 1);
    if (!paramUrl.includes('&token=')) {
      paramUrl = `${paramUrl}&token=${getToken()}`;
    }
    window.location.href = `${url}${paramUrl}`;
  }
}
